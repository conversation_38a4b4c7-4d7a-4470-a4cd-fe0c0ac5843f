import request from '@/utils/request'

// 查询安全投入管理（预算及实际投入记录）列表
export function listManagement(query) {
  return request({
    url: '/system/management/list',
    method: 'get',
    params: query
  })
}

// 查询安全投入管理（预算及实际投入记录）详细
export function getManagement(id) {
  return request({
    url: '/system/management/' + id,
    method: 'get'
  })
}

// 新增安全投入管理（预算及实际投入记录）
export function addManagement(data) {
  return request({
    url: '/system/management',
    method: 'post',
    data: data
  })
}

// 修改安全投入管理（预算及实际投入记录）
export function updateManagement(data) {
  return request({
    url: '/system/management',
    method: 'put',
    data: data
  })
}

// 删除安全投入管理（预算及实际投入记录）
export function delManagement(id) {
  return request({
    url: '/system/management/' + id,
    method: 'delete'
  })
}
