<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">项目管理</div>
            <div class="top-right">
              <!-- <el-button type="primary" icon="el-icon-download" size="small">导出项目</el-button> -->
              <el-button
                type="primary"
                icon="el-icon-upload2"
                size="small"
                @click="addProject"
                >添加项目</el-button
              >
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="88px"
          >
            <el-form-item label="项目名称" prop="corporatename">
              <el-input
                v-model="queryParams.corporatename"
                placeholder="请输入项目名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="负责人" prop="liaison">
              <el-input
                v-model="queryParams.liaison"
                placeholder="请输入负责人"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="负责人电话" prop="phonenumber">
              <el-input
                v-model="queryParams.phonenumber"
                placeholder="请输入负责人电话"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:projectinspection:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:projectinspection:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:projectinspection:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:projectinspection:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

          <el-table
            v-loading="loading"
            :data="projectinspectionList"
            @selection-change="handleSelectionChange"
            height="calc(100vh - 250px)"
            style="width: 100%"
          >
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column label="所属公司" align="center" prop="deptId">
              <template slot-scope="scope">
                {{ getDeptName(scope.row.deptId) }}
              </template>
            </el-table-column>
            <el-table-column
              label="项目名称"
              align="center"
              prop="corporatename"
            />
            <el-table-column
              label="创建时间 "
              align="center"
              prop="time"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.time, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="项目地址"
              align="center"
              prop="companyaddress"
            />
            <el-table-column label="项目负责人" align="center" prop="liaison" />
            <el-table-column
              label="负责人电话"
              align="center"
              prop="phonenumber"
            />
            <!-- <el-table-column label="负责人职务" align="center" prop="duties" /> -->
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="250"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click="showBuild(scope.row)"
                  >建筑</el-button
                >
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click="showEdit(scope.row)"
                  v-hasPermi="['system:project:edit']"
                  >编辑</el-button
                >
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:project:delete']"
                  >删除</el-button
                >
                <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:projectinspection:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:projectinspection:remove']">删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改企业项目对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司名称" prop="corporatename">
          <el-input v-model="form.corporatename" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="检查时间 " prop="time">
          <el-date-picker clearable
            v-model="form.time"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择检查时间 ">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="*地址" prop="companyaddress">
          <el-input v-model="form.companyaddress" placeholder="请输入*地址" />
        </el-form-item>
        <el-form-item label="*负责人" prop="liaison">
          <el-input v-model="form.liaison" placeholder="请输入*负责人" />
        </el-form-item>
        <el-form-item label="*负责人电话" prop="phonenumber">
          <el-input v-model="form.phonenumber" placeholder="请输入*负责人电话" />
        </el-form-item>
        <el-form-item label="*负责人职务" prop="duties">
          <el-input v-model="form.duties" placeholder="请输入*负责人职务" />
        </el-form-item>
        <el-form-item label="签名图片" prop="sign">
          <el-input v-model="form.sign" placeholder="请输入签名图片" />
        </el-form-item>
        <el-form-item label="签名时间" prop="signtime">
          <el-date-picker clearable
            v-model="form.signtime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择签名时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="公司代码" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="企业片区" prop="area">
          <el-input v-model="form.area" placeholder="请输入企业片区" />
        </el-form-item>
        <el-form-item label="开工日期" prop="startdate">
          <el-date-picker clearable
            v-model="form.startdate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开工日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="竣工日期" prop="enddate">
          <el-date-picker clearable
            v-model="form.enddate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择竣工日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="建设单位" prop="project">
          <el-input v-model="form.project" placeholder="请输入建设单位" />
        </el-form-item>
        <el-form-item label="监理单位" prop="construction">
          <el-input v-model="form.construction" placeholder="请输入监理单位" />
        </el-form-item>
        <el-form-item label="施工单位" prop="constructionunit">
          <el-input v-model="form.constructionunit" placeholder="请输入施工单位" />
        </el-form-item>
        <el-form-item label="地图坐标" prop="location">
          <el-input v-model="form.location" placeholder="请输入地图坐标" />
        </el-form-item>
        <el-form-item label="施工许可证编码" prop="Constructionpermitcode">
          <el-input v-model="form.Constructionpermitcode" placeholder="请输入施工许可证编码" />
        </el-form-item>
        <el-form-item label="安监检查人员" prop="safetyrectifier">
          <el-input v-model="form.safetyrectifier" placeholder="请输入安监检查人员" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->

    <el-dialog :visible.sync="showEditForm" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>
          {{
            editForm.projectinspectionid
              ? "编辑：" + editForm.corporatename
              : "新增"
          }}
        </div>
      </div>
      <div class="detail-content">
        <div class="detail-edit-content">
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="所属公司" prop="deptId">
              <el-select v-model="editForm.deptId" style="width: 100%">
                <template>
                  <div v-for="group in deptList" :key="group.id">
                    <el-option
                      :label="group.enterpriseName"
                      :value="group.id"
                      style="padding-left: 20px"
                    />
                    <template v-if="group.children && group.children.length">
                      <el-option
                        v-for="child in renderDeptOptions(group.children)"
                        :key="child.id"
                        :label="child.enterpriseName"
                        :value="child.id"
                        :style="{ paddingLeft: child.level * 20 + 'px' }"
                      />
                    </template>
                  </div>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称" prop="corporatename">
              <el-input
                v-model="editForm.corporatename"
                placeholder="请输入项目名称"
              />
            </el-form-item>
            <el-form-item label="项目地址" prop="companyaddress">
              <el-input
                v-model="editForm.companyaddress"
                placeholder="请输入项目地址"
              />
            </el-form-item>
            <el-form-item label="项目负责人" prop="liaison">
              <el-input
                v-model="editForm.liaison"
                placeholder="请输入项目负责人"
              />
            </el-form-item>
            <el-form-item label="项目负责人电话" prop="phonenumber">
              <el-input
                v-model="editForm.phonenumber"
                placeholder="请输入项目负责人电话"
              />
            </el-form-item>
            <!-- <el-form-item label="项目负责人职位" prop="duties">
              <el-input
                v-model="editForm.duties"
                placeholder="请输入项目负责人职位"
              />
            </el-form-item> -->
            <!-- <el-form-item label="使用类型" prop="phonenumber">
              <el-select v-model="editForm.useType" placeholder="请选择">
                <el-option
                  v-for="item in leixingList"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="单位名称" prop="unitName">
              <el-input
                v-model="editForm.unitName"
                placeholder="请输入单位名称"
              />
            </el-form-item>
            <el-form-item label="门头照" prop="fileList1">
              <el-upload
                style="margin-left: 20px"
                action
                :auto-upload="false"
                list-type="picture-card"
                :limit="1"
                :file-list="editForm.fileList1"
                :on-change="
                  (file, fileList) => {
                    uploadFile(file, fileList, 1);
                  }
                "
                accept="image/*"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="使用部位" prop="applicationLocation">
              <el-input
                v-model="editForm.applicationLocation"
                placeholder="请输入使用部位"
              />
            </el-form-item> -->
            <el-form-item label="所属区划" prop="belongingRegion">
              <el-cascader
                v-model="editForm.belongingRegion"
                :options="address"
              ></el-cascader>
            </el-form-item>

            <!-- <el-form-item label="是否开通企业码" prop="corporatename">
              <el-select v-model="editForm.isOpenCode" placeholder="请选择">
                <el-option
                  v-for="item in kaitongList"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="*营业执照" prop="fileList2">
              <el-upload
                style="margin-left: 20px"
                action
                :auto-upload="false"
                list-type="picture-card"
                :limit="1"
                :file-list="editForm.fileList2"
                :on-change="
                  (file, fileList) => {
                    uploadFile(file, fileList, 2);
                  }
                "
                accept="image/*"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="营业执照无法获取" prop="unableObtain">
              <el-switch v-model="editForm.unableObtain"> </el-switch>
            </el-form-item>
            <el-form-item
              v-if="editForm.unableObtain"
              label="*营业执照无法获取原因"
              prop="unableObtainContent"
            >
              <el-input
                v-model="editForm.unableObtainContent"
                placeholder="请输入营业执照无法获取原因"
              />
            </el-form-item>
            <el-form-item label="法定代表人" prop="legalRepresentative">
              <el-input
                v-model="editForm.legalRepresentative"
                placeholder="请输入法定代表人"
              />
            </el-form-item> -->
            <el-form-item label="消防安全管理人" prop="fireSafetyManager">
              <el-input
                v-model="editForm.fireSafetyManager"
                placeholder="请输入消防安全管理人"
              />
            </el-form-item>
            <el-form-item label="联系电话" prop="contactNumber">
              <el-input
                v-model="editForm.contactNumber"
                type="number"
                placeholder="请输入联系电话"
              />
            </el-form-item>
            <el-form-item label="单位类别1(人员密集场所)" prop="unitCategory1">
              <el-select
                v-model="editForm.unitCategory1"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in unitCateList1"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-tooltip
                class="item"
                effect="dark"
                content="点击新增自定义选项"
                placement="right"
              >
                <el-button
                  style="margin-left: 10px"
                  type="text"
                  icon="el-icon-plus"
                  @click="addCustom('aj_densely_populated_areas')"
                ></el-button>
              </el-tooltip>
            </el-form-item>
            <el-form-item
              label="单位类别2(多业态混合生产经营场所)"
              prop="unitCategory2"
            >
              <el-select
                v-model="editForm.unitCategory2"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in unitCateList2"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="单位类别3(九小场所)" prop="unitCategory3">
              <el-select
                v-model="editForm.unitCategory3"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in unitCateList3"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-tooltip
                class="item"
                effect="dark"
                content="点击新增自定义选项"
                placement="right"
              >
                <el-button
                  style="margin-left: 10px"
                  type="text"
                  icon="el-icon-plus"
                  @click="addCustom('aj_nine_small_places')"
                ></el-button>
              </el-tooltip>
            </el-form-item>
            <!-- <el-form-item label="租用建筑面积" prop="rentalBuildingArea">
              <el-input
                v-model="editForm.rentalBuildingArea"
                type="number"
                placeholder="请输入租用建筑面积"
              />
            </el-form-item>
            <el-form-item label="夹(插)层面积" prop="layerArea">
              <el-input
                v-model="editForm.layerArea"
                type="number"
                placeholder="请输入夹(插)层面积"
              />
            </el-form-item>
            <el-form-item label="冷库面积" prop="coldStorageArea">
              <el-input
                v-model="editForm.coldStorageArea"
                type="number"
                placeholder="请输入冷库面积"
              />
            </el-form-item> -->
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveUnit">确 定</el-button>
        <el-button @click="showEditForm = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="showBuildList" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailForm.corporatename }}</div>
        <div class="title-btn" style="margin-right: 50px">
          <el-button size="small" type="primary" @click="bindBuild"
            >绑定建筑</el-button
          >
        </div>
      </div>
      <div class="detail-content">
        <div
          class="detail-item"
          v-for="(item, index) in detailForm.buildList"
          :key="index"
        >
          <div class="detail-top">
            <div class="name">{{ item.buildingName }}</div>
            <div class="edit">
              <el-button
                size="small"
                type="danger"
                @click="unBindUnit(item)"
                v-hasPermi="['system:unit:unbind']"
                >解绑</el-button
              >
            </div>
          </div>
          <!-- <div class="detail-center">
                        <div class="detail-center-left">
                            <div class="detail-center-item">
                                <div class="detail-center-item-left">建筑名称：</div>
                                <div class="detail-center-item-right">{{ item.buildingName }}</div>
                            </div>
                            <div class="detail-center-item">
                                <div class="detail-center-item-left">建筑地址：</div>
                                <div class="detail-center-item-right">{{ item.buildingAddress }}{{ item.detailedAddress }}</div>
                            </div>
                            <div class="detail-center-item">
                                <div class="detail-center-item-left">经纬度：</div>
                                <div class="detail-center-item-right">{{ item.latitudeLongitude }}{{ item.detailedAddress }}</div>
                            </div>
                            <div class="detail-center-item">
                                <div class="detail-center-item-left">经纬度：</div>
                                <div class="detail-center-item-right">{{ item. }}{{ item.detailedAddress }}</div>
                            </div>
                        </div>
                    </div> -->
          <div class="detail-center">
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑名称：</div>
              <div class="detail-center-item-right">
                {{ item.buildingName }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑地址：</div>
              <div class="detail-center-item-right">
                {{ item.buildingAddress }} {{ item.detailedAddress }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">经纬度：</div>
              <div class="detail-center-item-right">
                {{ item.latitudeLongitude }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑结构：</div>
              <div class="detail-center-item-right">
                {{ item.buildingStructure }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">总建筑面积（平方米）：</div>
              <div class="detail-center-item-right">
                {{ item.totalConstructionArea }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑高度（米）：</div>
              <div class="detail-center-item-right">
                {{ item.buildingHeight }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑层数-地上：</div>
              <div class="detail-center-item-right">
                {{ item.buildingArrangement }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">地下：</div>
              <div class="detail-center-item-right">
                {{ item.undergroundFloors }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑消防设施：</div>
              <div class="detail-center-item-right">
                {{ item.protectionFacilities }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑使用性质：</div>
              <div class="detail-center-item-right">{{ item.natureUse }}</div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">产权类型：</div>
              <div class="detail-center-item-right">
                {{ item.propertyRightType }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑产权单位(人)：</div>
              <div class="detail-center-item-right">
                {{ item.propertyUnit }}
              </div>
            </div>
            <template v-if="item.propertyRightType == '单产权'">
              <div class="detail-center-item">
                <div class="detail-center-item-left">法定代表人：</div>
                <div class="detail-center-item-right">
                  {{ item.legalRepresentative }}
                </div>
              </div>
            </template>
            <template v-if="item.propertyRightType == '多产权'">
              <div class="detail-center-item">
                <div class="detail-center-item-left">建筑管理单位：</div>
                <div class="detail-center-item-right">
                  {{ item.managementUnit }}
                </div>
              </div>
              <div class="detail-center-item">
                <div class="detail-center-item-left">
                  产权或管理单位法定代表人
                </div>
                <div class="detail-center-item-right">
                  {{ item.legalRepresentative }}
                </div>
              </div>
            </template>
            <div class="detail-center-item">
              <div class="detail-center-item-left">联系电话：</div>
              <div class="detail-center-item-right">
                {{ item.contactNumber }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑使用情况：</div>
              <div class="detail-center-item-right">
                {{ item.usageSituation }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">租赁详情：</div>
              <div class="detail-center-item-right">
                <el-card
                  shadow="never"
                  v-if="item.ajRentInfoList && item.ajRentInfoList.length > 0"
                >
                  <el-collapse>
                    <el-collapse-item
                      v-for="item2 in item.ajRentInfoList"
                      :key="item2.id"
                    >
                      <template slot="title">
                        <div
                          style="
                            color: #333;
                            font-weight: bold;
                            font-size: 14px;
                            display: flex;
                          "
                        >
                          <div style="margin-right: 100px">
                            一级租赁单位（人）
                          </div>
                          <div>{{ item2.rentUnit }}</div>
                        </div>
                      </template>
                      <el-card
                        shadow="never"
                        v-if="item2.children && item2.children.length > 0"
                      >
                        <el-collapse>
                          <el-collapse-item
                            v-for="item3 in item2.children"
                            :key="item3.id"
                          >
                            <template slot="title">
                              <div
                                style="
                                  color: #333;
                                  font-weight: bold;
                                  font-size: 14px;
                                  display: flex;
                                "
                              >
                                <div style="margin-right: 100px">
                                  二级租赁单位（人）
                                </div>
                                <div>{{ item3.rentUnit }}</div>
                              </div>
                            </template>
                            <el-card
                              shadow="never"
                              v-if="item3.children && item3.children.length > 0"
                            >
                              <div
                                style="
                                  color: #333;
                                  font-weight: bold;
                                  font-size: 14px;
                                  display: flex;
                                "
                                v-for="item4 in item3.children"
                                :key="item4.id"
                              >
                                <div style="margin-right: 100px">
                                  三级租赁单位（人）
                                </div>
                                <div>{{ item4.rentUnit }}</div>
                              </div>
                            </el-card>
                          </el-collapse-item>
                        </el-collapse>
                      </el-card>
                    </el-collapse-item>
                  </el-collapse>
                </el-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="showBindBuild" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailForm.corporatename }}</div>
      </div>
      <div class="detail-content">
        <el-form
          :model="buildQueryParams"
          ref="buildQueryForm"
          size="small"
          :inline="true"
          label-width="120px"
          @submit.native.prevent
        >
          <el-form-item label="建筑名称" prop="buildingName">
            <el-input
              v-model="buildQueryParams.buildingName"
              placeholder="请输入建筑名称"
              clearable
              @keyup.enter.native="handleQueryBuild"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQueryBuild"
              >搜索</el-button
            >
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQueryBuild"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="buildList">
          <el-table-column
            label="建筑名称"
            align="center"
            prop="buildingName"
          />
          <el-table-column
            label="操作"
            fixed="right"
            width="150"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handlebind(scope.row)"
                v-hasPermi="['system:unit:bind']"
                >绑定</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="buildTotal > 0"
          :total="buildTotal"
          :page.sync="buildQueryParams.pageNum"
          :limit.sync="buildQueryParams.pageSize"
          @pagination="getBuildList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProjectinspection,
  getProjectinspection,
  delProjectinspection,
  addProjectinspection,
  updateProjectinspection,
  getUnitBuildList,
  getBuildUnitList,
  addCustomDict,
  getUnitCate1List,
  getUnitCate3List,
} from "@/api/system/projectinspection";
import { uploadFile } from "@/api/system/reports";
import {
  buildingListInfo,
  updateInfo,
  checkBuildingEnterprise,
  deleteBuildingEnterprise,
  bindUnitBuild,
} from "@/api/system/build";
import { listInfo } from "@/api/system/info";

export default {
  name: "Projectinspection",
  data() {
    return {
      buildList: [],
      buildTotal: 0,
      buildQueryParams: {
        pageNum: 1,
        pageSize: 10,
        buildingName: "",
      },
      showBindBuild: false,

      // 显示编辑表单
      showEditForm: false,
      // 编辑表单内容
      editForm: {
        fileList1: [],
        fileList2: [],
        belongingRegion: [],
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业项目表格数据
      projectinspectionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        corporatename: null,
        time: null,
        companyaddress: null,
        liaison: null,
        phonenumber: null,
        duties: null,
        sign: null,
        signtime: null,
        ip: null,
        companytype: null,
        area: null,
        startdate: null,
        enddate: null,
        project: null,
        construction: null,
        constructionunit: null,
        location: null,
        Constructionpermitcode: null,
        safetyrectifier: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "请选择所属公司", trigger: "blur" },
        ],
        corporatename: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        companyaddress: [
          { required: true, message: "请输入项目地址", trigger: "blur" },
        ],
        liaison: [
          { required: true, message: "请输入项目负责人", trigger: "blur" },
        ],
        phonenumber: [
          { required: true, message: "请输入项目负责人电话", trigger: "blur" },
        ],
      },
      // 使用类型
      leixingList: [
        {
          text: "自用",
          value: "自用",
        },
        {
          text: "租用",
          value: "租用",
        },
      ],
      // 是否开通
      kaitongList: [
        {
          text: "否",
          value: "否",
        },
        {
          text: "是",
          value: "是",
        },
      ],
      shiyongList: [
        {
          text: "自用",
          value: "自用",
        },
        {
          text: "出租",
          value: "出租",
        },
        {
          text: "合用",
          value: "合用",
        },
      ],
      unitCateList1: [],
      unitCateList2: [
        {
          text: "多种业态、多功能经营场所",
          value: "多种业态、多功能经营场所",
        },
        {
          text: "多功能劳动密集型企业",
          value: "多功能劳动密集型企业",
        },
      ],
      unitCateList3: [],

      requiredUnit: [
        // 'legalRepresentative',
        // 'contactNumber',
        // 'rentalBuildingArea',
        "deptId",
        "companyaddress",
        // 'applicationLocation',
        // 'unitName',
        // 'useType',
        "corporatename",
        "liaison",
        "phonenumber",
        // 'isOpenCode',
      ],
      showBuildList: false,
      detailForm: {},

      address: [
        {
          label: "上海市",
          value: "上海市",
          children: [
            {
              label: "奉贤区",
              value: "奉贤区",
              children: [
                {
                  label: "金海街道",
                  value: "金海街道",
                },
              ],
            },
          ],
        },
      ],
      deptList: [],
    };
  },
  created() {
    this.getUnitCate1List();
    this.getUnitCate3List();
    this.getList();
    this.getDeptList();
  },
  methods: {
    // 不扁平化
    renderDeptOptions(children, level = 1) {
      let result = [];
      children.forEach((child) => {
        result.push({
          ...child,
          level,
          enterpriseName: " ".repeat(level * 2) + child.enterpriseName,
        });
        if (child.children && child.children.length) {
          result = result.concat(
            this.renderDeptOptions(child.children, level + 1)
          );
        }
      });
      return result;
    },
    // 添加扁平化部门的方法
    flattenDept(children) {
      let result = [];
      children.forEach((child) => {
        result.push(child);
        if (child.children && child.children.length) {
          result = result.concat(this.flattenDept(child.children));
        }
      });
      return result;
    },
    getDeptName(deptId) {
      const dept = this.flattenDept(this.deptList).find(
        (item) => item.id === deptId
      );
      return dept ? dept.enterpriseName : "";
    },
    /** 查询部门列表 */
    getDeptList() {
      listInfo(this.deptParams).then((response) => {
        this.deptList = this.handleTree(response.data, "id");
        this.loading = false;
      });
    },
    getUnitCate1List() {
      getUnitCate1List().then((res) => {
        this.unitCateList1 = [];
        res.data.forEach((item) => {
          let obj = {
            text: item.dictLabel,
            value: item.dictValue,
          };
          this.unitCateList1.push(obj);
        });
      });
    },
    getUnitCate3List() {
      getUnitCate3List().then((res) => {
        this.unitCateList3 = [];
        res.data.forEach((item) => {
          let obj = {
            text: item.dictLabel,
            value: item.dictValue,
          };
          this.unitCateList3.push(obj);
        });
      });
    },
    addCustom(type) {
      let that = this;
      that
        .$prompt("请输入自定义内容", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          inputPattern: /^.+$/,
          inputErrorMessage: "内容不能为空",
        })
        .then(({ value }) => {
          addCustomDict({
            dictLabel: value,
            dictValue: value,
            dictSort:
              type == "aj_densely_populated_areas"
                ? that.unitCateList1.length + 1
                : that.unitCateList3.length + 1,
            dictType: type,
            listClass: "default",
            status: "0",
          }).then((res) => {
            if (res.code == 200) {
              that.$message.success("添加成功");
              if (type == "aj_densely_populated_areas") {
                that.getUnitCate1List();
              } else {
                that.getUnitCate3List();
              }
            } else {
              that.$message.error("添加失败");
            }
          });
        })
        .catch(() => {});
    },
    unBindUnit(item) {
      let that = this;
      let params = {
        buildingId: item.buildingId,
        enterpriseId: that.detailForm.projectinspectionid,
      };
      deleteBuildingEnterprise(params).then((res) => {
        if (res.code == 200) {
          that.$modal.msgSuccess("解绑成功");

          getUnitBuildList(that.detailForm.projectinspectionid).then((res) => {
            that.detailForm = {
              ...that.detailForm,
              buildList: res.data,
            };
          });
          this.updateBuildTypeByBuild(item);
        } else {
          that.$modal.msgError(res.msg);
        }
      });
    },
    updateBuildTypeByBuild(row) {
      getBuildUnitList(row.buildingId).then((res) => {
        if (res.code == 200) {
          let buildCateList = [];
          res.data.forEach((item) => {
            if (
              item.unitCategory1 ||
              item.unitCategory2 ||
              item.unitCategory3
            ) {
              if (item.unitCategory1) {
                if (buildCateList.indexOf("人员密集场所") == -1) {
                  buildCateList.push("人员密集场所");
                }
              }
              if (item.unitCategory2) {
                if (buildCateList.indexOf("多业态混合生产经营场所") == -1) {
                  buildCateList.push("多业态混合生产经营场所");
                }
              }
              if (item.unitCategory3) {
                if (buildCateList.indexOf("九小场所") == -1) {
                  buildCateList.push("九小场所");
                }
              }
            }
          });
          row.natureUse = buildCateList.join(",");
          updateInfo(row).then((res) => {
            if (res.code == 200) {
            }
          });
        }
      });
    },

    updateBuildTypeByUnit(unit) {
      getUnitBuildList(unit.projectinspectionid).then((res) => {
        res.data.forEach((item) => {
          this.updateBuildTypeByBuild(item);
        });
      });
    },

    bindBuild() {
      this.showBindBuild = true;
      this.showBuildList = false;
      this.getBuildList();
    },
    handlebind(row) {
      let params = {
        buildingId: row.buildingId,
        enterpriseId: this.detailForm.projectinspectionid,
      };
      checkBuildingEnterprise(params).then((res) => {
        if (res.code == 200) {
          bindUnitBuild(params).then((res2) => {
            if (res2.code == 200) {
              this.$modal.msgSuccess("绑定成功");

              if (
                this.detailForm.unitCategory1 ||
                this.detailForm.unitCategory2 ||
                this.detailForm.unitCategory3
              ) {
                let buildCateList = row.natureUse
                  ? row.natureUse.split(",")
                  : [];
                if (this.detailForm.unitCategory1) {
                  if (buildCateList.indexOf("人员密集场所") == -1) {
                    buildCateList.push("人员密集场所");
                  }
                }
                if (this.detailForm.unitCategory2) {
                  if (buildCateList.indexOf("多业态混合生产经营场所") == -1) {
                    buildCateList.push("多业态混合生产经营场所");
                  }
                }
                if (this.detailForm.unitCategory3) {
                  if (buildCateList.indexOf("九小场所") == -1) {
                    buildCateList.push("九小场所");
                  }
                }
                row.natureUse = buildCateList.join(",");
                updateInfo(row).then((res) => {
                  if (res.code == 200) {
                  }
                });
              }
            } else {
              this.$modal.msgError(res2.msg);
            }
          });
        } else {
          this.$modal.msgError(res.msg);
        }
      });
    },
    showBuild(row) {
      this.detailForm = row;
      getUnitBuildList(row.projectinspectionid).then((res) => {
        this.showBuildList = true;
        this.detailForm.buildList = res.data;
      });
    },
    saveUnit() {
      // console.log(this.editForm, "editForm");
      let flag = true;
      this.requiredUnit.forEach((item) => {
        if (this.editForm[item] == null || this.editForm[item] == undefined) {
          console.log(item, "item");
          flag = false;
        }
      });

      // if (this.editForm.unableObtain && !this.editForm.unableObtainContent) {
      //   flag = false;
      // }
      // if (!this.editForm.unableObtain && this.editForm.fileList2.length == 0) {
      //   flag = false;
      // }
      // if (this.editForm.belongingRegion.length == 0) {
      //   flag = false;
      // }
      if (!flag) {
        this.$modal.msgError("请填写完整");
        return;
      } else {
        this.onSubmit();
      }
    },
    addPrefixToUrls(urls) {
      if (urls) {
        let arr = urls.split(",").map((url) => {
          return {
            url: process.env.VUE_APP_BASE_API + url,
          };
        });
        return arr;
      }
      return "";
    },
    uploadFile(file, fileList, index) {
      let params = {
        file: file.raw,
      };
      uploadFile(params).then((res) => {
        if (res.code == 200) {
          if (index == 1) {
            this.editForm.fileList1 = [
              { url: process.env.VUE_APP_BASE_API + res.msg },
            ];
          } else {
            this.editForm.fileList2 = [
              { url: process.env.VUE_APP_BASE_API + res.msg },
            ];
          }
        }
      });
    },
    // 添加项目
    addProject() {
      this.editForm = {
        fileList1: [],
        fileList2: [],
        belongingRegion: [],
      };
      this.showEditForm = true;
    },
    removeBeforeProfile(url) {
      return url.replace(/^.*?\/profile/, "/profile");
    },
    // 编辑表单提价
    onSubmit() {
      let belongingRegion = this.editForm.belongingRegion.join("-");
      // this.editForm.belongingRegion = this.editForm.belongingRegion.join('-')
      if (this.editForm.fileList1.length > 0) {
        this.editForm.doorPhoto = this.editForm.fileList1
          .map((item) => this.removeBeforeProfile(item.url))
          .join(",");
      }
      if (this.editForm.fileList2.length > 0) {
        this.editForm.businessLicense = this.editForm.fileList2
          .map((item) => this.removeBeforeProfile(item.url))
          .join(",");
      }
      if (this.editForm.projectinspectionid != null) {
        updateProjectinspection({
          ...this.editForm,
          belongingRegion,
        }).then((response) => {
          this.$modal.msgSuccess("修改成功");
          this.showEditForm = false;
          this.getList();
          this.updateBuildTypeByUnit(this.editForm);
        });
      } else {
        addProjectinspection({
          ...this.editForm,
          belongingRegion,
        }).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.showEditForm = false;
          this.getList();
        });
      }
    },
    // 显示编辑表单
    showEdit(row) {
      this.editForm = JSON.parse(JSON.stringify(row));
      this.editForm.unableObtain =
        this.editForm.unableObtain == "false" ? false : true;
      this.editForm.belongingRegion = this.editForm.belongingRegion
        ? this.editForm.belongingRegion.split("-")
        : [];
      this.editForm.fileList1 =
        this.addPrefixToUrls(this.editForm.doorPhoto) || [];
      this.editForm.fileList2 =
        this.addPrefixToUrls(this.editForm.businessLicense) || [];
      this.showEditForm = true;
    },
    /** 查询企业项目列表 */
    getList() {
      this.loading = true;
      listProjectinspection(this.queryParams).then((response) => {
        this.projectinspectionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    getBuildList() {
      this.loading = true;
      buildingListInfo(this.buildQueryParams).then((response) => {
        this.buildList = response.rows;
        this.buildTotal = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        projectinspectionid: null,
        corporatename: null,
        time: null,
        companyaddress: null,
        liaison: null,
        phonenumber: null,
        duties: null,
        sign: null,
        signtime: null,
        ip: null,
        companytype: null,
        area: null,
        startdate: null,
        enddate: null,
        project: null,
        construction: null,
        constructionunit: null,
        location: null,
        Constructionpermitcode: null,
        safetyrectifier: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    handleQueryBuild() {
      this.buildQueryParams.pageNum = 1;
      this.getBuildList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    resetQueryBuild() {
      this.resetForm("buildQueryForm");
      this.handleQueryBuild();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.projectinspectionid);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业项目";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const projectinspectionid = row.projectinspectionid || this.ids;
      getProjectinspection(projectinspectionid).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业项目";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.projectinspectionid != null) {
            updateProjectinspection(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProjectinspection(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const projectinspectionids = row.projectinspectionid || this.ids;
      this.$modal
        .confirm("是否确认删除该企业项目")
        .then(function () {
          return delProjectinspection(projectinspectionids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/projectinspection/export",
        {
          ...this.queryParams,
        },
        `projectinspection_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  // overflow-y: auto;
  height: calc(100vh - 100px);

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      // .detail-center {
      //     display: flex;

      //     .detail-center-left {
      //         .detail-center-item {
      //             display: flex;
      //             line-height: 24px;
      //             margin: 12px 0;

      //             .detail-center-item-left {
      //                 width: 70px;
      //             }

      //             .detail-center-item-right {
      //                 flex: 1;
      //             }

      //         }
      //     }

      //     .detail-center-right {
      //         flex: 1;
      //         display: flex;
      //         margin: 12px 0;

      //         .el-image {
      //             margin-left: 12px;
      //             width: 156px !important;
      //             height: 156px !important;
      //         }
      //     }

      // }

      .detail-center {
        display: flex;
        flex-direction: column;
        align-items: center;
        // flex-wrap: wrap;

        .detail-center-item {
          width: 80%;
          display: flex;
          line-height: 24px;
          margin: 12px 0;

          .detail-center-item-left {
            width: 35%;
            text-align: right;
            color: #666666;
          }

          .detail-center-item-right {
            flex: 1;
            color: #333333;
            padding-left: 20px;
            font-weight: bold;
          }
        }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 60%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  height: calc(100vh - 162px);
  overflow-y: auto;
  font-size: 14px;
}

.detail-content {
  width: 100%;
  height: 70vh;
  overflow-y: auto;
  color: #333;

  .detail-edit-content {
    width: 80%;
    margin: 0 auto;
  }
}
</style>
