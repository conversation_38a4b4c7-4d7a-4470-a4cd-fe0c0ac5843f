<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="所属单位" prop="belongingUnit">
        <el-input
          v-model="queryParams.belongingUnit"
          placeholder="请输入所属单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故发生时间" prop="occurrenceTime">
        <el-date-picker
          clearable
          v-model="queryParams.occurrenceTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择事故发生时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="事故发生地点" prop="accidentPlace">
        <el-input
          v-model="queryParams.accidentPlace"
          placeholder="请输入事故发生地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故简要描述" prop="accidentRemark">
        <el-input
          v-model="queryParams.accidentRemark"
          placeholder="请输入事故简要描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="伤亡情况" prop="casualtySituation">
        <el-input
          v-model="queryParams.casualtySituation"
          placeholder="请输入伤亡情况"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故级别" prop="accidentLevel">
        <el-input
          v-model="queryParams.accidentLevel"
          placeholder="请输入事故级别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="初步经济损失" prop="preliminaryEconomicLosses">
        <el-input
          v-model="queryParams.preliminaryEconomicLosses"
          placeholder="请输入初步经济损失"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="附件" prop="accidentUrl">
        <el-input
          v-model="queryParams.accidentUrl"
          placeholder="请输入附件"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="事件编号" prop="accidentNumber">
        <el-input
          v-model="queryParams.accidentNumber"
          placeholder="请输入事件编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjEventReporting:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjEventReporting:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjEventReporting:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjEventReporting:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjEventReportingList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="所属单位" align="center" prop="belongingUnit" />
      <el-table-column
        label="事故发生时间"
        align="center"
        prop="occurrenceTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.occurrenceTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="事故发生地点"
        align="center"
        prop="accidentPlace"
      />
      <el-table-column
        label="事故简要描述"
        align="center"
        prop="accidentRemark"
      />
      <el-table-column
        label="伤亡情况"
        align="center"
        prop="casualtySituation"
      />
      <el-table-column label="事故级别" align="center" prop="accidentLevel" />
      <el-table-column
        label="初步经济损失"
        align="center"
        prop="preliminaryEconomicLosses"
      />
      <el-table-column label="附件" align="center" prop="accidentUrl">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.accidentUrl"
            size="mini"
            type="text"
            @click="handleView(scope.row.accidentUrl)"
            >查看</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="事件编号" align="center" prop="accidentNumber" />
      <el-table-column label="事件状态" align="center" prop="accidentStatus" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjEventReporting:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjEventReporting:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改事件上报对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属单位" prop="belongingUnit">
          <el-input v-model="form.belongingUnit" placeholder="请输入所属单位" />
        </el-form-item>
        <el-form-item label="事故发生时间" prop="occurrenceTime">
          <el-date-picker
            clearable
            v-model="form.occurrenceTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择事故发生时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="事故发生地点" prop="accidentPlace">
          <el-input
            v-model="form.accidentPlace"
            placeholder="请输入事故发生地点"
          />
        </el-form-item>
        <el-form-item label="事故简要描述" prop="accidentRemark">
          <el-input
            v-model="form.accidentRemark"
            placeholder="请输入事故简要描述"
          />
        </el-form-item>
        <el-form-item label="伤亡情况" prop="casualtySituation">
          <el-input
            v-model="form.casualtySituation"
            placeholder="请输入伤亡情况"
          />
        </el-form-item>
        <el-form-item label="事故级别" prop="accidentLevel">
          <el-input v-model="form.accidentLevel" placeholder="请输入事故级别" />
        </el-form-item>
        <el-form-item label="初步经济损失" prop="preliminaryEconomicLosses">
          <el-input
            v-model="form.preliminaryEconomicLosses"
            placeholder="请输入初步经济损失"
          />
        </el-form-item>
        <el-form-item label="附件" prop="accidentUrl">
          <file-upload v-model="form.accidentUrl"></file-upload>
        </el-form-item>
        <el-form-item label="事件编号" prop="accidentNumber">
          <el-input
            v-model="form.accidentNumber"
            placeholder="请输入事件编号"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjEventReporting,
  getZjEventReporting,
  delZjEventReporting,
  addZjEventReporting,
  updateZjEventReporting,
} from "@/api/inspection/zjEventReporting";

export default {
  name: "ZjEventReporting",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 事件上报表格数据
      zjEventReportingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        belongingUnit: null,
        occurrenceTime: null,
        accidentPlace: null,
        accidentRemark: null,
        casualtySituation: null,
        accidentLevel: null,
        preliminaryEconomicLosses: null,
        accidentUrl: null,
        accidentNumber: null,
        accidentStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      baseUrl: process.env.VUE_APP_BASE_URL,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async handleView(value) {
      if (!value) {
        this.$message.warning("该记录没有附件");
        return;
      }
      if (!value) {
        this.$message.warning("该记录没有附件");
        return;
      }
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const response = await fetch(fileUrl);
          const buffer = await response.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    /** 查询事件上报列表 */
    getList() {
      this.loading = true;
      listZjEventReporting(this.queryParams).then((response) => {
        this.zjEventReportingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        belongingUnit: null,
        occurrenceTime: null,
        accidentPlace: null,
        accidentRemark: null,
        casualtySituation: null,
        accidentLevel: null,
        preliminaryEconomicLosses: null,
        accidentUrl: null,
        createTime: null,
        createBy: null,
        updateBy: null,
        updateTime: null,
        accidentNumber: null,
        accidentStatus: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加事件上报";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjEventReporting(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改事件上报";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjEventReporting(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEventReporting(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除事件上报编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjEventReporting(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjEventReporting/export",
        {
          ...this.queryParams,
        },
        `zjEventReporting_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
