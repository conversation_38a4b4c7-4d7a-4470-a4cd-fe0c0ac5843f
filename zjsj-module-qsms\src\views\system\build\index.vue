<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">建筑列表</div>
            <div class="top-right">
              <el-button
                type="primary"
                icon="el-icon-upload2"
                size="small"
                @click="addBuild"
                >添加建筑</el-button
              >
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="120px"
          >
            <el-form-item label="建筑名称" prop="buildingName">
              <el-input
                v-model="queryParams.buildingName"
                placeholder="请输入建筑名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="联系电话" prop="contactNumber">
              <el-input
                v-model="queryParams.contactNumber"
                placeholder="请输入联系电话"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <!-- <el-row :gutter="10" class="mb8">
					<el-col :span="1.5">
						<el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
						v-hasPermi="['system:info:add']">新增</el-button>
					</el-col>
					<el-col :span="1.5">
						<el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
						v-hasPermi="['system:info:edit']">修改</el-button>
					</el-col>
					<el-col :span="1.5">
						<el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
						v-hasPermi="['system:info:remove']">删除</el-button>
					</el-col>
					<el-col :span="1.5">
						<el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
						v-hasPermi="['system:info:export']">导出</el-button>
					</el-col>
					<right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
					</el-row> -->

          <el-table
            v-loading="loading"
            :data="infoList"
            @selection-change="handleSelectionChange"
            height="calc(100vh - 250px)"
            style="width: 100%"
          >
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column
              label="建筑名称"
              align="center"
              prop="buildingName"
            />
            <el-table-column
              label="建筑地址"
              align="center"
              prop="buildingAddress"
            />
            <el-table-column
              label="详细地址"
              align="center"
              prop="detailedAddress"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.detailedAddress }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产权类型"
              align="center"
              prop="propertyRightType"
            />
            <el-table-column
              label="联系电话"
              align="center"
              prop="contactNumber"
            />
            <el-table-column
              label="使用情况"
              align="center"
              prop="usageSituation"
            />
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              width="150"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="showDetail(scope.row)"
                  v-hasPermi="['system:build:check']"
                  >查看</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  @click="showEdit(scope.row)"
                  v-hasPermi="['system:build:edit']"
                  >编辑</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  @click="showUnitList(scope.row)"
                  v-hasPermi="['system:build:unit']"
                  >单位</el-button
                >
                <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
									v-hasPermi="['system:info:edit']">修改</el-button>
								<el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
									v-hasPermi="['system:info:remove']">删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改建筑信息对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
			<el-form ref="form" :model="form" :rules="rules" label-width="80px">
				<el-form-item label="建筑名称" prop="buildingName">
					<el-input v-model="form.buildingName" placeholder="请输入建筑名称" />
				</el-form-item>
				<el-form-item label="建筑地址" prop="buildingAddress">
					<el-input v-model="form.buildingAddress" placeholder="请输入建筑地址" />
				</el-form-item>
				<el-form-item label="详细地址" prop="detailedAddress">
					<el-input v-model="form.detailedAddress" type="textarea" placeholder="请输入内容" />
				</el-form-item>
				<el-form-item label="建筑结构" prop="buildingStructure">
					<el-input v-model="form.buildingStructure" placeholder="请输入建筑结构" />
				</el-form-item>
				<el-form-item label="总建筑面积" prop="totalConstructionArea">
					<el-input v-model="form.totalConstructionArea" placeholder="请输入总建筑面积" />
				</el-form-item>
				<el-form-item label="建筑高度" prop="buildingHeight">
					<el-input v-model="form.buildingHeight" placeholder="请输入建筑高度" />
				</el-form-item>
				<el-form-item label="建筑层次" prop="buildingArrangement">
					<el-input v-model="form.buildingArrangement" placeholder="请输入建筑层次" />
				</el-form-item>
				<el-form-item label="地下几层" prop="undergroundFloors">
					<el-input v-model="form.undergroundFloors" placeholder="请输入地下几层" />
				</el-form-item>
				<el-form-item label="是否涉及" prop="isInvolve">
					<el-input v-model="form.isInvolve" placeholder="请输入是否涉及" />
				</el-form-item>
				<el-form-item label="建筑消防设施" prop="protectionFacilities">
					<el-input v-model="form.protectionFacilities" placeholder="请输入建筑消防设施" />
				</el-form-item>
				<el-form-item label="建筑使用性质" prop="natureUse">
					<el-input v-model="form.natureUse" placeholder="请输入建筑使用性质" />
				</el-form-item>
				<el-form-item label="产权单位" prop="propertyUnit">
					<el-input v-model="form.propertyUnit" placeholder="请输入产权单位" />
				</el-form-item>
				<el-form-item label="产权或管理单位法定代表人" prop="legalRepresentative">
					<el-input v-model="form.legalRepresentative" placeholder="请输入产权或管理单位法定代表人" />
				</el-form-item>
				<el-form-item label="联系电话" prop="contactNumber">
					<el-input v-model="form.contactNumber" placeholder="请输入联系电话" />
				</el-form-item>
				<el-form-item label="使用情况" prop="usageSituation">
					<el-input v-model="form.usageSituation" placeholder="请输入使用情况" />
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submitForm">确 定</el-button>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog> -->

    <el-dialog :visible.sync="showEditForm" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>编辑</div>
      </div>
      <div class="detail-content">
        <div class="detail-edit-content">
          <el-form ref="form" :model="editForm" label-width="180px">
            <el-form-item label="*建筑名称">
              <el-input v-model="editForm.buildingName"></el-input>
            </el-form-item>
            <el-form-item label="*建筑地址">
              <el-cascader
                v-model="editForm.buildingAddress"
                :options="address"
              ></el-cascader>
            </el-form-item>
            <el-form-item label="*详细地址">
              <el-input v-model="editForm.detailedAddress"></el-input>
            </el-form-item>
            <el-form-item label="*经纬度">
              <el-input v-model="editForm.latitudeLongitude"></el-input>
            </el-form-item>
            <el-form-item label="*建筑结构">
              <el-select
                v-model="editForm.buildingStructure"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in jieGouList"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="总结构面积（平方米）">
              <el-input v-model="editForm.totalConstructionArea"></el-input>
            </el-form-item>
            <el-form-item label="建筑高度（米）">
              <el-input v-model="editForm.buildingHeight"></el-input>
            </el-form-item>
            <el-form-item label="*建筑层数-地上">
              <el-input v-model="editForm.buildingArrangement"></el-input>
            </el-form-item>
            <el-form-item label="地下">
              <el-input v-model="editForm.undergroundFloors"></el-input>
            </el-form-item>
            <el-form-item label="*建筑消防设施">
              <el-select
                v-model="editForm.protectionFacilities"
                multiple
                placeholder="请选择"
              >
                <el-option
                  v-for="item in xiaofangList"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-tooltip
                class="item"
                effect="dark"
                content="点击新增自定义选项"
                placement="right"
              >
                <el-button
                  style="margin-left: 10px"
                  type="text"
                  icon="el-icon-plus"
                  @click="addCustom()"
                ></el-button>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="建筑使用性质">
              <el-select
                v-model="editForm.natureUse"
                multiple
                placeholder="请选择"
              >
                <el-option
                  v-for="item in xingzhiList"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="*产权类型">
              <el-select
                v-model="editForm.propertyRightType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in chanquanList"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="*建筑产权单位(人)">
              <el-input v-model="editForm.propertyUnit"></el-input>
            </el-form-item>
            <template v-if="editForm.propertyRightType == '单产权'">
              <el-form-item label="*法定代表人">
                <el-input v-model="editForm.legalRepresentative"></el-input>
              </el-form-item>
            </template>
            <template v-if="editForm.propertyRightType == '多产权'">
              <el-form-item label="*建筑管理单位">
                <el-input v-model="editForm.managementUnit"></el-input>
              </el-form-item>
              <el-form-item label="*产权或管理单位法定代表人">
                <el-input v-model="editForm.legalRepresentative"></el-input>
              </el-form-item>
            </template>
            <el-form-item label="*联系电话">
              <el-input v-model="editForm.contactNumber"></el-input>
            </el-form-item>
            <el-form-item label="建筑使用情况">
              <el-input v-model="editForm.usageSituation"></el-input>
            </el-form-item>
            <el-form-item label="租赁详情">
              <el-row>
                <el-card shadow="never">
                  <template
                    v-if="
                      editForm.ajRentInfoList &&
                      editForm.ajRentInfoList.length > 0
                    "
                  >
                    <el-row
                      v-for="(item, index) in editForm.ajRentInfoList"
                      :key="index"
                    >
                      <el-card shadow="never">
                        <el-row>
                          <div style="display: flex">
                            一级租赁单位(人)：<el-input
                              style="flex: 1"
                              v-model="item.rentUnit"
                            ></el-input
                            ><el-button
                              style="color: red"
                              icon="el-icon-delete"
                              type="text"
                              @click="toDelete(1, index)"
                            ></el-button>
                          </div>
                        </el-row>
                        <template
                          v-if="item.children && item.children.length > 0"
                        >
                          <el-row
                            v-for="(item2, index2) in item.children"
                            :key="index2"
                          >
                            <el-card shadow="never">
                              <el-row>
                                <div style="display: flex">
                                  二级租赁单位(人)：<el-input
                                    style="flex: 1"
                                    v-model="item2.rentUnit"
                                  ></el-input
                                  ><el-button
                                    type="text"
                                    icon="el-icon-plus"
                                    @click="addUnit(3, index, index2)"
                                  ></el-button
                                  ><el-button
                                    style="color: red"
                                    icon="el-icon-delete"
                                    type="text"
                                    @click="toDelete(2, index, index2)"
                                  ></el-button>
                                </div>
                              </el-row>
                              <template
                                v-if="
                                  item2.children && item2.children.length > 0
                                "
                              >
                                <el-row
                                  v-for="(item3, index3) in item2.children"
                                  :key="index3"
                                >
                                  <el-card shadow="never">
                                    <el-row>
                                      <div style="display: flex">
                                        三级租赁单位(人)：<el-input
                                          style="flex: 1"
                                          v-model="item3.rentUnit"
                                        ></el-input
                                        ><el-button
                                          style="color: red"
                                          type="text"
                                          icon="el-icon-delete"
                                          @click="
                                            toDelete(3, index, index2, index3)
                                          "
                                        ></el-button>
                                      </div>
                                    </el-row>
                                  </el-card>
                                </el-row>
                              </template>
                            </el-card>
                          </el-row>
                        </template>
                        <el-button
                          style="width: 100%"
                          type="primary"
                          @click="addUnit(2, index)"
                          >添加二级租赁单位</el-button
                        >
                      </el-card>
                    </el-row>
                  </template>
                  <el-button
                    style="width: 100%"
                    type="primary"
                    @click="addUnit(1)"
                    >添加一级租赁单位</el-button
                  >
                </el-card>
              </el-row>
            </el-form-item>
            <el-form-item
              style="display: flex; justify-content: center; margin-left: -80px"
            >
              <el-button @click="showEditForm = false">取消</el-button>
              <el-button type="primary" @click="saveBuild">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>

    <!-- <el-dialog :visible.sync="showCheckForm" width="80%">
			<div slot="title" class="dialog-title"
				style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; line-height: 32px; ">
				<div>查看</div>
			</div>
			<div class="detail-content">
				<div class="detail-edit-content">
					<el-form ref="form" :model="detailForm" label-width="150px" :disabled="true">
						<el-form-item label="建筑名称">
							<el-input v-model="detailForm.buildingName"></el-input>
						</el-form-item>
						<el-form-item label="建筑地址">
							<el-input v-model="detailForm.buildingAddress"></el-input>
						</el-form-item>
						<el-form-item label="详细地址">
							<el-input v-model="detailForm.detailedAddress"></el-input>
						</el-form-item>
						<el-form-item label="经纬度">
							<el-input v-model="detailForm.latitudeLongitude"></el-input>
						</el-form-item>
						<el-form-item label="建筑结构">
							<el-input v-model="detailForm.buildingStructure"></el-input>
						</el-form-item>
						<el-form-item label="总建筑面积（平方米）">
							<el-input v-model="detailForm.totalConstructionArea"></el-input>
						</el-form-item>
						<el-form-item label="建筑高度（米）">
							<el-input v-model="detailForm.buildingHeight"></el-input>
						</el-form-item>
						<el-form-item label="建筑层数-地上">
							<el-input v-model="detailForm.buildingArrangement"></el-input>
						</el-form-item>
						<el-form-item label="地下">
							<el-input v-model="detailForm.undergroundFloors"></el-input>
						</el-form-item>
						<el-form-item label="建筑消防设施">
							<el-input v-model="detailForm.protectionFacilities"></el-input>
						</el-form-item>
						<el-form-item label="建筑使用性质">
							<el-input v-model="detailForm.natureUse"></el-input>
						</el-form-item>
						<el-form-item label="产权类型">
							<el-input v-model="detailForm.propertyRightType"></el-input>
						</el-form-item>
						<el-form-item label="建筑产权单位(人)">
							<el-input v-model="detailForm.propertyUnit"></el-input>
						</el-form-item>
						<template v-if="detailForm.propertyRightType == '单产权'">
							<el-form-item label="法定代表人">
								<el-input v-model="detailForm.legalRepresentative"></el-input>
							</el-form-item>
						</template>
						<template v-if="detailForm.propertyRightType == '多产权'">
							<el-form-item label="建筑管理单位">
								<el-input v-model="detailForm.managementUnit"></el-input>
							</el-form-item>
							<el-form-item label="产权或管理单位法定代表人">
								<el-input v-model="detailForm.legalRepresentative"></el-input>
							</el-form-item>
						</template>
						<el-form-item label="联系电话">
							<el-input v-model="detailForm.contactNumber"></el-input>
						</el-form-item>
						<el-form-item label="建筑使用情况">
							<el-input v-model="detailForm.usageSituation"></el-input>
						</el-form-item>
					</el-form>
				</div>
			</div>
		</el-dialog> -->

    <el-dialog :visible.sync="showCheckForm" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailForm.buildingName }}</div>
        <div class="title-btn" style="margin-right: 50px"></div>
      </div>
      <div class="detail-content">
        <div class="detail-item">
          <!-- <div class="detail-top">
						<div class="name">{{ detailForm.buildingName }}</div>
						<div class="edit">
						</div>
					</div> -->
          <div class="detail-center">
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑名称：</div>
              <div class="detail-center-item-right">
                {{ detailForm.buildingName }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">所属区划：</div>
              <div class="detail-center-item-right">
                {{ detailForm.buildingAddress }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">详细地址：</div>
              <div class="detail-center-item-right">
                {{ detailForm.detailedAddress }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">经纬度：</div>
              <div class="detail-center-item-right">
                {{ detailForm.latitudeLongitude }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑结构：</div>
              <div class="detail-center-item-right">
                {{ detailForm.buildingStructure }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">总建筑面积（平方米）：</div>
              <div class="detail-center-item-right">
                {{ detailForm.totalConstructionArea }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑高度（米）：</div>
              <div class="detail-center-item-right">
                {{ detailForm.buildingHeight }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑层数-地上：</div>
              <div class="detail-center-item-right">
                {{ detailForm.buildingArrangement }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">地下：</div>
              <div class="detail-center-item-right">
                {{ detailForm.undergroundFloors }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑消防设施：</div>
              <div class="detail-center-item-right">
                {{ detailForm.protectionFacilities }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑使用性质：</div>
              <div class="detail-center-item-right">
                {{ detailForm.natureUse }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">产权类型：</div>
              <div class="detail-center-item-right">
                {{ detailForm.propertyRightType }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑产权单位(人)：</div>
              <div class="detail-center-item-right">
                {{ detailForm.propertyUnit }}
              </div>
            </div>
            <template v-if="detailForm.propertyRightType == '单产权'">
              <div class="detail-center-item">
                <div class="detail-center-item-left">法定代表人：</div>
                <div class="detail-center-item-right">
                  {{ detailForm.legalRepresentative }}
                </div>
              </div>
            </template>
            <template v-if="detailForm.propertyRightType == '多产权'">
              <div class="detail-center-item">
                <div class="detail-center-item-left">建筑管理单位：</div>
                <div class="detail-center-item-right">
                  {{ detailForm.managementUnit }}
                </div>
              </div>
              <div class="detail-center-item">
                <div class="detail-center-item-left">
                  产权或管理单位法定代表人：
                </div>
                <div class="detail-center-item-right">
                  {{ detailForm.legalRepresentative }}
                </div>
              </div>
            </template>
            <div class="detail-center-item">
              <div class="detail-center-item-left">联系电话：</div>
              <div class="detail-center-item-right">
                {{ detailForm.contactNumber }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">建筑使用情况：</div>
              <div class="detail-center-item-right">
                {{ detailForm.usageSituation }}
              </div>
            </div>
            <div class="detail-center-item">
              <div class="detail-center-item-left">租赁详情：</div>
              <div class="detail-center-item-right">
                <el-card
                  shadow="never"
                  v-if="
                    detailForm.ajRentInfoList &&
                    detailForm.ajRentInfoList.length > 0
                  "
                >
                  <el-collapse>
                    <el-collapse-item
                      v-for="(item, index) in detailForm.ajRentInfoList"
                      :key="index"
                    >
                      <template slot="title">
                        <div
                          style="
                            color: #333;
                            font-weight: bold;
                            font-size: 14px;
                            display: flex;
                          "
                        >
                          <div style="margin-right: 100px">
                            一级租赁单位（人）
                          </div>
                          <div>{{ item.rentUnit }}</div>
                        </div>
                      </template>
                      <el-card
                        shadow="never"
                        v-if="item.children && item.children.length > 0"
                      >
                        <el-collapse>
                          <el-collapse-item
                            v-for="(item2, index) in item.children"
                            :key="index"
                          >
                            <template slot="title">
                              <div
                                style="
                                  color: #333;
                                  font-weight: bold;
                                  font-size: 14px;
                                  display: flex;
                                "
                              >
                                <div style="margin-right: 100px">
                                  二级租赁单位（人）
                                </div>
                                <div>{{ item2.rentUnit }}</div>
                              </div>
                            </template>
                            <el-card
                              shadow="never"
                              v-if="item2.children && item2.children.length > 0"
                            >
                              <div
                                style="
                                  color: #333;
                                  font-weight: bold;
                                  font-size: 14px;
                                  display: flex;
                                "
                                v-for="(item3, index) in item2.children"
                                :key="index"
                              >
                                <div style="margin-right: 100px">
                                  三级租赁单位（人）
                                </div>
                                <div>{{ item3.rentUnit }}</div>
                              </div>
                            </el-card>
                          </el-collapse-item>
                        </el-collapse>
                      </el-card>
                    </el-collapse-item>
                  </el-collapse>
                </el-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="showUnit" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailForm.buildingName }}</div>
        <div class="title-btn" style="margin-right: 50px">
          <el-button size="small" type="primary" @click="bindUnit"
            >绑定单位</el-button
          >
        </div>
      </div>
      <div class="detail-content">
        <el-collapse accordion>
          <el-collapse-item
            :title="item.corporatename"
            v-for="(item, index) in detailForm.unitList"
            :key="index"
          >
            <template slot="title">
              <div
                style="
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                "
              >
                <div style="color: #333; font-weight: bold; font-size: 16px">
                  {{ item.corporatename }}
                </div>
                <div class="title-btn" style="margin-right: 30px">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="unBindUnit(item)"
                    v-hasPermi="['system:build:unbind']"
                    >解绑</el-button
                  >
                </div>
              </div>
            </template>
            <div class="detail-item">
              <!-- <div class="detail-top">
								<div class="name">{{ item.corporatename }}</div>
								<div class="edit">
								</div>
							</div> -->
              <div class="detail-center">
                <!-- <div class="detail-center-right">
									<el-image style="width: 100px; height: 100px" :src="img" fit="cover"
										:preview-src-list="item.imgList" v-for="(img, index) in item.imgList">
									</el-image>
								</div> -->
                <div class="detail-center-item">
                  <div class="detail-center-item-left">项目名称：</div>
                  <div class="detail-center-item-right">
                    {{ item.corporatename }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">单位名称：</div>
                  <div class="detail-center-item-right">
                    {{ item.unitName }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">使用类型：</div>
                  <div class="detail-center-item-right">{{ item.useType }}</div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">使用部位：</div>
                  <div class="detail-center-item-right">
                    {{ item.applicationLocation }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">所属区划：</div>
                  <div class="detail-center-item-right">
                    {{ item.belongingRegion }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">详细地址：</div>
                  <div class="detail-center-item-right">
                    {{ item.unitAddress }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">是否开通企业码：</div>
                  <div class="detail-center-item-right">
                    {{ item.isOpenCode }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">法定代表人：</div>
                  <div class="detail-center-item-right">
                    {{ item.legalRepresentative }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">消防安全管理人：</div>
                  <div class="detail-center-item-right">
                    {{ item.fireSafetyManager }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">联系电话：</div>
                  <div class="detail-center-item-right">
                    {{ item.contactNumber }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">
                    单位类别1(人员密集场所)：
                  </div>
                  <div class="detail-center-item-right">
                    {{ item.unitCategory1 }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">
                    单位类别2(多业态混合生产经营场所)：
                  </div>
                  <div class="detail-center-item-right">
                    {{ item.unitCategory2 }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">
                    单位类别3(九小场所)：
                  </div>
                  <div class="detail-center-item-right">
                    {{ item.unitCategory3 }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">
                    租用建筑面积（平方米）：
                  </div>
                  <div class="detail-center-item-right">
                    {{ item.rentalBuildingArea }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">夹(插)层面积：</div>
                  <div class="detail-center-item-right">
                    {{ item.layerArea }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">冷库面积：</div>
                  <div class="detail-center-item-right">
                    {{ item.coldStorageArea }}
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">营业执照：</div>
                  <div class="detail-center-item-right">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="item.businessLicense"
                      fit="cover"
                      :preview-src-list="[item.businessLicense]"
                    ></el-image>
                  </div>
                </div>
                <div class="detail-center-item">
                  <div class="detail-center-item-left">门头照：</div>
                  <div class="detail-center-item-right">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="item.doorPhoto"
                      fit="cover"
                      :preview-src-list="[item.doorPhoto]"
                    ></el-image>
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="showBindUnit" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailForm.buildingName }}</div>
      </div>
      <div class="detail-content">
        <el-form
          :model="unitQueryParams"
          ref="unitQueryForm"
          size="small"
          :inline="true"
          label-width="120px"
          @submit.native.prevent
        >
          <el-form-item label="项目名称" prop="corporatename">
            <el-input
              v-model="unitQueryParams.corporatename"
              placeholder="请输入项目名称"
              clearable
              @keyup.enter.native="handleQueryUnit"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQueryUnit"
              >搜索</el-button
            >
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQueryUnit"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <el-table v-loading="loading" :data="unitList">
          <el-table-column
            label="项目名称"
            align="center"
            prop="corporatename"
          />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handlebind(scope.row)"
                v-hasPermi="['system:build:bind']"
                >绑定</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="unitTotal > 0"
          :total="unitTotal"
          :page.sync="unitQueryParams.pageNum"
          :limit.sync="unitQueryParams.pageSize"
          @pagination="getUnitList"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
  checkBuildingEnterprise,
  deleteBuildingEnterprise,
  bindUnitBuild,
  getBuildXFList,
} from "@/api/system/build";
import {
  listProjectinspection,
  getBuildUnitList,
  addCustomDict,
} from "@/api/system/projectinspection";

export default {
  name: "Info",
  data() {
    return {
      unitList: [],
      unitTotal: 0,
      unitQueryParams: {
        pageNum: 1,
        pageSize: 10,
        corporatename: "",
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 建筑信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        buildingName: null,
        contactNumber: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},

      showCheckForm: false,

      jieGouList: [
        {
          text: "砖混结构",
          value: "砖混结构",
        },
        {
          text: "钢筋混凝土结构",
          value: "钢筋混凝土结构",
        },
        {
          text: "钢架结构",
          value: "钢架结构",
        },
        {
          text: "砖木结构",
          value: "砖木结构",
        },
      ],
      //  选中的建筑结构
      jiegouIndex: "",
      // 建筑消防设施
      xiaofangList: [
        {
          text: "火灾自动报警系统",
          value: "火灾自动报警系统",
        },
        {
          text: "自动喷水灭火系统",
          value: "自动喷水灭火系统",
        },
        {
          text: "室内消火栓系统",
          value: "室内消火栓系统",
        },
        {
          text: "机械排烟系统",
          value: "机械排烟系统",
        },
      ],
      xiaofangIndex: "",
      // 建筑使用性质
      xingzhiList: [
        {
          text: "人员密集场所",
          value: "人员密集场所",
        },
        {
          text: "多业态混合生产经营场所",
          value: "多业态混合生产经营场所",
        },
        {
          text: "九小场所",
          value: "九小场所",
        },
      ],
      // 产权类型
      chanquanList: [
        {
          text: "单产权",
          value: "单产权",
        },
        {
          text: "多产权",
          value: "多产权",
        },
      ],
      address: [
        {
          label: "上海市",
          value: "上海市",
          children: [
            {
              label: "奉贤区",
              value: "奉贤区",
              children: [
                // {
                // 	label: '西渡街道',
                // 	value: '西渡街道'
                // },
                // {
                // 	label: '奉浦街道',
                // 	value: '奉浦街道'
                // },
                {
                  label: "金海街道",
                  value: "金海街道",
                },
                // {
                // 	label: ' 南桥镇',
                // 	value: ' 南桥镇'
                // },
                // {
                // 	label: ' 奉城镇',
                // 	value: ' 奉城镇'
                // },
                // {
                // 	label: '庄行镇',
                // 	value: '庄行镇'
                // },
                // {
                // 	label: '金汇镇',
                // 	value: '金汇镇'
                // },
                // {
                // 	label: '四团镇',
                // 	value: '四团镇'
                // },
                // {
                // 	label: '青村镇',
                // 	value: '青村镇'
                // },
                // {
                // 	label: '柘林镇',
                // 	value: '柘林镇'
                // },
                // {
                // 	label: '海湾镇',
                // 	value: '海湾镇'
                // },
                // {
                // 	label: '海湾旅游区',
                // 	value: '海湾旅游区'
                // }
              ],
            },
          ],
        },
      ],

      showUnit: false,
      detailForm: {
        unitList: [],
      },

      showEditForm: false,
      editForm: {},

      required: [
        "buildingName",
        "detailedAddress",
        "latitudeLongitude",
        "buildingArrangement",
        "propertyUnit",
        "legalRepresentative",
        "contactNumber",
        "buildingStructure",
        "propertyRightType",
      ],

      showBindUnit: false,
    };
  },
  created() {
    this.getBuildXFList();
    this.getList();
  },
  methods: {
    getBuildXFList() {
      getBuildXFList().then((res) => {
        this.xiaofangList = [];
        res.data.forEach((item) => {
          let obj = {
            text: item.dictLabel,
            value: item.dictValue,
          };
          this.xiaofangList.push(obj);
        });
      });
    },
    addCustom() {
      let that = this;
      that
        .$prompt("请输入自定义内容", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          inputPattern: /^.+$/,
          inputErrorMessage: "内容不能为空",
        })
        .then(({ value }) => {
          addCustomDict({
            dictLabel: value,
            dictValue: value,
            dictSort: that.xiaofangList.length + 1,
            dictType: "aj_fire_protection_facilities",
            listClass: "default",
            status: "0",
          }).then((res) => {
            if (res.code == 200) {
              that.$message.success("添加成功");
              that.getBuildXFList();
            } else {
              that.$message.error("添加失败");
            }
          });
        })
        .catch(() => {});
    },
    unBindUnit(item) {
      let that = this;
      let params = {
        buildingId: that.detailForm.buildingId,
        enterpriseId: item.projectinspectionid,
      };
      deleteBuildingEnterprise(params).then((res) => {
        if (res.code == 200) {
          that.$modal.msgSuccess("解绑成功");
          getBuildUnitList(that.detailForm.buildingId).then((res) => {
            if (res.code == 200) {
              res.data.forEach((item) => {
                item.businessLicense =
                  process.env.VUE_APP_BASE_API + item.businessLicense;
                item.doorPhoto = process.env.VUE_APP_BASE_API + item.doorPhoto;
              });
              that.detailForm = {
                ...that.detailForm,
                unitList: res.data,
              };

              let buildCateList = [];
              that.detailForm.unitList.forEach((item) => {
                if (
                  item.unitCategory1 ||
                  item.unitCategory2 ||
                  item.unitCategory3
                ) {
                  if (row.unitCategory1) {
                    if (buildCateList.indexOf("人员密集场所") == -1) {
                      buildCateList.push("人员密集场所");
                    }
                  }
                  if (row.unitCategory2) {
                    if (buildCateList.indexOf("多业态混合生产经营场所") == -1) {
                      buildCateList.push("多业态混合生产经营场所");
                    }
                  }
                  if (row.unitCategory3) {
                    if (buildCateList.indexOf("九小场所") == -1) {
                      buildCateList.push("九小场所");
                    }
                  }
                }
              });
              this.detailForm.natureUse = buildCateList.join(",");
              updateInfo(this.detailForm).then((res) => {
                if (res.code == 200) {
                  this.getList();
                }
              });
            }
          });
        } else {
          that.$modal.msgError(res.msg);
        }
      });
    },
    handlebind(row) {
      let params = {
        buildingId: this.detailForm.buildingId,
        enterpriseId: row.projectinspectionid,
      };

      checkBuildingEnterprise(params).then((res) => {
        if (res.code == 200) {
          bindUnitBuild(params).then((res2) => {
            if (res2.code == 200) {
              this.$modal.msgSuccess("绑定成功");

              if (row.unitCategory1 || row.unitCategory2 || row.unitCategory3) {
                let buildCateList = this.detailForm.natureUse
                  ? this.detailForm.natureUse.split(",")
                  : [];
                if (row.unitCategory1) {
                  if (buildCateList.indexOf("人员密集场所") == -1) {
                    buildCateList.push("人员密集场所");
                  }
                }
                if (row.unitCategory2) {
                  if (buildCateList.indexOf("多业态混合生产经营场所") == -1) {
                    buildCateList.push("多业态混合生产经营场所");
                  }
                }
                if (row.unitCategory3) {
                  if (buildCateList.indexOf("九小场所") == -1) {
                    buildCateList.push("九小场所");
                  }
                }
                this.detailForm.natureUse = buildCateList.join(",");
                updateInfo(this.detailForm).then((res) => {
                  if (res.code == 200) {
                    this.getList();
                  }
                });
              }
            } else {
              this.$modal.msgError(res2.msg);
            }
          });
        } else {
          this.$modal.msgError(res.msg);
        }
      });
    },
    bindUnit() {
      this.showBindUnit = true;
      this.showUnit = false;
      this.getUnitList();
    },
    addUnit(type, index, index2) {
      if (type == 1) {
        if (!this.editForm.ajRentInfoList) {
          this.editForm.ajRentInfoList = [];
        }
        this.editForm.ajRentInfoList.push({
          rentUnit: "",
          children: [],
        });
      } else if (type == 2) {
        if (!this.editForm.ajRentInfoList[index].children) {
          this.editForm.ajRentInfoList[index].children = [];
        }
        this.editForm.ajRentInfoList[index].children.push({
          rentUnit: "",
          children: [],
        });
      } else {
        if (!this.editForm.ajRentInfoList[index].children[index2].children) {
          this.editForm.ajRentInfoList[index].children[index2].children = [];
        }
        this.editForm.ajRentInfoList[index].children[index2].children.push({
          rentUnit: "",
        });
      }
    },
    toDelete(type, index, index2, index3) {
      if (type == 1) {
        // 删除一级
        this.editForm.ajRentInfoList.splice(index, 1);
      } else if (type == 2) {
        // 删除二级
        this.editForm.ajRentInfoList[index].children.splice(index2, 1);
      } else {
        // 删除三级
        this.editForm.ajRentInfoList[index].children[index2].children.splice(
          index3,
          1
        );
      }
    },

    saveBuild() {
      let flag = true;
      this.required.forEach((item) => {
        if (this.editForm[item] == null || this.editForm[item] == undefined) {
          flag = false;
        }
      });
      if (
        this.editForm.propertyRightType == "多产权" &&
        !this.editForm.managementUnit
      ) {
        flag = false;
      }
      if (this.editForm.protectionFacilities.length == 0) {
        flag = false;
      }

      if (!flag) {
        this.$modal.msgError("请填写完整");
        return;
      } else {
        this.submit();
      }
    },

    submit() {
      let flag = true;
      this.editForm.ajRentInfoList.forEach((item) => {
        if (!item.rentUnit) {
          flag = false;
        }
        if (item.children) {
          item.children.forEach((item2) => {
            if (!item2.rentUnit) {
              flag = false;
            }
            if (item2.children) {
              item2.children.forEach((item3) => {
                if (!item3.rentUnit) {
                  flag = false;
                }
              });
            }
          });
        }
      });
      if (flag) {
        this.submitBuild();
      } else {
        // 失败弹窗
        this.$message.error("请填写完整");
      }
    },

    submitBuild() {
      this.editForm.buildingAddress = this.editForm.buildingAddress.join("-");
      this.editForm.natureUse = this.editForm.natureUse
        ? this.editForm.natureUse.join(",")
        : "";
      this.editForm.protectionFacilities = this.editForm.protectionFacilities
        ? this.editForm.protectionFacilities.join(",")
        : "";

      if (!this.editForm.buildingId) {
        addInfo(this.editForm).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess("添加成功");
            this.showEditForm = false;
            this.getList();
          }
        });
      } else {
        updateInfo(this.editForm).then((res) => {
          if (res.code == 200) {
            this.$modal.msgSuccess("修改成功");
            this.showEditForm = false;
            this.getList();
          }
        });
      }
    },
    // 显示编辑弹框
    showEdit(row) {
      this.editForm = JSON.parse(JSON.stringify(row));
      this.editForm.buildingAddress = this.editForm.buildingAddress
        ? this.editForm.buildingAddress.split("-")
        : "";
      this.editForm.natureUse = this.editForm.natureUse
        ? this.editForm.natureUse.split(",")
        : "";
      this.editForm.protectionFacilities = this.editForm.protectionFacilities
        ? this.editForm.protectionFacilities.split(",")
        : "";
      this.showEditForm = true;
    },

    addBuild() {
      this.editForm = {
        ajRentInfoList: [],
      };
      this.showEditForm = true;
    },

    // 显示单位列表
    showUnitList(row) {
      this.detailForm = row;
      getBuildUnitList(row.buildingId).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            item.businessLicense =
              process.env.VUE_APP_BASE_API + item.businessLicense;
            item.doorPhoto = process.env.VUE_APP_BASE_API + item.doorPhoto;
          });
          this.detailForm.unitList = res.data;
          this.showUnit = true;
        }
      });
    },
    // 显示编辑
    showDetailEdit(row) {
      this.showEditForm = true;
      this.detailForm.detailList = [{}];
      this.detailForm = row;
    },
    // 显示查看
    showDetail(row) {
      this.showCheckForm = true;
      this.detailForm = row;
    },
    /** 查询建筑信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getUnitList() {
      this.loading = true;
      listProjectinspection(this.unitQueryParams).then((response) => {
        this.unitList = response.rows;
        this.unitTotal = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        buildingId: null,
        buildingName: null,
        // buildingAddress: null,
        // detailedAddress: null,
        // buildingStructure: null,
        // totalConstructionArea: null,
        // buildingHeight: null,
        // buildingArrangement: null,
        // undergroundFloors: null,
        // isInvolve: null,
        // protectionFacilities: null,
        // natureUse: null,
        // propertyRightType: null,
        // propertyUnit: null,
        // legalRepresentative: null,
        contactNumber: null,
        // usageSituation: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleQueryUnit() {
      this.unitQueryParams.pageNum = 1;
      this.getUnitList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    resetQueryUnit() {
      this.resetForm("unitQueryForm");
      this.handleQueryUnit();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.buildingId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加建筑信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const buildingId = row.buildingId || this.ids;
      getInfo(buildingId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改建筑信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.buildingId != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const buildingIds = row.buildingId || this.ids;
      this.$modal
        .confirm('是否确认删除建筑信息编号为"' + buildingIds + '"的数据项？')
        .then(function () {
          return delInfo(buildingIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/info/export",
        {
          ...this.queryParams,
        },
        `info_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  // overflow-y: auto;
  height: calc(100vh - 100px);

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      .detail-center {
        display: flex;
        flex-direction: column;
        align-items: center;
        // flex-wrap: wrap;

        .detail-center-item {
          width: 80%;
          display: flex;
          line-height: 24px;
          margin: 12px 0;

          .detail-center-item-left {
            width: 35%;
            text-align: right;
            color: #666666;
          }

          .detail-center-item-right {
            flex: 1;
            color: #333333;
            padding-left: 20px;
            font-weight: bold;
          }
        }

        // .detail-center-left {
        // 	width: 25%;
        // 	text-align: center;
        // 	.detail-center-item {
        // 		display: flex;
        // 		line-height: 24px;
        // 		margin: 12px 0;

        // 		.detail-center-item-left {
        // 			width: 70px;
        // 		}

        // 		.detail-center-item-right {
        // 			flex: 1;
        // 		}

        // 	}
        // }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 70%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  height: calc(100vh - 150px);
  overflow-y: auto;
  font-size: 14px;
}
</style>
