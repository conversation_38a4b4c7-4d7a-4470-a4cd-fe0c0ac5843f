<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <!-- <el-form-item label="举报单号" prop="reportCode">
        <el-input
          v-model="queryParams.reportCode"
          placeholder="请输入举报单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="所属项目" prop="projectName">
        <selectPeopleTree
          v-model="queryParams.projectName"
          :people-list="projectList"
          placeholder="请选择所属项目"
          @change="handleSearchProjectChange"
        />
      </el-form-item>
      <!-- <el-form-item label="所属公司" prop="reporterCompName">
        <selectPeopleTree
          ref="chargePersonName"
          v-model="queryParams.reporterCompName"
          :people-list="companyList"
          placeholder="请搜索或选择所属公司"
          @change="handleSearchChange"
        />
      </el-form-item> -->
      <el-form-item label="举报人" prop="reporterUserId">
        <el-select
          v-model="queryParams.reporterUserId"
          placeholder="请选择举报人"
          style="width: 100%"
          filterable
          clearable
          :filter-method="handleFilter"
          @visible-change="handleVisibleChange"
        >
          <el-option
            v-for="item in userList"
            :key="item.userId"
            :label="item.nickName"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="举报人电话" prop="reporterPhone">
        <el-input
          v-model="queryParams.reporterPhone"
          placeholder="请输入举报人电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:hiddenDangerReport:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:hiddenDangerReport:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:hiddenDangerReport:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="hiddenDangerReportList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="举报单号" align="center" prop="reportCode" />
      <el-table-column
        label="举报人姓名"
        align="center"
        prop="reporterName"
        width="120"
      />
      <el-table-column label="举报人电话" align="center" prop="reporterPhone" />
      <el-table-column label="所属公司" align="center" prop="reporterComp" />
      <el-table-column label="所属项目" align="center" prop="projectName" />
      <!-- <el-table-column
        label="隐患详细位置"
        align="center"
        prop="hazardLocation"
      />
      <el-table-column
        label="隐患详细描述"
        align="center"
        prop="hazardDescription"
      /> -->
      <el-table-column
        label="隐患类型"
        align="center"
        prop="hazardCategoryName"
      />
      <el-table-column
        label="紧急程度"
        align="center"
        prop="emergencyLevel"
        :formatter="formatEmergencyLevelStatus"
      />
      <!-- <el-table-column
        label="现场图片/视频URL，多个用逗号分隔"
        align="center"
        prop="imageUrls"
      /> -->
      <el-table-column
        label="举报时间"
        align="center"
        prop="reportTime"
        width="180"
      />
      <el-table-column
        label="审核状态"
        align="center"
        prop="status"
        :formatter="formatStatus"
      />
      <!-- <el-table-column label="审核意见" align="center" prop="auditOpinion" /> -->
      <!-- <el-table-column label="审核人" align="center" prop="auditorId" /> -->
      <!-- <el-table-column
        label="审核时间"
        align="center"
        prop="auditTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.auditTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="奖励金额" align="center" prop="rewardValue" /> -->
      <!-- <el-table-column
        label="关联的隐患"
        align="center"
        prop="hiddenDangerId"
      /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:hiddenDangerReport:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:hiddenDangerReport:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改隐患举报对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="140px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-form-item label="举报单号" prop="reportCode">
          <el-input
            v-model="form.reportCode"
            placeholder="请输入举报单号"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="举报人" prop="reporterUser">
          <el-select
            v-model="form.reporterUser"
            placeholder="请选择举报人"
            style="width: 100%"
            filterable
            clearable
            :filter-method="handleFormFilter"
            @visible-change="handleFormVisibleChange"
            :disabled="isCheck"
            value-key="userId"
            @change="handleAddChange"
          >
            <el-option
              v-for="item in addUserList"
              :key="item.userId"
              :label="item.nickName"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="举报人电话" prop="reporterPhone">
          <el-input v-model="form.reporterPhone" disabled />
        </el-form-item>
        <el-form-item label="举报人所属公司" prop="reporterComp">
          <el-input v-model="form.reporterComp" disabled />
        </el-form-item>
        <!-- <el-form-item label="举报人姓名" prop="reporterName">
          <el-input
            v-model="form.reporterName"
            placeholder="请输入举报人姓名"
          />
        </el-form-item>
        <el-form-item label="举报人电话" prop="reporterPhone">
          <el-input
            v-model="form.reporterPhone"
            placeholder="请输入举报人电话"
          />
        </el-form-item>
        <el-form-item label="所属公司(由人员所属公司带入)" prop="reporterComp">
          <el-input
            v-model="form.reporterComp"
            placeholder="请输入所属公司(由人员所属公司带入)"
          />
        </el-form-item> -->
        <el-form-item label="所属项目" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择所属项目"
            @change="handleAddSearchProjectChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="隐患详细位置" prop="hazardLocation">
          <el-input
            v-model="form.hazardLocation"
            type="textarea"
            placeholder="请输入内容"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="隐患详细描述" prop="hazardDescription">
          <el-input
            v-model="form.hazardDescription"
            type="textarea"
            placeholder="请输入内容"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="隐患类型" prop="hazardCategoryName">
          <selectHazardTree
            v-model="form.hazardCategoryName"
            :people-list="hazardDTOTreeList"
            placeholder="请选择所属项目"
            @change="handleAddHazardChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="紧急程度" prop="emergencyLevel">
          <el-select
            v-model="form.emergencyLevel"
            placeholder="请选择紧急程度"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in emergencyLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="现场图片/视频" prop="imageUrls">
          <file-upload v-model="form.imageUrls" :disabled="isCheck" />
        </el-form-item>
        <el-form-item label="举报时间" prop="reportTime">
          <el-date-picker
            clearable
            v-model="form.reportTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择举报时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审核状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择审核状态"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditOpinion">
          <el-input
            v-model="form.auditOpinion"
            type="textarea"
            placeholder="请输入审核意见"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="审核人" prop="auditorId">
          <el-select
            v-model="form.auditorId"
            placeholder="请选择审核人"
            style="width: 100%"
            filterable
            clearable
            :filter-method="handleReviewFormFilter"
            @visible-change="handleReviewFormVisibleChange"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in reviewUserList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核时间" prop="auditTime">
          <el-date-picker
            clearable
            v-model="form.auditTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择审核时间"
            :disabled="isCheck"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="奖励金额" prop="rewardValue">
          <el-input
            v-model="form.rewardValue"
            placeholder="请输入奖励金额"
            @input="handleInput"
            :disabled="isCheck"
          />
        </el-form-item>
        <!-- <el-form-item label="关联的隐患" prop="hiddenDangerId">
          <el-input
            v-model="form.hiddenDangerId"
            placeholder="请输入关联的隐患"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHiddenDangerReport,
  getHiddenDangerReport,
  delHiddenDangerReport,
  addHiddenDangerReport,
  updateHiddenDangerReport,
} from "@/api/system/reportingHiddenDangers/index";
import {
  getWhistleblowerList,
  getHazardDTOTree,
} from "@/api/system/common/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import selectHazardTree from "@/views/components/selectHazardTree.vue";
import { getFileOrignalName } from "@/utils/common.js";
import { listUser } from "@/api/system/user";
import { querytree, getEnterpriseInfo } from "@/api/system/info";
export default {
  name: "HiddenDangerReport",
  components: {
    selectPeopleTree,
    selectHazardTree,
  },
  data() {
    return {
      statusOptions: [
        {
          label: "无效举报",
          value: 0,
        },
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "已受理(待整改)",
          value: 2,
        },
        {
          label: "已整改(待奖励)",
          value: 3,
        },
      ],
      emergencyLevelOptions: [
        {
          label: "重大隐患",
          value: 2,
        },
        {
          label: "一般隐患",
          value: 1,
        },
      ],
      projectList: [],
      companyList: [],
      allOptions: [], // 备份所有选项
      isReviewSearching: false, // 是否正在搜索
      isSearching: false, // 是否正在搜索
      isAddSearching: false, // 是否正在搜索
      showCount: 500, // 初始默认显示的条数
      isCheck: false,
      userList: [],
      addUserList: [],
      reviewUserList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患举报表格数据
      hiddenDangerReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportCode: null,
        reporterUserId: null,
        reporterName: null,
        reporterPhone: null,
        reporterComp: null,
        reporterCompName: null,
        projectId: null,
        projectName: null,
        hazardLocation: null,
        hazardDescription: null,
        hazardCategory: null,
        hazardCategoryName: null,
        emergencyLevel: null,
        imageUrls: null,
        reportTime: null,
        status: null,
        auditOpinion: null,
        auditorId: null,
        auditTime: null,
        rewardValue: null,
        hiddenDangerId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        reportCode: [
          { required: true, message: "举报单号不能为空", trigger: "blur" },
        ],
        reporterUser: [
          { required: true, message: "举报人不能为空", trigger: "blur" },
        ],
        reporterName: [
          { required: true, message: "举报人姓名不能为空", trigger: "blur" },
        ],
        reporterCompName: [
          {
            required: true,
            message: "所属公司不能为空",
            trigger: "blur",
          },
        ],
        projectName: [
          { required: true, message: "所属项目不能为空", trigger: "blur" },
        ],
        hazardLocation: [
          { required: true, message: "隐患详细位置不能为空", trigger: "blur" },
        ],
        hazardDescription: [
          { required: true, message: "隐患详细描述不能为空", trigger: "blur" },
        ],
        hazardCategoryName: [
          { required: true, message: "隐患类型不能为空", trigger: "blur" },
        ],
        emergencyLevel: [
          { required: true, message: "紧急程度不能为空", trigger: "blur" },
        ],
        reportTime: [
          { required: true, message: "举报时间不能为空", trigger: "blur" },
        ],
        status: [
          {
            required: true,
            message: "状态",
            trigger: "change",
          },
        ],
      },
      hazardDTOTreeList: [],
      defaultProps: {
        children: "hazardDTOList",
        label: "hazardName",
      },
    };
  },
  created() {
    this.getList();
    this.getUserList();
    this.getProjectList();
    this.getCompanyList();
    this.getHazardDTOTreeList();
  },
  methods: {
    handleNodeClick() {},
    getHazardDTOTreeList() {
      getHazardDTOTree().then((res) => {
        if (res.code == 200) {
          this.hazardDTOTreeList = res.data;
          console.log("隐患类型", this.hazardDTOTreeList);
        }
      });
    },
    formatStatus(row, column, value) {
      const option = this.statusOptions.find((item) => item.value == value);
      return option ? option.label : value;
    },
    formatEmergencyLevelStatus(row, column, value) {
      const option = this.emergencyLevelOptions.find(
        (item) => item.value == value
      );
      return option ? option.label : value;
    },
    handleInput(value) {
      // 清除除数字和小数点外的所有字符
      let val = value.replace(/[^\d.]/g, "");
      // 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");
      // 确保小数点不在开头
      val = val.replace(/^\./g, "");
      // 只保留两位小数
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      // 更新输入值
      this.form.rewardValue = val;
    },
    handleSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectId = selectedItem.id;
        this.queryParams.projectName = selectedItem.label;
      } else {
        this.queryParams.projectId = null;
        this.queryParams.projectName = null;
      }
    },
    handleAddSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectId = selectedItem.id;
        this.form.projectName = selectedItem.label;
      } else {
        this.form.projectId = null;
        this.form.projectName = null;
      }
      this.$forceUpdate();
    },
    handleAddHazardChange(selectedItem) {
      // console.log("aaaa", selectedItem);
      if (selectedItem) {
        this.form.hazardCategory = selectedItem.id;
        this.form.hazardCategoryName = selectedItem.label;
      } else {
        this.form.hazardCategory = null;
        this.form.hazardCategoryName = null;
      }
      this.$forceUpdate();
    },
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    handleSearchChange(selectedItem) {
      if (selectedItem) {
        this.queryParams.reporterComp = selectedItem.id;
        this.queryParams.reporterCompName = selectedItem.label;
      } else {
        this.queryParams.reporterComp = null;
        this.queryParams.reporterCompName = null;
      }
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    getUserList() {
      getWhistleblowerList({}).then((res) => {
        // console.log("获取用户信息", res);
        let tempUserList = res.data;
        this.allOptions = tempUserList.map((item) => {
          return {
            userId: item.userId,
            nickName: item.nickName,
            phonenumber: item.phonenumber,
            companyName: item.companyName,
          };
        });
        this.userList = this.allOptions.slice(0, this.showCount);
        this.addUserList = this.allOptions.slice(0, this.showCount);
        this.reviewUserList = this.allOptions.slice(0, this.showCount);
      });
      // listUser({
      //   pageNum: 1,
      //   pageSize: 9999999,
      // }).then((response) => {
      //   let tempUserList = response.rows;
      //   // console.log("人员公司", tempUserList);
      //   this.allOptions = tempUserList.map((item) => {
      //     return {
      //       userId: item.userId,
      //       userName: item.userName,
      //       nickName: item.nickName,
      //       phonenumber: item.phonenumber,
      //     };
      //   });
      //   this.userList = this.allOptions.slice(0, this.showCount);
      //   this.addUserList = this.allOptions.slice(0, this.showCount);
      //   this.reviewUserList = this.allOptions.slice(0, this.showCount);
      // });
    },
    handleFilter(query) {
      this.isSearching = true;
      if (!query) {
        this.userList = this.allOptions.slice(0, this.showCount);
      } else {
        const lowerQuery = query.toString().toLowerCase();
        this.userList = this.allOptions.filter((item) =>
          item.nickName.toLowerCase().includes(lowerQuery)
        );
      }
    },
    handleVisibleChange(visible) {
      if (!visible) {
        this.isSearching = false;
        this.userList = this.allOptions.slice(0, this.showCount);
      }
    },
    handleFormFilter(query) {
      this.isAddSearching = true;
      if (!query) {
        this.addUserList = this.allOptions.slice(0, this.showCount);
      } else {
        const lowerQuery = query.toString().toLowerCase();
        this.addUserList = this.allOptions.filter((item) =>
          item.nickName.toLowerCase().includes(lowerQuery)
        );
      }
    },
    handleFormVisibleChange(visible) {
      if (!visible) {
        this.isAddSearching = false;
        this.addUserList = this.allOptions.slice(0, this.showCount);
      }
    },
    handleReviewFormFilter(query) {
      this.isReviewSearching = true;
      if (!query) {
        this.reviewUserList = this.allOptions.slice(0, this.showCount);
      } else {
        const lowerQuery = query.toString().toLowerCase();
        this.reviewUserList = this.allOptions.filter((item) =>
          item.nickName.toLowerCase().includes(lowerQuery)
        );
      }
    },
    handleReviewFormVisibleChange(visible) {
      if (!visible) {
        this.isReviewSearching = false;
        this.reviewUserList = this.allOptions.slice(0, this.showCount);
      }
    },
    handleAddChange(selectedObj) {
      // console.log("selectedObj", this.form.reporterUser);
      if (selectedObj) {
        this.form.reporterUserId = selectedObj.userId;
        this.form.reporterName = selectedObj.nickName;
        this.form.reporterPhone = selectedObj.phonenumber;
        this.form.reporterComp = selectedObj.companyName;
      } else {
        this.form.reporterUserId = null;
        this.form.reporterName = null;
        this.form.reporterPhone = null;
        this.form.reporterComp = null;
      }
    },
    /** 查询隐患举报列表 */
    getList() {
      this.loading = true;
      listHiddenDangerReport(this.queryParams).then((response) => {
        this.hiddenDangerReportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        reportCode: null,
        reporterUserId: null,
        reporterName: null,
        reporterPhone: null,
        reporterComp: null,
        reporterCompName: null,
        projectId: null,
        projectName: null,
        hazardLocation: null,
        hazardDescription: null,
        hazardCategory: null,
        hazardCategoryName: null,
        emergencyLevel: null,
        imageUrls: null,
        reportTime: null,
        status: null,
        auditOpinion: null,
        auditorId: null,
        auditTime: null,
        rewardValue: null,
        hiddenDangerId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        reporterUser: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.reporterComp = null;
      this.queryParams.projectId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加隐患举报";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHiddenDangerReport(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.form.reporterUser = {
          userId: this.form.reporterUserId,
          nickName: this.form.reporterName,
          phonenumber: this.form.reporterPhone,
          companyName: this.form.reporterComp,
        };
        this.title = "修改隐患举报";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getHiddenDangerReport(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.form.reporterUser = {
          userId: this.form.reporterUserId,
          nickName: this.form.reporterName,
          phonenumber: this.form.reporterPhone,
          companyName: this.form.reporterComp,
        };
        this.title = "查看隐患举报";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateHiddenDangerReport(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHiddenDangerReport(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除隐患举报编号为"' + ids + '"的数据项？')
        .then(function () {
          return delHiddenDangerReport(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/hiddenDangerReport/export",
        {
          ...this.queryParams,
        },
        `隐患举报奖励.xlsx`
      );
    },
  },
};
</script>
