import request from '@/utils/request'

// 查询预算投入信息列表
export function listInput(query) {
  return request({
    url: '/system/input/list',
    method: 'get',
    params: query
  })
}

// 查询预算投入信息详细
export function getInput(id) {
  return request({
    url: '/system/input/' + id,
    method: 'get'
  })
}

// 新增预算投入信息
export function addInput(data) {
  return request({
    url: '/system/input',
    method: 'post',
    data: data
  })
}

// 修改预算投入信息
export function updateInput(data) {
  return request({
    url: '/system/input',
    method: 'put',
    data: data
  })
}

// 删除预算投入信息
export function delInput(id) {
  return request({
    url: '/system/input/' + id,
    method: 'delete'
  })
}
