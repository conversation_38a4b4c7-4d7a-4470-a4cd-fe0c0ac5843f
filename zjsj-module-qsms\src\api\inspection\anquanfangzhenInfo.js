import request from '@/utils/request'

// 查询安全方针目标列表
export function listAnquanfangzhenInfo(query) {
  return request({
    url: '/inspection/anquanfangzhenInfo/list',
    method: 'get',
    params: query
  })
}

// 查询安全方针目标详细
export function getAnquanfangzhenInfo(id) {
  return request({
    url: '/inspection/anquanfangzhenInfo/' + id,
    method: 'get'
  })
}

// 新增安全方针目标
export function addAnquanfangzhenInfo(data) {
  return request({
    url: '/inspection/anquanfangzhenInfo',
    method: 'post',
    data: data
  })
}

// 修改安全方针目标
export function updateAnquanfangzhenInfo(data) {
  return request({
    url: '/inspection/anquanfangzhenInfo',
    method: 'put',
    data: data
  })
}

// 删除安全方针目标
export function delAnquanfangzhenInfo(id) {
  return request({
    url: '/inspection/anquanfangzhenInfo/' + id,
    method: 'delete'
  })
}
