<template>
  <div class="work-ticket-tab">
    <el-table v-loading="loading" :data="workTicketList" border style="width: 100%">
      <el-table-column prop="ticketNumber" label="作业票编号" align="left" width="160" />
      <el-table-column prop="workType" label="作业类型" align="center" width="120" />
      <el-table-column prop="workLocation" label="作业地点" align="left" width="180" />
      <el-table-column prop="workContent" label="作业内容" align="left" min-width="200" />
      <el-table-column prop="workStartTime" label="开始时间" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.workStartTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="workEndTime" label="结束时间" align="center" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.workEndTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="workLeader" label="作业负责人" align="center" width="120" />
      <el-table-column prop="status" label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listZjWorkTicket } from "@/api/work/zjWorkTicket";

export default {
  name: "WorkTicketTab",
  props: {
    contractorId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      workTicketList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorId: null
      }
    };
  },
  watch: {
    contractorId: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.contractorId = newVal;
          this.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询作业票列表 */
    getList() {
      if (!this.contractorId) return;
      this.loading = true;
      listZjWorkTicket(this.queryParams).then(response => {
        this.workTicketList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.workTicketList = [];
        this.total = 0;
      });
    },

    /** 获取状态类型 */
    getStatusType(status) {
      const statusMap = {
        1: 'info',      // 待审批
        2: 'warning',   // 进行中
        3: 'success',   // 已完成
        4: 'danger'     // 已取消
      };
      return statusMap[status] || 'info';
    },

    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        1: '待审批',
        2: '进行中',
        3: '已完成',
        4: '已取消'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.work-ticket-tab {
  padding: 20px;
}
</style>
