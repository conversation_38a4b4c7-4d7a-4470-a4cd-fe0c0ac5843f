import request from '@/utils/request'

// 查询企业应急预案管理列表
export function listZjEnterpriseEmergencyPlan(query) {
  return request({
    url: '/inspection/zjEnterpriseEmergencyPlan/list',
    method: 'get',
    params: query
  })
}

// 查询企业应急预案管理详细
export function getZjEnterpriseEmergencyPlan(id) {
  return request({
    url: '/inspection/zjEnterpriseEmergencyPlan/' + id,
    method: 'get'
  })
}

// 新增企业应急预案管理
export function addZjEnterpriseEmergencyPlan(data) {
  return request({
    url: '/inspection/zjEnterpriseEmergencyPlan',
    method: 'post',
    data: data
  })
}

// 修改企业应急预案管理
export function updateZjEnterpriseEmergencyPlan(data) {
  return request({
    url: '/inspection/zjEnterpriseEmergencyPlan',
    method: 'put',
    data: data
  })
}

// 删除企业应急预案管理
export function delZjEnterpriseEmergencyPlan(id) {
  return request({
    url: '/inspection/zjEnterpriseEmergencyPlan/' + id,
    method: 'delete'
  })
}
