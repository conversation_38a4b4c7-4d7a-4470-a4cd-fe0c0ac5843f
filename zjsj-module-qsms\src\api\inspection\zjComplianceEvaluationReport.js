import request from '@/utils/request'

// 查询符合性评价报告列表
export function listZjComplianceEvaluationReport(query) {
  return request({
    url: '/inspection/zjComplianceEvaluationReport/list',
    method: 'get',
    params: query
  })
}

// 查询符合性评价报告详细
export function getZjComplianceEvaluationReport(id) {
  return request({
    url: '/inspection/zjComplianceEvaluationReport/' + id,
    method: 'get'
  })
}

// 新增符合性评价报告
export function addZjComplianceEvaluationReport(data) {
  return request({
    url: '/inspection/zjComplianceEvaluationReport',
    method: 'post',
    data: data
  })
}

// 修改符合性评价报告
export function updateZjComplianceEvaluationReport(data) {
  return request({
    url: '/inspection/zjComplianceEvaluationReport',
    method: 'put',
    data: data
  })
}

// 删除符合性评价报告
export function delZjComplianceEvaluationReport(id) {
  return request({
    url: '/inspection/zjComplianceEvaluationReport/' + id,
    method: 'delete'
  })
}
