<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="检查类型" prop="checkType">
        <el-select
          v-model="queryParams.checkType"
          placeholder="请选择检查类型"
          clearable
        >
          <el-option
            v-for="item in checkTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检查状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择检查状态"
          clearable
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-date"
          size="mini"
          @click="handleCalendarView"
          >日历视图</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-setting"
          size="mini"
          :disabled="multiple"
          @click="handleBatchManage"
          >批量管理</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="checkList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 250px)"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        prop="checkCode"
        label="检查编号"
        :show-overflow-tooltip="true"
        width="140"
      />
      <el-table-column
        prop="checkType"
        label="检查类型"
        align="center"
        width="100"
      />
      <el-table-column
        prop="checkContent"
        label="检查内容"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="inspector"
        label="检查人员"
        align="center"
        width="100"
      />
      <el-table-column
        prop="checkDate"
        label="检查日期"
        align="center"
        width="120"
      />
      <el-table-column
        prop="problemCount"
        label="发现问题"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <el-tag :type="scope.row.problemCount > 0 ? 'danger' : 'success'">
            {{ scope.row.problemCount }}个
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        label="检查状态"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag :options="statusDictOptions" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.status === '进行中'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleComplete(scope.row)"
            >完成</el-button
          >
          <el-button
            v-if="scope.row.status === '待开始'"
            size="mini"
            type="text"
            icon="el-icon-video-play"
            @click="handleStart(scope.row)"
            >开始</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改检查对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="检查编号" prop="checkCode">
              <el-input
                v-model="form.checkCode"
                placeholder="请输入检查编号"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查类型" prop="checkType">
              <el-select
                v-model="form.checkType"
                placeholder="请选择检查类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in checkTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查日期" prop="checkDate">
              <el-date-picker
                v-model="form.checkDate"
                type="date"
                placeholder="选择检查日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查人员" prop="inspector">
              <el-input v-model="form.inspector" placeholder="请输入检查人员" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查区域" prop="checkArea">
              <el-input v-model="form.checkArea" placeholder="请输入检查区域" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查内容" prop="checkContent">
              <el-input
                v-model="form.checkContent"
                type="textarea"
                placeholder="请输入检查内容"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查要求" prop="requirements">
              <el-input
                v-model="form.requirements"
                type="textarea"
                placeholder="请输入检查要求"
                :rows="3"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="适用范围" prop="applicableScope">
              <el-select
                v-model="form.applicableScope"
                multiple
                placeholder="请选择适用范围（可多选）"
                style="width: 100%"
              >
                <el-option
                  v-for="item in applicableScopeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="待开始">待开始</el-radio>
                <el-radio label="进行中">进行中</el-radio>
                <el-radio label="已完成">已完成</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看检查详情对话框 -->
    <el-dialog
      title="检查详情"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="检查编号">{{
          viewForm.checkCode
        }}</el-descriptions-item>
        <el-descriptions-item label="检查类型">{{
          viewForm.checkType
        }}</el-descriptions-item>
        <el-descriptions-item label="检查人员">{{
          viewForm.inspector
        }}</el-descriptions-item>
        <el-descriptions-item label="检查日期">{{
          viewForm.checkDate
        }}</el-descriptions-item>
        <el-descriptions-item label="检查区域">{{
          viewForm.checkArea
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="statusDictOptions" :value="viewForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="检查内容" :span="2">{{
          viewForm.checkContent
        }}</el-descriptions-item>
        <el-descriptions-item label="检查要求" :span="2">{{
          viewForm.requirements
        }}</el-descriptions-item>
        <el-descriptions-item label="适用范围" :span="2">
          <el-tag
            v-for="scope in viewForm.applicableScope"
            :key="scope"
            style="margin-right: 5px"
            >{{ scope }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{
          parseTime(viewForm.createTime)
        }}</el-descriptions-item>
      </el-descriptions>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "DailySafetyCheck",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 日常安全检查表格数据
      checkList: [
        {
          id: 1,
          checkCode: "RC2024001",
          checkType: "安全生产",
          checkContent: "A区域施工现场安全检查",
          inspector: "张三",
          checkDate: "2024-01-15",
          checkArea: "A区域",
          requirements: "严格按照安全规范进行检查",
          problemCount: 2,
          status: "已完成",
          applicableScope: ["建筑施工", "设备管理"],
          createTime: "2025-05-12 12:00:00",
        },
        {
          id: 2,
          checkCode: "RC2024002",
          checkType: "消防安全",
          checkContent: "B区域消防设施检查",
          inspector: "李四",
          checkDate: "2024-01-16",
          checkArea: "B区域",
          requirements: "检查消防设施是否正常运行",
          problemCount: 0,
          status: "进行中",
          applicableScope: ["消防安全"],
          createTime: "2025-05-12 13:00:00",
        },
        {
          id: 3,
          checkCode: "RC2024003",
          checkType: "环境安全",
          checkContent: "C区域环境卫生检查",
          inspector: "王五",
          checkDate: "2024-01-17",
          checkArea: "C区域",
          requirements: "保持环境整洁",
          problemCount: 1,
          status: "待开始",
          applicableScope: ["环境保护"],
          createTime: "2025-05-12 14:00:00",
        },
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹窗
      viewOpen: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkType: undefined,
        status: undefined,
        dateRange: [],
      },
      // 表单参数
      form: {},
      // 查看表单数据
      viewForm: {},

      // 检查类型选项
      checkTypeOptions: [
        { label: "安全生产", value: "安全生产" },
        { label: "消防安全", value: "消防安全" },
        { label: "环境安全", value: "环境安全" },
        { label: "设备安全", value: "设备安全" },
      ],
      // 状态选项
      statusOptions: [
        { label: "待开始", value: "待开始" },
        { label: "进行中", value: "进行中" },
        { label: "已完成", value: "已完成" },
      ],
      // 状态字典选项
      statusDictOptions: [
        { label: "待开始", value: "待开始" },
        { label: "进行中", value: "进行中" },
        { label: "已完成", value: "已完成" },
      ],
      // 适用范围选项
      applicableScopeOptions: [
        { label: "建筑施工", value: "建筑施工" },
        { label: "设备管理", value: "设备管理" },
        { label: "脚手架工程", value: "脚手架工程" },
        { label: "消防安全", value: "消防安全" },
        { label: "电气工程", value: "电气工程" },
        { label: "机械设备", value: "机械设备" },
        { label: "环境保护", value: "环境保护" },
        { label: "质量管控", value: "质量管控" },
      ],
      // 表单校验
      rules: {
        checkCode: [
          { required: true, message: "检查编号不能为空", trigger: "blur" },
        ],
        checkType: [
          { required: true, message: "检查类型不能为空", trigger: "change" },
        ],
        checkDate: [
          { required: true, message: "检查日期不能为空", trigger: "change" },
        ],
        inspector: [
          { required: true, message: "检查人员不能为空", trigger: "blur" },
        ],
        checkContent: [
          { required: true, message: "检查内容不能为空", trigger: "blur" },
        ],
        applicableScope: [
          { required: true, message: "适用范围不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 时间格式化
    parseTime,
    /** 查询日常安全检查列表 */
    getList() {
      this.loading = true;
      // 模拟数据加载
      setTimeout(() => {
        this.total = this.checkList.length;
        this.loading = false;
      }, 500);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        checkCode: undefined,
        checkType: undefined,
        checkDate: undefined,
        inspector: undefined,
        checkArea: undefined,
        checkContent: undefined,
        requirements: undefined,
        applicableScope: [],
        status: "待开始",
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加日常安全检查";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const selectedRow =
        row || this.checkList.find((item) => this.ids.includes(item.id));
      if (selectedRow) {
        this.form = { ...selectedRow };
      }
      this.open = true;
      this.title = "修改日常安全检查";
    },
    /** 日历视图按钮操作 */
    handleCalendarView() {
      this.$message.info("日历视图功能待实现");
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = { ...row };
      this.viewOpen = true;
    },
    /** 开始检查按钮操作 */
    handleStart(row) {
      this.$confirm("是否确认开始该检查?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$message.success("检查已开始");
        this.getList();
      });
    },
    /** 完成检查按钮操作 */
    handleComplete(row) {
      this.$confirm("是否确认完成该检查?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$message.success("检查已完成");
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            // 修改的提交
            this.$message.success("修改成功");
          } else {
            // 新增的提交
            this.$message.success("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      const names = row
        ? [row.checkCode]
        : this.checkList
            .filter((item) => ids.includes(item.id))
            .map((item) => item.checkCode);
      this.$confirm(
        '是否确认删除检查编号"' + names.join("、") + '"的检查记录?',
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.$message.success("删除成功");
          this.getList();
        })
        .catch(() => {});
    },
    /** 批量管理按钮操作 */
    handleBatchManage() {
      this.$message.info("批量管理功能待实现");
    },

    /** 导出按钮操作 */
    handleExport() {
      this.$message.info("导出功能待实现");
    },
  },
};
</script>

<style scoped>
.config-section {
  margin-bottom: 20px;
}

.config-section h4 {
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin: 0 10px;
  min-width: 80px;
}

.el-table .cell {
  padding: 8px 12px;
}
</style>
