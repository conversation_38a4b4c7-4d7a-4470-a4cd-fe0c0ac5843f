<template>
  <div class="app-container">
    <el-tabs
      v-model="activeTab"
      class="contractor-tabs"
      @tab-click="handleTabClick"
    >
      <!-- 评价记录 Tab -->
      <el-tab-pane label="评价记录" name="records">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="80px"
          class="search-form"
        >
          <el-form-item label="评价名称" prop="evaluationName">
            <el-input
              v-model="queryParams.evaluationName"
              placeholder="请输入内容"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="承包商" prop="contractorName">
            <el-select
              v-model="queryParams.contractorName"
              placeholder="请选择"
              clearable
              style="width: 240px"
            >
              <el-option
                label="山东临工工程机械有限公司"
                value="山东临工工程机械有限公司"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="评价结论" prop="evaluationConclusion">
            <el-select
              v-model="queryParams.evaluationConclusion"
              placeholder="请选择"
              clearable
              style="width: 240px"
            >
              <el-option label="合格" value="1" />
              <el-option label="不合格" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="toolbar-section">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-dropdown
                type="primary"
                size="mini"
                @command="handleAddByType"
                v-hasPermi="['contractor:zjContractorEvaluation:add']"
              >
                <el-button type="primary" size="mini">
                  新增评价<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="project">项目评价</el-dropdown-item>
                  <el-dropdown-item command="periodic"
                    >周期性评价</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>
        </div>

        <el-table v-loading="loading" :data="zjContractorEvaluationList" @selection-change="handleSelectionChange" height="calc(100vh - 310px)">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="评价名称"
            align="center"
            prop="evaluationName"
          />
          <el-table-column
            label="评价对象"
            align="center"
            prop="contractorName"
          />
          <el-table-column
            label="评价模板"
            align="center"
            prop="evaluationTemplateId"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.evaluationTemplateName || "未设置" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="评价方式"
            align="center"
            prop="evaluationMethod"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.contractore_valuation_method"
                :value="scope.row.evaluationMethod"
              />
              <!-- 兜底显示：如果字典转换失败，显示名称字段 -->
              <span
                v-if="
                  !scope.row.evaluationMethod && scope.row.evaluationMethodName
                "
              >
                {{ scope.row.evaluationMethodName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="评价规则"
            align="center"
            prop="evaluationRules"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.contractor_evaluation_rules"
                :value="scope.row.evaluationRules"
              />
              <!-- 兜底显示：如果字典转换失败，显示名称字段 -->
              <span
                v-if="
                  !scope.row.evaluationRules && scope.row.evaluationRulesName
                "
              >
                {{ scope.row.evaluationRulesName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="评价时间"
            align="center"
            prop="nextEvaluationTime"
            width="120"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.nextEvaluationTime, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="评价结论"
            align="center"
            prop="evaluationConclusion"
          >
            <template slot-scope="scope">
              <el-tag
                :type="
                  getEvaluationConclusionType(scope.row.evaluationConclusion)
                "
              >
                {{
                  getEvaluationConclusionText(scope.row.evaluationConclusion)
                }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="评价人"
            align="center"
            prop="evaluatorsName"
          />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="200"
          >
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleView(scope.row)"
                >查看详情</el-button
              >
              <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                v-hasPermi="['contractor:zjContractorEvaluation:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>

      <!-- 评价配置 Tab -->
      <el-tab-pane label="评价配置" name="config">
        <!-- 顶部搜索控制栏 -->
        <div class="config-header">
          <div class="search-controls">
            <el-select
              v-model="batchSelectValue"
              placeholder="批量选择"
              size="small"
              style="width: 120px; margin-right: 16px"
            >
              <el-option label="全部" value="all" />
              <el-option
                v-for="dict in dict.type.contractore_valuation_method"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            <el-input
              v-model="configSearchText"
              placeholder="请输入搜索内容"
              prefix-icon="el-icon-search"
              size="small"
              style="width: 300px"
              @keyup.enter.native="handleConfigSearch"
              clearable
            />
          </div>
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleConfigAddByType('project')"
              v-hasPermi="['contractor:zjEvaluationConfigInfo:add']"
            >
              项目评价新建
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleConfigAddByType('periodic')"
              v-hasPermi="['contractor:zjEvaluationConfigInfo:add']"
            >
              周期性评价新建
            </el-button>
          </div>
        </div>

        <!-- 卡片列表 -->
        <div v-loading="configLoading" class="config-cards">
          <div
            v-for="item in filteredConfigList"
            :key="item.id"
            class="config-card"
            @click="handleConfigCardClick(item)"
          >
            <div class="card-content">
              <div class="card-header">
                <div class="card-icon">
                  <i
                    :class="getCardIcon(item.evaluationMethod)"
                    class="card-icon-img"
                  ></i>
                </div>
                <div class="card-title">
                  {{ item.evaluationTemplateName || "未命名" }}
                </div>
              </div>

              <div class="card-date">
                {{ formatDate(item.createTime) }}
              </div>

              <div class="card-footer">
                <div class="card-tag">
                  <dict-tag
                    :options="dict.type.contractor_evaluation_rules"
                    :value="item.evaluationRules"
                  />
                </div>
                <div class="card-actions">
                  <el-tooltip content="复制" placement="top">
                    <i
                      class="el-icon-document-copy action-icon"
                      @click.stop="handleConfigCopy(item)"
                    ></i>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top">
                    <i
                      class="el-icon-delete action-icon delete-icon"
                      @click.stop="handleConfigDelete(item)"
                      v-hasPermi="['contractor:zjEvaluationConfigInfo:remove']"
                    ></i>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-if="!configLoading && filteredConfigList.length === 0"
          class="empty-state"
        >
          <i class="el-icon-document-add empty-icon"></i>
          <p class="empty-text">暂无评价配置数据</p>
          <el-button
            type="primary"
            size="small"
            @click="handleConfigAdd"
            v-hasPermi="['contractor:zjEvaluationConfigInfo:add']"
          >
            新建配置
          </el-button>
        </div>

        <pagination
          v-if="filteredConfigList.length > 0 && configTotal > 0"
          :total="configTotal"
          :page.sync="configQueryParams.pageNum"
          :limit.sync="configQueryParams.pageSize"
          @pagination="getConfigList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改承包商评价对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1100px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <!-- 基础信息区域 -->
        <el-divider content-position="left">基础信息</el-divider>
        <el-row :gutter="20">
          <!-- 左侧列 -->
          <el-col :span="12">
            <el-form-item label="评价名称" prop="evaluationName">
              <el-input
                v-model="form.evaluationName"
                placeholder="请输入评价名称"
                :disabled="isViewMode"
              />
            </el-form-item>
            <el-form-item label="评价模板" prop="evaluationTemplateId">
              <el-select
                v-model="form.evaluationTemplateId"
                placeholder="请选择评价模板"
                style="width: 100%"
                @change="handleTemplateChange"
                clearable
                :disabled="isViewMode"
              >
                <el-option
                  v-for="template in templateOptions"
                  :key="template.id"
                  :label="template.evaluationTemplateName"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="承包商ID"
              prop="contractorId"
              v-if="
                form.evaluationMethod !== '1' && form.evaluationMethod !== '2'
              "
            >
              <el-input
                v-model="form.contractorId"
                placeholder="请输入承包商ID"
                :disabled="isViewMode"
              />
            </el-form-item>
            <el-form-item
              label="项目名称"
              prop="projectName"
              v-if="form.evaluationMethod === '1'"
            >
              <el-input
                v-model="form.projectName"
                placeholder="请输入项目名称"
                :disabled="isViewMode"
              />
            </el-form-item>
            <el-form-item
              label="承包商"
              prop="contractorId"
              :required="form.evaluationMethod === '1' || form.evaluationMethod === '2'"
              v-if="
                form.evaluationMethod === '1' || form.evaluationMethod === '2'
              "
            >
              <el-select
                v-model="form.contractorId"
                placeholder="请选择承包商"
                style="width: 100%"
                :clearable="false"
                filterable
                @change="handleContractorChange"
                :disabled="isViewMode"
              >
                <el-option
                  v-for="contractor in contractorOptions"
                  :key="contractor.id"
                  :label="contractor.label"
                  :value="contractor.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 右侧列 -->
          <el-col :span="12">
            <el-form-item label="评价方式" prop="evaluationMethod">
              <el-select
                v-model="form.evaluationMethod"
                placeholder="请选择评价方式"
                style="width: 100%"
                disabled
              >
                <el-option
                  v-for="dict in dict.type.contractore_valuation_method"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="评价规则" prop="evaluationRules">
              <el-select
                v-model="form.evaluationRules"
                placeholder="请选择评价规则"
                style="width: 100%"
                :disabled="isViewMode"
              >
                <el-option
                  v-for="dict in dict.type.contractor_evaluation_rules"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="承包商名称"
              prop="contractorName"
              v-if="
                form.evaluationMethod !== '1' && form.evaluationMethod !== '2'
              "
            >
              <el-input
                v-model="form.contractorName"
                placeholder="请输入承包商名称"
                :disabled="isViewMode"
              />
            </el-form-item>
            <el-form-item label="评价结论" prop="evaluationConclusion">
              <el-select
                v-model="form.evaluationConclusion"
                placeholder="请选择评价结论"
                style="width: 100%"
                :disabled="isViewMode"
              >
                <el-option label="合格" value="1" />
                <el-option label="不合格" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 评价人员信息区域 -->
        <el-divider content-position="left">评价人员</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="评价人员ID"
              prop="evaluatorsId"
              v-if="
                form.evaluationMethod !== '1' && form.evaluationMethod !== '2'
              "
            >
              <el-input
                v-model="form.evaluatorsId"
                placeholder="请输入评价人员ID"
                :disabled="isViewMode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评价人员姓名" prop="evaluatorsName">
              <el-input
                v-model="form.evaluatorsName"
                placeholder="请输入评价人员姓名"
                :disabled="isViewMode"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 评价周期信息区域 (仅周期性评价显示) -->
        <div v-if="form.evaluationMethod === '2'">
          <el-divider content-position="left">评价周期设置</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="评价周期" prop="evaluationCycle">
                <el-input-number
                  v-model="form.evaluationCycle"
                  :min="1"
                  :max="12"
                  placeholder="周期"
                  style="width: 100%"
                  @change="calculateNextEvaluationTime"
                  :disabled="isViewMode"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="评价周期单位" prop="evaluationCycleUnit">
                <el-select
                  v-model="form.evaluationCycleUnit"
                  placeholder="请选择周期单位"
                  style="width: 100%"
                  @change="calculateNextEvaluationTime"
                  :disabled="isViewMode"
                >
                  <el-option label="年" value="year" />
                  <el-option label="月" value="month" />
                  <el-option label="日" value="day" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="下一周期评价时间" prop="nextEvaluationTime">
                <el-input
                  v-model="form.nextEvaluationTime"
                  placeholder="系统自动计算: YYYY-MM-DD"
                  style="width: 100%"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-divider content-position="center">评价内容</el-divider>
        <div v-if="!form.evaluationTemplateId" class="template-tip">
          <i class="el-icon-info"></i>
          <span>请先选择评价模板以加载评价内容</span>
        </div>
        <div
          v-else-if="!contractorEvaluationContentList || contractorEvaluationContentList.length === 0"
          class="template-tip"
        >
          <i class="el-icon-warning"></i>
          <span>所选评价模板暂无评价内容</span>
        </div>
        <el-table
          v-if="contractorEvaluationContentList && contractorEvaluationContentList.length > 0"
          :data="contractorEvaluationContentList"
          :row-class-name="rowZjContractorEvaluationContentIndex"
          ref="zjContractorEvaluationContent"
          border
          class="evaluation-table"
        >
          <el-table-column
            label="序号"
            align="center"
            prop="index"
            width="60"
          />
          <el-table-column
            label="评价项目"
            prop="evaluationProject"
            min-width="200"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.evaluationProject || "未设置" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="评分标准"
            prop="gradingCriteria"
            min-width="250"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.gradingCriteria || "未设置" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="项目分值"
            prop="projectScore"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.projectScore || 0 }}分</span>
            </template>
          </el-table-column>
          <el-table-column
            label="项目得分"
            prop="evaluationResults"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.evaluationResults"
                placeholder="得分"
                :min="0"
                :max="scope.row.projectScore || 100"
                :precision="1"
                size="small"
                style="width: 100%"
                :disabled="isViewMode || !scope.row.projectScore"
                @change="handleScoreChange(scope.row, scope.$index)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!isViewMode" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改评价配置对话框 -->
    <el-dialog
      :title="configTitle"
      :visible.sync="configOpen"
      width="90%"
      append-to-body
    >
      <el-form
        ref="configForm"
        :model="configForm"
        :rules="configRules"
        label-width="140px"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="评价模版名称" prop="evaluationTemplateName">
              <el-input
                v-model="configForm.evaluationTemplateName"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评价方式" prop="evaluationMethod">
              <el-select
                v-model="configForm.evaluationMethod"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <el-option
                  v-for="dict in dict.type.contractore_valuation_method"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="评价模板介绍" prop="evaluationTemplateIntro">
              <el-input
                type="textarea"
                v-model="configForm.evaluationTemplateIntro"
                placeholder="请输入内容"
                :rows="4"
                :maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="评价规则" prop="evaluationRules">
              <el-select
                v-model="configForm.evaluationRules"
                placeholder="请选择"
                style="width: 100%"
                disabled
              >
                <el-option
                  v-for="dict in dict.type.contractor_evaluation_rules"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">评价内容</el-divider>

        <!-- 评价内容表格 -->
        <div class="evaluation-content-section">
          <div class="table-header-info">
            <span class="total-score-info">
              当前总分：<span :class="{ 'score-error': totalScore !== 100 }">{{
                totalScore
              }}</span
              >分
              <span v-if="totalScore !== 100" class="score-tip"
                >（分值总和必须为100分）</span
              >
            </span>
          </div>

          <el-table
            :data="configContentList"
            :row-class-name="rowConfigContentIndex"
            @selection-change="handleConfigContentSelectionChange"
            ref="configContentTable"
            border
            class="evaluation-content-table"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              label="评价项目"
              prop="evaluationProject"
              min-width="200"
            >
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.evaluationProject"
                  placeholder="请输入评价项目"
                  @blur="validateTotalScore"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="评分标准"
              prop="gradingCriteria"
              min-width="200"
            >
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.gradingCriteria"
                  placeholder="请输入评分标准"
                />
              </template>
            </el-table-column>
            <el-table-column label="项目分值" prop="projectScore" width="120">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.projectScore"
                  :min="0"
                  :max="100"
                  :precision="0"
                  placeholder="分值"
                  @change="validateTotalScore"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="120"
              align="center"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="saveConfigContentRow(scope.row, scope.$index)"
                  :disabled="
                    !scope.row.evaluationProject ||
                    !scope.row.gradingCriteria ||
                    !scope.row.projectScore
                  "
                >
                  保存
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  @click="cancelConfigContentRow(scope.row, scope.$index)"
                >
                  取消
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="table-actions">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="small"
              @click="handleAddConfigContent"
              class="add-evaluation-btn"
            >
              新增评价项目
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="small"
              @click="handleDeleteConfigContent"
              :disabled="!checkedConfigContent.length"
            >
              删除选中项目
            </el-button>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitConfigForm"
          :disabled="totalScore !== 100"
          >确 定</el-button
        >
        <el-button @click="cancelConfig">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjContractorEvaluation,
  getZjContractorEvaluation,
  delZjContractorEvaluation,
  addZjContractorEvaluation,
  updateZjContractorEvaluation,
} from "@/api/contractor/zjContractorEvaluation";
import {
  listZjEvaluationConfigInfo,
  getZjEvaluationConfigInfo,
  delZjEvaluationConfigInfo,
  addZjEvaluationConfigInfo,
  updateZjEvaluationConfigInfo,
} from "@/api/contractor/zjEvaluationConfigInfo";
import {
  listZjEvaluationConfigContent,
  getZjEvaluationConfigContent,
  delZjEvaluationConfigContent,
  addZjEvaluationConfigContent,
  updateZjEvaluationConfigContent,
} from "@/api/contractor/zjEvaluationConfigContent";
import { getContractorInfo } from "@/api/contractor/zjContractorInfo";

export default {
  name: "ZjContractorEvaluation",
  dicts: ["contractore_valuation_method", "contractor_evaluation_rules"],
  data() {
    return {
      // 当前激活的tab
      activeTab: "records",
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedZjContractorEvaluationContent: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评价记录表格数据
      zjContractorEvaluationList: [],
      // 评价内容表格数据
      contractorEvaluationContentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否为查看模式（只读）
      isViewMode: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        evaluationName: null,
        evaluationConclusion: null,
        contractorName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        evaluationName: [
          { required: true, message: "评价名称不能为空", trigger: "blur" },
          {
            max: 100,
            message: "评价名称长度不能超过100个字符",
            trigger: "blur",
          },
        ],
        evaluationMethod: [
          { required: true, message: "请选择评价方式", trigger: "change" },
        ],
        evaluationRules: [
          { required: true, message: "请选择评价规则", trigger: "change" },
        ],
        evaluatorsName: [
          { required: true, message: "评价人员姓名不能为空", trigger: "blur" },
        ],
        evaluatorsId: [
          {
            validator: (rule, value, callback) => {
              // 项目评价(1)和周期性评价(2)时不需要验证评价人员ID
              if (
                this.form.evaluationMethod === "1" ||
                this.form.evaluationMethod === "2"
              ) {
                callback();
              } else {
                // 其他评价方式时，评价人员ID可选，不强制要求
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        evaluationTemplateId: [
          { required: true, message: "请选择评价模板", trigger: "change" },
        ],
        contractorName: [
          {
            validator: (rule, value, callback) => {
              // 项目评价(1)和周期性评价(2)时不需要验证承包商名称
              if (
                this.form.evaluationMethod === "1" ||
                this.form.evaluationMethod === "2"
              ) {
                callback();
              } else {
                if (!value || value.trim() === "") {
                  callback(new Error("承包商名称不能为空"));
                } else {
                  callback();
                }
              }
            },
            trigger: "blur",
          },
        ],
        contractorId: [
          {
            validator: (rule, value, callback) => {
              // 项目评价(1)和周期性评价(2)时需要验证承包商ID
              if (
                this.form.evaluationMethod === "1" ||
                this.form.evaluationMethod === "2"
              ) {
                if (!value) {
                  callback(new Error("请选择承包商"));
                } else {
                  callback();
                }
              } else {
                // 其他评价方式时，承包商ID可选，不强制要求
                callback();
              }
            },
            trigger: "change",
          },
        ],
        projectName: [
          {
            validator: (rule, value, callback) => {
              // 只有在项目评价时才验证项目名称
              if (this.form.evaluationMethod === "1") {
                if (!value || value.trim() === "") {
                  callback(new Error("项目名称不能为空"));
                } else if (value.length > 100) {
                  callback(new Error("项目名称长度不能超过100个字符"));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        evaluationConclusion: [
          { required: true, message: "请选择评价结论", trigger: "change" },
        ],
        evaluationCycle: [
          { required: true, message: "请输入评价周期", trigger: "blur" },
          {
            type: "number",
            min: 1,
            max: 12,
            message: "评价周期必须在1-12之间",
            trigger: "blur",
          },
        ],
        evaluationCycleUnit: [
          { required: true, message: "请选择评价周期单位", trigger: "change" },
        ],
      },

      // 评价配置相关数据
      configLoading: false,
      configIds: [],
      configSingle: true,
      configMultiple: true,
      showConfigSearch: true,
      configTotal: 0,
      zjEvaluationConfigInfoList: [],
      configTitle: "",
      configOpen: false,
      configQueryParams: {
        pageNum: 1,
        pageSize: 10,
        evaluationTemplateName: null,
        evaluationMethod: null,
        evaluationRules: null,
      },
      configForm: {},
      configRules: {
        evaluationTemplateName: [
          { required: true, message: "评价模版名称不能为空", trigger: "blur" },
          {
            max: 50,
            message: "评价模版名称长度不能超过50个字符",
            trigger: "blur",
          },
        ],
        evaluationMethod: [
          { required: true, message: "评价方式不能为空", trigger: "change" },
        ],
        evaluationRules: [
          { required: true, message: "评价规则不能为空", trigger: "change" },
        ],
        evaluationTemplateIntro: [
          {
            max: 500,
            message: "评价模板介绍长度不能超过500个字符",
            trigger: "blur",
          },
        ],
      },
      // 新增的卡片视图相关数据
      batchSelectValue: "all",
      configSearchText: "",

      // 评价配置内容相关数据
      configContentList: [],
      checkedConfigContent: [],
      originalConfigContentList: [], // 用于保存原始数据，支持取消操作

      // 评价模板选项数据
      templateOptions: [], // 评价模板选项列表（新增/编辑用）

      // 承包商选择器相关数据
      contractorOptions: [], // 承包商选项列表
      
    };
  },
  computed: {
    
    // 过滤后的配置列表
    filteredConfigList() {
      let list = this.zjEvaluationConfigInfoList;

      // 按照批量选择过滤
      if (this.batchSelectValue !== "all") {
        list = list.filter(
          (item) => item.evaluationMethod === this.batchSelectValue
        );
      }

      // 按照搜索文本过滤
      if (this.configSearchText) {
        const searchText = this.configSearchText.toLowerCase();
        list = list.filter(
          (item) =>
            (item.evaluationTemplateName &&
              item.evaluationTemplateName.toLowerCase().includes(searchText)) ||
            (item.evaluationTemplateIntro &&
              item.evaluationTemplateIntro.toLowerCase().includes(searchText))
        );
      }

      return list;
    },

    // 计算总分
    totalScore() {
      return this.configContentList.reduce((total, item) => {
        return total + (item.projectScore || 0);
      }, 0);
    },

    // 评价总分（满分）
    evaluationTotalScore() {
      return this.contractorEvaluationContentList.reduce((total, item) => {
        return total + (item.projectScore || 0);
      }, 0);
    },

    // 实际得分
    actualTotalScore() {
      return this.contractorEvaluationContentList.reduce((total, item) => {
        return total + (item.evaluationResults || 0);
      }, 0);
    },

    // 得分率
    scorePercentage() {
      if (this.evaluationTotalScore === 0) return 0;
      return Math.round(
        (this.actualTotalScore / this.evaluationTotalScore) * 100
      );
    },

    // 评价结论文本
    evaluationResultText() {
      if (this.scorePercentage >= 90) return "优秀";
      if (this.scorePercentage >= 80) return "良好";
      if (this.scorePercentage >= 70) return "合格";
      if (this.scorePercentage >= 60) return "基本合格";
      return "不合格";
    },

    // 评价结论样式
    evaluationResultClass() {
      if (this.scorePercentage >= 80) return "result-excellent";
      if (this.scorePercentage >= 70) return "result-good";
      if (this.scorePercentage >= 60) return "result-qualified";
      return "result-unqualified";
    },
  },
  created() {
    // 根据当前激活的tab加载对应数据
    this.loadTabData(this.activeTab);
  },
  methods: {
    /** 处理tab切换 */
    handleTabClick(tab) {
      this.loadTabData(tab.name);
    },

    /** 根据tab名称加载对应数据 */
    loadTabData(tabName) {
      if (tabName === "records") {
        // 切换到评价记录tab时加载数据
        this.getList();
      } else if (tabName === "config") {
        // 切换到评价配置tab时加载数据
        this.getConfigList();
      }
    },

    /** 查询评价记录列表 */
    getList() {
      this.loading = true;
      listZjContractorEvaluation(this.queryParams)
        .then((response) => {
          // 处理返回的数据，进行字段转换和映射
          this.zjContractorEvaluationList = response.rows.map((item) => {
            return {
              ...item,
              // 确保评价模板名称字段存在
              evaluationTemplateName: item.evaluationTemplateName,
              // 处理评价方式：如果后端返回名称，需要转换为字典值
              evaluationMethod: this.convertMethodNameToValue(
                item.evaluationMethodName,
                item.evaluationMethod
              ),
              // 处理评价规则：如果后端返回名称，需要转换为字典值
              evaluationRules: this.convertRulesNameToValue(
                item.evaluationRulesName,
                item.evaluationRules
              ),
              // 确保评价结论为字符串类型
              evaluationConclusion: String(item.evaluationConclusion || ""),
              // 格式化创建时间
              createTime: item.createTime,
              // 格式化下次评价时间
              nextEvaluationTime: item.nextEvaluationTime,
            };
          });
          this.total = response.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        evaluationName: null,
        evaluationMethod: null,
        evaluationRules: null,
        evaluatorsId: null,
        evaluatorsName: null,
        evaluationTemplateId: null,
        evaluationConclusion: null,
        contractorId: "", // 确保字段存在，用于承包商选择器
        contractorName: "", // 确保字段存在，用于存储承包商名称
        projectName: null,
        evaluationCycle: null,
        evaluationCycleUnit: null,
        nextEvaluationTime: null,
        createTime: null,
        createBy: null,
        updateBy: null,
        updateTime: null,
      };
      this.contractorEvaluationContentList = [];
      this.isViewMode = false; // 重置查看模式
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 按类型新增按钮操作 */
    handleAddByType(command) {
      this.reset();

      // 根据选择的类型预设评价方式
      if (command === "project") {
        this.form.evaluationMethod = "1"; // 项目评价
        this.title = "添加项目评价";
      } else if (command === "periodic") {
        this.form.evaluationMethod = "2"; // 周期性评价
        this.title = "添加周期性评价";
        // 为周期性评价设置默认周期
        this.form.evaluationCycle = 1;
        this.form.evaluationCycleUnit = "year";
        // 计算默认的下一周期评价时间
        this.$nextTick(() => {
          this.calculateNextEvaluationTime();
        });
      }

      this.loadTemplateOptions(this.form.evaluationMethod);
      this.loadContractorOptions(); // 加载承包商选项
      this.open = true;
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjContractorEvaluation(id).then((response) => {
        this.form = response.data;
        
        // 处理评价内容列表，确保不为null
        this.contractorEvaluationContentList = 
          response.data.contractorEvaluationContentList || [];

        // 处理评价方式：如果后端返回名称，需要转换为字典值
        this.form.evaluationMethod = this.convertMethodNameToValue(
          response.data.evaluationMethodName,
          response.data.evaluationMethod
        );

        // 处理评价规则：如果后端返回名称，需要转换为字典值
        this.form.evaluationRules = this.convertRulesNameToValue(
          response.data.evaluationRulesName,
          response.data.evaluationRules
        );

        // 加载选项数据
        this.loadTemplateOptions(this.form.evaluationMethod);
        this.loadContractorOptions(); // 加载承包商选项

        this.isViewMode = true; // 设置为查看模式
        this.open = true;
        this.title = "查看评价详情";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjContractorEvaluation(id).then((response) => {
        this.form = response.data;
        
        // 处理评价内容列表，确保不为null
        this.contractorEvaluationContentList = 
          response.data.contractorEvaluationContentList || [];

        // 处理评价方式：如果后端返回名称，需要转换为字典值
        this.form.evaluationMethod = this.convertMethodNameToValue(
          response.data.evaluationMethodName,
          response.data.evaluationMethod
        );

        // 处理评价规则：如果后端返回名称，需要转换为字典值
        this.form.evaluationRules = this.convertRulesNameToValue(
          response.data.evaluationRulesName,
          response.data.evaluationRules
        );

        // 确保评价结论为字符串类型
        this.form.evaluationConclusion = String(response.data.evaluationConclusion || "");

        // 根据评价方式加载对应的模板选项
        this.loadTemplateOptions(this.form.evaluationMethod);
        this.loadContractorOptions(); // 加载承包商选项

        this.open = true;
        // 根据评价方式设置标题
        if (this.form.evaluationMethod === "1") {
          this.title = "修改项目评价";
        } else if (this.form.evaluationMethod === "2") {
          this.title = "修改周期性评价";
        } else {
          this.title = "修改评价记录";
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 特殊验证：项目评价时必须填写项目名称
          if (
            this.form.evaluationMethod === "1" &&
            (!this.form.projectName || this.form.projectName.trim() === "")
          ) {
            this.$modal.msgError("项目评价必须填写项目名称");
            return;
          }

          // 验证评价内容是否完整
          if (!this.validateEvaluationContent()) {
            return;
          }

          this.form.contractorEvaluationContentList =
            this.contractorEvaluationContentList;
          if (this.form.id != null) {
            updateZjContractorEvaluation(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjContractorEvaluation(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 验证评价内容 */
    validateEvaluationContent() {
      // 检查是否有评价内容
      if (
        !this.contractorEvaluationContentList ||
        !Array.isArray(this.contractorEvaluationContentList) ||
        this.contractorEvaluationContentList.length === 0
      ) {
        this.$modal.msgError("请先选择评价模板以加载评价内容");
        return false;
      }

      // 检查每个评价项目是否都有得分
      const incompleteItems = this.contractorEvaluationContentList.filter(
        (item) =>
          item.evaluationResults === null ||
          item.evaluationResults === undefined
      );

      if (incompleteItems.length > 0) {
        this.$modal.msgError("请为所有评价项目填写得分");
        return false;
      }

      // 检查得分是否超出范围
      const invalidScores = this.contractorEvaluationContentList.filter(
        (item) =>
          item.evaluationResults < 0 ||
          item.evaluationResults > item.projectScore
      );

      if (invalidScores.length > 0) {
        this.$modal.msgError("评价得分不能小于0分或超过项目分值");
        return false;
      }

      return true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除评价记录编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjContractorEvaluation(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 评价内容序号 */
    rowZjContractorEvaluationContentIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },

    /** 加载评价模板选项 */
    loadTemplateOptions(evaluationMethod = null) {
      const params = {};
      if (evaluationMethod) {
        params.evaluationMethod = evaluationMethod;
      }
      listZjEvaluationConfigInfo(params)
        .then((response) => {
          this.templateOptions = response.rows || [];
        })
        .catch(() => {
          this.templateOptions = [];
        });
    },


    /** 加载承包商选项 */
    loadContractorOptions() {
      getContractorInfo()
        .then((response) => {
          if (response.code === 200 && response.data) {
            this.contractorOptions = response.data.map((item) => ({
              id: item.id,
              contractorName: item.contractorName,
              value: item.id,
              label: item.contractorName,
            }));
          } else {
            this.contractorOptions = [];
          }
        })
        .catch(() => {
          this.contractorOptions = [];
          this.$modal.msgError("获取承包商列表失败");
        });
    },

    /** 处理承包商选择变化 */
    handleContractorChange(contractorId) {
      if (contractorId) {
        const selectedContractor = this.contractorOptions.find(
          (item) => item.id === contractorId
        );
        if (selectedContractor) {
          this.form.contractorName = selectedContractor.contractorName;
        }
      } else {
        this.form.contractorName = "";
      }
    },

    /** 处理评价模板选择变化 */
    handleTemplateChange(templateId) {
      if (!templateId) {
        this.contractorEvaluationContentList = [];
        return;
      }

      // 获取选中模板的详细信息
      getZjEvaluationConfigInfo(templateId)
        .then((response) => {
          if (
            response.data &&
            response.data.contentList &&
            response.data.contentList.length > 0
          ) {
            // 将模板的评价内容转换为评价记录的评价内容格式
            this.contractorEvaluationContentList =
              response.data.contentList.map((item, index) => ({
                id: null, // 新建时ID为null
                configContentId: item.id, // 关联配置内容ID
                evaluationProject: item.evaluationProject, // 评价项目
                gradingCriteria: item.gradingCriteria, // 评分标准
                projectScore: parseInt(item.projectScore) || 0, // 项目分值（转换为数字）
                evaluationResults: 0, // 项目得分（默认为0）
                index: index + 1, // 序号
              }));

            // 更新表单中的评价模板名称
            const selectedTemplate = this.templateOptions.find(
              (t) => t.id === templateId
            );
            if (selectedTemplate) {
              this.form.evaluationTemplateName =
                selectedTemplate.evaluationTemplateName;
            }

            // 自动根据得分率设置评价结论
            this.updateEvaluationConclusion();

            // 如果已设置评价周期，重新计算下一周期评价时间
            this.calculateNextEvaluationTime();
          } else {
            this.contractorEvaluationContentList = [];
            this.$modal.msgWarning("所选评价模板暂无评价内容");
          }
        })
        .catch(() => {
          this.contractorEvaluationContentList = [];
          this.$modal.msgError("加载评价模板内容失败");
        });
    },

    /** 处理分数变化 */
    handleScoreChange(row, index) {
      // 验证得分不能超过项目分值
      if (row.evaluationResults > row.projectScore) {
        row.evaluationResults = row.projectScore;
        this.$modal.msgWarning(`得分不能超过项目分值${row.projectScore}分`);
      }

      // 得分不能为负数
      if (row.evaluationResults < 0) {
        row.evaluationResults = 0;
      }

      // 更新评价结论
      this.updateEvaluationConclusion();
    },

    /** 更新评价结论 */
    updateEvaluationConclusion() {
      // 根据得分率自动设置评价结论
      if (this.scorePercentage >= 70) {
        this.form.evaluationConclusion = "1"; // 合格
      } else {
        this.form.evaluationConclusion = "2"; // 不合格
      }
    },

    /** 计算下一周期评价时间 */
    calculateNextEvaluationTime() {
      if (!this.form.evaluationCycle || !this.form.evaluationCycleUnit) {
        this.form.nextEvaluationTime = null;
        return;
      }

      const currentDate = new Date();
      const cycle = parseInt(this.form.evaluationCycle);

      let nextDate = new Date(currentDate);

      switch (this.form.evaluationCycleUnit) {
        case "year":
          nextDate.setFullYear(currentDate.getFullYear() + cycle);
          break;
        case "month":
          nextDate.setMonth(currentDate.getMonth() + cycle);
          break;
        case "day":
          nextDate.setDate(currentDate.getDate() + cycle);
          break;
        default:
          this.form.nextEvaluationTime = null;
          return;
      }

      // 格式化为 YYYY-MM-DD
      const year = nextDate.getFullYear();
      const month = String(nextDate.getMonth() + 1).padStart(2, "0");
      const day = String(nextDate.getDate()).padStart(2, "0");

      this.form.nextEvaluationTime = `${year}-${month}-${day}`;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "contractor/zjContractorEvaluation/export",
        {
          ...this.queryParams,
        },
        `承包商评价记录_${new Date().getTime()}.xlsx`
      );
    },
    // 评价配置相关方法
    /** 查询评价配置列表 */
    getConfigList() {
      this.configLoading = true;
      listZjEvaluationConfigInfo(this.configQueryParams).then((response) => {
        this.zjEvaluationConfigInfoList = response.rows;
        this.configTotal = response.total;
        this.configLoading = false;
      });
    },
    /** 评价配置搜索按钮操作 */
    handleConfigQuery() {
      this.configQueryParams.pageNum = 1;
      this.getConfigList();
    },
    /** 评价配置重置按钮操作 */
    resetConfigQuery() {
      this.resetForm("configQueryForm");
      this.handleConfigQuery();
    },
    /** 评价配置多选框选中数据 */
    handleConfigSelectionChange(selection) {
      this.configIds = selection.map((item) => item.id);
      this.configSingle = selection.length !== 1;
      this.configMultiple = !selection.length;
    },
    /** 评价配置新增按钮操作 */
    handleConfigAdd() {
      this.resetConfig();
      this.configOpen = true;
      this.configTitle = "添加评价配置";
    },
    /** 评价配置修改按钮操作 */
    handleConfigUpdate(row) {
      this.resetConfig();
      const id = row.id || this.configIds;
      getZjEvaluationConfigInfo(id).then((response) => {
        this.configForm = response.data;

        // 加载评价内容数据
        if (response.data.contentList) {
          this.configContentList = response.data.contentList.map((item) => ({
            ...item,
            isNew: false,
            isSaved: true,
          }));
          this.originalConfigContentList = JSON.parse(
            JSON.stringify(this.configContentList)
          );
        }

        this.configOpen = true;
        this.configTitle = "修改评价配置";
      });
    },
    /** 评价配置提交按钮 */
    submitConfigForm() {
      this.$refs["configForm"].validate((valid) => {
        if (valid) {
          // 验证总分是否为100
          if (this.totalScore !== 100) {
            this.$modal.msgError("项目评价分值总和必须为100分，请调整分值");
            return;
          }

          // 验证是否有评价内容
          if (this.configContentList.length === 0) {
            this.$modal.msgError("请添加至少一个评价项目");
            return;
          }

          // 验证每个评价项目是否完整
          const incompleteItems = this.configContentList.filter(
            (item) =>
              !item.evaluationProject ||
              !item.gradingCriteria ||
              !item.projectScore
          );
          if (incompleteItems.length > 0) {
            this.$modal.msgError("请完善所有评价项目的信息");
            return;
          }

          // 准备提交数据
          const submitData = {
            ...this.configForm,
            contentList: this.configContentList.map((item) => ({
              id: item.id,
              evaluationProject: item.evaluationProject,
              gradingCriteria: item.gradingCriteria,
              projectScore: item.projectScore,
              configId: this.configForm.id,
            })),
          };

          if (this.configForm.id != null) {
            updateZjEvaluationConfigInfo(submitData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.configOpen = false;
              this.getConfigList();
            });
          } else {
            addZjEvaluationConfigInfo(submitData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.configOpen = false;
              this.getConfigList();
            });
          }
        }
      });
    },
    /** 评价配置删除按钮操作 */
    handleConfigDelete(row) {
      const ids = row.id || this.configIds;
      this.$modal
        .confirm('是否确认删除评价配置编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjEvaluationConfigInfo(ids);
        })
        .then(() => {
          this.getConfigList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 评价配置导出按钮操作 */
    handleConfigExport() {
      this.download(
        "contractor/zjEvaluationConfigInfo/export",
        {
          ...this.configQueryParams,
        },
        `zjEvaluationConfigInfo_${new Date().getTime()}.xlsx`
      );
    },
    /** 评价配置取消按钮 */
    cancelConfig() {
      this.configOpen = false;
      this.resetConfig();
    },
    /** 评价配置表单重置 */
    resetConfig() {
      this.configForm = {
        id: null,
        evaluationTemplateName: null,
        evaluationMethod: null,
        evaluationTemplateIntro: null,
        evaluationRules: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.configContentList = [];
      this.originalConfigContentList = [];
      this.checkedConfigContent = [];
      this.resetForm("configForm");
    },

    // 新增的卡片视图相关方法
    /** 搜索配置 */
    handleConfigSearch() {
      // 搜索逻辑已通过计算属性 filteredConfigList 实现
    },

    /** 按类型新增配置 */
    handleConfigAddByType(type) {
      this.resetConfig();

      // 根据类型预设评价方式和评价规则
      if (type === "project") {
        this.configForm.evaluationMethod = "1"; // 项目评价
        this.configForm.evaluationRules = "1"; // 打分制
        this.configTitle = "项目评价新增";
      } else if (type === "periodic") {
        this.configForm.evaluationMethod = "2"; // 周期性评价
        this.configForm.evaluationRules = "1"; // 打分制
        this.configTitle = "周期性评价新增";
      }

      this.configOpen = true;
    },

    /** 卡片点击事件 */
    handleConfigCardClick(item) {
      this.handleConfigUpdate(item);
    },

    /** 复制配置 */
    handleConfigCopy(item) {
      this.resetConfig();

      // 复制数据，但清除ID以创建新记录
      this.configForm = {
        ...item,
        id: null,
        evaluationTemplateName: item.evaluationTemplateName + "(副本)",
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };

      // 复制评价内容数据
      if (item.contentList) {
        this.configContentList = item.contentList.map((contentItem) => ({
          ...contentItem,
          id: null,
          configId: null,
          isNew: true,
          isSaved: true,
        }));
        this.originalConfigContentList = JSON.parse(
          JSON.stringify(this.configContentList)
        );
      }

      this.configTitle = "复制评价配置";
      this.configOpen = true;
    },

    /** 获取卡片图标 */
    getCardIcon(evaluationMethod) {
      if (evaluationMethod === "1") {
        return "el-icon-document"; // 项目评价
      } else if (evaluationMethod === "2") {
        return "el-icon-time"; // 周期性评价
      }
      return "el-icon-document";
    },

    /** 格式化日期 */
    formatDate(dateString) {
      if (!dateString) {
        return new Date().toISOString().slice(0, 10);
      }

      try {
        const date = new Date(dateString);
        return date.toISOString().slice(0, 10);
      } catch (e) {
        return new Date().toISOString().slice(0, 10);
      }
    },

    // 评价内容管理相关方法
    /** 评价配置内容序号 */
    rowConfigContentIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },

    /** 新增评价项目 */
    handleAddConfigContent() {
      const newItem = {
        id: null,
        evaluationProject: "",
        gradingCriteria: "",
        projectScore: null,
        configId: null,
        isNew: true, // 标记为新增项
      };
      this.configContentList.push(newItem);
    },

    /** 删除选中的评价项目 */
    handleDeleteConfigContent() {
      if (this.checkedConfigContent.length === 0) {
        this.$modal.msgError("请先选择要删除的评价项目");
        return;
      }

      const configContentList = this.configContentList;
      const checkedConfigContent = this.checkedConfigContent;
      this.configContentList = configContentList.filter(function (item) {
        return checkedConfigContent.indexOf(item.index) === -1;
      });

      this.checkedConfigContent = [];
      this.validateTotalScore();
    },

    /** 评价内容选择变化 */
    handleConfigContentSelectionChange(selection) {
      this.checkedConfigContent = selection.map((item) => item.index);
    },

    /** 保存单行评价内容 */
    saveConfigContentRow(row, index) {
      if (!row.evaluationProject || !row.gradingCriteria || !row.projectScore) {
        this.$modal.msgError("请完整填写评价项目、评分标准和项目分值");
        return;
      }

      // 标记该行已保存
      this.$set(row, "isSaved", true);
      this.$modal.msgSuccess("保存成功");
    },

    /** 取消单行编辑 */
    cancelConfigContentRow(row, index) {
      if (row.isNew && !row.isSaved) {
        // 如果是新增且未保存的项，直接删除
        this.configContentList.splice(index, 1);
      } else {
        // 如果是编辑现有项，恢复原始值
        const originalItem = this.originalConfigContentList.find(
          (item) => item.id === row.id
        );
        if (originalItem) {
          this.$set(this.configContentList, index, { ...originalItem });
        }
      }
      this.validateTotalScore();
    },

    /** 验证总分 */
    validateTotalScore() {
      // 触发计算属性重新计算
      this.$nextTick(() => {
        if (this.totalScore > 100) {
          this.$modal.msgWarning("总分不能超过100分，请调整项目分值");
        }
      });
    },

    // 数据转换辅助方法

    /** 将评价方式名称转换为字典值 */
    convertMethodNameToValue(methodName, originalValue) {
      // 如果已经有字典值，直接返回
      if (originalValue) return originalValue;

      // 如果没有字典值但有名称，则根据名称查找对应的字典值
      if (methodName && this.dict.type.contractore_valuation_method) {
        const methodDict = this.dict.type.contractore_valuation_method.find(
          (d) => d.label === methodName
        );
        return methodDict ? methodDict.value : "";
      }

      return "";
    },

    /** 将评价规则名称转换为字典值 */
    convertRulesNameToValue(rulesName, originalValue) {
      // 如果已经有字典值，直接返回
      if (originalValue) return originalValue;

      // 如果没有字典值但有名称，则根据名称查找对应的字典值
      if (rulesName && this.dict.type.contractor_evaluation_rules) {
        const rulesDict = this.dict.type.contractor_evaluation_rules.find(
          (d) => d.label === rulesName
        );
        return rulesDict ? rulesDict.value : "";
      }

      return "";
    },

    /** 获取评价结论的标签类型 */
    getEvaluationConclusionType(conclusion) {
      const conclusionStr = String(conclusion || "").trim();
      switch (conclusionStr) {
        case "1":
          return "success";
        case "2":
          return "danger";
        default:
          return "info";
      }
    },

    /** 获取评价结论的显示文本 */
    getEvaluationConclusionText(conclusion) {
      const conclusionStr = String(conclusion || "").trim();
      switch (conclusionStr) {
        case "1":
          return "合格";
        case "2":
          return "不合格";
        default:
          return conclusionStr || "未设置";
      }
    },
  },
};
</script>

<style scoped>
/* 整体容器样式 */
.app-container {
  padding: 20px;
}

/* 标签页样式 */
.contractor-tabs {
  background: #fff;
  border-radius: 4px;
}

.contractor-tabs ::v-deep .el-tabs__header {
  margin: 0;
  padding: 0 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  border-radius: 5px 5px 0 0;
}

.contractor-tabs ::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

.contractor-tabs ::v-deep .el-tabs__item {
  font-size: 14px;
  font-weight: 500;
  height: 50px;
  line-height: 50px;
  color: #606266;
}

.contractor-tabs ::v-deep .el-tabs__item.is-active {
  color: #409eff;
}

.contractor-tabs ::v-deep .el-tabs__content {
  padding: 20px;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 10px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

/* 工具栏样式 */
.toolbar-section {
  margin-bottom: 8px;
}

.mb8 {
  margin-bottom: 8px;
}

/* 评价配置卡片样式 */
.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.search-controls {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.config-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  min-height: 200px;
  margin-bottom: 20px;
}

.config-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.card-icon-img {
  font-size: 20px;
  color: white;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
  word-break: break-word;
}

.card-date {
  font-size: 14px;
  color: #909399;
  margin-bottom: 16px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-tag {
  flex: 1;
}

.card-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.action-icon {
  font-size: 16px;
  color: #909399;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s ease;
}

.action-icon:hover {
  color: #409eff;
}

.delete-icon:hover {
  color: #f56c6c;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
  margin-bottom: 20px;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  margin-bottom: 20px;
}


/* 评价内容样式 */
.evaluation-content-section {
  margin-top: 20px;
}

.table-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.total-score-info {
  font-size: 14px;
  color: #606266;
}

.total-score-info span {
  font-weight: 600;
  color: #409eff;
}

.total-score-info .score-error {
  color: #f56c6c !important;
}

.score-tip {
  color: #e6a23c;
  margin-left: 8px;
}

.evaluation-content-table {
  margin-bottom: 16px;
}

.evaluation-content-table ::v-deep .el-table__body-wrapper {
  max-height: 400px;
  overflow-y: auto;
}

.evaluation-content-table ::v-deep .el-input__inner {
  border: 1px solid #dcdfe6;
  transition: border-color 0.2s ease;
}

.evaluation-content-table ::v-deep .el-input__inner:focus {
  border-color: #409eff;
}

.evaluation-content-table ::v-deep .el-input-number .el-input__inner {
  text-align: center;
}

.table-actions {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}

.add-evaluation-btn {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
  color: white;
}

.add-evaluation-btn:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #85ce61 100%);
}

/* 对话框样式调整 */
.el-dialog__wrapper .el-dialog {
  margin-top: 3vh !important;
  max-width: 95vw;
  min-width: 1200px;
}

.el-dialog__body {
  padding: 20px !important;
  max-height: 85vh;
  overflow-y: auto;
}

.dialog-footer {
  text-align: center;
  padding: 20px 0 10px;
}

/* 表单样式调整 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-select.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 评价表格样式 */
.evaluation-table {
  margin-top: 16px;
}

.evaluation-table ::v-deep .el-table__header {
  background-color: #f5f7fa;
}

.evaluation-table ::v-deep .el-table__header th {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
}

.evaluation-table ::v-deep .el-table__body tr:hover > td {
  background-color: #f5f7fa;
}

/* 模板提示样式 */
.template-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  margin-top: 16px;
}

.template-tip i {
  font-size: 18px;
  margin-right: 8px;
  color: #c0c4cc;
}

.template-tip span {
  font-size: 14px;
}

/* 评价周期样式 */
.evaluation-cycle-form {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin: 12px 0;
}

.evaluation-cycle-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.evaluation-cycle-title::before {
  content: "";
  width: 4px;
  height: 16px;
  background: #409eff;
  border-radius: 2px;
  margin-right: 8px;
}

.evaluation-cycle-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  gap: 12px;
  align-items: end;
}

.cycle-input-wrapper {
  display: flex;
  flex-direction: column;
}

.cycle-input-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.next-time-display {
  background: #e8f4fd;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  padding: 8px 12px;
  font-family: "Courier New", monospace;
  font-size: 14px;
  color: #409eff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .app-container {
    padding: 16px;
  }

  .contractor-tabs ::v-deep .el-tabs__content {
    padding: 16px;
  }

  .config-cards {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 14px;
    padding: 18px;
  }

  .config-card {
    padding: 18px;
  }

  .el-dialog__wrapper .el-dialog {
    min-width: 95vw !important;
    margin: 2vh auto !important;
  }

  .evaluation-content-table ::v-deep .el-table__body-wrapper {
    max-height: 350px;
  }

  .evaluation-content-table ::v-deep .el-table__header-wrapper,
  .evaluation-content-table ::v-deep .el-table__body-wrapper {
    overflow-x: auto;
  }
}

/* 中等屏幕优化 */
@media (max-width: 1024px) {
  .app-container {
    padding: 14px;
  }

  .search-form,
  .toolbar-section,
  .config-header {
    padding: 14px 18px;
    margin-bottom: 14px;
  }

  .config-cards {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
    padding: 16px;
  }

  .pagination-container {
    padding: 12px 16px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 12px;
    height: calc(100vh - 60px);
  }

  .contractor-tabs ::v-deep .el-tabs__content {
    padding: 12px;
  }

  .search-form,
  .toolbar-section,
  .config-header {
    padding: 12px 16px;
    margin-bottom: 12px;
  }

  .table-container,
  .pagination-container {
    margin-bottom: 12px;
  }

  .config-cards {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 12px;
  }

  .config-card {
    padding: 16px;
  }

  .el-dialog__wrapper .el-dialog {
    width: 98vw !important;
    min-width: auto !important;
    margin: 1vh auto !important;
  }

  .el-dialog__body {
    padding: 15px !important;
    max-height: 90vh;
  }

  .evaluation-content-table {
    font-size: 12px;
  }

  .evaluation-content-table ::v-deep .el-table__body-wrapper {
    max-height: 250px;
  }

  .table-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
