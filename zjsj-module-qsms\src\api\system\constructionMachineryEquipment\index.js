import request from '@/utils/request'

// 查询在建项目安管人员及施工机械设备使用情况列表
export function listEquipment(query) {
  return request({
    url: '/system/equipment/list',
    method: 'get',
    params: query
  })
}

// 查询在建项目安管人员及施工机械设备使用情况详细
export function getEquipment(id) {
  return request({
    url: '/system/equipment/' + id,
    method: 'get'
  })
}

// 新增在建项目安管人员及施工机械设备使用情况
export function addEquipment(data) {
  return request({
    url: '/system/equipment',
    method: 'post',
    data: data
  })
}

// 修改在建项目安管人员及施工机械设备使用情况
export function updateEquipment(data) {
  return request({
    url: '/system/equipment',
    method: 'put',
    data: data
  })
}

// 删除在建项目安管人员及施工机械设备使用情况
export function delEquipment(id) {
  return request({
    url: '/system/equipment/' + id,
    method: 'delete'
  })
}
