<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="统计周期" prop="statPeriod">
        <el-input
          v-model="queryParams.statPeriod"
          placeholder="请输入统计周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计范围" prop="statScope">
        <el-input
          v-model="queryParams.statScope"
          placeholder="请输入统计范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="特别重大事故起数" prop="特别重大事故起数">
        <el-input
          v-model="queryParams.特别重大事故起数"
          placeholder="请输入特别重大事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="特别重大事故死亡人数" prop="特别重大事故死亡人数">
        <el-input
          v-model="queryParams.特别重大事故死亡人数"
          placeholder="请输入特别重大事故死亡人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="特别重大事故重伤人数" prop="特别重大事故重伤人数">
        <el-input
          v-model="queryParams.特别重大事故重伤人数"
          placeholder="请输入特别重大事故重伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="特别重大事故直接经济损失"
        prop="特别重大事故直接经济损失"
      >
        <el-input
          v-model="queryParams.特别重大事故直接经济损失"
          placeholder="请输入特别重大事故直接经济损失"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重大事故起数" prop="重大事故起数">
        <el-input
          v-model="queryParams.重大事故起数"
          placeholder="请输入重大事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重大事故死亡人数" prop="重大事故死亡人数">
        <el-input
          v-model="queryParams.重大事故死亡人数"
          placeholder="请输入重大事故死亡人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重大事故重伤人数" prop="重大事故重伤人数">
        <el-input
          v-model="queryParams.重大事故重伤人数"
          placeholder="请输入重大事故重伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重大事故直接经济损失" prop="重大事故直接经济损失">
        <el-input
          v-model="queryParams.重大事故直接经济损失"
          placeholder="请输入重大事故直接经济损失"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="较大事故起数" prop="较大事故起数">
        <el-input
          v-model="queryParams.较大事故起数"
          placeholder="请输入较大事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="较大事故死亡人数" prop="较大事故死亡人数">
        <el-input
          v-model="queryParams.较大事故死亡人数"
          placeholder="请输入较大事故死亡人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="较大事故重伤人数" prop="较大事故重伤人数">
        <el-input
          v-model="queryParams.较大事故重伤人数"
          placeholder="请输入较大事故重伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="较大事故直接经济损失" prop="较大事故直接经济损失">
        <el-input
          v-model="queryParams.较大事故直接经济损失"
          placeholder="请输入较大事故直接经济损失"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一般事故起数" prop="一般事故起数">
        <el-input
          v-model="queryParams.一般事故起数"
          placeholder="请输入一般事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一般事故死亡人数" prop="一般事故死亡人数">
        <el-input
          v-model="queryParams.一般事故死亡人数"
          placeholder="请输入一般事故死亡人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一般事故重伤人数" prop="一般事故重伤人数">
        <el-input
          v-model="queryParams.一般事故重伤人数"
          placeholder="请输入一般事故重伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一般事故直接经济损失" prop="一般事故直接经济损失">
        <el-input
          v-model="queryParams.一般事故直接经济损失"
          placeholder="请输入一般事故直接经济损失"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故总起数" prop="totalAccidentCount">
        <el-input
          v-model="queryParams.totalAccidentCount"
          placeholder="请输入事故总起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="死亡总人数" prop="totalDeathCount">
        <el-input
          v-model="queryParams.totalDeathCount"
          placeholder="请输入死亡总人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重伤总人数" prop="totalInjuryCount">
        <el-input
          v-model="queryParams.totalInjuryCount"
          placeholder="请输入重伤总人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="直接经济损失总计" prop="totalDirectLoss">
        <el-input
          v-model="queryParams.totalDirectLoss"
          placeholder="请输入直接经济损失总计"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="高处坠落事故起数" prop="高处坠落事故起数">
        <el-input
          v-model="queryParams.高处坠落事故起数"
          placeholder="请输入高处坠落事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物体打击事故起数" prop="物体打击事故起数">
        <el-input
          v-model="queryParams.物体打击事故起数"
          placeholder="请输入物体打击事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="坍塌事故起数" prop="坍塌事故起数">
        <el-input
          v-model="queryParams.坍塌事故起数"
          placeholder="请输入坍塌事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="机械伤害事故起数" prop="机械伤害事故起数">
        <el-input
          v-model="queryParams.机械伤害事故起数"
          placeholder="请输入机械伤害事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="触电事故起数" prop="触电事故起数">
        <el-input
          v-model="queryParams.触电事故起数"
          placeholder="请输入触电事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="其他类型事故起数" prop="其他类型事故起数">
        <el-input
          v-model="queryParams.其他类型事故起数"
          placeholder="请输入其他类型事故起数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计编制人" prop="compiler">
        <el-input
          v-model="queryParams.compiler"
          placeholder="请输入统计编制人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计审核人" prop="reviewer">
        <el-input
          v-model="queryParams.reviewer"
          placeholder="请输入统计审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计审批人" prop="approver">
        <el-input
          v-model="queryParams.approver"
          placeholder="请输入统计审批人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制时间" prop="compileTime">
        <el-date-picker
          clearable
          v-model="queryParams.compileTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择编制时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审批时间" prop="approveTime">
        <el-date-picker
          clearable
          v-model="queryParams.approveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审批时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:statistics:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:statistics:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:statistics:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:statistics:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="statisticsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="统计记录ID" align="center" prop="id" /> -->
      <el-table-column label="统计周期" align="center" prop="statPeriod" />
      <!-- 月度/季度/年度/专项 -->
      <el-table-column label="统计类型" align="center" prop="statType" />
      <el-table-column label="统计范围" align="center" prop="statScope" />
      <el-table-column
        label="特别重大事故起数"
        align="center"
        prop="特别重大事故起数"
      />
      <!-- <el-table-column
        label="特别重大事故死亡人数"
        align="center"
        prop="特别重大事故死亡人数"
      />
      <el-table-column
        label="特别重大事故重伤人数"
        align="center"
        prop="特别重大事故重伤人数"
      />
      <el-table-column
        label="特别重大事故直接经济损失"
        align="center"
        prop="特别重大事故直接经济损失"
      /> -->
      <el-table-column
        label="重大事故起数"
        align="center"
        prop="重大事故起数"
      />
      <!-- <el-table-column
        label="重大事故死亡人数"
        align="center"
        prop="重大事故死亡人数"
      />
      <el-table-column
        label="重大事故重伤人数"
        align="center"
        prop="重大事故重伤人数"
      />
      <el-table-column
        label="重大事故直接经济损失"
        align="center"
        prop="重大事故直接经济损失"
      /> -->
      <el-table-column
        label="较大事故起数"
        align="center"
        prop="较大事故起数"
      />
      <!-- <el-table-column
        label="较大事故死亡人数"
        align="center"
        prop="较大事故死亡人数"
      />
      <el-table-column
        label="较大事故重伤人数"
        align="center"
        prop="较大事故重伤人数"
      />
      <el-table-column
        label="较大事故直接经济损失"
        align="center"
        prop="较大事故直接经济损失"
      /> -->
      <el-table-column
        label="一般事故起数"
        align="center"
        prop="一般事故起数"
      />
      <el-table-column
        label="一般事故死亡人数"
        align="center"
        prop="一般事故死亡人数"
      />
      <el-table-column
        label="一般事故重伤人数"
        align="center"
        prop="一般事故重伤人数"
      />
      <el-table-column
        label="一般事故直接经济损失"
        align="center"
        prop="一般事故直接经济损失"
      />
      <el-table-column
        label="事故总起数"
        align="center"
        prop="totalAccidentCount"
      />
      <el-table-column
        label="死亡总人数"
        align="center"
        prop="totalDeathCount"
      />
      <el-table-column
        label="重伤总人数"
        align="center"
        prop="totalInjuryCount"
      />
      <!-- <el-table-column
        label="直接经济损失总计"
        align="center"
        prop="totalDirectLoss"
      />
      <el-table-column
        label="高处坠落事故起数"
        align="center"
        prop="高处坠落事故起数"
      />
      <el-table-column
        label="物体打击事故起数"
        align="center"
        prop="物体打击事故起数"
      />
      <el-table-column
        label="坍塌事故起数"
        align="center"
        prop="坍塌事故起数"
      />
      <el-table-column
        label="机械伤害事故起数"
        align="center"
        prop="机械伤害事故起数"
      />
      <el-table-column
        label="触电事故起数"
        align="center"
        prop="触电事故起数"
      />
      <el-table-column
        label="其他类型事故起数"
        align="center"
        prop="其他类型事故起数"
      /> -->
      <!-- <el-table-column label="统计说明" align="center" prop="statDesc" />
      <el-table-column
        label="统计状态：草稿/审核中/已审核/已发布"
        align="center"
        prop="statStatus"
      />
      <el-table-column
        label="统计表文件地址"
        align="center"
        prop="statFileUrl"
      />
      <el-table-column label="统计编制人" align="center" prop="compiler" />
      <el-table-column label="统计审核人" align="center" prop="reviewer" />
      <el-table-column label="统计审批人" align="center" prop="approver" />
      <el-table-column
        label="编制时间"
        align="center"
        prop="compileTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.compileTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approveTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:statistics:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:statistics:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改伤亡事故统计（按周期、范围汇总事故及伤亡数据）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="统计周期" prop="statPeriod">
          <el-input v-model="form.statPeriod" placeholder="请输入统计周期" />
        </el-form-item>
        <el-form-item label="统计范围" prop="statScope">
          <el-input v-model="form.statScope" placeholder="请输入统计范围" />
        </el-form-item>
        <el-form-item label="特别重大事故起数" prop="特别重大事故起数">
          <el-input
            v-model="form.特别重大事故起数"
            placeholder="请输入特别重大事故起数"
          />
        </el-form-item>
        <el-form-item label="特别重大事故死亡人数" prop="特别重大事故死亡人数">
          <el-input
            v-model="form.特别重大事故死亡人数"
            placeholder="请输入特别重大事故死亡人数"
          />
        </el-form-item>
        <el-form-item label="特别重大事故重伤人数" prop="特别重大事故重伤人数">
          <el-input
            v-model="form.特别重大事故重伤人数"
            placeholder="请输入特别重大事故重伤人数"
          />
        </el-form-item>
        <el-form-item
          label="特别重大事故直接经济损失"
          prop="特别重大事故直接经济损失"
        >
          <el-input
            v-model="form.特别重大事故直接经济损失"
            placeholder="请输入特别重大事故直接经济损失"
          />
        </el-form-item>
        <el-form-item label="重大事故起数" prop="重大事故起数">
          <el-input
            v-model="form.重大事故起数"
            placeholder="请输入重大事故起数"
          />
        </el-form-item>
        <el-form-item label="重大事故死亡人数" prop="重大事故死亡人数">
          <el-input
            v-model="form.重大事故死亡人数"
            placeholder="请输入重大事故死亡人数"
          />
        </el-form-item>
        <el-form-item label="重大事故重伤人数" prop="重大事故重伤人数">
          <el-input
            v-model="form.重大事故重伤人数"
            placeholder="请输入重大事故重伤人数"
          />
        </el-form-item>
        <el-form-item label="重大事故直接经济损失" prop="重大事故直接经济损失">
          <el-input
            v-model="form.重大事故直接经济损失"
            placeholder="请输入重大事故直接经济损失"
          />
        </el-form-item>
        <el-form-item label="较大事故起数" prop="较大事故起数">
          <el-input
            v-model="form.较大事故起数"
            placeholder="请输入较大事故起数"
          />
        </el-form-item>
        <el-form-item label="较大事故死亡人数" prop="较大事故死亡人数">
          <el-input
            v-model="form.较大事故死亡人数"
            placeholder="请输入较大事故死亡人数"
          />
        </el-form-item>
        <el-form-item label="较大事故重伤人数" prop="较大事故重伤人数">
          <el-input
            v-model="form.较大事故重伤人数"
            placeholder="请输入较大事故重伤人数"
          />
        </el-form-item>
        <el-form-item label="较大事故直接经济损失" prop="较大事故直接经济损失">
          <el-input
            v-model="form.较大事故直接经济损失"
            placeholder="请输入较大事故直接经济损失"
          />
        </el-form-item>
        <el-form-item label="一般事故起数" prop="一般事故起数">
          <el-input
            v-model="form.一般事故起数"
            placeholder="请输入一般事故起数"
          />
        </el-form-item>
        <el-form-item label="一般事故死亡人数" prop="一般事故死亡人数">
          <el-input
            v-model="form.一般事故死亡人数"
            placeholder="请输入一般事故死亡人数"
          />
        </el-form-item>
        <el-form-item label="一般事故重伤人数" prop="一般事故重伤人数">
          <el-input
            v-model="form.一般事故重伤人数"
            placeholder="请输入一般事故重伤人数"
          />
        </el-form-item>
        <el-form-item label="一般事故直接经济损失" prop="一般事故直接经济损失">
          <el-input
            v-model="form.一般事故直接经济损失"
            placeholder="请输入一般事故直接经济损失"
          />
        </el-form-item>
        <el-form-item label="事故总起数" prop="totalAccidentCount">
          <el-input
            v-model="form.totalAccidentCount"
            placeholder="请输入事故总起数"
          />
        </el-form-item>
        <el-form-item label="死亡总人数" prop="totalDeathCount">
          <el-input
            v-model="form.totalDeathCount"
            placeholder="请输入死亡总人数"
          />
        </el-form-item>
        <el-form-item label="重伤总人数" prop="totalInjuryCount">
          <el-input
            v-model="form.totalInjuryCount"
            placeholder="请输入重伤总人数"
          />
        </el-form-item>
        <el-form-item label="直接经济损失总计" prop="totalDirectLoss">
          <el-input
            v-model="form.totalDirectLoss"
            placeholder="请输入直接经济损失总计"
          />
        </el-form-item>
        <el-form-item label="高处坠落事故起数" prop="高处坠落事故起数">
          <el-input
            v-model="form.高处坠落事故起数"
            placeholder="请输入高处坠落事故起数"
          />
        </el-form-item>
        <el-form-item label="物体打击事故起数" prop="物体打击事故起数">
          <el-input
            v-model="form.物体打击事故起数"
            placeholder="请输入物体打击事故起数"
          />
        </el-form-item>
        <el-form-item label="坍塌事故起数" prop="坍塌事故起数">
          <el-input
            v-model="form.坍塌事故起数"
            placeholder="请输入坍塌事故起数"
          />
        </el-form-item>
        <el-form-item label="机械伤害事故起数" prop="机械伤害事故起数">
          <el-input
            v-model="form.机械伤害事故起数"
            placeholder="请输入机械伤害事故起数"
          />
        </el-form-item>
        <el-form-item label="触电事故起数" prop="触电事故起数">
          <el-input
            v-model="form.触电事故起数"
            placeholder="请输入触电事故起数"
          />
        </el-form-item>
        <el-form-item label="其他类型事故起数" prop="其他类型事故起数">
          <el-input
            v-model="form.其他类型事故起数"
            placeholder="请输入其他类型事故起数"
          />
        </el-form-item>
        <el-form-item label="统计说明" prop="statDesc">
          <el-input
            v-model="form.statDesc"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="统计表文件地址" prop="statFileUrl">
          <el-input
            v-model="form.statFileUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="统计编制人" prop="compiler">
          <el-input v-model="form.compiler" placeholder="请输入统计编制人" />
        </el-form-item>
        <el-form-item label="统计审核人" prop="reviewer">
          <el-input v-model="form.reviewer" placeholder="请输入统计审核人" />
        </el-form-item>
        <el-form-item label="统计审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="请输入统计审批人" />
        </el-form-item>
        <el-form-item label="编制时间" prop="compileTime">
          <el-date-picker
            clearable
            v-model="form.compileTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择编制时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批时间" prop="approveTime">
          <el-date-picker
            clearable
            v-model="form.approveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审批时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listStatistics,
  getStatistics,
  delStatistics,
  addStatistics,
  updateStatistics,
} from "@/api/system/casualtyAccident/index";

export default {
  name: "Statistics",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 伤亡事故统计（按周期、范围汇总事故及伤亡数据）表格数据
      statisticsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        statPeriod: null,
        statType: null,
        statScope: null,
        特别重大事故起数: null,
        特别重大事故死亡人数: null,
        特别重大事故重伤人数: null,
        特别重大事故直接经济损失: null,
        重大事故起数: null,
        重大事故死亡人数: null,
        重大事故重伤人数: null,
        重大事故直接经济损失: null,
        较大事故起数: null,
        较大事故死亡人数: null,
        较大事故重伤人数: null,
        较大事故直接经济损失: null,
        一般事故起数: null,
        一般事故死亡人数: null,
        一般事故重伤人数: null,
        一般事故直接经济损失: null,
        totalAccidentCount: null,
        totalDeathCount: null,
        totalInjuryCount: null,
        totalDirectLoss: null,
        高处坠落事故起数: null,
        物体打击事故起数: null,
        坍塌事故起数: null,
        机械伤害事故起数: null,
        触电事故起数: null,
        其他类型事故起数: null,
        statDesc: null,
        statStatus: null,
        statFileUrl: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        statPeriod: [
          { required: true, message: "统计周期不能为空", trigger: "blur" },
        ],
        statType: [
          {
            required: true,
            message: "统计类型：月度/季度/年度/专项不能为空",
            trigger: "change",
          },
        ],
        statScope: [
          { required: true, message: "统计范围不能为空", trigger: "blur" },
        ],
        compiler: [
          { required: true, message: "统计编制人不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询伤亡事故统计（按周期、范围汇总事故及伤亡数据）列表 */
    getList() {
      this.loading = true;
      listStatistics(this.queryParams).then((response) => {
        this.statisticsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        statPeriod: null,
        statType: null,
        statScope: null,
        特别重大事故起数: null,
        特别重大事故死亡人数: null,
        特别重大事故重伤人数: null,
        特别重大事故直接经济损失: null,
        重大事故起数: null,
        重大事故死亡人数: null,
        重大事故重伤人数: null,
        重大事故直接经济损失: null,
        较大事故起数: null,
        较大事故死亡人数: null,
        较大事故重伤人数: null,
        较大事故直接经济损失: null,
        一般事故起数: null,
        一般事故死亡人数: null,
        一般事故重伤人数: null,
        一般事故直接经济损失: null,
        totalAccidentCount: null,
        totalDeathCount: null,
        totalInjuryCount: null,
        totalDirectLoss: null,
        高处坠落事故起数: null,
        物体打击事故起数: null,
        坍塌事故起数: null,
        机械伤害事故起数: null,
        触电事故起数: null,
        其他类型事故起数: null,
        statDesc: null,
        statStatus: null,
        statFileUrl: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加伤亡事故统计（按周期、范围汇总事故及伤亡数据）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getStatistics(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改伤亡事故统计（按周期、范围汇总事故及伤亡数据）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateStatistics(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStatistics(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除伤亡事故统计（按周期、范围汇总事故及伤亡数据）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delStatistics(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/statistics/export",
        {
          ...this.queryParams,
        },
        `statistics_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
