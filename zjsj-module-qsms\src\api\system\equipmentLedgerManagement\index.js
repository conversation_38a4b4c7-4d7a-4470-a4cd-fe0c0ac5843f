import request from '@/utils/request'

// 查询大型设备台账列表
export function listEquipment(query) {
  return request({
    url: '/system/largeEquipment/list',
    method: 'get',
    params: query
  })
}

// 查询大型设备台账详细
export function getEquipment(id) {
  return request({
    url: '/system/largeEquipment/' + id,
    method: 'get'
  })
}

// 新增大型设备台账
export function addEquipment(data) {
  return request({
    url: '/system/largeEquipment',
    method: 'post',
    data: data
  })
}

// 修改大型设备台账
export function updateEquipment(data) {
  return request({
    url: '/system/largeEquipment',
    method: 'put',
    data: data
  })
}

// 删除大型设备台账
export function delEquipment(id) {
  return request({
    url: '/system/largeEquipment/' + id,
    method: 'delete'
  })
}
