import request from '@/utils/request'

// 查询国外在建项目信息列表
export function listXmxx(query) {
  return request({
    url: '/inspection/xmxx/list',
    method: 'get',
    params: query
  })
}

// 查询国外在建项目信息详细
export function getXmxx(id) {
  return request({
    url: '/inspection/xmxx/' + id,
    method: 'get'
  })
}

// 新增国外在建项目信息
export function addXmxx(data) {
  return request({
    url: '/inspection/xmxx',
    method: 'post',
    data: data
  })
}

// 修改国外在建项目信息
export function updateXmxx(data) {
  return request({
    url: '/inspection/xmxx',
    method: 'put',
    data: data
  })
}

// 删除国外在建项目信息
export function delXmxx(id) {
  return request({
    url: '/inspection/xmxx/' + id,
    method: 'delete'
  })
}
