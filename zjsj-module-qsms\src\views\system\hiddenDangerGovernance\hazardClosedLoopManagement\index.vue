<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="隐患标题" prop="hazardTitle">
        <el-input
          v-model="queryParams.hazardTitle"
          placeholder="请输入隐患标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隐患等级" prop="hazardLevel">
        <el-select
          v-model="queryParams.hazardLevel"
          placeholder="请选择隐患等级"
          clearable
        >
          <el-option label="一般隐患" value="一般隐患" />
          <el-option label="重大隐患" value="重大隐患" />
          <el-option label="特别重大隐患" value="特别重大隐患" />
        </el-select>
      </el-form-item>
      <el-form-item label="闭环状态" prop="closedLoopStatus">
        <el-select
          v-model="queryParams.closedLoopStatus"
          placeholder="请选择闭环状态"
          clearable
        >
          <el-option label="待整改" value="待整改" />
          <el-option label="整改中" value="整改中" />
          <el-option label="待验收" value="待验收" />
          <el-option label="已闭环" value="已闭环" />
        </el-select>
      </el-form-item>
      <el-form-item label="发现时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-share"
          size="mini"
          @click="handleFlowChart"
          >流程图</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-setting"
          size="mini"
          :disabled="multiple"
          @click="handleBatchManage"
          >批量管理</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="hazardList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 250px)"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        prop="hazardTitle"
        label="隐患标题"
        :show-overflow-tooltip="true"
        width="200"
      />
      <el-table-column prop="hazardNumber" label="隐患编号" width="140" />
      <el-table-column
        prop="discoverTime"
        label="发现时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.discoverTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="hazardDescription"
        label="隐患描述"
        :show-overflow-tooltip="true"
        width="200"
      />
      <el-table-column
        prop="discoverer"
        label="发现人"
        align="center"
        width="100"
      />
      <el-table-column
        prop="responsiblePerson"
        label="整改负责人"
        align="center"
        width="100"
      />
      <el-table-column
        prop="rectificationDeadline"
        label="整改期限"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.rectificationDeadline) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="closedLoopStatus"
        label="状态"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="closedLoopStatusOptions"
            :value="scope.row.closedLoopStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-if="scope.row.closedLoopStatus !== '已闭环'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleProcess(scope.row)"
            >处理</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改隐患对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="隐患标题" prop="hazardTitle">
              <el-input
                v-model="form.hazardTitle"
                placeholder="请输入隐患标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隐患等级" prop="hazardLevel">
              <el-select
                v-model="form.hazardLevel"
                placeholder="请选择隐患等级"
                style="width: 100%"
              >
                <el-option label="一般隐患" value="一般隐患" />
                <el-option label="重大隐患" value="重大隐患" />
                <el-option label="特别重大隐患" value="特别重大隐患" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="隐患描述" prop="hazardDescription">
              <el-input
                v-model="form.hazardDescription"
                type="textarea"
                placeholder="请输入隐患描述"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="发现人" prop="discoverer">
              <el-input v-model="form.discoverer" placeholder="请输入发现人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发现时间" prop="discoverTime">
              <el-date-picker
                v-model="form.discoverTime"
                type="datetime"
                placeholder="选择发现时间"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="责任人" prop="responsiblePerson">
              <el-input
                v-model="form.responsiblePerson"
                placeholder="请输入责任人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改期限" prop="rectificationDeadline">
              <el-date-picker
                v-model="form.rectificationDeadline"
                type="datetime"
                placeholder="选择整改期限"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="整改要求" prop="rectificationRequirement">
              <el-input
                v-model="form.rectificationRequirement"
                type="textarea"
                placeholder="请输入整改要求"
                :rows="3"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="隐患位置" prop="hazardLocation">
              <el-input
                v-model="form.hazardLocation"
                placeholder="请输入隐患位置"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="闭环状态" prop="closedLoopStatus">
              <el-select
                v-model="form.closedLoopStatus"
                placeholder="请选择闭环状态"
                style="width: 100%"
              >
                <el-option label="待整改" value="待整改" />
                <el-option label="整改中" value="整改中" />
                <el-option label="待验收" value="待验收" />
                <el-option label="已闭环" value="已闭环" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="附件上传">
              <el-upload
                ref="upload"
                :action="uploadAction"
                :headers="uploadHeaders"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                multiple
                :limit="5"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              >
                <el-button size="small" type="primary" icon="el-icon-upload"
                  >上传隐患相关附件</el-button
                >
                <div slot="tip" class="el-upload__tip">
                  支持上传PDF/图片/Word文档，单文件不超过10MB，最多5个文件
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看隐患详情对话框 -->
    <el-dialog
      title="隐患详情"
      :visible.sync="viewOpen"
      width="900px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="隐患编号">{{
          viewForm.hazardNumber
        }}</el-descriptions-item>
        <el-descriptions-item label="隐患等级">
          <el-tag :type="getHazardLevelType(viewForm.hazardLevel)">{{
            viewForm.hazardLevel
          }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="隐患标题" :span="2">{{
          viewForm.hazardTitle
        }}</el-descriptions-item>
        <el-descriptions-item label="隐患描述" :span="2">{{
          viewForm.hazardDescription
        }}</el-descriptions-item>
        <el-descriptions-item label="发现人">{{
          viewForm.discoverer
        }}</el-descriptions-item>
        <el-descriptions-item label="发现时间">{{
          parseTime(viewForm.discoverTime)
        }}</el-descriptions-item>
        <el-descriptions-item label="责任人">{{
          viewForm.responsiblePerson
        }}</el-descriptions-item>
        <el-descriptions-item label="整改期限">{{
          parseTime(viewForm.rectificationDeadline)
        }}</el-descriptions-item>
        <el-descriptions-item label="隐患位置">{{
          viewForm.hazardLocation
        }}</el-descriptions-item>
        <el-descriptions-item label="闭环状态">
          <dict-tag
            :options="closedLoopStatusOptions"
            :value="viewForm.closedLoopStatus"
          />
        </el-descriptions-item>
        <el-descriptions-item label="整改要求" :span="2">{{
          viewForm.rectificationRequirement
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{
          parseTime(viewForm.createTime)
        }}</el-descriptions-item>
      </el-descriptions>

      <div
        v-if="viewForm.attachments && viewForm.attachments.length > 0"
        style="margin-top: 20px"
      >
        <h4>相关附件：</h4>
        <el-tag
          v-for="file in viewForm.attachments"
          :key="file.name"
          style="margin-right: 10px; margin-bottom: 5px; cursor: pointer"
          @click="downloadFile(file)"
        >
          <i class="el-icon-paperclip" /> {{ file.name }}
        </el-tag>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 隐患处理对话框 -->
    <el-dialog
      title="隐患处理"
      :visible.sync="processOpen"
      width="700px"
      append-to-body
    >
      <el-form
        ref="processForm"
        :model="processForm"
        :rules="processRules"
        label-width="120px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="处理类型" prop="processType">
              <el-radio-group v-model="processForm.processType">
                <el-radio label="整改">整改</el-radio>
                <el-radio label="验收">验收</el-radio>
                <el-radio label="闭环">闭环</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="处理描述" prop="processDescription">
              <el-input
                v-model="processForm.processDescription"
                type="textarea"
                placeholder="请输入处理描述"
                :rows="4"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="处理人" prop="processor">
              <el-input
                v-model="processForm.processor"
                placeholder="请输入处理人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理时间" prop="processTime">
              <el-date-picker
                v-model="processForm.processTime"
                type="datetime"
                placeholder="选择处理时间"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitProcessForm">确 定</el-button>
        <el-button @click="processOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "HazardClosedLoopManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患列表数据
      hazardList: [
        {
          id: 1,
          hazardNumber: "YH20250612001",
          hazardTitle: "机台防护罩缺失",
          hazardDescription: "生产车间3号机台防护罩缺失，存在安全隐患",
          hazardLevel: "重大隐患",
          discoverer: "李明",
          discoverTime: "2025-06-12 10:30:00",
          responsiblePerson: "张三",
          rectificationDeadline: "2025-06-15 18:00:00",
          rectificationRequirement: "立即停机，安装防护罩后方可恢复生产",
          hazardLocation: "生产车间A区3号机台",
          closedLoopStatus: "整改中",
          createTime: "2025-06-12 10:35:00",
          attachments: [{ name: "现场照片1.jpg", url: "/files/photo1.jpg" }],
        },
        {
          id: 2,
          hazardNumber: "YH20250612002",
          hazardTitle: "消防通道堵塞",
          hazardDescription: "二楼消防通道被杂物堵塞，影响紧急疏散",
          hazardLevel: "一般隐患",
          discoverer: "王五",
          discoverTime: "2025-06-12 14:20:00",
          responsiblePerson: "李四",
          rectificationDeadline: "2025-06-13 17:00:00",
          rectificationRequirement: "清理消防通道杂物，确保通道畅通",
          hazardLocation: "办公楼二楼东侧消防通道",
          closedLoopStatus: "待整改",
          createTime: "2025-06-12 14:25:00",
          attachments: [],
        },
        {
          id: 3,
          hazardNumber: "YH20250611001",
          hazardTitle: "电线老化",
          hazardDescription: "配电箱内部分电线出现老化现象，绝缘层破损",
          hazardLevel: "重大隐患",
          discoverer: "赵六",
          discoverTime: "2025-06-11 09:15:00",
          responsiblePerson: "钱七",
          rectificationDeadline: "2025-06-14 12:00:00",
          rectificationRequirement: "更换老化电线，重新进行绝缘测试",
          hazardLocation: "一号配电房主配电箱",
          closedLoopStatus: "已闭环",
          createTime: "2025-06-11 09:20:00",
          attachments: [
            { name: "电线检查报告.pdf", url: "/files/report1.pdf" },
            { name: "整改后照片.jpg", url: "/files/photo2.jpg" },
          ],
        },
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹窗
      viewOpen: false,
      // 是否显示处理弹窗
      processOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hazardTitle: undefined,
        hazardLevel: undefined,
        closedLoopStatus: undefined,
        dateRange: [],
      },
      // 表单参数
      form: {},
      // 查看表单数据
      viewForm: {},
      // 处理表单数据
      processForm: {},
      // 闭环状态选项
      closedLoopStatusOptions: [
        { label: "待整改", value: "待整改" },
        { label: "整改中", value: "整改中" },
        { label: "待验收", value: "待验收" },
        { label: "已闭环", value: "已闭环" },
      ],
      // 文件列表
      fileList: [],
      // 上传地址
      uploadAction: process.env.VUE_APP_BASE_API + "/system/upload",
      // 上传请求头
      uploadHeaders: {
        Authorization: "Bearer " + this.$store.getters.token,
      },
      // 表单校验
      rules: {
        hazardTitle: [
          { required: true, message: "隐患标题不能为空", trigger: "blur" },
        ],
        hazardDescription: [
          { required: true, message: "隐患描述不能为空", trigger: "blur" },
        ],
        hazardLevel: [
          { required: true, message: "隐患等级不能为空", trigger: "change" },
        ],
        discoverer: [
          { required: true, message: "发现人不能为空", trigger: "blur" },
        ],
        discoverTime: [
          { required: true, message: "发现时间不能为空", trigger: "change" },
        ],
        responsiblePerson: [
          { required: true, message: "责任人不能为空", trigger: "blur" },
        ],
        rectificationDeadline: [
          { required: true, message: "整改期限不能为空", trigger: "change" },
        ],
        rectificationRequirement: [
          { required: true, message: "整改要求不能为空", trigger: "blur" },
        ],
        hazardLocation: [
          { required: true, message: "隐患位置不能为空", trigger: "blur" },
        ],
        closedLoopStatus: [
          { required: true, message: "闭环状态不能为空", trigger: "change" },
        ],
      },
      // 处理表单校验
      processRules: {
        processType: [
          { required: true, message: "处理类型不能为空", trigger: "change" },
        ],
        processDescription: [
          { required: true, message: "处理描述不能为空", trigger: "blur" },
        ],
        processor: [
          { required: true, message: "处理人不能为空", trigger: "blur" },
        ],
        processTime: [
          { required: true, message: "处理时间不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 时间格式化
    parseTime,
    /** 查询隐患列表 */
    getList() {
      this.loading = true;
      // 模拟数据加载
      setTimeout(() => {
        this.total = this.hazardList.length;
        this.loading = false;
      }, 500);
    },
    // 获取隐患等级标签类型
    getHazardLevelType(level) {
      const levelMap = {
        一般隐患: "",
        重大隐患: "warning",
        特别重大隐患: "danger",
      };
      return levelMap[level] || "";
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        hazardTitle: undefined,
        hazardDescription: undefined,
        hazardLevel: undefined,
        discoverer: undefined,
        discoverTime: undefined,
        responsiblePerson: undefined,
        rectificationDeadline: undefined,
        rectificationRequirement: undefined,
        hazardLocation: undefined,
        closedLoopStatus: "待整改",
        attachments: [],
      };
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增隐患";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const selectedRow =
        row || this.hazardList.find((item) => this.ids.includes(item.id));
      if (selectedRow) {
        this.form = { ...selectedRow };
        this.fileList = selectedRow.attachments || [];
      }
      this.open = true;
      this.title = "修改隐患";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = { ...row };
      this.viewOpen = true;
    },
    /** 处理按钮操作 */
    handleProcess(row) {
      this.processForm = {
        id: row.id,
        processType: "",
        processDescription: "",
        processor: "",
        processTime: "",
      };
      this.processOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.attachments = this.fileList;
          if (this.form.id != null) {
            // 修改的提交
            this.$modal.msgSuccess("修改成功");
          } else {
            // 新增的提交
            const newId =
              Math.max(...this.hazardList.map((item) => item.id)) + 1;
            this.form.id = newId;
            this.form.hazardNumber =
              "YH" +
              new Date().toISOString().slice(0, 10).replace(/-/g, "") +
              String(newId).padStart(3, "0");
            this.form.createTime = new Date()
              .toISOString()
              .slice(0, 19)
              .replace("T", " ");
            this.hazardList.unshift(this.form);
            this.$modal.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 提交处理表单 */
    submitProcessForm() {
      this.$refs["processForm"].validate((valid) => {
        if (valid) {
          // 根据处理类型更新隐患状态
          const statusMap = {
            整改: "整改中",
            验收: "待验收",
            闭环: "已闭环",
          };
          const hazard = this.hazardList.find(
            (item) => item.id === this.processForm.id
          );
          if (hazard) {
            hazard.closedLoopStatus = statusMap[this.processForm.processType];
          }
          this.$modal.msgSuccess("处理成功");
          this.processOpen = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      const titles = row
        ? [row.hazardTitle]
        : this.hazardList
            .filter((item) => ids.includes(item.id))
            .map((item) => item.hazardTitle);
      this.$modal
        .confirm('是否确认删除隐患"' + titles.join("、") + '"?')
        .then(() => {
          this.hazardList = this.hazardList.filter(
            (item) => !ids.includes(item.id)
          );
          this.$modal.msgSuccess("删除成功");
          this.getList();
        })
        .catch(() => {});
    },
    /** 流程图按钮操作 */
    handleFlowChart() {
      this.$modal.msgInfo("流程图功能待实现");
    },
    /** 批量管理按钮操作 */
    handleBatchManage() {
      this.$modal.msgInfo("批量管理功能待实现");
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.msgInfo("导出功能待实现");
    },
    /** 文件上传前检查 */
    beforeUpload(file) {
      const isValidType = [
        "application/pdf",
        "image/jpeg",
        "image/png",
        "image/jpg",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ].includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isValidType) {
        this.$modal.msgError("只能上传PDF、图片或Word文档!");
        return false;
      }
      if (!isLt10M) {
        this.$modal.msgError("上传文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    /** 文件上传成功 */
    handleUploadSuccess(response, file, fileList) {
      this.fileList = fileList;
      this.$modal.msgSuccess("文件上传成功");
    },
    /** 删除文件 */
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    /** 预览文件 */
    handlePreview(file) {
      window.open(file.url);
    },
    /** 下载文件 */
    downloadFile(file) {
      window.open(file.url);
    },
  },
};
</script>

<style scoped>
.el-upload__tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin: 0 10px;
  min-width: 80px;
}
</style>
