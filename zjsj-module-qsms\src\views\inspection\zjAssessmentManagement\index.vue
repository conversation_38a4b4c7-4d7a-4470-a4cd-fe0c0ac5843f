<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="考核标准文件" name="first">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="120px"
        >
          <el-form-item label="标准名称" prop="unitName">
            <el-input
              v-model="queryParams.unitName"
              placeholder="请输入单位名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="评定周期" prop="evaluationCycle">
            <el-input
              v-model="queryParams.evaluationCycle"
              placeholder="请输入评定周期"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="执行得分" prop="executionScore">
            <el-input
              v-model="queryParams.executionScore"
              placeholder="请输入执行得分"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="扣分说明" prop="deductionExplanation">
            <el-input
              v-model="queryParams.deductionExplanation"
              placeholder="请输入扣分说明"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="综合等级" prop="comprehensiveLevel">
            <el-input
              v-model="queryParams.comprehensiveLevel"
              placeholder="请输入综合等级"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item label="负责人" prop="assessorName">
            <el-input
              v-model="queryParams.assessorName"
              placeholder="请输入考核人"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="更新时间" prop="assessorTime">
            <el-date-picker
              clearable
              v-model="queryParams.assessorTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择更新时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjAssessmentManagement:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjAssessmentManagement:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjAssessmentManagement:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjAssessmentManagement:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjAssessmentManagementList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
          <el-table-column label="标准名称" align="center" prop="unitName" />
          <!-- <el-table-column
            label="评定周期"
            align="center"
            prop="evaluationCycle"
          />
          <el-table-column
            label="执行得分"
            align="center"
            prop="executionScore"
          />
          <el-table-column
            label="扣分说明"
            align="center"
            prop="deductionExplanation"
          />
          <el-table-column
            label="综合等级"
            align="center"
            prop="comprehensiveLevel"
          /> -->
          <el-table-column label="负责人" align="center" prop="assessorName" />
          <el-table-column
            label="更新时间"
            align="center"
            prop="assessorTime"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.assessorTime, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="附件" prop="fjUrl" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleSafetyTrainingRecords(scope.row.fjUrl)"
                >查看</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjAssessmentManagement:edit']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjAssessmentManagement:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="考核模板" name="second">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="120px"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入关键字"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="版本号" prop="regulationVersion">
            <el-input
              v-model="queryParams.regulationVersion"
              placeholder="请输入版本号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <!-- <el-form-item label="类型" prop="regulationType">
            <el-select style="width: 100%">
              <el-option label="全部" value="0"></el-option>
              <el-option label="规章" value="1"></el-option>
              <el-option label="文件" value="2"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="时间" prop="effectDate">
            <el-date-picker
              clearable
              v-model="queryParams.effectDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjRulesRegulations:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjRulesRegulations:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjRulesRegulations:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjRulesRegulations:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjAssessmentManagementList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="考核模板名称"
            align="center"
            prop="regulationName"
          />
          <el-table-column
            label="负责人"
            align="center"
            prop="regulationNumber"
          />
          <el-table-column label="更新时间" align="center" prop="releaseDate">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.releaseDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="检查项"
            align="center"
            prop="regulationType"
          />

          <!-- <el-table-column label="发布部门" align="center" prop="deptId" /> -->

          <!-- <el-table-column label="状态" align="center" prop="status" />
          <el-table-column
            label="有效期"
            align="center"
            prop="regulationValidity"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.regulationValidity, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="附件" prop="attachmentUrl"></el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >新增</el-button
              > -->
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:remove']"
                >废止</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>
    <!-- 添加或修改考核管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <div v-if="activeTab == 'first'">
          <el-form-item label="标准名称" prop="unitName">
            <el-input v-model="form.unitName" placeholder="请输入单位名称" />
          </el-form-item>
          <!-- <el-form-item label="评定周期" prop="evaluationCycle">
          <el-input
            v-model="form.evaluationCycle"
            placeholder="请输入评定周期"
          />
        </el-form-item>
        <el-form-item label="执行得分" prop="executionScore">
          <el-input
            v-model="form.executionScore"
            placeholder="请输入执行得分"
          />
        </el-form-item>
        <el-form-item label="扣分说明" prop="deductionExplanation">
          <el-input
            v-model="form.deductionExplanation"
            placeholder="请输入扣分说明"
          />
        </el-form-item>
        <el-form-item label="综合等级" prop="comprehensiveLevel">
          <el-input
            v-model="form.comprehensiveLevel"
            placeholder="请输入综合等级"
          />
        </el-form-item> -->
          <el-form-item label="负责人" prop="assessorName">
            <el-input v-model="form.assessorName" placeholder="请输入考核人" />
          </el-form-item>
          <el-form-item label="更新时间" prop="assessorTime">
            <el-date-picker
              clearable
              v-model="form.assessorTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择更新时间"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="附件上传">
            <file-upload v-model="form.fjUrl"></file-upload>
          </el-form-item>
        </div>
        <div class="" v-if="activeTab == 'second'">
          <el-form-item label="考核模板名称" prop="regulationName">
            <el-input
              v-model="form.regulationName"
              placeholder="请输入单位名称"
            />
          </el-form-item>
          <el-form-item label="负责人" prop="assessorName">
            <el-input v-model="form.assessorName" placeholder="请输入考核人" />
          </el-form-item>
          <el-form-item label="更新时间" prop="assessorTime">
            <el-date-picker
              clearable
              v-model="form.assessorTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择更新时间"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="检查项" prop="checkItems">
            <el-checkbox-group v-model="form.checkItems">
              <el-checkbox label="检查项1">检查项1</el-checkbox>
              <el-checkbox label="检查项2">检查项2</el-checkbox>
              <el-checkbox label="检查项3">检查项3</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjAssessmentManagement,
  getZjAssessmentManagement,
  delZjAssessmentManagement,
  addZjAssessmentManagement,
  updateZjAssessmentManagement,
} from "@/api/inspection/zjAssessmentManagement";

export default {
  name: "ZjAssessmentManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考核管理表格数据
      zjAssessmentManagementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        unitName: null,
        evaluationCycle: null,
        executionScore: null,
        deductionExplanation: null,
        comprehensiveLevel: null,
        assessorName: null,
        assessorTime: null,
      },
      // 表单参数
      form: {
        checkItems: [],
      },
      // 表单校验
      rules: {},
      activeTab: "first",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    /** 查询考核管理列表 */
    getList() {
      this.loading = true;
      listZjAssessmentManagement(this.queryParams).then((response) => {
        this.zjAssessmentManagementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        unitName: null,
        evaluationCycle: null,
        executionScore: null,
        deductionExplanation: null,
        comprehensiveLevel: null,
        assessorName: null,
        assessorTime: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        checkItems: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考核管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjAssessmentManagement(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改考核管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjAssessmentManagement(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjAssessmentManagement(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除考核管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjAssessmentManagement(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjAssessmentManagement/export",
        {
          ...this.queryParams,
        },
        `zjAssessmentManagement_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
