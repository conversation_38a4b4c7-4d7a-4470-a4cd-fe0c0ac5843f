<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">任务列表</div>
            <div class="top-right">
              <el-form
                ref="queryForm"
                size="small"
                :inline="true"
                label-width="80px"
              >
                <el-form-item label="任务单号">
                  <span>{{ taskDetail.projectNo }}</span>
                </el-form-item>
                <el-form-item label="负责人">
                  <span>{{ taskDetail.inspectorName }}</span>
                </el-form-item>
                <el-form-item label="检查时间">
                  <span>{{ taskDetail.inspectTime }}</span>
                </el-form-item>
                <el-form-item label="截止时间">
                  <span>{{ taskDetail.deadlineTime }}</span>
                </el-form-item>
                <el-form-item>
                  <el-button
                    size="mini"
                    type="primary"
                    v-if="roles.includes('street_admin')"
                    @click="issuedClick"
                    >下发</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            label-width="80px"
          >
            <el-form-item label="工程名称" prop="projectName">
              <el-input
                v-model="queryParams.projectName"
                placeholder="请输入工程名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="下发状态" prop="xiafaType">
              <el-select
                v-model="queryParams.xiafaType"
                placeholder="选择下发状态"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="dict in xiafaList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="工程编号" prop="projectNo">
              <el-input
                v-model="queryParams.projectNo"
                placeholder="请输入工程编号"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="检查状态" prop="inspectStatus">
              <el-select
                v-model="queryParams.inspectStatus"
                placeholder="选择检查状态"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="dict in dict.type.inspect_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >查询</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <el-table
            v-loading="loading"
            :data="taskDetail.detailList"
            ref="projectTable"
            @selection-change="listSelect"
          >
            <el-table-column type="selection" width="35"> </el-table-column>
            <el-table-column label="工程编号" align="center" prop="projectNo" />
            <el-table-column
              label="工程名称"
              align="center"
              prop="projectName"
            />
            <el-table-column
              label="工程地址"
              align="center"
              prop="projectAddress"
            />
            <!-- <el-table-column label="工程类型" align="center" prop="projectType" /> -->
            <el-table-column
              label="问题整改状态"
              align="center"
              prop="registerStatus"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.register_status"
                  :value="scope.row.registerStatus"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="检查状态"
              align="center"
              prop="inspectStatus"
            >
              <template slot-scope="scope">
                <!-- <dict-tag :options="dict.type.inspect_status" :value="scope.row.inspectStatus" /> -->
                <dict-tag
                  :options="
                    scope.row.isTransfer == 1
                      ? computedInspectStatus
                      : dict.type.inspect_status
                  "
                  :value="scope.row.inspectStatus"
                />
              </template>
            </el-table-column>
            <el-table-column label="下发时间" align="center" prop="xiafaTime">
              <template slot-scope="scope">
                <span>{{ scope.row.xiafaTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="下发状态" align="center" prop="xiafaType">
              <template slot-scope="scope">
                <span>{{
                  scope.row.xiafaType == 1
                    ? "已下发"
                    : scope.row.xiafaType == 0
                    ? "未下发"
                    : ""
                }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="评分" align="center" prop="evaluationScore" >
                            <template slot-scope="scope">
                                <el-rate v-model="scope.row.evaluationScore" disabled></el-rate>
                            </template>
                        </el-table-column> -->
            <el-table-column
              label="操作"
              align="center"
              width="200"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleCheck(scope.row)"
                  >查看</el-button
                >
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleShowTrack(scope.row)"
                  >巡查轨迹</el-button
                >
                <!-- v-if="scope.row.registerStatus == 2 && (scope.row.inspectStatus == 3 || scope.row.inspectStatus == 4)" -->
                <!-- <el-button size="mini" type="primary" @click="showEvaluationDialog(scope.row)"
                                    style="margin-top: 10px;">评价</el-button> 
                                <el-button size="mini" type="primary"
                                    @click="showEvaluationHistory(scope.row)"
                                    style="margin-left: 10px; margin-top: 10px;">评价历史</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <!-- 下发弹窗 -->
    <el-dialog
      title=""
      :visible.sync="isIssued"
      width="30%"
      :close-on-click-modal="false"
    >
      <div class="issued-container">
        <div class="issued-content">
          <p>下发人：</p>
          <el-select v-model="issuedSelect" multiple placeholder="请选择">
            <el-option
              v-for="item in issuedList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </div>
        <div class="btn-group">
          <el-button @click="isIssued = false">取 消</el-button>
          <el-button
            type="primary"
            :loading="evaluationLoading"
            @click="handleIssued"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="检查详情"
      :visible.sync="detailDialogVisible"
      width="60%"
      :close-on-click-modal="false"
    >
      <div class="detail-content">
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="工程编号">{{
              currentDetail.projectId
            }}</el-descriptions-item>
            <el-descriptions-item label="工程名称">{{
              currentDetail.projectName
            }}</el-descriptions-item>
            <el-descriptions-item label="工程地址">{{
              currentDetail.projectAddress
            }}</el-descriptions-item>
            <!-- <el-descriptions-item label="工程类型">{{ currentDetail.projectType }}</el-descriptions-item> -->
            <!-- <el-descriptions-item label="问题整改状态"><dict-tag :options="dict.type.register_status"
                                :value="currentDetail.registerStatus" /></el-descriptions-item> -->
            <el-descriptions-item label="检查状态">
              <dict-tag
                :options="
                  currentDetail.isTransfer == 1
                    ? computedInspectStatus
                    : dict.type.inspect_status
                "
                :value="currentDetail.inspectStatus"
              />
            </el-descriptions-item>
            <el-descriptions-item label="下发时间">{{
              currentDetail.xiafaTime
            }}</el-descriptions-item>
            <!-- <el-descriptions-item label="问题大类">{{ currentDetail.problemCategory }}</el-descriptions-item> -->
            <el-descriptions-item label="检查人">{{
              currentDetail.inspectorNickName
            }}</el-descriptions-item>
            <el-descriptions-item label="巡检得分">{{
              currentDetail.taskScore
            }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 巡查结果 -->
        <div
          class="detail-section"
          v-if="
            currentDetail.questionContent &&
            currentDetail.questionContent.length > 0
          "
        >
          <h3>巡查结果</h3>
          <div
            v-for="(inspection, index) in currentDetail.questionContent"
            :key="inspection.inspectionId"
            class="inspection-item"
          >
            <div class="inspection-header">
              <div class="inspection-index">{{ index + 1 }}</div>
              <div class="inspection-title">
                {{ inspection.inspectionContent }}
              </div>
            </div>
            <!-- 事件问题列表 -->
            <div
              class="problem-category"
              v-if="
                inspection.eventProblems && inspection.eventProblems.length > 0
              "
            >
              <div class="category-title">
                <i class="el-icon-warning-outline"></i>
                事件问题
              </div>
              <div class="problem-list">
                <div
                  v-for="(event, eventIndex) in inspection.eventProblems"
                  :key="event.subclassId + '-event-' + eventIndex"
                  class="problem-item-detail"
                >
                  <div class="problem-content-wrapper">
                    <span class="problem-description">{{
                      event.problemDescription
                    }}</span>
                    <div class="problem-tags">
                      <el-tag
                        v-if="event.isEngineering"
                        type="warning"
                        size="mini"
                        >工程措施</el-tag
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 部件问题列表 -->
            <div
              class="problem-category"
              v-if="
                inspection.componentProblems &&
                inspection.componentProblems.length > 0
              "
            >
              <div class="category-title">
                <i class="el-icon-setting"></i>
                部件问题
              </div>
              <div class="problem-list">
                <div
                  v-for="(
                    componentGroup, groupIndex
                  ) in inspection.componentProblems"
                  :key="componentGroup.subclassId + '-group-' + groupIndex"
                  class="component-group"
                >
                  <!-- 问题描述 -->
                  <div
                    class="component-group-title"
                    v-if="componentGroup.problemDescription"
                  >
                    {{ componentGroup.problemDescription }}
                  </div>

                  <!-- 具体部件问题列表 -->
                  <div
                    v-if="
                      componentGroup.children &&
                      componentGroup.children.length > 0
                    "
                    class="component-children"
                  >
                    <div
                      v-for="(
                        component, componentIndex
                      ) in componentGroup.children"
                      :key="
                        component.subclassId + '-component-' + componentIndex
                      "
                      class="problem-item-detail"
                    >
                      <div class="component-info">
                        <div class="component-main">
                          <span class="component-name">{{
                            component.specificComponents
                          }}</span>
                          <span class="component-option">{{
                            component.option
                          }}</span>
                        </div>
                        <div class="component-stats">
                          <span class="problem-count"
                            >问题数量：{{ component.problemCount }}</span
                          >
                          <span
                            class="immediate-count"
                            v-if="component.immediateCount > 0"
                          >
                            即改即知：{{ component.immediateCount }}
                          </span>
                        </div>
                        <div class="problem-tags">
                          <el-tag v-if="component.isEngineering" type="warning"
                            >工程措施</el-tag
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 备注信息 -->
            <div class="inspection-remark" v-if="inspection.remark">
              <span class="remark-label">备注：</span>
              <span class="remark-text">{{ inspection.remark }}</span>
            </div>

            <!-- 检查项图片 -->
            <div
              class="inspection-images"
              v-if="inspection.imageList && inspection.imageList.length > 0"
            >
              <div class="images-title">检查图片</div>
              <media-preview :mediaList="inspection.imageList" alt="检查图片" />
            </div>
          </div>
        </div>

        <!-- 巡查结果 -->
        <div></div>

        <div class="detail-section" v-if="currentDetail.signatureImage">
          <h3>签字图片</h3>
          <media-preview
            :mediaList="[currentDetail.signatureImage]"
            alt="图片描述"
          />
        </div>

        <div
          class="detail-section"
          v-if="
            currentDetail.mediaList2 &&
            currentDetail.mediaList2.length > 0 &&
            currentDetail.inspectStatus !== '2'
          "
        >
          <h3>
            {{ currentDetail.isTransfer == "0" ? "整改" : "核查" }}图片和视频
          </h3>
          <div class="image-list">
            <media-preview
              :mediaList="currentDetail.mediaList2"
              alt="图片描述"
            />
          </div>
        </div>
        <div
          class="detail-section"
          v-if="
            currentDetail.verifyInformation &&
            currentDetail.inspectStatus !== '2'
          "
        >
          <h3>{{ currentDetail.isTransfer == "0" ? "整改" : "核查" }}信息</h3>
          <div class="image-list">
            {{ currentDetail.verifyInformation }}
          </div>
        </div>

        <div
          class="detail-section"
          v-if="
            currentDetail.returnReason && currentDetail.inspectStatus == '2'
          "
        >
          <h3>退回原因</h3>
          <div class="image-list">
            {{ currentDetail.returnReason }}
          </div>
        </div>
      </div>

      <!-- 底部按钮区域 -->
      <div
        slot="footer"
        class="dialog-footer"
        v-if="
          currentDetail.inspectStatus === '1' ||
          currentDetail.inspectStatus === '5'
        "
      >
        <el-button
          v-if="
            currentDetail.inspectStatus === '1' && roles.includes('qujiadmin')
          "
          type="primary"
          @click="transferClick"
          >转派</el-button
        >
        <!-- <el-button v-if="currentDetail.inspectStatus === '1' && isXiansuo" type="primary"
                    @click="handleConfirm">线索确认</el-button> -->
        <el-button
          v-if="currentDetail.inspectStatus === '1' && isXiansuo"
          type="primary"
          @click="showRegisterDialog"
          >问题整改</el-button
        >
        <!-- <el-button v-if="currentDetail.inspectStatus === '5'" @click="showBackDialog">退 回</el-button> -->
        <el-button
          v-if="currentDetail.inspectStatus === '5'"
          type="primary"
          @click="handleJieAn"
          >完结</el-button
        >
        <el-button @click="detailDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 转派弹窗 -->
    <el-dialog
      title=""
      :visible.sync="isTransfer"
      width="30%"
      :close-on-click-modal="false"
    >
      <div class="issued-container">
        <div class="issued-content">
          <p>转派人：</p>
          <el-select v-model="transferItem" placeholder="请选择">
            <el-option
              v-for="item in transferPeople"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            >
            </el-option>
          </el-select>
        </div>
        <div class="btn-group">
          <el-button @click="isIssued = false">取 消</el-button>
          <el-button
            type="primary"
            :loading="evaluationLoading"
            @click="handleTransfer"
            >转派</el-button
          >
        </div>
      </div>
    </el-dialog>
    <!-- 问题整改描述输入弹窗 -->
    <el-dialog
      title="问题整改"
      :visible.sync="registerDialogVisible"
      width="40%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="registerForm" ref="registerForm" :rules="registerRules">
        <el-form-item
          label="问题整改描述"
          prop="description"
          :label-width="'100px'"
        >
          <el-input
            type="textarea"
            v-model="registerForm.registerDescribe"
            :rows="4"
            placeholder="请输入问题整改描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="registerDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleRegister"
          :loading="registerLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 退回描述输入弹窗 -->
    <el-dialog
      title="退回"
      :visible.sync="backDialogVisible"
      width="40%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="backForm" ref="backForm" :rules="backRules">
        <el-form-item
          label="退回描述"
          prop="returnReason"
          :label-width="'100px'"
        >
          <el-input
            type="textarea"
            v-model="backForm.returnReason"
            :rows="4"
            placeholder="请输入问题整改描述"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="backDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleBack" :loading="backLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 添加地图弹窗 -->
    <el-dialog
      title="巡查轨迹"
      :visible.sync="mapDialogVisible"
      width="80%"
      append-to-body
    >
      <div id="container" style="height: 600px"></div>
    </el-dialog>
    <!-- 评价历史列表弹窗 -->
    <el-dialog
      title="评价历史"
      :visible.sync="evaluationHistoryVisible"
      width="80%"
      append-to-body
    >
      <el-table
        v-loading="evaluationHistoryLoading"
        :data="evaluationHistoryList"
        border
        stripe
      >
        <el-table-column label="评价人" align="center" prop="createName" />
        <el-table-column
          label="评价时间"
          align="center"
          prop="createTime"
          width="160"
        />
        <el-table-column
          label="安全问题评分"
          align="center"
          prop="securityIssueRating"
        >
          <template slot-scope="scope">
            <el-rate
              v-model="scope.row.securityIssueRating"
              disabled
              show-score
            />
          </template>
        </el-table-column>
        <el-table-column
          label="养护问题评分"
          align="center"
          prop="maintenanceIssuesRating"
        >
          <template slot-scope="scope">
            <el-rate
              v-model="scope.row.maintenanceIssuesRating"
              disabled
              show-score
            />
          </template>
        </el-table-column>
        <el-table-column
          label="环境问题评分"
          align="center"
          prop="environmentRating"
        >
          <template slot-scope="scope">
            <el-rate
              v-model="scope.row.environmentRating"
              disabled
              show-score
            />
          </template>
        </el-table-column>
        <el-table-column
          label="检查问题评分"
          align="center"
          prop="inspectRating"
        >
          <template slot-scope="scope">
            <el-rate v-model="scope.row.inspectRating" disabled show-score />
          </template>
        </el-table-column>
        <el-table-column
          label="综合评价"
          align="center"
          prop="comprehensiveRating"
        />
      </el-table>
      <pagination
        v-show="evaluationHistoryTotal > 0"
        :total="evaluationHistoryTotal"
        :page.sync="evaluationHistoryParams.pageNum"
        :limit.sync="evaluationHistoryParams.pageSize"
        @pagination="getEvaluationHistoryList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="evaluationHistoryVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 评价弹窗 -->
    <el-dialog
      title="工程评价"
      :visible.sync="evaluationDialogVisible"
      width="50%"
      append-to-body
    >
      <el-form
        :model="evaluationForm"
        ref="evaluationForm"
        :rules="evaluationRules"
        label-width="100px"
      >
        <el-form-item label="安全问题" prop="securityIssueRating">
          <el-rate
            v-model="evaluationForm.securityIssueRating"
            :max="5"
          ></el-rate>
        </el-form-item>
        <el-form-item label="养护问题" prop="maintenanceIssuesRating">
          <el-rate
            v-model="evaluationForm.maintenanceIssuesRating"
            :max="5"
          ></el-rate>
        </el-form-item>
        <el-form-item label="环境问题" prop="environmentRating">
          <el-rate
            v-model="evaluationForm.environmentRating"
            :max="5"
          ></el-rate>
        </el-form-item>
        <el-form-item label="检查问题" prop="inspectRating">
          <el-rate v-model="evaluationForm.inspectRating" :max="5"></el-rate>
        </el-form-item>
        <el-form-item label="综合评价" prop="comprehensiveRating">
          <el-input
            type="textarea"
            v-model="evaluationForm.comprehensiveRating"
            :rows="4"
            placeholder="请输入综合评价意见"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="evaluationDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="submitEvaluation"
          :loading="evaluationLoading"
          >提 交</el-button
        >
      </div>
    </el-dialog>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getInfo"
    />
  </div>
</template>

<script>
import {
  getJianChaTaskInfo,
  lian,
  selectUserByRoleKey,
  xiafa,
  getDetailInfo,
} from "@/api/inspection/jiancha";
import {
  listMfQualityEvaluation,
  addMfQualityEvaluation,
} from "@/api/inspection/mfQualityEvaluation";
import store from "@/store";
export default {
  name: "Checkdeinspectrecords",
  dicts: ["inspect_status", "register_status", "pz_street"],
  components: {
    MediaPreview: () => import("@/components/MediaPreview"),
    AudioPlayer: () => import("@/components/AudioPlayer"),
  },
  data() {
    return {
      params: {
        taskType: "日常检查",
      },
      // 编辑表单内容
      editForm: {},
      // 详情表单内容
      detailForm: {},
      // 显示详情表单
      showDetailForm: false,
      // 显示编辑表单
      showEditForm: false,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 5,
      // 项目检查表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        street: "",
        inspector: "",
        taskType: "",
        projectType: "",
        projectName: "",
        xiafaType: "",
        taskId: null,
        projectNo: "",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 提交按钮加载状态
      submitLoading: false,
      // 选中的项目
      selectedProjects: [],
      // 初始化时复制一份数据用于筛选
      originalList: [],
      // 详情弹窗显示控制
      detailDialogVisible: false,
      // 当前查看的详情数据，添加默认值
      currentDetail: {
        projectNo: "",
        projectName: "",
        projectAddress: "",
        projectType: "",
        caseStatus: "",
        checkStatus: "",
        reportTime: "",
        problemCategory: "",
        partProblems: [],
        eventProblems: [],
        problemImages: [],
        signatureImage: "",
        bjjcPatrolInfoList: [],
        sjjcPatrolInfoList: [],
        questionContent: [],
      },
      taskDetail: {},
      // 问题整改相关数据
      registerDialogVisible: false,
      registerLoading: false,
      registerForm: {
        registerDescribe: "",
      },
      registerRules: {
        registerDescribe: [
          { required: true, message: "请输入问题整改描述", trigger: "blur" },
          {
            min: 1,
            max: 500,
            message: "长度在 1 到 500 个字符",
            trigger: "blur",
          },
        ],
      },
      // 退回相关数据
      backDialogVisible: false,
      backLoading: false,
      backForm: {
        returnReason: "",
      },
      backRules: {
        returnReason: [
          { required: true, message: "请输入问题整改描述", trigger: "blur" },
          {
            min: 1,
            max: 500,
            message: "长度在 1 到 500 个字符",
            trigger: "blur",
          },
        ],
      },
      mapDialogVisible: false,
      map: null,
      polyline: null,
      markers: [],
      pingjia: 0,
      // 添加评价相关数据
      evaluationDialogVisible: false,
      evaluationLoading: false,
      currentEvaluationRow: null,
      evaluationForm: {
        securityIssueRating: 0,
        maintenanceIssuesRating: 0,
        environmentRating: 0,
        inspectRating: 0,
        comprehensiveRating: "",
        detailId: "",
      },
      evaluationRules: {
        securityIssueRating: [
          { required: true, message: "请为安全问题评分", trigger: "change" },
        ],
        maintenanceIssuesRating: [
          { required: true, message: "请为养护问题评分", trigger: "change" },
        ],
        environmentRating: [
          { required: true, message: "请为环境问题评分", trigger: "change" },
        ],
        inspectRating: [
          { required: true, message: "请为检查问题评分", trigger: "change" },
        ],
        comprehensiveRating: [
          { required: true, message: "请填写综合评价意见", trigger: "blur" },
          { max: 200, message: "综合评价不能超过200个字符", trigger: "blur" },
        ],
      },
      evaluationHistoryVisible: false,
      evaluationHistoryLoading: false,
      evaluationHistoryList: [],
      evaluationHistoryTotal: 0,
      evaluationHistoryParams: {
        pageNum: 1,
        pageSize: 10,
        detailId: "",
      },
      roles: [],
      listSelected: [],
      isIssued: false,
      issuedList: [],
      issuedSelect: [],
      //转派
      isTransfer: false,
      transferPeople: [],
      transferItem: "",
      //线索
      isXiansuo: false,
      xiafaList: [
        { label: "未下发", value: 0 },
        { label: "已下发", value: 1 },
      ],
    };
  },
  created() {
    this.roles = store.getters.roles;
    this.isXiansuo = this.roles.includes("qujiadmin");
    // 获取任务单号
    this.params.taskNo = this.$route.query.taskNo;
    // 获取任务单号
    if (this.$route.query.id) {
      // 编辑
      // this.taskDetail.id = this.$route.query.id;
      this.queryParams.taskId = this.$route.query.id;
      this.getInfo();
    }
  },
  mounted() {},
  beforeDestroy() {
    // 清理地图实例
    if (this.map) {
      this.map.destroy();
    }
  },
  computed: {
    computedInspectStatus() {
      return this.dict.type.inspect_status.map((option) => {
        if (option.label === "待整改") {
          return { ...option, label: "待核查" };
        }
        return option;
      });
    },
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.getInfo();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        street: "",
        inspector: "",
        taskType: "",
        projectType: "",
        projectName: "",
        xiafaType: "",
        taskId: null,
        projectNo: "",
      };
      this.queryParams.taskId = this.$route.query.id;
      this.getInfo();
    },
    // 选择列表下发
    listSelect(val) {
      this.listSelected = val;
      console.log(this.listSelected);
    },
    //点击下发
    issuedClick() {
      if (this.listSelected.length == 0) {
        this.$message({
          message: "请选择工程！",
          type: "warning",
        });
        return;
      }
      this.isIssued = true;
      this.getIssued();
    },
    //下发人列表
    getIssued() {
      let params = {
        roleKey: "jiedaojiancha",
      };
      selectUserByRoleKey(params).then((res) => {
        console.log(res.data);
        if (res.code === 200) {
          this.issuedList = res.data;
        }
      });
    },
    handleIssued() {
      if (this.issuedSelect.length == 0) {
        this.$message({
          message: "请选择下发人！",
          type: "warning",
        });
        return;
      }
      const selectedUsers = this.issuedList.filter((user) =>
        this.issuedSelect.includes(user.userId)
      );
      console.log(this.listSelected);
      const ids = this.listSelected.map((item) => item.id).join(",");
      const inspectorName = selectedUsers
        .map((user) => user.userName)
        .join(",");
      const inspectorNickName = selectedUsers
        .map((user) => user.nickName)
        .join(",");
      let params = {
        ids: ids,
        inspectorName: inspectorName,
        inspectorNickName: inspectorNickName,
      };
      console.log(params);
      xiafa(params).then((res) => {
        if (res.code === 200) {
          this.$modal.msgSuccess("下发成功");
          this.isIssued = false;
          this.issuedSelect = [];
          this.getInfo();
        }
      });
    },
    //转派
    transferClick() {
      this.isTransfer = true;
      this.getTransferList();
    },
    //转派人列表
    getTransferList() {
      let params = {
        roleKey: "pqwgy",
      };
      selectUserByRoleKey(params).then((res) => {
        console.log(res.data);
        if (res.code === 200) {
          this.transferPeople = res.data;
        }
      });
    },
    //转派
    handleTransfer() {
      if (this.transferItem == "") {
        this.$message({
          message: "请选择转派人！",
          type: "warning",
        });
        return;
      }

      const selectedUsers = this.transferPeople.filter(
        (user) => String(this.transferItem) === String(user.userId)
      );
      let params = {
        id: this.currentDetail.id,
        inspectStatus: 2,
        isTransfer: 1,
        inspectorName: selectedUsers.userName,
      };
      lian(params).then((res) => {
        if (res.code === 200) {
          this.$modal.msgSuccess("转派成功");
          this.isTransfer = false;
          this.getInfo();
          this.detailDialogVisible = false;
        }
      });
    },
    //线索确认
    handleConfirm() {
      this.$confirm("确认线索确认吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          lian({
            id: this.currentDetail.id,
            inspectStatus: 3,
            registerStatus: 2,
          }).then((res) => {
            this.registerLoading = true;
            if (res.code == 200) {
              this.$message.success("线索确认成功");
              this.registerDialogVisible = false;
              this.detailDialogVisible = false;
              this.getInfo();
            }
          });
        })
        .catch(() => {});
    },
    // 显示巡查轨迹
    handleShowTrack(row) {
      if (!row.trajectoryCoor) {
        this.$modal.msgError("该任务没有巡查轨迹");
        return;
      }
      this.mapDialogVisible = true;
      this.$nextTick(() => {
        this.initMap(row);
      });
    },
    // 初始化地图
    initMap(row) {
      if (!this.map) {
        this.map = new AMap.Map("container", {
          zoom: 13,
          center: [116.397428, 39.90923], // 默认中心点
        });
      }

      // 清除之前的轨迹和标记
      if (this.polyline) {
        this.polyline.setMap(null);
      }
      this.markers.forEach((marker) => marker.setMap(null));
      this.markers = [];

      // 解析轨迹数据
      try {
        let roadData = [];
        let roadInfo = [];
        let list1 = row.trajectoryCoor.split("#");
        roadInfo = list1.map((item) => {
          let list2 = item.split("-");
          let cood = list2[0].split(",");
          let obj = {
            longitude: cood[0],
            latitude: cood[1],
          };
          roadData.push(obj);
          return {
            time: list2[1],
            cood: obj,
          };
        });
        // let roadData = [
        //     { longitude: 116.397428, latitude: 39.90923 },
        //     { longitude: 116.398000, latitude: 39.91200 },
        //     { longitude: 116.399000, latitude: 39.91500 },
        //     { longitude: 116.400000, latitude: 39.91700 },
        //     { longitude: 116.401000, latitude: 39.91923 },
        // ]
        row.trackData = JSON.stringify(roadData);
        const trackData = JSON.parse(row.trackData);
        if (trackData && trackData.length > 0) {
          // 创建轨迹点数组
          const path = trackData.map((point) => [
            point.longitude,
            point.latitude,
          ]);

          // 创建轨迹线
          this.polyline = new AMap.Polyline({
            path: path,
            strokeColor: "#3366FF",
            strokeWeight: 6,
            strokeOpacity: 0.8,
          });
          this.polyline.setMap(this.map);

          // 添加所有轨迹点的实心圆点
          path.forEach((pos, idx) => {
            // 起点和终点已用特殊图标，其他点用小圆点
            if (idx === 0 || idx === path.length - 1) return;
            const circleMarker = new AMap.Marker({
              position: pos,
              icon: new AMap.Icon({
                size: new AMap.Size(12, 12),
                image:
                  'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12"><circle cx="6" cy="6" r="6" fill="%233366FF"/></svg>',
                imageSize: new AMap.Size(12, 12),
              }),
              offset: new AMap.Pixel(-6, -6),
            });
            this.markers.push(circleMarker);
            circleMarker.setMap(this.map);
            // 鼠标悬停显示巡检时间
            circleMarker.on("mouseover", function (e) {
              let time = roadInfo[idx].time;
              const infoWindow = new AMap.InfoWindow({
                content: `<div style="font-size:14px;">巡检时间：${time}</div>`,
                offset: new AMap.Pixel(0, -10),
              });
              infoWindow.open(
                circleMarker.getMap(),
                circleMarker.getPosition()
              );
              circleMarker._infoWindow = infoWindow;
            });
            circleMarker.on("mouseout", function (e) {
              if (circleMarker._infoWindow) {
                circleMarker._infoWindow.close();
              }
            });
          });

          // 添加起点和终点标记
          const startMarker = new AMap.Marker({
            position: path[0],
            icon: new AMap.Icon({
              size: new AMap.Size(25, 34),
              image: "https://webapi.amap.com/theme/v1.3/markers/n/start.png",
              imageSize: new AMap.Size(25, 34),
            }),
          });
          const endMarker = new AMap.Marker({
            position: path[path.length - 1],
            icon: new AMap.Icon({
              size: new AMap.Size(25, 34),
              image: "https://webapi.amap.com/theme/v1.3/markers/n/end.png",
              imageSize: new AMap.Size(25, 34),
            }),
          });

          this.markers.push(startMarker, endMarker);
          startMarker.setMap(this.map);
          endMarker.setMap(this.map);

          startMarker.on("mouseover", function (e) {
            let time = roadInfo[0].time;
            const infoWindow = new AMap.InfoWindow({
              content: `<div style="font-size:14px;">巡检时间：${time}</div>`,
              offset: new AMap.Pixel(0, -10),
            });
            infoWindow.open(startMarker.getMap(), startMarker.getPosition());
          });
          startMarker.on("mouseout", function (e) {
            if (startMarker._infoWindow) {
              startMarker._infoWindow.close();
            }
          });

          endMarker.on("mouseover", function (e) {
            let time = roadInfo[roadInfo.length - 1].time;
            const infoWindow = new AMap.InfoWindow({
              content: `<div style="font-size:14px;">巡检时间：${time}</div>`,
              offset: new AMap.Pixel(0, -10),
            });
            infoWindow.open(endMarker.getMap(), endMarker.getPosition());
          });
          endMarker.on("mouseout", function (e) {
            if (endMarker._infoWindow) {
              endMarker._infoWindow.close();
            }
          });

          // 调整地图视野以包含所有轨迹点
          this.map.setFitView([this.polyline]);
        }
      } catch (error) {
        console.error("解析轨迹数据失败:", error);
        this.$modal.msgError("轨迹数据格式错误");
      }
    },
    getTaskDetail() {
      getJianChaTaskInfo(this.taskDetail.id).then((res) => {
        res.data.detailList.forEach((element) => {
          element.problemImageUrl = element.problemImageUrl
            ? element.problemImageUrl
            : "";
          let mediaList = [];
          if (element.problemImageUrl) {
            mediaList = element.problemImageUrl.split(",").map((item) => {
              return {
                url: process.env.VUE_APP_BASE_API + item,
              };
            });
          }
          element.mediaList = mediaList;
          element.verifyPicurl = element.verifyPicurl
            ? element.verifyPicurl
            : "";
          let mediaList2 = [];
          if (element.verifyPicurl) {
            mediaList2 = element.verifyPicurl.split(",").map((item) => {
              return {
                url: process.env.VUE_APP_BASE_API + item,
              };
            });
          }

          element.mediaList2 = mediaList2;
          element.signatureImage = element.signatureImageUrl
            ? process.env.VUE_APP_BASE_API + element.signatureImageUrl
            : "";
          element.voiceUrl = element.voiceUrl
            ? process.env.VUE_APP_BASE_API + element.voiceUrl
            : "";
        });
        this.taskDetail = res.data;
        console.log(this.taskDetail);
      });
    },
    getInfo() {
      getDetailInfo(this.queryParams).then((res) => {
        res.data.detailList.forEach((element) => {
          element.problemImageUrl = element.problemImageUrl
            ? element.problemImageUrl
            : "";
          let mediaList = [];
          if (element.problemImageUrl) {
            mediaList = element.problemImageUrl.split(",").map((item) => {
              return {
                url: process.env.VUE_APP_BASE_API + item,
              };
            });
          }
          element.mediaList = mediaList;
          element.verifyPicurl = element.verifyPicurl
            ? element.verifyPicurl
            : "";
          let mediaList2 = [];
          if (element.verifyPicurl) {
            mediaList2 = element.verifyPicurl.split(",").map((item) => {
              return {
                url: process.env.VUE_APP_BASE_API + item,
              };
            });
          }

          element.mediaList2 = mediaList2;
          element.signatureImage = element.signatureImageUrl
            ? process.env.VUE_APP_BASE_API + element.signatureImageUrl
            : "";
          element.voiceUrl = element.voiceUrl
            ? process.env.VUE_APP_BASE_API + element.voiceUrl
            : "";
        });
        this.taskDetail = res.data;
        this.total = this.taskDetail.detailList.length;
        console.log(this.taskDetail);
      });
    },
    // 查看详情
    handleCheck(row) {
      // 确保所有必要的数组属性都存在
      this.currentDetail = {
        ...this.currentDetail, // 保持默认值
        ...row, // 覆盖实际数据
        // 确保这些属性始终是数组
        partProblems: row.partProblems || [],
        eventProblems: row.eventProblems || [],
        problemImages: row.problemImages || [],
      };

      // 处理巡查结果数据
      if (this.currentDetail.questionContent) {
        try {
          // 如果是字符串，尝试解析为JSON
          if (typeof this.currentDetail.questionContent === "string") {
            this.currentDetail.questionContent = JSON.parse(
              this.currentDetail.questionContent
            );
          }

          // 处理图片URL
          if (Array.isArray(this.currentDetail.questionContent)) {
            this.currentDetail.questionContent.forEach((inspection) => {
              if (inspection.imageList && Array.isArray(inspection.imageList)) {
                inspection.imageList = inspection.imageList.map((imageUrl) => {
                  if (typeof imageUrl === "string") {
                    return {
                      url: imageUrl.startsWith("http")
                        ? imageUrl
                        : process.env.VUE_APP_BASE_API + imageUrl,
                    };
                  }
                  return imageUrl;
                });
              }
            });
          }
        } catch (error) {
          console.error("解析巡查结果数据失败:", error);
          this.currentDetail.questionContent = [];
        }
      } else {
        this.currentDetail.questionContent = [];
      }

      this.detailDialogVisible = true;
    },
    // 显示问题整改弹窗
    showRegisterDialog() {
      this.registerForm.description = "";
      this.registerDialogVisible = true;
    },
    showBackDialog() {
      this.backForm.returnReason = "";
      this.backDialogVisible = true;
    },
    // 处理问题整改
    handleRegister() {
      this.$refs.registerForm.validate((valid) => {
        if (valid) {
          this.$confirm("确认要问题整改吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.registerLoading = true;

              lian({
                id: this.currentDetail.id,
                registerDescribe: this.registerForm.registerDescribe,
                inspectStatus: 2,
                registerStatus: 1,
              })
                .then((res) => {
                  this.$message.success("发送成功");
                  this.registerDialogVisible = false;
                  this.detailDialogVisible = false;
                  this.getInfo();
                })
                .finally(() => {
                  this.registerLoading = false;
                });
            })
            .catch(() => {});
        }
      });
    },

    handleBack() {
      this.$refs.backForm.validate((valid) => {
        if (valid) {
          this.$confirm("确认要退回吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.backLoading = true;

              lian({
                id: this.currentDetail.id,
                returnReason: this.backForm.returnReason,
                inspectStatus: 2,
              })
                .then((res) => {
                  this.$message.success("退回成功");
                  this.backDialogVisible = false;
                  this.detailDialogVisible = false;
                  this.getInfo();
                })
                .finally(() => {
                  this.backLoading = false;
                });
            })
            .catch(() => {});
        }
      });
    },
    handleJieAn() {
      this.$confirm("确认要结案吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          lian({
            id: this.currentDetail.id,
            inspectStatus: 3,
            registerStatus: 2,
          }).then((res) => {
            this.$message.success("结案成功");
            this.backDialogVisible = false;
            this.detailDialogVisible = false;
            this.getInfo();
          });
        })
        .catch(() => {});
    },
    showEvaluationDialog(row) {
      this.currentEvaluationRow = row;
      // 重置表单
      this.evaluationForm = {
        securityIssueRating: 0,
        maintenanceIssuesRating: 0,
        environmentRating: 0,
        inspectRating: 0,
        comprehensiveRating: "",
        // projectId: row.projectId,
        detailId: this.taskDetail.id,
      };
      this.evaluationDialogVisible = true;
    },
    submitEvaluation() {
      this.$refs.evaluationForm.validate((valid) => {
        if (valid) {
          this.evaluationLoading = true;

          // 调用评价接口
          addMfQualityEvaluation(this.evaluationForm)
            .then((res) => {
              this.$message.success("评价提交成功！");
              this.evaluationDialogVisible = false;

              // 更新任务列表
              this.getInfo();
            })
            .catch((err) => {
              console.error("评价提交失败:", err);
              this.$message.error("评价提交失败，请重试");
            })
            .finally(() => {
              this.evaluationLoading = false;
            });
        }
      });
    },
    showEvaluationHistory(row) {
      // this.evaluationHistoryParams.projectId = row.projectId;
      this.evaluationHistoryParams.detailId = this.taskDetail.id;
      this.evaluationHistoryVisible = true;
      this.getEvaluationHistoryList();
    },
    getEvaluationHistoryList() {
      this.evaluationHistoryLoading = true;
      listMfQualityEvaluation(this.evaluationHistoryParams)
        .then((response) => {
          this.evaluationHistoryList = response.rows;
          this.evaluationHistoryTotal = response.total;
        })
        .finally(() => {
          this.evaluationHistoryLoading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  // overflow-y: auto;
  height: calc(100vh - 100px);

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;
    padding: 0;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      .detail-center {
        display: flex;

        .detail-center-left {
          .detail-center-item {
            display: flex;
            line-height: 24px;
            margin: 12px 0;

            .detail-center-item-left {
              width: 70px;
            }

            .detail-center-item-right {
              flex: 1;
            }
          }
        }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 60%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
    align-items: center;

    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  width: calc(100% - 200px); // 减去左侧菜单的宽度
  height: 60px;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.12);
  z-index: 100;

  .button-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    padding-right: 20px;

    .el-button {
      padding: 12px 35px;
      margin-left: 10px;
    }
  }
}

.box-card {
  height: calc(100vh - 200px); // 减去顶部卡片高度和底部按钮高度
  overflow-y: auto;
  font-size: 14px;
}

.detail-content {
  padding: 20px;

  .detail-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }

  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .problem-image {
    width: 150px;
    height: 150px;
    border-radius: 4px;
  }

  .signature-image {
    max-width: 300px;
    max-height: 150px;
  }
}

::v-deep .el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
}

.problem-item {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  .problem-main {
    padding: 16px;
    border-bottom: 1px solid #ebeef5;

    .problem-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .problem-index {
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: #409eff;
        color: #fff;
        border-radius: 50%;
        margin-right: 12px;
        font-size: 14px;
      }

      .problem-name {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }

    .problem-content {
      display: flex;
      margin-left: 36px;

      .content-label {
        color: #909399;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .content-text {
        color: #606266;
        line-height: 1.5;
      }
    }
  }

  .subclass-list {
    padding: 12px 16px 12px 52px;
    background: #f8f9fb;

    .subclass-item {
      display: flex;
      align-items: center;
      padding: 8px 0;

      &:not(:last-child) {
        border-bottom: 1px dashed #ebeef5;
      }

      .subclass-index {
        color: #409eff;
        margin-right: 12px;
        font-size: 14px;
        width: 40px;
        text-align: right;
      }

      .subclass-name {
        color: #606266;
        flex: 1;
        line-height: 1.5;
      }
    }
  }
}

// 当只有一个问题时的样式
.problem-item:only-child {
  margin-bottom: 0;
}

// 添加过渡动画
.problem-item {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;

  .el-button {
    min-width: 100px;

    &.el-button--primary {
      margin-left: 12px;
    }
  }
}

// 调整内容区域样式，避免内容被底部按钮遮挡
.detail-content {
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

// 问题整改描述输入框样式
.el-textarea {
  width: 100%;

  // :deep(.el-textarea__inner) {
  //   font-family: inherit;
  // }
}
.issued-container {
  width: 100%;
  .issued-content {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .btn-group {
    width: 100%;
    padding-right: 30px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 50px;
  }
}

// 巡查结果样式
.inspection-item {
  margin-bottom: 24px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.inspection-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  // background: linear-gradient(135deg, #409EFF 0%, #66B1FF 100%);
  // color: #fff;

  .inspection-index {
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    margin-right: 12px;
    font-weight: bold;
    font-size: 14px;
  }

  .inspection-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
  }
}

.inspection-remark {
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;

  .remark-label {
    color: #909399;
    margin-right: 8px;
    font-weight: 500;
  }

  .remark-text {
    color: #606266;
  }
}

.problem-category {
  padding: 16px 20px;

  &:not(:last-child) {
    border-bottom: 1px solid #f0f2f5;
  }

  .category-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #303133;

    i {
      margin-right: 8px;
      font-size: 16px;

      &.el-icon-warning-outline {
        color: #e6a23c;
      }

      &.el-icon-setting {
        color: #409eff;
      }
    }
  }

  .problem-list {
    margin-left: 24px;
  }
}

.problem-item-detail {
  background: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background: #f5f7fa;
    border-color: #c0c4cc;
  }
}

.problem-content-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .problem-description {
    color: #606266;
    font-size: 14px;
    flex: 1;
    margin-right: 12px;
  }

  .problem-tags {
    display: flex;
    gap: 6px;
  }
}

.component-info {
  .component-main {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .component-name {
      // background: #E1F3FF;
      // color: #409EFF;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 14px;
      margin-right: 8px;
    }

    .component-option {
      color: #303133;
      font-weight: 500;
      font-size: 14px;
    }
  }

  .component-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    font-size: 13px;

    .problem-count {
      color: #606266;
    }

    .immediate-count {
      color: #f56c6c;
      font-weight: 500;
    }
  }

  .problem-tags {
    display: flex;
    gap: 6px;
  }
}

.inspection-images {
  padding: 16px 20px;
  background: #fafbfc;
  border-top: 1px solid #e4e7ed;

  .images-title {
    color: #303133;
    font-weight: 500;
    margin-bottom: 12px;
    font-size: 14px;
  }
}

.component-group {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.component-group-title {
  background: #f0f9ff;
  color: #1e40af;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
  border-left: 3px solid #3b82f6;
}

.component-children {
  margin-left: 16px;
}
</style>
