import request from '@/utils/request'

// 查询设备检查记录列表
export function listRecords(query) {
  return request({
    url: '/system/equipmentInspectionRecords/list',
    method: 'get',
    params: query
  })
}

// 查询设备检查记录详细
export function getRecords(id) {
  return request({
    url: '/system/equipmentInspectionRecords/' + id,
    method: 'get'
  })
}

// 新增设备检查记录
export function addRecords(data) {
  return request({
    url: '/system/equipmentInspectionRecords',
    method: 'post',
    data: data
  })
}

// 修改设备检查记录
export function updateRecords(data) {
  return request({
    url: '/system/equipmentInspectionRecords',
    method: 'put',
    data: data
  })
}

// 删除设备检查记录
export function delRecords(id) {
  return request({
    url: '/system/equipmentInspectionRecords/' + id,
    method: 'delete'
  })
}
