<template>
    <div class="app-container">
        <el-row>
            <el-col :span="24" :xs="24">
                <el-card body-style="padding: 12px">
                    <div class="top">
                        <div class="top-left">整改分析</div>
                        <div class="top-right">
                            <el-date-picker style="margin-right: 20px;" v-model="timeRange" type="daterange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                            </el-date-picker>
                            <el-select v-model="echartType1" placeholder="请选择" style="width: 100px;">
                                <el-option v-for="item in echartTypeList1" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                            <el-button type="primary" icon="el-icon-download" size="small">搜索</el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-card class="box-card">
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane :label="tabItem.label" :name="tabItem.name" v-for="tabItem in tabsList">
                            <div>
                                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                                    <el-form-item label="问题等级" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="规范大类" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="用户名称" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="项目名称" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="检查类型" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" icon="el-icon-search" size="mini"
                                            @click="handleQuery">搜索</el-button>
                                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                                    </el-form-item>
                                </el-form>
                                <!-- 饼图 -->
                                <div v-if="echartType1 == 1"
                                    style="width: 100%; height: calc(100vh - 380px); overflow-x: auto;">
                                    <div
                                        style="width: 100%; height: 12%; overflow: auto; display: flex; justify-content: center; flex-wrap: wrap;">
                                        <div class="lengend-item" v-for="(item, index) in echartData.data">
                                            <div class="lengend-color"
                                                :style="{ backgroundColor: echartData.colorList[index] }"></div>
                                            <div class="lengend-name">{{ item.name }}</div>
                                        </div>
                                    </div>
                                    <div style="width: 100%; height: 58%; display: flex; align-items: center;">
                                        <pieChart height="100%" :data="echartData" @pieClick="handlePieClick" />
                                    </div>
                                </div>
                                <!-- 折线图 -->
                                <div v-if="echartType1 == 2"
                                    style="width: 100%; height: calc(100vh - 380px); overflow-x: auto;">
                                    <div
                                        style="width: 100%; height: 12%; overflow: auto; display: flex; justify-content: center; flex-wrap: wrap;">
                                        <div class="lengend-item" v-for="(item, index) in chart2Lengend">
                                            <div class="lengend-color" :style="{ backgroundColor: item.color }"></div>
                                            <div class="lengend-name">{{ item.label }}</div>
                                        </div>
                                    </div>
                                    <div style="width: 100%; height: 88%; display: flex; align-items: center;">
                                        <lineChart height="88%" :data="lineData" @lineClick="handleLineClick" />
                                    </div>
                                </div>
                                <!-- 柱状图 -->
                                <div v-if="echartType1 == 3"
                                    style="width: 100%; height: calc(100vh - 380px); overflow-x: auto;">
                                    <div style="width: 100%; height: 98%; display: flex; align-items: center;">
                                        <barChart height="94%" :data="barData" @barClick="handleBarClick" />
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </el-card>
            </el-col>
        </el-row>

        <!-- 饼图 -->
        <el-dialog title="饼图" :visible.sync="showPiePop" width="80%" style="margin-top: 5vh;">
            <div style="width: 100%; height: 66vh;">
                
            </div>
        </el-dialog>
        <!-- 折线图 -->
        <el-dialog title="折线图" :visible.sync="showLinePop" width="80%" style="margin-top: 5vh;">
            <div style="width: 100%; height: 66vh;">
                
            </div>
        </el-dialog>
        <!-- 柱状图 -->
        <el-dialog title="柱状图" :visible.sync="showBarPop" width="80%" style="margin-top: 5vh;">
            <div style="width: 100%; height: 66vh;">
                
            </div>
        </el-dialog>
    </div>

</template>

<script>
import pieChart from "@/views/components/pieChart.vue";
import lineChart from "@/views/components/lineChart.vue";
import barChart from "@/views/components/barChart.vue";
export default {
    components: {
        pieChart,
        lineChart,
        barChart
    },
    data() {
        return {
            // 分类数据 图表类型
            echartType1: 1,
            echartTypeList1: [
                {
                    value: 1,
                    label: '饼图'
                },
                {
                    value: 2,
                    label: '折线图'
                },
                {
                    value: 3,
                    label: '柱状图'
                }
            ],
            // 显示折线图弹窗
            showLinePop: false,
            // 显示饼图弹窗
            showPiePop: false,
            // 显示柱状图弹窗
            showBarPop: false,
            // 饼图数据
            echartData: {
                colorList: ['#3C80E8', '#8EE98F', '#A1FFEB', '#54C255', '#A1CDFF', '#FF920D', '#FECF77', '#F3B2B1', '#B38DFF'],
                data: [
                    { value: 310, name: '消防安全' },
                    { value: 335, name: '安全基础管理' },
                    { value: 234, name: '电气安全' },
                    { value: 135, name: '特种设备' },
                    { value: 148, name: '危化品' },
                    { value: 234, name: '固废' },
                    { value: 135, name: '仓储安全管理' },
                    { value: 148, name: '特殊作业' },
                    { value: 148, name: '其他40项' }
                ]
            },
            // 折线图数据
            lineData: {
                grid: {
                    top: 10,
                    left: '3%',
                    right: '3%',
                    bottom: '8%',
                },
                xAxisData: ['6月', '7月', '8月', '9月', '10月', '11月', '12月', '1月', '2月', '3月', '4月', '5月'],
                seriesData: [
                    {
                        name: '一般',
                        data: [
                            {
                                value: 310,
                                name: '6月'
                            },
                            {
                                value: 335,
                                name: '7月'
                            },
                            {
                                value: 234,
                                name: '8月'
                            },
                            {
                                value: 135,
                                name: '9月'
                            },
                            {
                                value: 148,
                                name: '10月'
                            },
                            {
                                value: 135,
                                name: '11月'
                            },
                            {
                                value: 148,
                                name: '12月'
                            },
                            {
                                value: 148,
                                name: '1月'
                            },
                            {
                                value: 135,
                                name: '2月'
                            },
                            {
                                value: 148,
                                name: '3月'
                            },
                            {
                                value: 135,
                                name: '4月'
                            },
                            {
                                value: 148,
                                name: '5月'
                            }
                        ],
                        backgroundColor: '#54C255'
                    },
                    {
                        name: '较大',
                        data: [
                            {
                                value: 210,
                                name: '6月'
                            },
                            {
                                value: 235,
                                name: '7月'
                            },
                            {
                                value: 334,
                                name: '8月'
                            },
                            {
                                value: 235,
                                name: '9月'
                            },
                            {
                                value: 108,
                                name: '10月'
                            },
                            {
                                value: 204,
                                name: '11月'
                            },
                            {
                                value: 105,
                                name: '12月'
                            },
                            {
                                value: 108,
                                name: '1月'
                            },
                            {
                                value: 204,
                                name: '2月'
                            },
                            {
                                value: 105,
                                name: '3月'
                            },
                            {
                                value: 108,
                                name: '4月'
                            },
                            {
                                value: 204,
                                name: '5月'
                            }
                        ],
                        backgroundColor: '#FF920D'
                    },
                    {
                        name: '重大',
                        data: [
                            {
                                value: 206,
                                name: '6月'
                            },
                            {
                                value: 205,
                                name: '7月'
                            },
                            {
                                value: 304,
                                name: '8月'
                            },
                            {
                                value: 205,
                                name: '9月'
                            },
                            {
                                value: 104,
                                name: '10月'
                            },
                            {
                                value: 206,
                                name: '11月'
                            },
                            {
                                value: 113,
                                name: '12月'
                            },
                            {
                                value: 118,
                                name: '1月'
                            },
                            {
                                value: 118,
                                name: '2月'
                            },
                            {
                                value: 115,
                                name: '3月'
                            },
                            {
                                value: 118,
                                name: '4月'
                            },
                            {
                                value: 318,
                                name: '5月'
                            }
                        ],
                        backgroundColor: '#E54545'
                    }
                ]
            },
            // 折线图图例
            chart2Lengend: [
                {
                    color: '#54C255',
                    label: '一般'
                },
                {
                    color: '#FF920D',
                    label: '较大'
                },
                {
                    color: '#E54545',
                    label: '重大'
                },
            ],
            // 柱状图数据
            barData: {
                colorList: ['#3C80E8', '#8EE98F', '#A1FFEB', '#54C255', '#A1CDFF', '#FF920D', '#FECF77', '#F3B2B1', '#B38DFF'],
                chart2Lengend: [
                    {
                        color: '#54C255',
                        label: '一般'
                    },
                    {
                        color: '#FF920D',
                        label: '较大'
                    },
                    {
                        color: '#E54545',
                        label: '重大'
                    },
                ],
                grid: {
                    top: 10,
                    left: '3%',
                    right: '3%',
                    bottom: '10%',
                },
                xAxis: {
                    type: 'value',
                    axisTick: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'category',
                    data: ['6月', '7月', '8月', '9月', '10月', '11月', '12月', '1月', '2月', '3月', '4月', '5月',],
                    axisLabel: {
                        interval: 0,
                    }
                },
                series: [
                    {
                        name: '一般',
                        type: 'bar',
                        stack: 'total',
                        data: [
                            {
                                value: 310,
                                name: '6月'
                            },
                            {
                                value: 335,
                                name: '7月'
                            },
                            {
                                value: 234,
                                name: '8月'
                            },
                            {
                                value: 135,
                                name: '9月'
                            },
                            {
                                value: 148,
                                name: '10月'
                            },
                            {
                                value: 135,
                                name: '11月'
                            },
                            {
                                value: 148,
                                name: '12月'
                            },
                            {
                                value: 148,
                                name: '1月'
                            },
                            {
                                value: 135,
                                name: '2月'
                            },
                            {
                                value: 148,
                                name: '3月'
                            },
                            {
                                value: 135,
                                name: '4月'
                            },
                            {
                                value: 148,
                                name: '5月'
                            }
                        ],
                        label: {
                            show: true,
                            formatter: (params) => Math.round(params.value * 100) / 1000 + '%'
                        },
                        itemStyle: {
                            color: '#54C255'
                        }
                    },
                    {
                        name: '较大',
                        type: 'bar',
                        stack: 'total',
                        data: [
                            {
                                value: 210,
                                name: '6月'
                            },
                            {
                                value: 235,
                                name: '7月'
                            },
                            {
                                value: 334,
                                name: '8月'
                            },
                            {
                                value: 235,
                                name: '9月'
                            },
                            {
                                value: 108,
                                name: '10月'
                            },
                            {
                                value: 204,
                                name: '11月'
                            },
                            {
                                value: 105,
                                name: '12月'
                            },
                            {
                                value: 108,
                                name: '1月'
                            },
                            {
                                value: 204,
                                name: '2月'
                            },
                            {
                                value: 105,
                                name: '3月'
                            },
                            {
                                value: 108,
                                name: '4月'
                            },
                            {
                                value: 204,
                                name: '5月'
                            }
                        ],
                        label: {
                            show: true,
                            formatter: (params) => Math.round(params.value * 100) / 1000 + '%'
                        },
                        itemStyle: {
                            color: '#FF920D'
                        }
                    },
                    {
                        name: '重大',
                        type: 'bar',
                        stack: 'total',
                        data: [
                            {
                                value: 206,
                                name: '6月'
                            },
                            {
                                value: 205,
                                name: '7月'
                            },
                            {
                                value: 304,
                                name: '8月'
                            },
                            {
                                value: 205,
                                name: '9月'
                            },
                            {
                                value: 104,
                                name: '10月'
                            },
                            {
                                value: 206,
                                name: '11月'
                            },
                            {
                                value: 113,
                                name: '12月'
                            },
                            {
                                value: 118,
                                name: '1月'
                            },
                            {
                                value: 118,
                                name: '2月'
                            },
                            {
                                value: 115,
                                name: '3月'
                            },
                            {
                                value: 118,
                                name: '4月'
                            },
                            {
                                value: 318,
                                name: '5月'
                            }
                        ],
                        label: {
                            show: true,
                            formatter: (params) => Math.round(params.value * 100) / 1000 + '%'
                        },
                        itemStyle: {
                            color: '#E54545'
                        }
                    }
                ]
            },
            timeRange: [],
            activeName: '1',
            activeName2: '1',
            tabsList: [
                { name: '1', label: '问题等级', prop: 'questionlevel' },
                { name: '2', label: '大类', prop: 'bigclass' },
                { name: '3', label: '小类', prop: 'smallclass' },
                { name: '4', label: '用户', prop: 'user' },
                { name: '5', label: '检查角色', prop: 'checkrole' },
                { name: '6', label: '所属园区', prop: 'park' },
                { name: '7', label: '检查类型', prop: 'checktype' },
                { name: '8', label: '项目名称', prop: 'projectname' },
                { name: '9', label: '工程类型', prop: 'projecttype' },
                { name: '10', label: '按月分类', prop: 'month' },
                { name: '11', label: '按年分类', prop: 'year' }
            ],
            tabsList2: [
                { name: '1', label: '问题等级', prop: 'questionlevel' },
                { name: '2', label: '大类', prop: 'bigclass' },
                { name: '3', label: '小类', prop: 'smallclass' },
                { name: '4', label: '用户', prop: 'user' },
                { name: '5', label: '检查角色', prop: 'checkrole' },
                { name: '6', label: '所属园区', prop: 'park' },
                { name: '7', label: '检查类型', prop: 'checktype' },
                { name: '8', label: '项目名称', prop: 'projectname' },
                { name: '9', label: '工程类型', prop: 'projecttype' },
                { name: '10', label: '按月分类', prop: 'month' },
                { name: '11', label: '按年分类', prop: 'year' }
            ],
            queryParams: {
                questionlevel: []
            },
            questionLevelList: [
                {
                    value: 1,
                    label: '一般'
                },
                {
                    value: 2,
                    label: '较大'
                },
                {
                    value: 3,
                    label: '重大'
                }
            ],
        };
    },
    created() {

    },
    methods: {

        // 饼图点击事件
        handlePieClick(params) {
            console.log(params)

            this.showPiePop = true
        },
        // 折线图点击事件
        handleLineClick(params) {
            console.log(params)
            this.showLinePop = true
        },
        // 柱状图点击事件
        handleBarClick(params) {
            console.log(params)
            this.showBarPop = true
        },
        handleClick(tab, event) {
            console.log(tab.name);
            // 初始化表格
            
        },
        handleQuery() {

        },
        resetQuery() {

        }
    }
};
</script>
<style lang="scss" scoped>
.app-container {
    width: 100%;
    height: calc(100vh - 100px);


}

::v-deep .el-dialog__header {
    border-bottom: 1px solid #EBEBEB;
}

::v-deep .el-dialog__body {
    padding: 10px 20px 30px 20px !important;
}

.el-select {
    margin-left: 0px !important;
    margin-right: 10px;
}

.top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .top-left {
        font-weight: bold;
    }

    .top-right {
        display: flex;
    }
}

.el-row {
    margin-bottom: 10px;

    &:last-child {
        margin-bottom: 0;
    }
}

.box-card {
    position: relative;
    height: calc(100vh - 162px);
    font-size: 14px;


}

.lengend-item {
    display: flex;
    align-items: center;

    .lengend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .lengend-name {
        font-size: 14px;
        line-height: 24px;
        color: #333333;
        margin-left: 8px;
        margin-right: 18px;
    }
}
</style>