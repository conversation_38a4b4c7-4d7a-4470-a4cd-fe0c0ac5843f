import request from '@/utils/request'

// 查询安全活动记录列表
export function listZjSafetyActivityRecords(query) {
  return request({
    url: '/system/safeActivityInfo/list',
    method: 'get',
    params: query
  })
}

// 查询安全活动记录详细
export function getZjSafetyActivityRecords(id) {
  return request({
    url: '/system/safeActivityInfo/' + id,
    method: 'get'
  })
}

// 新增安全活动记录
export function addZjSafetyActivityRecords(data) {
  return request({
    url: '/system/safeActivityInfo',
    method: 'post',
    data: data
  })
}

// 修改安全活动记录
export function updateZjSafetyActivityRecords(data) {
  return request({
    url: '/system/safeActivityInfo',
    method: 'put',
    data: data
  })
}

// 删除安全活动记录
export function delZjSafetyActivityRecords(id) {
  return request({
    url: '/system/safeActivityInfo/' + id,
    method: 'delete'
  })
}
