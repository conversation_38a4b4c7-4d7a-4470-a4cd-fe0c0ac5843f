<template>
    <el-dialog title="选择参与人员" :visible.sync="dialogVisible" width="900px" top="5vh" append-to-body
        :close-on-click-modal="false" custom-class="personnel-selector-dialog" @close="handleClose">
        <div class="personnel-selector">
            <div class="selector-content">
                <!-- 左侧组织架构树 -->
                <div class="left-panel">
                    <div class="org-tree-container">
                        <div class="tree-title">组织架构</div>
                        <div class="tree-content">
                            <org-tree :type="'1'" @nodeClick="handleOrgTreeNodeClick" />
                        </div>
                    </div>
                </div>

                <!-- 分隔线 -->
                <div class="divider" />

                <!-- 右侧人员列表 -->
                <div class="right-panel">
                    <div class="personnel-list-container">
                        <div class="list-header">
                            <span class="list-title">人员列表</span>
                            <div class="list-actions">
                                <el-button size="mini" type="text" @click="selectAll">全选</el-button>
                                <el-button size="mini" type="text" @click="deselectAll">取消全选</el-button>
                            </div>
                        </div>

                        <div v-loading="loading" class="personnel-list">
                            <el-checkbox-group v-model="selectedPersonnelIds">
                                <div v-for="person in personnelList" :key="person.id" class="personnel-item">
                                    <el-checkbox :label="person.id">
                                        <div class="person-info">
                                            <span class="person-name">{{ person.employeeName }}</span>
                                            <span class="person-details">
                                                <span class="person-dept">{{ person.companyName }}</span>
                                                <span class="person-post">{{ person.employeePost }}</span>
                                            </span>
                                        </div>
                                    </el-checkbox>
                                </div>
                            </el-checkbox-group>

                            <div v-if="!loading && personnelList.length === 0" class="empty-text">
                                请点击左侧组织架构选择部门
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <div class="selected-count">
                <span v-if="personnelList.length > 0">
                    当前部门：{{ currentDeptSelectedCount }}/{{ personnelList.length }} 人
                </span>
                <span v-if="personnelList.length > 0 && totalSelectedCount > 0" class="separator">|</span>
                <span v-if="totalSelectedCount > 0">
                    总计已选择：{{ totalSelectedCount }} 人
                </span>
                <span v-if="personnelList.length === 0 && totalSelectedCount === 0" class="no-selection">
                    请选择部门查看人员
                </span>
            </div>
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import OrgTree from '@/views/components/orgTree.vue'
import { listZjEmployeeInfo } from '@/api/inspection/zjEmployeeInfo'

export default {
    name: 'PersonnelSelector',
    components: {
        OrgTree
    },
    props: {
        value: {
            type: Boolean,
            default: false
        },
        // 预选的人员列表
        defaultSelected: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dialogVisible: false,
            loading: false,
            personnelList: [],
            selectedPersonnelIds: [], // 存储选中人员的ID数组
            currentDeptId: null
        }
    },
    computed: {
        // 当前部门已选择的人数
        currentDeptSelectedCount() {
            if (!this.personnelList.length) return 0
            const currentDeptIds = this.personnelList.map(person => person.id)
            return this.selectedPersonnelIds.filter(id => currentDeptIds.includes(id)).length
        },
        // 总已选择人数
        totalSelectedCount() {
            return this.selectedPersonnelIds.length
        },
        // 获取已选择的完整人员对象数组
        selectedPersonnelObjects() {
            return this.selectedPersonnelIds.map(id => {
                // 从当前部门列表中查找
                let person = this.personnelList.find(p => p.id === id)
                if (!person) {
                    // 如果当前部门列表中没有，从默认选中中查找
                    person = this.defaultSelected.find(p => p.id === id)
                }
                return person
            }).filter(Boolean) // 过滤掉undefined
        }
    },
    watch: {
        value(newVal) {
            this.dialogVisible = newVal
            if (newVal) {
                // 打开对话框时，恢复默认选中的人员ID
                this.selectedPersonnelIds = this.defaultSelected.map(person => person.id || person.employeeName)
            }
        },
        dialogVisible(newVal) {
            this.$emit('input', newVal)
        }
    },
    methods: {
        // 处理组织架构树节点点击
        handleOrgTreeNodeClick(nodeData) {
            console.log('选择的部门:', nodeData)
            this.currentDeptId = nodeData.id
            this.getPersonnelList(nodeData.id)
        },

        // 获取人员列表
        getPersonnelList(deptId) {
            this.loading = true
            const queryParams = {
                deptId: deptId,
                pageNum: 1,
                pageSize: 1000 // 获取所有人员
            }

            listZjEmployeeInfo(queryParams).then(response => {
                this.personnelList = response.rows || []
                this.loading = false
                // 获取人员列表后自动全选
                this.$nextTick(() => {
                    this.selectAll()
                })
            }).catch(error => {
                console.error('获取人员列表失败:', error)
                this.loading = false
                this.personnelList = []
            })
        },

        // 全选
        selectAll() {
            const newSelections = this.personnelList.map(person => person.id)
            const allSelected = [...new Set([...this.selectedPersonnelIds, ...newSelections])]
            this.selectedPersonnelIds = allSelected
        },

        // 取消全选（只取消当前显示的人员）
        deselectAll() {
            const currentIds = this.personnelList.map(person => person.id)
            this.selectedPersonnelIds = this.selectedPersonnelIds.filter(id => !currentIds.includes(id))
        },

        // 确定选择
        handleConfirm() {
            // 返回完整的人员对象数组，包含id和employeeName
            const selectedPersonnelData = this.selectedPersonnelIds.map(id => {
                // 优先从当前显示的人员列表中查找
                let person = this.personnelList.find(p => p.id === id)
                if (!person) {
                    // 如果当前列表中没有，从默认选中中查找
                    person = this.defaultSelected.find(p => p.id === id)
                }
                if (person) {
                    return {
                        id: person.id,
                        name: person.employeeName || person.name
                    }
                }
                return null
            }).filter(Boolean)

            this.$emit('confirm', selectedPersonnelData)
            this.handleClose()
        },

        // 取消选择
        handleCancel() {
            this.selectedPersonnelIds = this.defaultSelected.map(person => person.id || person.employeeName)
            this.handleClose()
        },

        // 关闭对话框
        handleClose() {
            this.dialogVisible = false
            this.personnelList = []
            this.currentDeptId = null
            // 重置选择状态
            this.selectedPersonnelIds = []
        }
    }
}
</script>

<style scoped lang="scss">
// 弹窗自定义样式
::v-deep .personnel-selector-dialog {
    .el-dialog__body {
        padding: 20px;
        max-height: 70vh;
        overflow: hidden;
    }
}

.personnel-selector {
    height: 500px;
    max-height: 60vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .selector-content {
        display: flex;
        height: 100%;
        overflow: hidden;
        border-radius: 4px;
        border: 1px solid #e4e7ed;

        .left-panel {
            width: 300px;
            min-width: 300px;
            display: flex;
            flex-direction: column;
            background-color: #fafbfc;
            overflow: hidden;

            .org-tree-container {
                height: 100%;
                padding: 15px;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                .tree-title {
                    font-size: 14px;
                    font-weight: bold;
                    margin-bottom: 12px;
                    color: #303133;
                    flex-shrink: 0;
                    border-bottom: 1px solid #e4e7ed;
                    padding-bottom: 8px;
                }

                .tree-content {
                    flex: 1;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;

                    ::v-deep .org-tree {
                        height: 100%;
                        overflow-y: auto !important;
                        overflow-x: hidden;

                        &::-webkit-scrollbar {
                            width: 6px;
                        }

                        &::-webkit-scrollbar-track {
                            background: #f1f1f1;
                            border-radius: 3px;
                        }

                        &::-webkit-scrollbar-thumb {
                            background: #c1c1c1;
                            border-radius: 3px;
                        }

                        &::-webkit-scrollbar-thumb:hover {
                            background: #a8a8a8;
                        }
                    }

                    ::v-deep .tree {
                        height: 100% !important;
                        overflow: hidden !important;
                    }

                    ::v-deep .tree-scroll-container {
                        height: 100% !important;
                        overflow-y: auto !important;
                        overflow-x: hidden !important;
                    }
                }
            }
        }

        .divider {
            width: 1px;
            background-color: #e4e7ed;
            flex-shrink: 0;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background-color: #ffffff;

            .personnel-list-container {
                height: 100%;
                padding: 15px;
                display: flex;
                flex-direction: column;
                overflow: hidden;

                .list-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 12px;
                    flex-shrink: 0;
                    border-bottom: 1px solid #e4e7ed;
                    padding-bottom: 8px;

                    .list-title {
                        font-size: 14px;
                        font-weight: bold;
                        color: #303133;
                    }

                    .list-actions {
                        .el-button {
                            padding: 0;
                            margin-left: 10px;
                        }
                    }
                }

                .personnel-list {
                    flex: 1;
                    overflow-y: auto;
                    overflow-x: hidden;
                    border: 1px solid #e4e7ed;
                    border-radius: 4px;
                    padding: 8px;
                    background-color: #fafafa;

                    .personnel-item {
                        margin-bottom: 4px;
                        padding: 6px 0;
                        border-bottom: 1px solid #f0f0f0;
                        flex-shrink: 0;

                        &:last-child {
                            border-bottom: none;
                        }

                        .person-info {
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            width: 100%;

                            .person-name {
                                font-weight: 500;
                                color: #303133;
                                font-size: 14px;
                                flex-shrink: 0;
                                min-width: 50px;
                                max-width: 80px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .person-details {
                                display: flex;
                                flex-direction: column;
                                align-items: flex-end;
                                text-align: right;
                                flex: 1;
                                margin-left: 8px;
                                min-width: 0;
                                max-width: calc(100% - 90px);

                                .person-dept {
                                    color: #909399;
                                    font-size: 11px;
                                    line-height: 1.2;
                                    width: 100%;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                }

                                .person-post {
                                    color: #67c23a;
                                    font-size: 11px;
                                    line-height: 1.2;
                                    margin-top: 2px;
                                    width: 100%;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                }
                            }
                        }
                    }

                    .empty-text {
                        text-align: center;
                        color: #909399;
                        font-size: 14px;
                        margin-top: 50px;
                    }
                }
            }
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .selected-count {
        color: #409eff;
        font-size: 14px;

        .separator {
            margin: 0 8px;
            color: #dcdfe6;
        }

        .no-selection {
            color: #909399;
        }
    }
}

// 自定义滚动条样式
.personnel-list {
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    // 强制显示滚动条
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

// 优化复选框样式
::v-deep .personnel-list .el-checkbox {
    width: 100%;
    margin-right: 0;
    display: flex !important;
    align-items: flex-start;
    max-width: 100%;
    box-sizing: border-box;

    .el-checkbox__label {
        width: calc(100% - 16px);
        padding-left: 6px;
        line-height: 1.4;
        flex: 1;
        min-width: 0;
        overflow: hidden;
        box-sizing: border-box;
    }

    .el-checkbox__input {
        line-height: 1.4;
        flex-shrink: 0;
        margin-top: 2px;
        width: 16px;
    }
}

// 优化复选框组样式
::v-deep .personnel-list .el-checkbox-group {
    width: 100%;
    box-sizing: border-box;

    .el-checkbox {
        display: flex !important;
        margin-bottom: 0;
        align-items: flex-start;
        width: 100%;
        box-sizing: border-box;
    }
}

// 确保弹窗在不同屏幕尺寸下正常显示
@media (max-height: 800px) {
    ::v-deep .personnel-selector-dialog {
        .el-dialog__body {
            max-height: 65vh;
        }
    }

    .personnel-selector {
        height: 450px;
        max-height: 55vh;
    }
}

@media (max-height: 600px) {
    ::v-deep .personnel-selector-dialog {
        .el-dialog__body {
            max-height: 60vh;
        }
    }

    .personnel-selector {
        height: 400px;
        max-height: 50vh;
    }
}
</style>
