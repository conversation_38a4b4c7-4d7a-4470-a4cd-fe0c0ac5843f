<template>
  <div class="add-work-ticket">
    <div class="header" v-if="!isView">
      <el-button type="primary" @click="submitForm">保 存</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
    <div class="header" v-else>
      <el-button @click="cancel">返 回</el-button>
    </div>
    <div class="form-container">
      <el-form ref="form" :model="localForm" :rules="rules" label-width="140px">
        <el-form-item label="作业票名称" prop="ticketName">
          <el-input
            v-model="localForm.ticketName"
            placeholder="请输入作业票名称"
          />
        </el-form-item>
        <el-form-item label="作业票编号" prop="ticketNumber">
          <el-input
            v-model="localForm.ticketNumber"
            placeholder="请输入作业票编号"
          />
        </el-form-item>
        <el-form-item label="作业负责人" prop="chargePerson">
          <selectPeopleTree
            v-model="localForm.chargePerson"
            :peopleList="peopleList"
            placeholder="请搜索或选择作业负责人"
            @change="handleChargePersonChange"
          ></selectPeopleTree>
        </el-form-item>
        <el-form-item label="作业申请单位" prop="applicantOrgan">
          <el-input
            v-model="localForm.applicantOrgan"
            placeholder="请输入作业申请单位"
          />
        </el-form-item>
        <el-form-item label="作业区域" prop="workArea">
          <selectPeopleTree
            v-model="localForm.workArea"
            :peopleList="workAreaList"
            placeholder="请搜索或选择作业区域"
            @change="handleWorkAreaChange"
            :disabled="isAdd"
          ></selectPeopleTree>
          <!-- <el-input v-model="localForm.workArea" placeholder="请输入所属区域" /> -->
        </el-form-item>
        <el-form-item label="作业地点" prop="homeworkLocation">
          <el-input
            v-model="localForm.homeworkLocation"
            placeholder="请输入作业地点"
          />
        </el-form-item>
        <el-form-item label="是否是承包商作业" prop="contractorJob">
          <!-- <el-input
            v-model="localForm.contractorJob"
            placeholder="请输入是否是承包商作业"
          /> -->
          <el-select
            v-model="localForm.contractorJob"
            placeholder="请选择是否是承包商作业"
          >
            <el-option label="是" value="是" />
            <el-option label="否" value="否" />
          </el-select>
        </el-form-item>
        <el-form-item label="作业单位" prop="workUnit">
          <el-input v-model="localForm.workUnit" placeholder="请输入作业单位" />
        </el-form-item>
        <el-form-item label="所属作业活动" prop="homeworkName">
          <el-input
            v-model="localForm.homeworkName"
            placeholder="请输入所属作业活动"
            :disabled="isAdd"
          />
        </el-form-item>
        <!-- <el-form-item label="作业类型" prop="assignmentType">
          <el-select
            v-model="localForm.assignmentType"
            placeholder="请选择作业类型"
            style="width: 100%"
            disabled
          >
            <el-option label="一级" value="1" />
            <el-option label="二级" value="2" />
            <el-option label="三级" value="3" />
            <el-option label="四级" value="4" />
          </el-select>
        </el-form-item> -->
        <div v-if="this.formType == 'lsyd'">
          <el-form-item label="用电原因" prop="lsydUseElectricityReason">
            <el-input
              v-model="localForm.lsydUseElectricityReason"
              placeholder="请输入用电原因"
            />
          </el-form-item>
          <el-form-item label="用电人" prop="lsydElectricityUsers">
            <selectPeopleTree
              v-model="localForm.lsydElectricityUsers"
              :peopleList="peopleList"
              placeholder="请搜索或选择用电人"
              @change="handleLsydElectricityUsersChange"
            ></selectPeopleTree>
          </el-form-item>
          <!-- <el-form-item label="用电人id" prop="lsydElectricityUsersId">
          <el-input
            v-model="localForm.lsydElectricityUsersId"
            placeholder="请输入用电人id"
          />
        </el-form-item> -->
          <el-form-item label="用电设备名称" prop="lsydElectricalEquipment">
            <el-input
              v-model="localForm.lsydElectricalEquipment"
              placeholder="请输入用电设备名称"
            />
          </el-form-item>
          <el-form-item label="设备额定功率" prop="lsydPowerEquipmentRated">
            <el-input
              v-model="localForm.lsydPowerEquipmentRated"
              placeholder="请输入设备额定功率"
            />
          </el-form-item>
          <el-form-item label="电源接入点" prop="lsydPowerAccessPoint">
            <el-input
              v-model="localForm.lsydPowerAccessPoint"
              placeholder="请输入电源接入点"
            />
          </el-form-item>
          <el-form-item
            label="许可用电功率"
            prop="lsydPermittedPowerConsumption"
          >
            <el-input
              v-model="localForm.lsydPermittedPowerConsumption"
              placeholder="请输入许可用电功率"
            />
          </el-form-item>
          <el-form-item label="工作电压" prop="lsydWorkingVoltage">
            <el-input
              v-model="localForm.lsydWorkingVoltage"
              placeholder="请输入工作电压"
            />
          </el-form-item>
        </div>

        <div v-if="this.formType == 'dh'">
          <el-form-item label="动火作业级别" prop="dhWorkOperationLeve">
            <el-input
              v-model="localForm.dhWorkOperationLeve"
              placeholder="请输入动火作业级别"
            />
          </el-form-item>
          <el-form-item label="动火部位" prop="hotWorkArea">
            <el-input
              v-model="localForm.hotWorkArea"
              placeholder="请输入动火部位"
            />
          </el-form-item>
          <el-form-item label="动火方式" prop="dhWorkOperationType">
            <el-checkbox-group v-model="localForm.dhWorkOperationType">
              <el-checkbox
                v-for="item in fireMethodList"
                :key="item.dictCode"
                :label="item.dictLabel"
                >{{ item.dictLabel }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div v-if="this.formType == 'sx'">
          <el-form-item label="受限空间名称" prop="sxkjRestrictedSpaceName">
            <el-input
              v-model="localForm.sxkjRestrictedSpaceName"
              placeholder="请输入受限空间名称"
            />
          </el-form-item>
          <el-form-item
            label="受限空间内原有介质名称"
            prop="sxkjOriginalMediumName"
          >
            <el-input
              v-model="localForm.sxkjOriginalMediumName"
              placeholder="请输入受限空间内原有介质名称"
            />
          </el-form-item>
        </div>
        <div v-if="this.formType == 'mb'">
          <el-form-item
            label="盲板作业类别"
            prop="mbBlindPlateHomeworkCategory"
          >
            <el-input
              v-model="localForm.mbBlindPlateHomeworkCategory"
              placeholder="请输入盲板作业类别"
            />
          </el-form-item>
          <el-form-item label="设备、管道名称" prop="mbEquipmentPipelineName">
            <el-input
              v-model="localForm.mbEquipmentPipelineName"
              placeholder="请输入设备、管道名称"
            />
          </el-form-item>
          <el-form-item label="管道介质" prop="mbPipelineMedium">
            <el-input
              v-model="localForm.mbPipelineMedium"
              placeholder="请输入管道介质"
            />
          </el-form-item>
          <el-form-item label="管道温度" prop="mbPipeTemperature">
            <el-input
              v-model="localForm.mbPipeTemperature"
              placeholder="请输入管道温度"
            />
          </el-form-item>
          <el-form-item label="管道压力" prop="mbPipelinePressure">
            <el-input
              v-model="localForm.mbPipelinePressure"
              placeholder="请输入管道压力"
            />
          </el-form-item>
          <el-form-item label="盲板材质" prop="mbBlindPlateMaterial">
            <el-input
              v-model="localForm.mbBlindPlateMaterial"
              placeholder="请输入盲板材质"
            />
          </el-form-item>
          <el-form-item label="盲板规格" prop="mbBlindPlateSpecifications">
            <el-input
              v-model="localForm.mbBlindPlateSpecifications"
              placeholder="请输入盲板规格"
            />
          </el-form-item>
          <el-form-item label="盲板编号" prop="mbBlindPlateNumber">
            <el-input
              v-model="localForm.mbBlindPlateNumber"
              placeholder="请输入盲板编号"
            />
          </el-form-item>
          <el-form-item label="盲板位置图" prop="mbBlindPlatePositionUrl">
            <el-input
              v-model="localForm.mbBlindPlatePositionUrl"
              placeholder="请输入盲板位置图"
            />
          </el-form-item>
        </div>

        <div v-if="this.formType == 'dz'">
          <el-form-item label="指挥人员" prop="dzCommanderName">
            <selectPeopleTree
              v-model="localForm.dzCommanderName"
              :peopleList="peopleList"
              placeholder="请搜索或选择指挥人员姓名"
              @change="handleDzCommanderChange"
            ></selectPeopleTree>
          </el-form-item>
          <!-- <el-form-item label="司索人id" prop="dzSisuoRenId">
          <el-input v-model="localForm.dzSisuoRenId" placeholder="请输入司索人id" />
          </el-form-item> -->
          <el-form-item label="司索人名称" prop="dzSisuoRenName">
            <selectPeopleTree
              v-model="localForm.dzSisuoRenName"
              :peopleList="peopleList"
              placeholder="请搜索或选择司索人名称"
              @change="handleDzSisuoRenChange"
            ></selectPeopleTree>
          </el-form-item>
          <el-form-item label="吊具名称" prop="dzLiftingEquipment">
            <el-input
              v-model="localForm.dzLiftingEquipment"
              placeholder="请输入吊具名称"
            />
          </el-form-item>
          <el-form-item label="吊物内容">
            <!-- <editor
              v-model="localForm.dzHangingObjectContent"
              :min-height="192"
            /> -->
            <el-input
              v-model="localForm.dzHangingObjectContent"
              placeholder="请输入吊物内容"
            />
          </el-form-item>
          <el-form-item label="吊物质量" prop="dzHangingWeight">
            <el-input
              v-model="localForm.dzHangingWeight"
              placeholder="请输入吊物质量"
            />
          </el-form-item>
          <!-- :1-一级 2-二级 3-三级 -->
          <el-form-item label="作业级别" prop="dzHomeworkLevel">
            <!-- :1-一级 2-二级 3-三级 -->
            <!-- <el-input
            v-model="localForm.dzHomeworkLevel"
            placeholder="请输入作业级别"
          /> -->
            <el-select
              v-model="localForm.dzHomeworkLevel"
              placeholder="请选择作业级别"
              style="width: 100%"
            >
              <el-option label="一级" value="一级" />
              <el-option label="二级" value="2" />
              <el-option label="三级" value="3" />
            </el-select>
          </el-form-item>
        </div>
        <div v-if="this.formType == 'dt'">
          <!-- 动土作业简图 -->
          <el-form-item
            label="动土作业简图"
            prop="dtExcavationOperationDiagram"
          >
            <ImageUpload
              v-model="localForm.dtExcavationOperationDiagram"
              :limit="1"
            ></ImageUpload>
          </el-form-item>
        </div>
        <div v-if="this.formType == 'dl'">
          <el-form-item label="断路原因" prop="dlCircuitBreakReason">
            <el-input
              v-model="localForm.dlCircuitBreakReason"
              placeholder="请输入断路原因"
            />
          </el-form-item>
          <el-form-item
            label="断路地段相关说明"
            prop="dlBrokenSectionInstructions"
          >
            <el-input
              v-model="localForm.dlBrokenSectionInstructions"
              placeholder="请输入断路地段相关说明"
            />
          </el-form-item>
          <el-form-item label="断路地段示意图" prop="dlBrokenSectionSketchMap">
            <el-input
              v-model="localForm.dlBrokenSectionSketchMap"
              placeholder="请输入断路地段示意图"
            />
          </el-form-item>
          <el-form-item label="涉及相关单位" prop="dlRelatedUnitsInvolved">
            <el-input
              v-model="localForm.dlRelatedUnitsInvolved"
              placeholder="请输入涉及相关单位"
            />
          </el-form-item>
        </div>
        <div v-if="this.formType == 'gc'">
          <!-- ：1-Ⅰ 2-Ⅱ 3-Ⅲ 4-Ⅳ -->
          <el-form-item label="高处作业级别" prop="gcHomeworkLevel">
            <!-- <el-input
            v-model="localForm.gcHomeworkLevel"
            placeholder="请输入高处作业级别"
          /> -->
            <el-select
              v-model="localForm.gcHomeworkLevel"
              placeholder="请选择高处作业级别"
              style="width: 100%"
            >
              <el-option label="一级" value="1" />
              <el-option label="二级" value="2" />
              <el-option label="三级" value="3" />
              <el-option label="四级" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item label="作业高度" prop="gcWorkingAltitude">
            <el-input
              v-model="localForm.gcWorkingAltitude"
              placeholder="请输入作业高度"
            />
          </el-form-item>
        </div>
        <el-form-item label="风险辨识" prop="riskIdentification">
          <!-- <el-input
            v-model="localForm.riskIdentification"
            placeholder="请输入风险辨识"
          /> -->
          <el-checkbox-group v-model="localForm.riskIdentification">
            <el-checkbox
              v-for="item in riskIdentificationList"
              :key="item.dictCode"
              :label="item.dictLabel"
              >{{ item.dictLabel }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <!-- <el-form-item label="作业内容" prop="homeworkContent">
          <editor v-model="localForm.homeworkContent" :min-height="192" />
        </el-form-item> -->

        <el-form-item label="关联设备" prop="linkedDevice">
          <el-input
            v-model="localForm.linkedDevice"
            placeholder="请输入关联设备"
          />
        </el-form-item>
        <el-form-item label="计划开始时间" prop="planBeginTime">
          <el-date-picker
            clearable
            v-model="localForm.planBeginTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划结束时间" prop="planEndTime">
          <el-date-picker
            clearable
            v-model="localForm.planEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <!-- 0-否1-是 -->
        <el-form-item label="是否主票 " prop="isMainTicket">
          <!-- <el-input placeholder="请输入是否主票" /> -->
          <el-select v-model="localForm.isMainTicket" style="width: 100%">
            <el-option label="否" value="0" />
            <el-option label="是" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="监管人姓名" prop="supervisorName">
          <selectPeopleTree
            v-model="localForm.supervisorName"
            :peopleList="peopleList"
            placeholder="请搜索或选择监管人姓名"
            @change="handleSupervisorChange"
          ></selectPeopleTree>
        </el-form-item>
        <el-form-item label="模版名称" prop="templateName">
          <el-input
            v-model="localForm.templateName"
            placeholder="请输入模版名称"
            :disabled="isAdd"
          />
        </el-form-item>
      </el-form>
    </div>
    <!-- <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">保存</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div> -->
  </div>
</template>

<script>
import {
  addZjWorkTicket,
  updateZjWorkTicket,
  getWorkAreaList,
  getDictDataList,
} from "@/api/work/zjWorkTicket";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { listPeople } from "@/api/system/info";
export default {
  name: "addWorkTicket",
  components: {
    selectPeopleTree,
  },
  props: {
    form: {
      type: Object,
      default: () => {},
    },
    isAdd: {
      type: Boolean,
      default: false,
    },
    isView: {
      type: Boolean,
      default: false,
    },

    formType: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      // 表单校验
      rules: {
        // 公共
        ticketName: [
          { required: true, message: "请输入作业票名称", trigger: "change" },
        ],
        ticketNumber: [
          { required: true, message: "请输入作业票编号", trigger: "change" },
        ],
        chargePerson: [
          { required: true, message: "请选择作业负责人", trigger: "change" },
        ],
        applicantOrgan: [
          { required: true, message: "请选择作业申请单位", trigger: "blur" },
        ],
        workArea: [
          { required: true, message: "请选择作业区域", trigger: "blur" },
        ],
        homeworkLocation: [
          { required: true, message: "请选择作业地点", trigger: "blur" },
        ],
        contractorJob: [
          {
            required: true,
            message: "请选择是否是承包商作业",
            trigger: "change",
          },
        ],
        workUnit: [
          { required: true, message: "请输入作业单位", trigger: "blur" },
        ],
        homeworkName: [
          { required: true, message: "请输入所属作业活动", trigger: "blur" },
        ],
        // homeworkContent: [
        //   { required: true, message: "请输入作业内容", trigger: "change" },
        // ],
        riskIdentification: [
          { required: true, message: "请选择风险辨识", trigger: "change" },
        ],

        planBeginTime: [
          { required: true, message: "请选择计划开始时间", trigger: "blur" },
        ],
        planEndTime: [
          { required: true, message: "请选择计划结束时间", trigger: "blur" },
        ],
        // 八大类
        // lsyd
        lsydUseElectricityReason: [
          { required: true, message: "请输入用电原因", trigger: "blur" },
        ],
        lsydElectricityUsers: [
          { required: true, message: "请输入用电人", trigger: "blur" },
        ],
        lsydElectricalEquipment: [
          { required: true, message: "请输入用电设备名称", trigger: "blur" },
        ],
        lsydPowerEquipmentRated: [
          { required: true, message: "请输入设备额定功率", trigger: "blur" },
        ],
        lsydPowerAccessPoint: [
          { required: true, message: "请输入电源接入点", trigger: "blur" },
        ],
        lsydPermittedPowerConsumption: [
          { required: true, message: "请输入许可用电功率", trigger: "blur" },
        ],
        lsydWorkingVoltage: [
          { required: true, message: "请输入工作电压", trigger: "blur" },
        ],

        // dh
        // sx
        // mb
        // dz
        // dt
        // dl
        // gc
        gcHomeworkLevel: [
          { required: true, message: "请选择高处作业级别", trigger: "blur" },
        ],
        gcWorkingAltitude: [
          { required: true, message: "请输入作业高度", trigger: "blur" },
        ],
      },
      peopleList: [],
      workAreaList: [],
      riskIdentificationList: [],
      fireMethodList: [],
      localForm: { ...this.form },
    };
  },
  watch: {
    form: {
      handler(newVal, oldVal) {
        this.localForm = { ...newVal };
      },
      deep: true,
    },
  },

  mounted() {
    this.getPeopleList();
    this.getWorkAreaList();
    // 风险辨识
    this.getRiskIdentification();
    // 动火方式
    this.getFireMethod();
    // console.log(this.localForm, "mounted");
    console.log(this.formType, "formType");
    this.localForm.riskIdentification =
      this.localForm.riskIdentification?.length > 0
        ? this.localForm.riskIdentification
        : [];

    this.localForm.dhWorkOperationType =
      this.localForm.dhWorkOperationType?.length > 0
        ? this.localForm.dhWorkOperationType
        : [];

    // 页面加载时滚动到顶部
    window.scrollTo(0, 0);
    this.$nextTick(() => {
      const formContainer = this.$el.querySelector(".form-container");
      if (formContainer) {
        formContainer.scrollTop = 0;
      }
    });
  },
  methods: {
    // 风险辨识
    getRiskIdentification() {
      const params = {
        pageNum: "1",
        pageSize: "99",
        dictType: "fxbs",
      };
      getDictDataList(params).then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.riskIdentificationList = res.rows;
          // console.log(this.riskIdentificationList);
        }
      });
    },
    // 动火方式
    getFireMethod() {
      const params = {
        pageNum: "1",
        pageSize: "99",
        dictType: "dhfs",
      };
      getDictDataList(params).then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.fireMethodList = res.rows;
        }
      });
    },

    // 获取人员
    getPeopleList() {
      console.log(this.form);
      listPeople().then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.peopleList = res.data;
          // console.log(this.peopleList, "peopleList");
        }
      });
    },
    // 获取作业区域
    getWorkAreaList() {
      getWorkAreaList().then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.workAreaList = res.data;
          // console.log(this.workAreaList, "workAreaList");
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // console.log(this.localForm);

          this.localForm.riskIdentification =
            this.localForm.riskIdentification?.length > 0
              ? this.localForm.riskIdentification.join(",")
              : "";
          this.localForm.dhWorkOperationType =
            this.localForm.dhWorkOperationType?.length > 0
              ? this.localForm.dhWorkOperationType.join(",")
              : "";

          if (this.localForm.id != null) {
            updateZjWorkTicket(this.localForm).then((res) => {
              this.$modal.msgSuccess("保存成功");
              //   this.open = false;
              this.$emit("refreshList");
            });
          } else {
            addZjWorkTicket(this.localForm).then((res) => {
              this.$modal.msgSuccess("保存成功");
              //   this.open = false;
              this.$emit("refreshList");
            });
          }
        }
      });
    },
    handleChargePersonChange(selectedItem) {
      if (selectedItem) {
        this.localForm.chargePersonId = selectedItem.id;
        this.localForm.chargePerson = selectedItem.label;
      } else {
        this.localForm.chargePersonId = null;
        this.localForm.chargePerson = null;
      }
    },
    handleWorkAreaChange(selectedItem) {
      if (selectedItem) {
        this.localForm.workAreaId = selectedItem.id;
        this.localForm.workArea = selectedItem.label;
      } else {
        this.localForm.workAreaId = null;
        this.localForm.workArea = null;
      }
    },
    handleLsydElectricityUsersChange(selectedItem) {
      if (selectedItem) {
        this.localForm.lsydElectricityUsersId = selectedItem.id;
        this.localForm.lsydElectricityUsers = selectedItem.label;
      } else {
        this.localForm.lsydElectricityUsersId = null;
        this.localForm.lsydElectricityUsers = null;
      }
    },
    handleSupervisorChange(selectedItem) {
      if (selectedItem) {
        this.localForm.supervisorId = selectedItem.id;
        this.localForm.supervisorName = selectedItem.label;
      } else {
        this.localForm.supervisorId = null;
        this.localForm.supervisorName = null;
      }
    },
    handleDzCommanderChange(selectedItem) {
      if (selectedItem) {
        this.localForm.dzCommanderId = selectedItem.id;
        this.localForm.dzCommanderName = selectedItem.label;
      } else {
        this.localForm.dzCommanderId = null;
        this.localForm.dzCommanderName = null;
      }
    },
    handleDzSisuoRenChange(selectedItem) {
      if (selectedItem) {
        this.localForm.dzSisuoRenId = selectedItem.id;
        this.localForm.dzSisuoRenName = selectedItem.label;
      } else {
        this.localForm.dzSisuoRenId = null;
        this.localForm.dzSisuoRenName = null;
      }
    },

    cancel() {
      this.reset();
      this.$emit("close");
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ticketName: null,
        chargePerson: null,
        ticketNumber: null,
        applicantOrgan: null,
        workArea: null,
        homeworkLocation: null,
        contractorJob: null,
        workUnit: null,
        lsydUseElectricityReason: null,
        lsydElectricityUsers: null,
        lsydElectricityUsersId: null,
        lsydElectricalEquipment: null,
        lsydPowerEquipmentRated: null,
        lsydPowerAccessPoint: null,
        lsydPermittedPowerConsumption: null,
        lsydWorkingVoltage: null,
        homeworkContent: null,
        riskIdentification: [],
        linkedDevice: null,
        planBeginTime: null,
        planEndTime: null,
        isMainTicket: null,
        dhWorkOperationLeve: null,
        dhWorkOperationType: null,
        hotWorkArea: null,
        sxkjRestrictedSpaceName: null,
        sxkjOriginalMediumName: null,
        mbBlindPlateHomeworkCategory: null,
        mbEquipmentPipelineName: null,
        mbPipelineMedium: null,
        mbPipeTemperature: null,
        mbPipelinePressure: null,
        mbBlindPlateMaterial: null,
        mbBlindPlateSpecifications: null,
        mbBlindPlateNumber: null,
        mbBlindPlatePositionUrl: null,
        supervisorName: null,
        supervisorId: null,
        dzCommanderId: null,
        dzCommanderName: null,
        dzSisuoRenId: null,
        dzSisuoRenName: null,
        dzLiftingEquipment: null,
        dzHangingObjectContent: null,
        dzHangingWeight: null,
        dzHomeworkLevel: null,
        dlCircuitBreakReason: null,
        dlBrokenSectionInstructions: null,
        dlBrokenSectionSketchMap: null,
        dlRelatedUnitsInvolved: null,
        gcHomeworkLevel: null,
        gcWorkingAltitude: null,
        templateName: null,
      };
      this.resetForm("form");
    },
  },
};
</script>

<style scoped>
.add-work-ticket {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);

  min-height: 600px;
  overflow: hidden;
}

.form-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #fff;
  margin: 30px;
  margin-bottom: 0;
  width: 700px;
}

/* 表单滚动区域样式 */
.form-container::-webkit-scrollbar {
  width: 6px;
  display: none;
}

.form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 表单项目间距调整 */
.el-form-item {
  margin-bottom: 18px;
}

/* 确保选择器组件宽度正确 */
::v-deep .el-select {
  width: 100%;
}

::v-deep .el-date-editor {
  width: 100%;
}

/* 底部按钮区域 */
.dialog-footer {
  padding: 20px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  text-align: right;
  margin: 0 -20px;
  margin-top: auto;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-work-ticket {
    height: auto;
    min-height: 100vh;
  }

  .form-container {
    padding: 15px;
    margin: -15px;
    margin-bottom: 0;
  }

  .dialog-footer {
    padding: 15px;
    margin: 0 -15px;
  }

  ::v-deep .el-form-item__label {
    width: 120px !important;
  }

  ::v-deep .el-form-item__content {
    margin-left: 120px !important;
  }
}

@media (max-width: 480px) {
  .form-container {
    padding: 10px;
    margin: -10px;
    margin-bottom: 0;
  }

  .dialog-footer {
    padding: 10px;
    margin: 0 -10px;
  }

  ::v-deep .el-form-item__label {
    width: 100px !important;
  }

  ::v-deep .el-form-item__content {
    margin-left: 100px !important;
  }
}

/* 确保编辑器组件高度适应 */
::v-deep .w-e-text-container {
  min-height: 192px !important;
  max-height: 300px;
  overflow-y: auto;
}

/* 表单分组样式（可选） */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.form-section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.header {
  margin-bottom: 30px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  position: absolute;
  width: 101.5%;
  left: -20px;
  top: 0;
  z-index: 100;
}
.back-btn {
  background-color: #fff;
  color: #000;
  font-weight: 600;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 10px 20px;
}
</style>
