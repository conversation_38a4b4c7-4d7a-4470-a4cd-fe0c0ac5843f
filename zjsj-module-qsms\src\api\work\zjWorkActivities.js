import request from '@/utils/request'

// 查询工作活动列表
export function listZjWorkActivities(query) {
  return request({
    url: '/work/zjWorkActivities/list',
    method: 'get',
    params: query
  })
}

// 查询工作活动详细
export function getZjWorkActivities(id) {
  return request({
    url: '/work/zjWorkActivities/' + id,
    method: 'get'
  })
}

// 新增工作活动
export function addZjWorkActivities(data) {
  return request({
    url: '/work/zjWorkActivities',
    method: 'post',
    data: data
  })
}

// 修改工作活动
export function updateZjWorkActivities(data) {
  return request({
    url: '/work/zjWorkActivities',
    method: 'put',
    data: data
  })
}

// 删除工作活动
export function delZjWorkActivities(id) {
  return request({
    url: '/work/zjWorkActivities/' + id,
    method: 'delete'
  })
}
