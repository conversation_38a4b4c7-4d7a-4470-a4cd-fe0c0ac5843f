import request from '@/utils/request'

// 查询项目安全员列表
export function listOfficers(query) {
  return request({
    url: '/system/officers/list',
    method: 'get',
    params: query
  })
}

// 查询项目安全员详细
export function getOfficers(id) {
  return request({
    url: '/system/officers/' + id,
    method: 'get'
  })
}

// 新增项目安全员
export function addOfficers(data) {
  return request({
    url: '/system/officers',
    method: 'post',
    data: data
  })
}

// 修改项目安全员
export function updateOfficers(data) {
  return request({
    url: '/system/officers',
    method: 'put',
    data: data
  })
}

// 删除项目安全员
export function delOfficers(id) {
  return request({
    url: '/system/officers/' + id,
    method: 'delete'
  })
}
