<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
      style="display: flex; justify-content: left; align-items: center"
    >
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!-- v-hasPermi="['inspection:zjNewsInfo:add']" -->
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjNewsInfo:edit']"
          >修改</el-button
        >
      </el-col> -->
      <!-- v-hasPermi="['inspection:zjNewsInfo:remove']" -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >批量删除</el-button
        >
      </el-col>
      <!-- v-hasPermi="['inspection:zjNewsInfo:export']" -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjNewsInfoList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 280px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" />
      <el-table-column
        label="姓名"
        align="center"
        prop="name"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="性别" align="center" prop="gender">
        <template slot-scope="scope">
          <span v-if="scope.row.gender == 1">男</span>
          <span v-else-if="scope.row.gender == 2">女</span>
          <span v-else></span>
        </template>
      </el-table-column>
      <el-table-column
        label="出生日期"
        align="center"
        prop="birthDate"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <!-- 

      <el-table-column label="是否置顶" align="center" prop="isTop">
        <template slot-scope="scope">
          <span v-if="scope.row.isTop === 0">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column> -->

      <!-- <el-table-column label="附件url" align="center" prop="attachmentUrl">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.attachmentUrl"
            size="mini"
            type="text"
            @click="handleViewAttachment(scope.row.attachmentUrl)"
            >查看</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <!-- v-hasPermi="['inspection:zjNewsInfo:remove']" -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      style="height: 800px; overflow: hidden"
      append-to-body
      class="operateDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        class="formDialog"
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="
          width: 100%;
          margin: 0 auto;
          padding: 20px;
          overflow: auto;
          height: 600px;
        "
      >
        <el-collapse
          expand-icon-position="left"
          v-model="activeNames"
          :accordion="false"
          isActive
        >
          <el-collapse-item title="个人信息" name="1">
            <template #title="{ isActive }">
              <div style="width: 5px; height: 40%; background-color: #4680ff">
                &nbsp;
              </div>
              {{ isActive }}
              <div style="margin-left: 4px">个人信息</div>
            </template>
            <el-row :gutter="20">
              <el-col :span="12" style="padding: 0px">
                <!-- <el-form-item
                  label="编号"
                  prop="code"
                  class="formDialogStyle formLeftStyle"
                >
                  <el-input v-model="form.code"></el-input>
                </el-form-item> -->
                <el-form-item
                  label="姓名"
                  prop="name"
                  class="formDialogStyle formLeftStyle"
                >
                  <el-input v-model="form.name"></el-input>
                  <!-- <el-select v-model="form.name" placeholder="">
                    <el-option label="示例姓名1" value="示例姓名1"></el-option>
                    <el-option label="示例姓名2" value="示例姓名2"></el-option>
                  </el-select> -->
                </el-form-item>
                <!-- <el-form-item
                  label="车间"
                  prop="workshop"
                  class="formDialogStyle"
                >
                  <el-select v-model="form.workshop" placeholder="">
                    <el-option label="车间1" value="车间1"></el-option>
                    <el-option label="车间2" value="车间2"></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item
                  label="性别"
                  prop="gender"
                  class="formDialogStyle"
                >
                  <el-radio-group v-model="form.gender">
                    <el-radio :label="1">男</el-radio>
                    <el-radio :label="2">女</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="体重（公斤）"
                  prop="weight"
                  class="formDialogStyle formBottomStyle"
                >
                  <el-input v-model="form.weight" type="number"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" style="padding: 0px">
                <!-- <el-form-item
                  label="所属厂区"
                  prop="factoryArea"
                  class="formDialogStyle formRightStyle"
                >
                  <el-select v-model="form.factoryArea" placeholder="">
                    <el-option label="厂区1" value="厂区1"></el-option>
                    <el-option label="厂区2" value="厂区2"></el-option>
                  </el-select>
                </el-form-item> -->
                <!-- <el-form-item
                  label="账号"
                  prop="account"
                  class="formDialogStyle"
                >
                  <el-select v-model="form.account" placeholder="">
                    <el-option label="账号1" value="账号1"></el-option>
                    <el-option label="账号2" value="账号2"></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item
                  label="岗位"
                  prop="position"
                  class="formDialogStyle"
                >
                  <el-input v-model="form.position"></el-input>
                  <!-- <el-select v-model="form.position" placeholder="">
                    <el-option label="岗位1" value="岗位1"></el-option>
                    <el-option label="岗位2" value="岗位2"></el-option>
                  </el-select> -->
                </el-form-item>
                <el-form-item
                  label="出生日期"
                  prop="birthDate"
                  class="formDialogStyle formBottomStyle"
                >
                  <el-date-picker
                    v-model="form.birthDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker>
                </el-form-item>
                <!-- <el-form-item
                  label="安全科"
                  prop="safetyDepartment"
                  class="formDialogStyle formBottomStyle"
                >
                  <el-select v-model="form.safetyDepartment" placeholder="">
                    <el-option label="安全科1" value="安全科1"></el-option>
                    <el-option label="安全科2" value="安全科2"></el-option>
                  </el-select>
                </el-form-item> -->
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template #title="{ isActive }">
              <div style="width: 5px; height: 40%; background-color: #4680ff">
                &nbsp;
              </div>
              {{ isActive }}
              <div style="margin-left: 4px">健康基本信息</div>
            </template>
            <div class="formTopStyle formTitle">
              一、个人生活史:（如有 "是" 或 "否" 请在 "□" 内打√）
            </div>
            <el-row :gutter="20">
              <el-col :span="24" style="padding: 0px">
                <el-form-item
                  label="吸烟"
                  prop="smokingStatus"
                  class="formDialogStyle formLeftStyle"
                >
                  <el-radio-group
                    v-model="form.smokingStatus"
                    @change="getInfo"
                  >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                    <el-radio :label="3">已戒</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="饮酒"
                  prop="drinkingStatus"
                  class="formDialogStyle formLeftStyle"
                >
                  <el-radio-group v-model="form.drinkingStatus">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="formTopStyle formTitle">二、目前患有何种慢性疾病：</div>
            <el-row :gutter="20">
              <el-col :span="24" style="padding: 0px">
                <el-form-item
                  label="你是否患有肝病、肾病、胰腺炎、前列腺、肿瘤（癌症）或其它疾病文本内容"
                  prop="chronicDisease"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.chronicDisease">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="formTopStyle formTitle">三、目前身体状态征询：</div>
            <el-row :gutter="20">
              <el-col :span="24" style="padding: 0px">
                <el-form-item
                  label="1、你听力较差吗?"
                  prop="hearingProblem"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.hearingProblem">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="2、你是否经常有耳鸣现象?"
                  prop="tinnitus"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.tinnitus">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="3、有时你会流鼻血吗?"
                  prop="nosebleed"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.nosebleed">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="4、你是否有常年腹泻的现象?"
                  prop="diarrhea"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.diarrhea">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="5、你是否常感到关节肿痛?"
                  prop="jointPain"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.jointPain">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="6、你经常失眠吗?"
                  prop="insomnia"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.insomnia">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="四：目前有无长期服药史(连续服药3个月以上)"
                  prop="longTermMedication"
                  class="formDialogStyle formLeftStyle formLongTitleText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.longTermMedication">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="五：你对自身健康状况是否了解："
                  prop="healthAwareness"
                  class="formDialogStyle formLeftStyle formLongTitleText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.healthAwareness">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="六：你的体检次数："
                  prop="physicalExamFrequency"
                  class="formDialogStyle formLeftStyle formLongTitleText formBottomStyle"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.physicalExamFrequency">
                    <el-radio :label="1">一年一次</el-radio>
                    <el-radio :label="2">半年一次</el-radio>
                    <el-radio :label="3">三月一次</el-radio>
                    <el-radio :label="4">基本不参加</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item name="3">
            <template #title="{ isActive }">
              <div style="width: 5px; height: 40%; background-color: #4680ff">
                &nbsp;
              </div>
              {{ isActive }}
              <div style="margin-left: 4px">病史征询</div>
            </template>

            <el-row :gutter="20">
              <el-col :span="24" style="padding: 0px">
                <el-form-item
                  label="1、你是否有高血压、高血脂、糖尿病?"
                  prop="hypertensionDiabetes"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.hypertensionDiabetes">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="2、你是否有胸闷、经常性头晕、眼冒金星或发黑、牙龈出血、口臭现象?"
                  prop="chestDiscomfort"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.chestDiscomfort">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <!-- <el-form-item
                  label="3、有时你会流鼻血吗?"
                  prop="chronicDiseaseDetail"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.chronicDiseaseDetail">
                    <el-radio label="是">是</el-radio>
                    <el-radio label="否">否</el-radio>
                  </el-radio-group>
                </el-form-item> -->
                <el-form-item
                  label="3、你是否有贫血、低血压、紫癜、高胆固醇、胆囊炎?"
                  prop="anemiaEtc"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <!-- <span class="asterisk" style="color: red">*</span> -->
                  <!-- <template #prefix>
                    <span class="asterisk" style="color: red">*</span>
                  </template> -->
                  <el-radio-group v-model="form.anemiaEtc">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="4、你现在是否患有非传染性疾病，该病在情绪波动较大或高强度工作过程中有可能导致发病、病情加重或危及生命安全的情况。如:心脏病、哮喘、癫痫等?"
                  prop="nonInfectiousDisease"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.nonInfectiousDisease">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="5、你是否有肢体抽筋(或酸痛 )、脱发、指甲发白或有斑点?"
                  prop="bodyCramps"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.bodyCramps">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="你是否有气虚(虚弱无力)、焦躁不安、易疲劳、易倦感?"
                  prop="qiDeficiency"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.qiDeficiency">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="你曾有过痉挛或癫痫的现象吗?"
                  prop="convulsionEpilepsy"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.convulsionEpilepsy">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="你曾否受伤造成骨折或骨裂现象?"
                  prop="fractureHistory"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.fractureHistory">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div
                  class="formTopStyle formTitle"
                  style="font-weight: 550; font-size: 14px; display: flex"
                >
                  <div style="color: #ff4949; margin-left: 5px">*</div>
                  <div style="margin-left: 4px">
                    6、是否曾患过下列传染性疾病:
                  </div>
                </div>
                <!-- <el-form-item
                  label=" 6、是否曾患过下列传染性疾病:"
                  class="formDialogStyle formLongText"
                  label-width="120%"
                >
                </el-form-item> -->
                <el-form-item
                  label="乙型肝炎表面抗原阳性"
                  prop="hepatitisB"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.hepatitisB">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="丙型肝炎抗体阳性"
                  prop="hepatitisC"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.hepatitisC">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="肺结核"
                  prop="tuberculosis"
                  class="formDialogStyle formLeftStyle formLongText"
                  label-width="600px"
                >
                  <el-radio-group v-model="form.tuberculosis">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="体检报告"
                  prop="medicalReport"
                  class="formTopStyle formLongText formBottomStyle formFileUpload"
                  label-width="600px"
                >
                  <file-upload
                    v-model="form.medicalReport"
                    :isShowTip="false"
                    :fileType="['pdf', 'doc', 'docx']"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <div
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 10px;
        "
      >
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </div>
    </el-dialog>
    <!-- 附件信息弹窗 -->
    <el-dialog
      title="附件信息"
      :visible.sync="attachmentDialogVisible"
      width="40%"
      append-to-body
    >
      <div v-for="(item, index) in attachmentList" :key="index">
        <div class="demo-list-item__name">
          附件{{ index + 1 }}:
          <span @click="handleView(item)" class="attachment-item">
            <i class="el-icon-document"></i>
            {{ item.split("/").pop() }}
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjNewsInfo,
  getZjNewsInfo,
  delZjNewsInfo,
  addZjNewsInfo,
  updateZjNewsInfo,
} from "@/api/inspection/zjNewsInfo";
import {
  healthList,
  getHealthDetail,
  addHealth,
  updateHealth,
  delHealth,
  exportHealth,
} from "@/api/healthRecord/index";

export default {
  name: "ZjNewsInfo",
  data() {
    return {
      activeNames: ["1", "2", "3", "4"],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻管理表格数据
      zjNewsInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 弹出层是否显示附件信息
      attachmentDialogVisible: false,
      // 附件列表
      attachmentList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
      },
      // 表单参数
      form: {
        id: "",
        // 姓名
        name: "",
        // 所属厂区
        // factoryArea: "",
        // 账号
        // account: "",
        // 车间
        // workshop: "",
        // 岗位
        position: "",
        // 性别，1-男，2-女
        gender: "",
        // 出生日期
        birthDate: "",
        // 体重（公斤）
        weight: "",
        // 安全科
        // safetyDepartment: "",
        // 吸烟状态，1-是，2-否，3-已戒
        smokingStatus: "",
        // 饮酒状态，1-是，2-否
        drinkingStatus: "",
        // 是否患有肝病、肾病等慢性疾病，1-是，2-否
        chronicDisease: "",
        // 听力较差吗，1-是，2-否
        hearingProblem: "",
        // 经常有耳鸣现象吗，1-是，2-否
        tinnitus: "",
        // 有时会流鼻血吗，1-是，2-否
        nosebleed: "",
        // 有常年腹泻的现象吗，1-是，2-否
        diarrhea: "",
        // 常感到关节肿痛吗，1-是，2-否
        jointPain: "",
        // 经常失眠吗，1-是，2-否
        insomnia: "",
        // 目前有无长期服药史(连续服药3个月以上)，1-是，2-否
        longTermMedication: "",
        // 对自身健康状况是否了解，1-是，2-否
        healthAwareness: "",
        // 体检次数，1-一年一次，2-半年一次，3-三月一次，4-基本不参加
        physicalExamFrequency: "",
        // 是否有高血压、高血脂、糖尿病，1-是，2-否
        hypertensionDiabetes: "",
        // 是否有胸闷、经常性头晕等情况，1-是，2-否
        chestDiscomfort: "",
        // 是否有贫血、低血压等情况，1-是，2-否
        anemiaEtc: "",
        // 是否患有非传染性疾病，1-是，2-否
        nonInfectiousDisease: "",
        // 是否有肢体抽筋（或酸痛）等情况，1-是，2-否
        bodyCramps: "",
        // 是否有气虚（虚弱无力）等情况，1-是，2-否
        qiDeficiency: "",
        // 是否有过痉挛或癫痫的现象，1-是，2-否
        convulsionEpilepsy: "",
        // 是否受伤造成骨折或骨裂现象，1-是，2-否
        fractureHistory: "",
        // 是否曾患乙型肝炎表面抗原阳性，1-是，2-否
        hepatitisB: "",
        // 是否曾患丙型肝炎抗体阳性，1-是，2-否
        hepatitisC: "",
        // 是否曾患肺结核，1-是，2-否
        tuberculosis: "",
        // 体检报告附件路径
        medicalReport: "",
      },
      // 表单校验
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        // factoryArea: [
        //   { required: true, message: "请选择所属厂区", trigger: "change" },
        // ],
        // account: [{ required: true, message: "请选择账号", trigger: "change" }],
        gender: [{ required: true, message: "请选择性别", trigger: "change" }],
        birthDate: [
          { required: true, message: "请选择出生日期", trigger: "change" },
        ],
        weight: [{ required: true, message: "请输入体重", trigger: "blur" }],
        // safetyDepartment: [
        //   { required: true, message: "请选择安全科", trigger: "change" },
        // ],
        smokingStatus: [
          { required: true, message: "请选择吸烟状态", trigger: "change" },
        ],
        drinkingStatus: [
          { required: true, message: "请选择饮酒状态", trigger: "change" },
        ],
        chronicDisease: [
          { required: true, message: "请选择慢性疾病", trigger: "change" },
        ],
        hearingProblem: [
          { required: true, message: "请选择听力状况", trigger: "change" },
        ],
        tinnitus: [
          { required: true, message: "请选择耳鸣状况", trigger: "change" },
        ],
        nosebleed: [
          { required: true, message: "请选择流鼻血状况", trigger: "change" },
        ],
        diarrhea: [
          { required: true, message: "请选择腹泻状况", trigger: "change" },
        ],
        jointPain: [
          { required: true, message: "请选择关节肿痛状况", trigger: "change" },
        ],
        insomnia: [
          { required: true, message: "请选择失眠状况", trigger: "change" },
        ],
        longTermMedication: [
          { required: true, message: "请选择服药史状况", trigger: "change" },
        ],
        healthAwareness: [
          { required: true, message: "请选择健康状况", trigger: "change" },
        ],
        physicalExamFrequency: [
          { required: true, message: "请选择体检次数", trigger: "change" },
        ],
        hypertensionDiabetes: [
          { required: true, message: "请选择三高状况", trigger: "change" },
        ],
        chestDiscomfort: [
          { required: true, message: "请选择胸闷头晕状况", trigger: "change" },
        ],
        anemiaEtc: [
          {
            required: true,
            message: "请选择贫血、低血压状况",
            trigger: "change",
          },
        ],
        nonInfectiousDisease: [
          {
            required: true,
            message: "请选择非传染性状况",
            trigger: "change",
          },
        ],
        bodyCramps: [
          {
            required: true,
            message: "请选择肢体状况",
            trigger: "change",
          },
        ],
        qiDeficiency: [
          {
            required: true,
            message: "请选择气虚状况",
            trigger: "change",
          },
        ],
        convulsionEpilepsy: [
          {
            required: true,
            message: "请选择痉挛癫痫状况",
            trigger: "change",
          },
        ],
        fractureHistory: [
          {
            required: true,
            message: "请选择骨折状况",
            trigger: "change",
          },
        ],
        hepatitisB: [
          {
            required: true,
            message: "请选择乙肝状况",
            trigger: "change",
          },
        ],
        hepatitisC: [
          {
            required: true,
            message: "请选择丙肝状况",
            trigger: "change",
          },
        ],
        tuberculosis: [
          {
            required: true,
            message: "请选择肺结核状况",
            trigger: "change",
          },
        ],
        medicalReport: [
          {
            required: true,
            message: "请上传附件",
            trigger: "change",
          },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.editorRef && this.$refs.editorRef.quill) {
        console.log("this.$refs.editorRef.quill", this.$refs.editorRef.quill);
        this.$refs.editorRef.quill.on(
          "text-change",
          (delta, oldDelta, source) => {
            console.log("text-change", delta, oldDelta, source);
            if (source === "user") {
              this.checkForPastedImages();
            }
          }
        );
      }
    });
  },
  methods: {
    /** 图片上传失败回调 */
    handleImageUploadError(error) {
      this.$message.error("图片上传失败: " + error.message);
    },

    handleViewAttachment(value) {
      this.attachmentDialogVisible = true;
      console.log(value, "附件");
      this.attachmentList = value.split(",");
    },
    async handleView(value) {
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    /** 查询新闻管理列表 */
    getList() {
      this.loading = true;
      healthList(this.queryParams).then((res) => {
        console.log("获取数组", res);
        this.zjNewsInfoList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: "",
        // 姓名
        name: "",
        // 所属厂区
        // factoryArea: "",
        // 账号
        // account: "",
        // 车间
        // workshop: "",
        // 岗位
        position: "",
        // 性别，1-男，2-女
        gender: "",
        // 出生日期
        birthDate: "",
        // 体重（公斤）
        weight: "",
        // 安全科
        // safetyDepartment: "",
        // 吸烟状态，1-是，2-否，3-已戒
        smokingStatus: "",
        // 饮酒状态，1-是，2-否
        drinkingStatus: "",
        // 是否患有肝病、肾病等慢性疾病，1-是，2-否
        chronicDisease: "",
        // 听力较差吗，1-是，2-否
        hearingProblem: "",
        // 经常有耳鸣现象吗，1-是，2-否
        tinnitus: "",
        // 有时会流鼻血吗，1-是，2-否
        nosebleed: "",
        // 有常年腹泻的现象吗，1-是，2-否
        diarrhea: "",
        // 常感到关节肿痛吗，1-是，2-否
        jointPain: "",
        // 经常失眠吗，1-是，2-否
        insomnia: "",
        // 目前有无长期服药史(连续服药3个月以上)，1-是，2-否
        longTermMedication: "",
        // 对自身健康状况是否了解，1-是，2-否
        healthAwareness: "",
        // 体检次数，1-一年一次，2-半年一次，3-三月一次，4-基本不参加
        physicalExamFrequency: "",
        // 是否有高血压、高血脂、糖尿病，1-是，2-否
        hypertensionDiabetes: "",
        // 是否有胸闷、经常性头晕等情况，1-是，2-否
        chestDiscomfort: "",
        // 是否有贫血、低血压等情况，1-是，2-否
        anemiaEtc: "",
        // 是否患有非传染性疾病，1-是，2-否
        nonInfectiousDisease: "",
        // 是否有肢体抽筋（或酸痛）等情况，1-是，2-否
        bodyCramps: "",
        // 是否有气虚（虚弱无力）等情况，1-是，2-否
        qiDeficiency: "",
        // 是否有过痉挛或癫痫的现象，1-是，2-否
        convulsionEpilepsy: "",
        // 是否受伤造成骨折或骨裂现象，1-是，2-否
        fractureHistory: "",
        // 是否曾患乙型肝炎表面抗原阳性，1-是，2-否
        hepatitisB: "",
        // 是否曾患丙型肝炎抗体阳性，1-是，2-否
        hepatitisC: "",
        // 是否曾患肺结核，1-是，2-否
        tuberculosis: "",
        // 体检报告附件路径
        medicalReport: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增健康档案";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHealthDetail(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改健康档案";
      });
    },
    temporarySave() {
      this.open = false;
    },
    /** 提交按钮 */
    submitForm(type) {
      console.log("提交清单", this.form);
      // this.form.newStatus = type;
      // this.form.publishTime = new Date();
      this.$refs["form"].validate((valid, fields) => {
        console.log("valid", fields);
        if (valid) {
          if (this.form.id != null && this.form.id != "") {
            updateHealth(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHealth(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        } else {
          const obj = Object.keys(fields)[0];
          const firstValueList = fields[obj];
          this.$message.error("" + firstValueList[0].message);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // console.log("批量删除", row);
      const ids = [];
      ids.push(row.id || this.ids);
      this.$modal
        .confirm("是否确认删除?")
        .then(function () {
          return delHealth(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/form/export",
        {
          ...this.queryParams,
        },
        `健康档案_${new Date().getTime()}.xlsx`
      );
    },
    handleImageUploadSuccess() {},
    getInfo() {
      // console.log("getInfo", this.form.smokingStatus);
    },
    cancleSave() {},
  },
};
</script>
<style scoped lang="scss">
::v-deep .demo-list-item__name {
  .attachment-item {
    cursor: pointer;
    color: #409eff;
    &:hover {
      text-decoration-line: underline;
    }
  }
}
::v-deep .formDialog .el-form-item__label {
  color: #303133;
  padding: 4px;
  // height: 60px;
  border-right: 1px solid #606266 !important;
}
.operateDialog ::v-deep .el-dialog__body {
  padding: 10px 20px !important;
}
::v-deep .formDialog .el-form-item__content {
  padding: 4px 0 0 4px;
}
::v-deep .formFileUpload .el-form-item__content {
  border-left: 1px solid;
}
::v-deep .formFileUpload .el-form-item__label {
  border-right: 0px !important;
}

::v-deep .el-select {
  width: 100% !important;
}

::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
.formDialogStyle {
  border-top: 1px solid #8a8787;
  border-right: 1px solid #8a8787;
  padding: 0px 12px;
  // height: 60px;
}
.formTopStyle {
  border-top: 1px solid #8a8787;
  padding: 0px 12px;
}
// .formRightStyle {
//   border-right: 1px solid #8a8787;
// }
.formBottomStyle {
  border-bottom: 1px solid #8a8787;
}
.formLeftStyle {
  border-left: 1px solid #8a8787;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0px;
  font-size: 13px;
  color: #303133;
  border-left: 1px solid #8a8787;
  border-right: 1px solid #8a8787 !important;
}
::v-deep .formDialog .el-form-item {
  margin-bottom: 0px !important;
}
::v-deep .formDialog .el-form-item {
  margin-bottom: 0px !important;
  text-align: left;
}
::v-deep .formDialog .formLongText .el-form-item__label {
  margin-bottom: 0px !important;
  text-align: left;
}
::v-deep .formDialog .formLongTitleText .el-form-item__label {
  margin-bottom: 0px !important;
  text-align: left;
  color: #303133;
  font-size: 18px;
  // font-weight: 550;
}
.formTitle {
  display: flex;
  justify-content: left;
  align-items: center;
  height: 40px;
  color: #303133;
  font-size: 18px;
  // font-weight: 550;
}
::v-deep .el-form-item__error {
  display: none;
  color: #ff4949;
  font-size: 10px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 65% !important;
  // top: 27% !important;
  // left: 50% !important;
  left: 1% !important;
}
</style>
