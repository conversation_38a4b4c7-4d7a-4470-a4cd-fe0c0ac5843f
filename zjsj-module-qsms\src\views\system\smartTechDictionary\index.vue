<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="技术标准名称" prop="techName">
        <el-input
          v-model="queryParams.techName"
          placeholder="请输入技术标准名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="技术分类" prop="techCategory">
        <el-select
          v-model="queryParams.techCategory"
          placeholder="请选择技术分类"
          style="width: 100%"
        >
          <el-option
            v-for="item in techCategoryOptions"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用" prop="isActive">
        <el-select
          v-model="queryParams.isActive"
          placeholder="请选择是否启用"
          style="width: 100%"
        >
          <el-option
            v-for="item in isActiveOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:smartTechDictionary:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:smartTechDictionary:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:smartTechDictionary:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="smartTechDictionaryList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="技术标准名称" align="center" prop="techName" />
      <el-table-column label="技术分类" align="center" prop="techCategory">
        <template slot-scope="scope">
          <span>{{
            getDictLabel(techCategoryOptions, scope.row.techCategory) ||
            scope.row.techCategory ||
            "-"
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="技术描述" align="center" prop="description" /> -->
      <!-- <el-table-column label="评分规则" align="center" prop="scoringRule" /> -->
      <el-table-column
        label="是否启用"
        align="center"
        prop="isActive"
        :formatter="formatStatus"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:smartTechDictionary:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:smartTechDictionary:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改智能技术字典对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-form-item label="技术标准名称" prop="techName">
          <el-input
            v-model="form.techName"
            placeholder="请输入技术标准名称"
            :disabled="isCheck"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="技术分类" prop="techCategory">
          <el-select
            v-model="form.techCategory"
            placeholder="请选择技术分类"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in techCategoryOptions"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="技术描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入技术描述"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="评分规则" prop="scoringRule">
          <el-input
            v-model="form.scoringRule"
            type="textarea"
            placeholder="请输入评分规则"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="isActive">
          <el-select
            v-model="form.isActive"
            placeholder="请选择是否启用"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in isActiveOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSmartTechDictionary,
  getSmartTechDictionary,
  delSmartTechDictionary,
  addSmartTechDictionary,
  updateSmartTechDictionary,
} from "@/api/system/smartTechDictionary/index";
import { getDicts } from "@/api/system/dict/data";
export default {
  name: "SmartTechDictionary",
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      isCheck: false,
      isActiveOptions: [
        {
          label: "是",
          value: 1,
        },
        {
          label: "否",
          value: 0,
        },
      ],
      techCategoryOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 智能技术字典表格数据
      smartTechDictionaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        techName: null,
        techCategory: null,
        description: null,
        scoringRule: null,
        isActive: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        techName: [
          { required: true, message: "技术标准名称不能为空", trigger: "blur" },
        ],
        techCategory: [
          { required: true, message: "技术分类不能为空", trigger: "blur" },
        ],
        isActive: [
          {
            required: true,
            message: "是否启用",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.loadDict();
  },
  methods: {
    formatStatus(row, column, value) {
      const option = this.isActiveOptions.find((item) => item.value == value);
      return option ? option.label : value;
    },
    getDictLabel(dictOptions, value) {
      if (!dictOptions || !Array.isArray(dictOptions) || !value) {
        return "";
      }
      const dict = dictOptions.find(
        (item) => item.dictValue === value || item.dictValue === String(value)
      );
      return dict ? dict.dictLabel : "";
    },
    loadDict() {
      getDicts("technical_classification").then((response) => {
        this.techCategoryOptions = response.data;
      });
    },
    /** 查询智能技术字典列表 */
    getList() {
      this.loading = true;
      listSmartTechDictionary(this.queryParams).then((response) => {
        this.smartTechDictionaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        techName: null,
        techCategory: null,
        description: null,
        scoringRule: null,
        isActive: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加智能技术字典";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSmartTechDictionary(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改智能技术字典";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getSmartTechDictionary(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "查看智能技术字典";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSmartTechDictionary(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSmartTechDictionary(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除智能技术字典编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSmartTechDictionary(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/smartTechDictionary/export",
        {
          ...this.queryParams,
        },
        `智能技术标准管理.xlsx`
      );
    },
  },
};
</script>
