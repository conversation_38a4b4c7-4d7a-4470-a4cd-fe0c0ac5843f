<template>
  <el-dialog
    title="附件信息"
    :visible.sync="dialogVisible"
    width="40%"
    append-to-body
  >
    <div v-for="(item, index) in attachmentList" :key="index">
      <div class="demo-list-item__name">
        附件{{ index + 1 }}:
        <span @click="handleAttach(item)" class="attachment-item">
          <i class="el-icon-document"></i>
          {{ item.split("/").pop() }}
        </span>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "AttachmentDialog",
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  props: {
    // 支持 v-model 的 prop
    value: {
      type: Boolean,
      default: false,
    },
    attachmentList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    dialogVisible: {
      get() {
        console.log(this.value);
        return this.value;
      },
      set(value) {
        this.$emit("input", value);
      },
    },
  },
  mounted() {},
  methods: {
    async handleAttach(value) {
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const response = await fetch(fileUrl);
          const buffer = await response.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
  },
};
</script>

<style lang="scss">
.demo-list-item__name {
  .attachment-item {
    cursor: pointer;
    color: #409eff;
    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>
