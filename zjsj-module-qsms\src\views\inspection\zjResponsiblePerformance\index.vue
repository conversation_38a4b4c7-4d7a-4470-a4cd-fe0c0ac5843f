<template>
  <div class="app-container">
    <el-row>
      <el-col :span="3">
        <orgTree :type="'1'" @node-click="handleOrgTreeNodeClick"></orgTree>
      </el-col>
      <el-col :span="21">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="120px"
        >
          <el-form-item label="模块" prop="module">
            <el-select v-model="queryParams.module">
              <el-option label="全部" value="1"></el-option>
              <el-option label="安全生产管理办法" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="来源模块" prop="sourceModule">
            <el-select v-model="queryParams.sourceModule">
              <el-option label="全部" value="1"></el-option>
              <el-option label="安全生产管理办法" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status">
              <el-option label="全部" value="1"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="职务" prop="position">
            <el-input
              v-model="queryParams.position"
              placeholder="请输入职务"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="负责人姓名" prop="responsibleName">
            <el-input
              v-model="queryParams.responsibleName"
              placeholder="请输入负责人姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="任职起始日期" prop="tenureStart">
            <el-date-picker
              clearable
              v-model="queryParams.tenureStart"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择任职起始日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="全资格证书编号" prop="qualification">
            <el-input
              v-model="queryParams.qualification"
              placeholder="请输入全资格证书编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjResponsiblePerformance:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjResponsiblePerformance:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjResponsiblePerformance:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjResponsiblePerformance:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjResponsiblePerformanceList"
          @selection-change="handleSelectionChange"
          height="calc(100vh - 250px)"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="主键" align="center" prop="id" />
          <el-table-column label="职务" align="center" prop="position" />
          <el-table-column
            label="负责人姓名"
            align="center"
            prop="responsibleName"
          />
          <el-table-column
            label="任职起始日期"
            align="center"
            prop="tenureStart"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.tenureStart, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="全资格证书编号"
            align="center"
            prop="qualification"
          /> -->
          <el-table-column
            label="业务"
            prop="moduleName"
            align="center"
          ></el-table-column>
          <el-table-column
            label="行为内容"
            prop="sourceModuleName"
            align="center"
          ></el-table-column>
          <el-table-column label="时间" prop="createTime" align="center">
            <template slot-scope="scope">
              <span>
                {{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="来源模块" prop=""></el-table-column>
          <el-table-column label="当前状态" prop=""></el-table-column>
          <el-table-column label="履职结果" prop="">
            <template #scope="{ row }">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(row)"
                v-hasPermi="['inspection:zjResponsiblePerformance:edit']"
                >查看</el-button
              >
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjResponsiblePerformance:edit']"
                >生成履职报告</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjResponsiblePerformance:edit']"
                >查看上次报告</el-button
              >
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjResponsiblePerformance:edit']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjResponsiblePerformance:remove']"
                >删除</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改负责人履职管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="业务" prop="moduleName">
          <el-input v-model="form.moduleName" placeholder="请输入业务" />
        </el-form-item>
        <el-form-item label="行为内容" prop="sourceModuleName">
          <el-input
            v-model="form.sourceModuleName"
            placeholder="请输入行为内容"
          />
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-date-picker
            v-model="form.time"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="到"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="来源模块" prop="sourceModule">
          <el-input v-model="form.sourceModule" placeholder="请输入来源模块" />
        </el-form-item>
        <el-form-item label="当前状态" prop="currentStatus">
          <el-input v-model="form.currentStatus" placeholder="请输入当前状态" />
        </el-form-item>
        <!-- <el-form-item label="职务" prop="position">
          <el-input v-model="form.position" placeholder="请输入职务" />
        </el-form-item>
        <el-form-item label="负责人姓名" prop="responsibleName">
          <el-input
            v-model="form.responsibleName"
            placeholder="请输入负责人姓名"
          />
        </el-form-item>
        <el-form-item label="任职起始日期" prop="tenureStart">
          <el-date-picker
            clearable
            v-model="form.tenureStart"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择任职起始日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="全资格证书编号" prop="qualification">
          <el-input
            v-model="form.qualification"
            placeholder="请输入全资格证书编号"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjResponsiblePerformance,
  getZjResponsiblePerformance,
  delZjResponsiblePerformance,
  addZjResponsiblePerformance,
  updateZjResponsiblePerformance,
} from "@/api/inspection/zjResponsiblePerformance";
import orgTree from "../../components/orgTree.vue";

export default {
  name: "ZjResponsiblePerformance",
  components: {
    orgTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 负责人履职管理表格数据
      zjResponsiblePerformanceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        position: null,
        responsibleName: null,
        tenureStart: null,
        qualification: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询负责人履职管理列表 */
    getList() {
      this.loading = true;
      listZjResponsiblePerformance(this.queryParams).then((response) => {
        this.zjResponsiblePerformanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        position: null,
        responsibleName: null,
        tenureStart: null,
        qualification: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加负责人履职管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjResponsiblePerformance(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改负责人履职管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjResponsiblePerformance(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjResponsiblePerformance(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除负责人履职管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjResponsiblePerformance(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjResponsiblePerformance/export",
        {
          ...this.queryParams,
        },
        `zjResponsiblePerformance_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
