<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="评价模版名称" prop="evaluationTemplateName">
        <el-input
          v-model="queryParams.evaluationTemplateName"
          placeholder="请输入评价模版名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="评价方式：1-项目评价 2-周期性评价" prop="evaluationMethod">
        <el-input
          v-model="queryParams.evaluationMethod"
          placeholder="请输入评价方式：1-项目评价 2-周期性评价"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评价模板介绍" prop="evaluationTemplateIntro">
        <el-input
          v-model="queryParams.evaluationTemplateIntro"
          placeholder="请输入评价模板介绍"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评价规则:1-打分制  2-判断制" prop="evaluationRules">
        <el-input
          v-model="queryParams.evaluationRules"
          placeholder="请输入评价规则:1-打分制  2-判断制"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['contractor:zjEvaluationConfigInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['contractor:zjEvaluationConfigInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:zjEvaluationConfigInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['contractor:zjEvaluationConfigInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjEvaluationConfigInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column
        label="评价模版名称"
        align="center"
        prop="evaluationTemplateName"
      />
      <!-- ：1-项目评价 2-周期性评价 -->
      <el-table-column
        label="评价方式"
        align="center"
        prop="evaluationMethod"
      />
      <el-table-column
        label="评价模板介绍"
        align="center"
        prop="evaluationTemplateIntro"
      />
      <!-- 1-打分制  2-判断制 -->
      <el-table-column label="评价规则" align="center" prop="evaluationRules" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:zjEvaluationConfigInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:zjEvaluationConfigInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评价配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评价模版名称" prop="evaluationTemplateName">
          <el-input
            v-model="form.evaluationTemplateName"
            placeholder="请输入评价模版名称"
          />
        </el-form-item>
        <el-form-item label="评价方式" prop="evaluationMethod">
          <el-input
            v-model="form.evaluationMethod"
            placeholder="请输入评价方式"
          />
        </el-form-item>
        <el-form-item label="评价模板介绍" prop="evaluationTemplateIntro">
          <el-input
            v-model="form.evaluationTemplateIntro"
            placeholder="请输入评价模板介绍"
          />
        </el-form-item>
        <el-form-item label="评价规则" prop="evaluationRules">
          <el-input
            v-model="form.evaluationRules"
            placeholder="请输入评价规则:1-打分制  2-判断制"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjEvaluationConfigInfo,
  getZjEvaluationConfigInfo,
  delZjEvaluationConfigInfo,
  addZjEvaluationConfigInfo,
  updateZjEvaluationConfigInfo,
} from "@/api/contractor/zjEvaluationConfigInfo";

export default {
  name: "ZjEvaluationConfigInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评价配置表格数据
      zjEvaluationConfigInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        evaluationTemplateName: null,
        evaluationMethod: null,
        evaluationTemplateIntro: null,
        evaluationRules: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询评价配置列表 */
    getList() {
      this.loading = true;
      listZjEvaluationConfigInfo(this.queryParams).then((response) => {
        this.zjEvaluationConfigInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        evaluationTemplateName: null,
        evaluationMethod: null,
        evaluationTemplateIntro: null,
        evaluationRules: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加评价配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjEvaluationConfigInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改评价配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjEvaluationConfigInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEvaluationConfigInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除评价配置编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjEvaluationConfigInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "contractor/zjEvaluationConfigInfo/export",
        {
          ...this.queryParams,
        },
        `zjEvaluationConfigInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
