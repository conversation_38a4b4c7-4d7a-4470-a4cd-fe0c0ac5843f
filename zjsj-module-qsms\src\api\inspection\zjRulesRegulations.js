import request from '@/utils/request'

// 查询规章制度列表
export function listZjRulesRegulations(query) {
  return request({
    url: '/inspection/zjRulesRegulations/list',
    method: 'get',
    params: query
  })
}

// 查询规章制度详细
export function getZjRulesRegulations(id) {
  return request({
    url: '/inspection/zjRulesRegulations/' + id,
    method: 'get'
  })
}

// 新增规章制度
export function addZjRulesRegulations(data) {
  return request({
    url: '/inspection/zjRulesRegulations',
    method: 'post',
    data: data
  })
}

// 修改规章制度
export function updateZjRulesRegulations(data) {
  return request({
    url: '/inspection/zjRulesRegulations',
    method: 'put',
    data: data
  })
}

// 删除规章制度
export function delZjRulesRegulations(id) {
  return request({
    url: '/inspection/zjRulesRegulations/' + id,
    method: 'delete'
  })
}
