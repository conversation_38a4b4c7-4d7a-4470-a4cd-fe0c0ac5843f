<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="项目名称" prop="projectName">
        <selectPeopleTree
          v-model="queryParams.projectName"
          :peopleList="projectList"
          placeholder="请选择项目名称"
          @change="handleProjectChange"
        ></selectPeopleTree>
      </el-form-item>
      <el-form-item label="事故类型" prop="accidentType">
        <el-select
          v-model="queryParams.accidentType"
          placeholder="请选择事故类型"
          clearable
          multiple
          collapse-tags
          @keyup.enter.native="handleQuery"
          style="width: 180px"
        >
          <el-option
            v-for="dict in dict.type.yjjy_sglx"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属公司" prop="companyName">
        <el-select
          ref="queryCompanySelect"
          v-model="queryParams.companyName"
          placeholder="请选择所属公司"
          clearable
          style="width: 180px"
          popper-class="org-tree-select-dropdown"
        >
          <el-option
            :value="queryParams.companyName"
            :label="queryParams.companyName"
            style="height: auto; padding: 0; border: none"
          >
            <div class="tree-select-wrapper">
              <el-tree
                v-loading="companyTreeLoading"
                :data="companyTreeData"
                :props="companyTreeProps"
                highlight-current
                @node-click="handleQueryCompanyNodeClick"
              >
                <template #default="{ node, data }">
                  <el-tooltip
                    effect="dark"
                    :content="data.label"
                    placement="top"
                  >
                    <span class="el-tree-node__label">
                      {{ node.label }}
                    </span>
                  </el-tooltip>
                </template>
              </el-tree>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="存放地点" prop="storageLoation">
        <el-input v-model="queryParams.storageLoation" placeholder="请输入存放地点" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="上传人" prop="uploaderName">
        <el-input v-model="queryParams.uploaderName" placeholder="请输入上传人" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjEmergencyFacilities:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjEmergencyFacilities:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjEmergencyFacilities:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjEmergencyFacilities:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjEmergencyFacilitiesList"
      height="calc(100vh - 230px)"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="事故类型"
        align="center"
        prop="accidentType"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <template
            v-if="
              typeof scope.row.accidentType === 'string' &&
              scope.row.accidentType.includes(',')
            "
          >
            <dict-tag
              v-for="(type, index) in scope.row.accidentType.split(',')"
              :key="index"
              :options="dict.type.yjjy_sglx"
              :value="type.trim()"
              style="margin-right: 5px"
            />
          </template>
          <dict-tag
            v-else
            :options="dict.type.yjjy_sglx"
            :value="scope.row.accidentType"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        show-overflow-tooltip
      />
      <el-table-column
        label="所属公司"
        align="center"
        prop="companyName"
        show-overflow-tooltip
      />
      <el-table-column
        label="存放地点"
        align="center"
        prop="storageLoation"
        show-overflow-tooltip
      />
      <el-table-column
        label="上传人"
        align="center"
        prop="uploaderName"
        show-overflow-tooltip
      />
      <el-table-column
        label="上传时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="物资信息"
        align="center"
        prop="materialinformationUrl"
        width="120"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.materialinformationUrl"
            type="text"
            size="mini"
            @click="viewAttachment(scope.row.materialinformationUrl)"
          >
            查看附件
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="更新时间"
        align="center"
        prop="updateTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="更新者"
        align="center"
        prop="updateBy"
        show-overflow-tooltip
      />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['inspection:zjEmergencyFacilities:query']"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            v-hasPermi="['inspection:zjEmergencyFacilities:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['inspection:zjEmergencyFacilities:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改应急设施装备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="事故类型" prop="accidentType">
          <el-select
            v-model="form.accidentType"
            placeholder="请选择事故类型"
            multiple
            collapse-tags
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.yjjy_sglx"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择项目名称"
            @change="handleFormProjectChange"
          />
        </el-form-item>
        <el-form-item label="所属公司" prop="companyName">
          <el-select
            ref="formCompanySelect"
            v-model="form.companyName"
            placeholder="请选择所属公司"
            clearable
            style="width: 100%"
            popper-class="org-tree-select-dropdown"
          >
            <el-option
              :value="form.companyName"
              :label="form.companyName"
              style="height: auto; padding: 0; border: none"
            >
              <div class="tree-select-wrapper">
                <el-tree
                  v-loading="companyTreeLoading"
                  :data="companyTreeData"
                  :props="companyTreeProps"
                  highlight-current
                  @node-click="handleFormCompanyNodeClick"
                >
                  <template #default="{ node, data }">
                    <el-tooltip
                      effect="dark"
                      :content="data.label"
                      placement="top"
                    >
                      <span class="el-tree-node__label">
                        {{ node.label }}
                      </span>
                    </el-tooltip>
                  </template>
                </el-tree>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="存放地点" prop="storageLoation">
          <el-input
            v-model="form.storageLoation"
            placeholder="请输入存放地点"
          />
        </el-form-item>
        <el-form-item label="物资信息" prop="materialinformationUrl">
          <file-upload
            v-model="form.materialinformationUrl"
            :limit="5"
            :fileSize="50"
            :fileType="['doc', 'docx', 'pdf', 'xlsx', 'xls']"
            :is-show-tip="true"
          />
          <div
            class="el-upload__tip"
            slot="tip"
            style="color: #999; font-size: 12px; margin-top: 5px"
          >
            支持上传doc/docx/pdf/xlsx/xls格式文件，单个文件大小不超过50MB，最多上传5个文件
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看应急设施装备对话框 -->
    <el-dialog
      title="查看应急设施装备"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-form :model="viewForm" label-width="100px">
        <el-form-item label="事故类型">
          <template v-if="viewForm.accidentType">
            <dict-tag
              v-for="(type, index) in typeof viewForm.accidentType === 'string'
                ? viewForm.accidentType.split(',')
                : viewForm.accidentType"
              :key="index"
              :options="dict.type.yjjy_sglx"
              :value="type.trim ? type.trim() : type"
              style="margin-right: 5px"
            />
          </template>
          <span v-else>-</span>
        </el-form-item>
        <el-form-item label="项目名称">
          <span>{{ viewForm.projectName || "-" }}</span>
        </el-form-item>
        <el-form-item label="所属公司">
          <span>{{ viewForm.companyName || "-" }}</span>
        </el-form-item>
        <el-form-item label="存放地点">
          <span>{{ viewForm.storageLoation || "-" }}</span>
        </el-form-item>
        <el-form-item label="上传人">
          <span>{{ viewForm.uploaderName || "-" }}</span>
        </el-form-item>
        <el-form-item label="上传时间">
          <span>{{
            parseTime(viewForm.createTime, "{y}-{m}-{d} {h}:{i}") || "-"
          }}</span>
        </el-form-item>
        <!-- <el-form-item label="更新者">
          <span>{{ viewForm.updateBy || '-' }}</span>
        </el-form-item> -->
        <el-form-item label="更新时间">
          <span>{{
            parseTime(viewForm.updateTime, "{y}-{m}-{d} {h}:{i}") || "-"
          }}</span>
        </el-form-item>
        <el-form-item label="物资信息">
          <el-button
            v-if="viewForm.materialinformationUrl"
            type="text"
            @click="viewAttachment(viewForm.materialinformationUrl)"
          >
            查看附件
          </el-button>
          <span v-else>-</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjEmergencyFacilities,
  getZjEmergencyFacilities,
  delZjEmergencyFacilities,
  addZjEmergencyFacilities,
  updateZjEmergencyFacilities,
} from "@/api/inspection/zjEmergencyFacilities";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { querytree } from "@/api/system/info";
import { getEnterpriseInfo } from "@/api/system/info";

export default {
  name: "ZjEmergencyFacilities",
  dicts: ["yjjy_sglx"],
  components: {
    selectPeopleTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应急设施装备表格数据
      zjEmergencyFacilitiesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 查看表单参数
      viewForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accidentType: [],
        projectName: null,
        projectId: null,
        companId: null,
        companyName: null,
        storageLoation: null,
        uploaderName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accidentType: [
          { required: true, message: "事故类型不能为空", trigger: "change" },
        ],
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "change" },
        ],
        companyName: [
          { required: true, message: "所属公司不能为空", trigger: "blur" },
        ],
        storageLoation: [
          { required: true, message: "存放地点不能为空", trigger: "blur" },
        ],
      },
      // 项目列表
      projectList: [],
      // 企业信息数据
      companyTreeData: [],
      companyTreeLoading: false,
      companyTreeProps: {
        children: "children",
        label: "label",
        id: "id",
        isLeaf: "isLeaf",
      },
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    this.getCompanyTreeData();
  },
  methods: {
    /** 查询应急设施装备列表 */
    getList() {
      this.loading = true;
      // 将数组转换为逗号分隔的字符串发送给后端
      const params = { ...this.queryParams };
      if (
        Array.isArray(params.accidentType) &&
        params.accidentType.length > 0
      ) {
        params.accidentType = params.accidentType.join(",");
      } else if (Array.isArray(params.accidentType)) {
        params.accidentType = null;
      }

      // 确保 projectId 和 companId 以字符串形式传输
      if (params.projectId !== null && params.projectId !== undefined) {
        params.projectId = String(params.projectId);
        params.companId = String(params.projectId); // 保持一致
      }

      listZjEmergencyFacilities(params).then((response) => {
        this.zjEmergencyFacilitiesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accidentType: [],
        projectName: null,
        projectId: null,
        companId: null,
        companyName: null,
        storageLoation: null,
        materialinformationUrl: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加应急设施装备";
    },
    /** 查看按钮操作 */
    handleView(row) {
      const id = row.id;
      getZjEmergencyFacilities(id).then((response) => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjEmergencyFacilities(id).then((response) => {
        this.form = response.data;
        // 将逗号分隔的字符串转换为数组用于编辑回显
        if (
          this.form.accidentType &&
          typeof this.form.accidentType === "string"
        ) {
          this.form.accidentType = this.form.accidentType
            .split(",")
            .map((item) => item.trim());
        } else if (!this.form.accidentType) {
          this.form.accidentType = [];
        }
        // 确保 companId 与 projectId 一致
        if (this.form.projectId) {
          this.form.companId = this.form.projectId;
        }
        this.open = true;
        this.title = "修改应急设施装备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 将数组转换为逗号分隔的字符串发送给后端
          const formData = { ...this.form };
          if (
            Array.isArray(formData.accidentType) &&
            formData.accidentType.length > 0
          ) {
            formData.accidentType = formData.accidentType.join(",");
          } else if (Array.isArray(formData.accidentType)) {
            formData.accidentType = null;
          }

          // 确保 projectId 和 companId 以字符串形式传输
          if (formData.projectId !== null && formData.projectId !== undefined) {
            formData.projectId = String(formData.projectId);
            formData.companId = String(formData.projectId); // 保持一致
          }

          if (this.form.id != null) {
            updateZjEmergencyFacilities(formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEmergencyFacilities(formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除应急设施装备编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjEmergencyFacilities(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 将数组转换为逗号分隔的字符串用于导出
      const params = { ...this.queryParams };
      if (
        Array.isArray(params.accidentType) &&
        params.accidentType.length > 0
      ) {
        params.accidentType = params.accidentType.join(",");
      } else if (Array.isArray(params.accidentType)) {
        params.accidentType = null;
      }

      // 确保 projectId 和 companId 以字符串形式传输
      if (params.projectId !== null && params.projectId !== undefined) {
        params.projectId = String(params.projectId);
        params.companId = String(params.projectId); // 保持一致
      }

      this.download(
        "inspection/zjEmergencyFacilities/export",
        params,
        `zjEmergencyFacilities_${new Date().getTime()}.xlsx`
      );
    },
    /** 获取项目列表 */
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    /** 获取企业信息数据 */
    getCompanyTreeData() {
      this.companyTreeLoading = true;
      getEnterpriseInfo()
        .then((res) => {
          const deptList = res.data;
          this.companyTreeData = [];
          deptList.forEach((item) => {
            this.companyTreeData.push({
              label: item.label,
              id: item.id,
              children: item.children,
            });
          });
          this.companyTreeLoading = false;
        })
        .catch((err) => {
          this.companyTreeLoading = false;
          console.error(err);
        });
    },
    /** 项目选择变化处理 */
    handleProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectName = selectedItem.label;
        this.queryParams.projectId = String(selectedItem.id);
        this.queryParams.companId = String(selectedItem.id); // 保持一致
      } else {
        this.queryParams.projectName = null;
        this.queryParams.projectId = null;
        this.queryParams.companId = null;
      }
    },
    /** 表单项目选择变化处理 */
    handleFormProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectName = selectedItem.label;
        this.form.projectId = String(selectedItem.id);
        this.form.companId = String(selectedItem.id); // 保持一致
      } else {
        this.form.projectName = null;
        this.form.projectId = null;
        this.form.companId = null;
      }
    },
    /** 查看附件 */
    viewAttachment(fileInfo) {
      if (fileInfo) {
        // 如果是文件路径字符串，直接打开
        if (typeof fileInfo === "string") {
          window.open(process.env.VUE_APP_BASE_API + fileInfo, "_blank");
        }
        // 如果是文件对象数组，处理多个文件
        else if (Array.isArray(fileInfo)) {
          fileInfo.forEach((file) => {
            if (file.url) {
              window.open(process.env.VUE_APP_BASE_API + file.url, "_blank");
            }
          });
        }
        // 如果是单个文件对象
        else if (fileInfo.url) {
          window.open(process.env.VUE_APP_BASE_API + fileInfo.url, "_blank");
        }
      }
    },
    /** 处理查询表单公司选择 */
    handleQueryCompanyNodeClick(nodeData) {
      if (nodeData && nodeData.label) {
        this.queryParams.companyName = nodeData.label;
        // 选择后关闭下拉框
        this.$nextTick(() => {
          if (this.$refs.queryCompanySelect) {
            this.$refs.queryCompanySelect.blur();
          }
        });
      }
    },
    /** 处理表单公司选择 */
    handleFormCompanyNodeClick(nodeData) {
      if (nodeData && nodeData.label) {
        this.form.companyName = nodeData.label;
        // 选择后关闭下拉框
        this.$nextTick(() => {
          if (this.$refs.formCompanySelect) {
            this.$refs.formCompanySelect.blur();
          }
        });
      }
    },
  },
};
</script>

<style scoped>
/* 树形下拉选择器样式 */
.tree-select-wrapper {
  width: 300px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

::v-deep .org-tree-select-dropdown .el-select-dropdown__item {
  padding: 0 !important;
  height: auto !important;
  line-height: normal !important;
}

::v-deep .org-tree-select-dropdown .el-select-dropdown__item:hover {
  background-color: transparent !important;
}

/* 企业树形组件样式 */
::v-deep .tree-select-wrapper .el-tree-node__label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

::v-deep .tree-select-wrapper .el-tree-node__label::-webkit-scrollbar {
  display: none;
}

::v-deep
  .tree-select-wrapper
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}
</style>
