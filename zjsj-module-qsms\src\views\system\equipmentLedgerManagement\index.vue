<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="设备编号" prop="equipmentCode">
        <el-input
          v-model="queryParams.equipmentCode"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input
          v-model="queryParams.equipmentName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="所在项目" prop="projectName">
        <!-- <el-input
          v-model="queryParams.projectId"
          placeholder="请输入所在项目"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <selectPeopleTree
          v-model="queryParams.projectName"
          :people-list="projectList"
          placeholder="请选择所在项目"
          @change="handleSearchProjectChange"
        />
      </el-form-item>
      <el-form-item label="当前状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择当前状态">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:equipment:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:equipment:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:equipment:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="equipmentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="设备编号" align="center" prop="equipmentCode" />
      <el-table-column label="设备名称" align="center" prop="equipmentName" />
      <el-table-column label="设备型号" align="center" prop="equipmentModel" />
      <!-- <el-table-column label="生产厂家" align="center" prop="manufacturer" />
      <el-table-column
        label="出厂日期"
        align="center"
        prop="manufactureDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.manufactureDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产权单位" align="center" prop="ownerCompany" />
      <el-table-column
        label="产权备案编号"
        align="center"
        prop="certificateNum"
      /> -->
      <el-table-column label="所在项目" align="center" prop="projectName" />
      <el-table-column label="所属分公司" align="center" prop="branchName" />
      <el-table-column
        label="当前状态"
        align="center"
        prop="status"
        :formatter="formatStatus"
      />
      <!-- <el-table-column
        label="设备在现场的安装位置"
        align="center"
        prop="position"
      />
    -->
      <!-- <el-table-column
        label="最新检测报告照片 URL"
        align="center"
        prop="inspectionCertPic"
      /> -->
      <el-table-column
        label="最新检测报告照片"
        align="center"
        prop="inspectionCertPic"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.inspectionCertPic" class="contract-file">
            <div
              v-for="(item, index) in scope.row.inspectionCertPic.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="220"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:equipment:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdateStatus(scope.row)"
            v-hasPermi="['system:equipment:edit']"
            >修改状态</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:equipment:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改大型设备台账对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="设备编号" prop="equipmentCode">
          <el-input
            v-model="form.equipmentCode"
            placeholder="请输入设备编号"
            maxlength="50"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="设备名称" prop="equipmentName">
          <el-input
            v-model="form.equipmentName"
            placeholder="请输入设备名称"
            maxlength="100"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="设备型号" prop="equipmentModel">
          <el-input
            v-model="form.equipmentModel"
            placeholder="请输入设备型号"
            maxlength="100"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="生产厂家" prop="manufacturer">
          <el-input
            v-model="form.manufacturer"
            placeholder="请输入生产厂家"
            maxlength="255"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="出厂日期" prop="manufactureDate">
          <el-date-picker
            clearable
            v-model="form.manufactureDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出厂日期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="产权单位" prop="ownerCompany">
          <el-input
            v-model="form.ownerCompany"
            placeholder="请输入产权单位"
            maxlength="255"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="产权备案编号" prop="certificateNum">
          <el-input
            v-model="form.certificateNum"
            placeholder="请输入产权备案编号"
            maxlength="100"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="当前所在项目" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择所在项目"
            @change="handleProjectChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="所属分公司" prop="branchName">
          <selectComponyTree
            ref="chargePersonName"
            v-model="form.branchName"
            :people-list="companyList"
            placeholder="请搜索或选择所属分公司"
            @change="handleChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="当前状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择当前状态"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="安装位置" prop="position">
          <el-input
            v-model="form.position"
            type="textarea"
            placeholder="请输入内容"
            maxlength="500"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="计划安装时间" prop="installDate">
          <el-date-picker
            clearable
            v-model="form.installDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划安装时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划拆除时间" prop="removeDate">
          <el-date-picker
            clearable
            v-model="form.removeDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划拆除时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最新检测报告照片" prop="inspectionCertPic">
          <file-upload
            v-model="form.inspectionCertPic"
            :file-type="['png', 'jpg', 'gif', 'jpeg', 'svg']"
            :disabled="isCheck"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="修改状态"
      :visible.sync="openStatusDialog"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="当前状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择当前状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormStatusDialog"
          >确 定</el-button
        >
        <el-button @click="cancelStatusDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEquipment,
  getEquipment,
  delEquipment,
  addEquipment,
  updateEquipment,
} from "@/api/system/equipmentLedgerManagement/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import selectComponyTree from "@/views/components/selectComponyTree.vue";
import { getFileOrignalName } from "@/utils/common.js";
import { querytree } from "@/api/system/info";
import { getEnterpriseInfo } from "@/api/system/info";
export default {
  name: "Equipment",
  components: {
    selectPeopleTree,
    selectComponyTree,
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      isCheck: false,
      companyList: [],
      projectList: [],
      statusOptions: [
        {
          label: "未安装",
          value: 0,
        },
        {
          label: "安装中",
          value: 1,
        },
        {
          label: "验收中",
          value: 2,
        },
        {
          label: "运行中",
          value: 3,
        },
        {
          label: "维修中",
          value: 4,
        },
        {
          label: "已报停",
          value: 5,
        },
        {
          label: "已拆卸",
          value: 6,
        },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 大型设备台账表格数据
      equipmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openStatusDialog: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        equipmentCode: null,
        equipmentName: null,
        equipmentModel: null,
        manufacturer: null,
        manufactureDate: null,
        ownerCompany: null,
        certificateNum: null,
        projectId: null,
        projectName: null,
        position: null,
        status: null,
        inspectionCertPic: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        equipmentCode: [
          { required: true, message: "设备编号不能为空", trigger: "blur" },
        ],
        equipmentName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
        ],
        equipmentModel: [
          { required: true, message: "设备型号不能为空", trigger: "blur" },
        ],
        ownerCompany: [
          { required: true, message: "产权单位不能为空", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "所在项目不能为空", trigger: "change" },
        ],
        status: [
          { required: true, message: "当前状态不能为空", trigger: "change" },
        ],
        branchName: [
          { required: true, message: "所属分公司不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    this.getCompanyList();
  },
  methods: {
    handleChange(selectedItem) {
      if (selectedItem) {
        this.form.branchName = selectedItem.label;
        this.form.branchId = selectedItem.id;
      } else {
        this.form.branchName = null;
        this.form.branchId = null;
      }
      this.$forceUpdate();
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    getFileOrignalName,
    formatStatus(row, column, value) {
      // console.log("ssssaaaaa", row, column, value);
      const option = this.statusOptions.find((item) => item.value == value);
      return option ? option.label : value;
    },
    handleProjectChange(selectedItem) {
      // console.log("projectChange", selectedItem);
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectName = selectedItem.label;
        this.form.projectId = selectedItem.id;
      } else {
        this.form.projectName = null;
        this.form.projectId = null;
      }
    },
    handleSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectId = selectedItem.id;
        this.queryParams.projectName = selectedItem.label;
      } else {
        this.queryParams.projectId = null;
        this.queryParams.projectName = null;
      }
    },
    /** 获取项目列表 */
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    /** 查询大型设备台账列表 */
    getList() {
      this.loading = true;
      listEquipment(this.queryParams).then((response) => {
        this.equipmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelStatusDialog() {
      this.openStatusDialog = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        equipmentCode: null,
        equipmentName: null,
        equipmentModel: null,
        manufacturer: null,
        manufactureDate: null,
        ownerCompany: null,
        certificateNum: null,
        projectId: null,
        projectName: null,
        position: null,
        status: null,
        inspectionCertPic: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        installDate: null,
        removeDate: null,
        branchName: null,
        branchId: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.projectId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加大型设备台账";
    },
    /** 修改按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      this.isCheck = true;
      getEquipment(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "查看大型设备台账";
      });
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      this.isCheck = false;
      getEquipment(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改大型设备台账";
      });
    },
    handleUpdateStatus(row) {
      this.reset();
      const id = row.id || this.ids;
      this.isCheck = false;
      getEquipment(id).then((response) => {
        this.form = response.data;
        console.log("uuuu", this.form);
        this.openStatusDialog = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEquipment(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEquipment(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    submitFormStatusDialog() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEquipment(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.openStatusDialog = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除大型设备台账编号为"' + ids + '"的数据项？')
        .then(function () {
          return delEquipment(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/largeEquipment/export",
        {
          ...this.queryParams,
        },
        `大型设备台账管理.xlsx`
      );
    },
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>