import request from '@/utils/request'

// 查询隐患排查治理列表
export function listZjHazardInvestigation(query) {
  return request({
    url: '/inspection/zjHazardInvestigation/list',
    method: 'get',
    params: query
  })
}

// 查询隐患排查治理详细
export function getZjHazardInvestigation(id) {
  return request({
    url: '/inspection/zjHazardInvestigation/' + id,
    method: 'get'
  })
}

// 新增隐患排查治理
export function addZjHazardInvestigation(data) {
  return request({
    url: '/inspection/zjHazardInvestigation',
    method: 'post',
    data: data
  })
}

// 修改隐患排查治理
export function updateZjHazardInvestigation(data) {
  return request({
    url: '/inspection/zjHazardInvestigation',
    method: 'put',
    data: data
  })
}

// 删除隐患排查治理
export function delZjHazardInvestigation(id) {
  return request({
    url: '/inspection/zjHazardInvestigation/' + id,
    method: 'delete'
  })
}
