import request from '@/utils/request'

// 查询法律法规与标准列表
export function listZjLegalRegulationsStandards(query) {
  return request({
    url: '/inspection/zjLegalRegulationsStandards/list',
    method: 'get',
    params: query
  })
}

// 查询法律法规与标准详细
export function getZjLegalRegulationsStandards(id) {
  return request({
    url: '/inspection/zjLegalRegulationsStandards/' + id,
    method: 'get'
  })
}

// 新增法律法规与标准
export function addZjLegalRegulationsStandards(data) {
  return request({
    url: '/inspection/zjLegalRegulationsStandards',
    method: 'post',
    data: data
  })
}

// 修改法律法规与标准
export function updateZjLegalRegulationsStandards(data) {
  return request({
    url: '/inspection/zjLegalRegulationsStandards',
    method: 'put',
    data: data
  })
}

// 删除法律法规与标准
export function delZjLegalRegulationsStandards(id) {
  return request({
    url: '/inspection/zjLegalRegulationsStandards/' + id,
    method: 'delete'
  })
}
