<template>
    <div class="app-container">
        <el-row style="margin-right: auto">
            <el-col :span="24" style="line-height: 40px;">手机视角</el-col>
        </el-row>
        <iframe :src="iframeUrl" scrolling="auto" frameborder="0" class="trend-container2" id="iframe"></iframe>
    </div>
</template>

<script>
import Cookies from "js-cookie";
export default {
    data() {
        return {
            iframeUrl: 'http://*************:8955/fx_aj/#/pages/index/index?type=admin',
            userName: '',
            password: '',
        };
    },
    created() {
        this.username = Cookies.get("username");
        this.password = Cookies.get("password");
        if (this.username && this.password) {
            this.iframeUrl = this.iframeUrl + '&username=' + this.username + '&password=' + this.password;
        }
    },
    methods: {
    }
};
</script>
<style lang="scss" scoped>
.app-container {
    width: 100%;
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
    align-items: center;
    .trend-container2 {
        width: 400px;
        height: 100%;
    }
}
</style>