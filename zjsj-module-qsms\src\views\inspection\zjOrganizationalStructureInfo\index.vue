<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <!-- <el-form-item label="父类id" prop="parentId">
        <el-input
          v-model="queryParams.parentId"
          placeholder="请输入父类id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="短名称" prop="shortName">
        <el-input
          v-model="queryParams.shortName"
          placeholder="请输入短名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="拼音" prop="pinYin">
        <el-input
          v-model="queryParams.pinYin"
          placeholder="请输入拼音"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="编号" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="信用代码" prop="creditCode">
        <el-input
          v-model="queryParams.creditCode"
          placeholder="请输入信用代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="排序" prop="sort">
        <el-input
          v-model="queryParams.sort"
          placeholder="请输入排序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="real_id" prop="realId">
        <el-input
          v-model="queryParams.realId"
          placeholder="请输入real_id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="标识" prop="bizRelNode">
        <el-input
          v-model="queryParams.bizRelNode"
          placeholder="请输入标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="子节点" prop="childNodes">
        <el-input
          v-model="queryParams.childNodes"
          placeholder="请输入子节点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="资源" prop="source">
        <el-input
          v-model="queryParams.source"
          placeholder="请输入资源"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目id" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="能不能查询" prop="canSelect">
        <el-input
          v-model="queryParams.canSelect"
          placeholder="请输入能不能查询"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="职权编码" prop="authorityCodes">
        <el-input
          v-model="queryParams.authorityCodes"
          placeholder="请输入职权编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="isLeaf">
        <el-input
          v-model="queryParams.isLeaf"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="legalEntity">
        <el-input
          v-model="queryParams.legalEntity"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="工程" prop="project">
        <el-input
          v-model="queryParams.project"
          placeholder="请输入工程"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="${comment}" prop="bureau">
        <el-input
          v-model="queryParams.bureau"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="srcTenantId">
        <el-input
          v-model="queryParams.srcTenantId"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="srcId">
        <el-input
          v-model="queryParams.srcId"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="${comment}" prop="userFocus">
        <el-input
          v-model="queryParams.userFocus"
          placeholder="请输入${comment}"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="租户ID‌" prop="tenantId">
        <el-input
          v-model="queryParams.tenantId"
          placeholder="请输入租户ID‌"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 
      <el-form-item label="启用" prop="enable">
        <el-input
          v-model="queryParams.enable"
          placeholder="请输入启用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjOrganizationalStructureInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjOrganizationalStructureInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjOrganizationalStructureInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjOrganizationalStructureInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjOrganizationalStructureInfoList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 280px)"
    >
      <!-- <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键id" align="center" prop="id" />
      <el-table-column label="父类id" align="center" prop="parentId" /> -->
      <el-table-column
        label="名称"
        align="center"
        prop="name"
        width="180"
        show-overflow-tooltip
      />
      <!-- <el-table-column label="短名称" align="center" prop="shortName" />
      <el-table-column label="拼音" align="center" prop="pinYin" />
       -->
      <el-table-column
        label="编码"
        align="center"
        prop="code"
        width="250"
        show-overflow-tooltip
      />
      <!-- <el-table-column label="信用代码" align="center" prop="creditCode" />
      <el-table-column label="状态" align="center" prop="status" /> -->
      <el-table-column label="类别" align="center" prop="type" width="120" />
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column label="状态" align="center" prop="dataStatus" />
      <!-- <el-table-column label="real_id" align="center" prop="realId" /> -->
      <!-- <el-table-column label="标识" align="center" prop="bizRelNode" />
      <el-table-column label="全id" align="center" prop="fullId" />
      <el-table-column label="节点id" align="center" prop="fullNodeId" />
      <el-table-column label="全名称" align="center" prop="fullName" />
      <el-table-column label="子节点" align="center" prop="childNodes" />
      <el-table-column label="资源" align="center" prop="source" />
      <el-table-column label="项目id" align="center" prop="projectId" />
      <el-table-column label="项目状态" align="center" prop="projectStatus" />
      <el-table-column label="类别" align="center" prop="authType" />
      <el-table-column label="能不能查询" align="center" prop="canSelect" />
      <el-table-column label="职权编码" align="center" prop="authorityCodes" />
      <el-table-column label="${comment}" align="center" prop="isLeaf" />
      <el-table-column label="${comment}" align="center" prop="legalEntity" />
      <el-table-column label="是否是工程" align="center" prop="project">
        <template slot-scope="{ row }">
          {{ row.project ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column label="${comment}" align="center" prop="bureau" />
      <el-table-column label="${comment}" align="center" prop="srcType" />
      <el-table-column label="${comment}" align="center" prop="srcTenantId" />
      <el-table-column label="${comment}" align="center" prop="srcId" />
      <el-table-column label="${comment}" align="center" prop="userFocus" />
      <el-table-column label="租户ID‌" align="center" prop="tenantId" /> -->
      <el-table-column label="启用" align="center" prop="enable" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjOrganizationalStructureInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjOrganizationalStructureInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改中江组织架构对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="父类id" prop="parentId">
          <el-input v-model="form.parentId" placeholder="请输入父类id" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="短名称" prop="shortName">
          <el-input v-model="form.shortName" placeholder="请输入短名称" />
        </el-form-item>
        <el-form-item label="拼音" prop="pinYin">
          <el-input v-model="form.pinYin" placeholder="请输入拼音" />
        </el-form-item>
        <el-form-item label="编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item label="信用代码" prop="creditCode">
          <el-input v-model="form.creditCode" placeholder="请输入信用代码" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="real_id" prop="realId">
          <el-input v-model="form.realId" placeholder="请输入real_id" />
        </el-form-item>
        <el-form-item label="标识" prop="bizRelNode">
          <el-input v-model="form.bizRelNode" placeholder="请输入标识" />
        </el-form-item>
        <el-form-item label="全id" prop="fullId">
          <el-input
            v-model="form.fullId"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="节点id" prop="fullNodeId">
          <el-input
            v-model="form.fullNodeId"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="全名称" prop="fullName">
          <el-input
            v-model="form.fullName"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="子节点" prop="childNodes">
          <el-input v-model="form.childNodes" placeholder="请输入子节点" />
        </el-form-item>
        <el-form-item label="资源" prop="source">
          <el-input v-model="form.source" placeholder="请输入资源" />
        </el-form-item>
        <el-form-item label="项目id" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目id" />
        </el-form-item>
        <el-form-item label="能否查询" prop="canSelect">
          <el-input v-model="form.canSelect" placeholder="请输入能不能查询" />
        </el-form-item>
        <el-form-item label="职权编码" prop="authorityCodes">
          <el-input
            v-model="form.authorityCodes"
            placeholder="请输入职权编码"
          />
        </el-form-item>
        <!-- <el-form-item label="${comment}" prop="isLeaf">
          <el-input v-model="form.isLeaf" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="legalEntity">
          <el-input v-model="form.legalEntity" placeholder="请输入${comment}" />
        </el-form-item> -->
        <el-form-item label="工程" prop="project">
          <el-input v-model="form.project" placeholder="请输入工程" />
        </el-form-item>
        <!-- <el-form-item label="${comment}" prop="bureau">
          <el-input v-model="form.bureau" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="srcTenantId">
          <el-input v-model="form.srcTenantId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="srcId">
          <el-input v-model="form.srcId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="userFocus">
          <el-input v-model="form.userFocus" placeholder="请输入${comment}" />
        </el-form-item> -->
        <el-form-item label="租户ID‌" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户ID‌" />
        </el-form-item>
        <el-form-item label="启用" prop="enable">
          <el-input v-model="form.enable" placeholder="请输入启用" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjOrganizationalStructureInfo,
  getZjOrganizationalStructureInfo,
  delZjOrganizationalStructureInfo,
  addZjOrganizationalStructureInfo,
  updateZjOrganizationalStructureInfo,
} from "@/api/inspection/zjOrganizationalStructureInfo";

export default {
  name: "ZjOrganizationalStructureInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 中江组织架构表格数据
      zjOrganizationalStructureInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentId: null,
        name: null,
        shortName: null,
        pinYin: null,
        code: null,
        creditCode: null,
        status: null,
        type: null,
        sort: null,
        realId: null,
        bizRelNode: null,
        fullId: null,
        fullNodeId: null,
        fullName: null,
        childNodes: null,
        dataStatus: null,
        source: null,
        projectId: null,
        projectStatus: null,
        authType: null,
        canSelect: null,
        authorityCodes: null,
        isLeaf: null,
        legalEntity: null,
        project: null,
        bureau: null,
        srcType: null,
        srcTenantId: null,
        srcId: null,
        userFocus: null,
        tenantId: null,
        enable: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询中江组织架构列表 */
    getList() {
      this.loading = true;
      listZjOrganizationalStructureInfo(this.queryParams).then((response) => {
        this.zjOrganizationalStructureInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        parentId: null,
        name: null,
        shortName: null,
        pinYin: null,
        code: null,
        creditCode: null,
        status: null,
        type: null,
        sort: null,
        realId: null,
        bizRelNode: null,
        fullId: null,
        fullNodeId: null,
        fullName: null,
        childNodes: null,
        dataStatus: null,
        source: null,
        projectId: null,
        projectStatus: null,
        authType: null,
        canSelect: null,
        authorityCodes: null,
        isLeaf: null,
        legalEntity: null,
        project: null,
        bureau: null,
        srcType: null,
        srcTenantId: null,
        srcId: null,
        userFocus: null,
        tenantId: null,
        enable: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加中江组织架构";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjOrganizationalStructureInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改中江组织架构";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjOrganizationalStructureInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjOrganizationalStructureInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除中江组织架构编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjOrganizationalStructureInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjOrganizationalStructureInfo/export",
        {
          ...this.queryParams,
        },
        `zjOrganizationalStructureInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
