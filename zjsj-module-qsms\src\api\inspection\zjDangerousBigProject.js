import request from '@/utils/request'

// 查询同步危大工程主记录记录列表
export function listZjDangerousBigProject(query) {
  return request({
    url: '/inspection/zjDangerousBigProject/list',
    method: 'get',
    params: query
  })
}

// 查询同步危大工程主记录记录详细
export function getZjDangerousBigProject(id) {
  return request({
    url: '/inspection/zjDangerousBigProject/' + id,
    method: 'get'
  })
}

// 新增同步危大工程主记录记录
export function addZjDangerousBigProject(data) {
  return request({
    url: '/inspection/zjDangerousBigProject',
    method: 'post',
    data: data
  })
}

// 修改同步危大工程主记录记录
export function updateZjDangerousBigProject(data) {
  return request({
    url: '/inspection/zjDangerousBigProject',
    method: 'put',
    data: data
  })
}

// 删除同步危大工程主记录记录
export function delZjDangerousBigProject(id) {
  return request({
    url: '/inspection/zjDangerousBigProject/' + id,
    method: 'delete'
  })
}
