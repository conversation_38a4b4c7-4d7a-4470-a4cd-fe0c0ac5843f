<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="项目名称" prop="assessmentProjectName">
        <el-input
          v-model="queryParams.assessmentProjectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属公司" prop="company">
        <el-input
          v-model="queryParams.company"
          placeholder="请输入所属公司"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核周期" prop="assessmentCycle">
        <el-input
          v-model="queryParams.assessmentCycle"
          placeholder="请输入考核周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核时间" prop="assessmentTime">
        <el-input
          v-model="queryParams.assessmentTime"
          placeholder="请输入考核时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:assessment:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:assessment:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:assessment:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="assessmentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号"> </el-table-column>
      <el-table-column
        label="项目名称"
        align="center"
        prop="assessmentProjectName"
      />
      <el-table-column
        label="所属公司"
        align="center"
        prop="company"
        show-overflow-tooltip
      />
      <el-table-column
        label="考核周期"
        align="center"
        prop="assessmentCycle"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="考核时间"
        align="center"
        prop="assessmentTime"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="项目经理得分"
        align="center"
        prop="pmScore"
        width="140"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.pmScore ? JSON.parse(scope.row.pmScore).userName : ""
          }}{{ scope.row.pmScore ? ":" : ""
          }}{{
            scope.row.pmScore ? JSON.parse(scope.row.pmScore).totalScore : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="安全员得分"
        align="center"
        prop="safetyOfficerScore"
        width="140"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div
            v-for="(item, index) in JSON.parse(scope.row.safetyOfficerScore)"
            :key="index"
          >
            {{ item.userName }}:{{ item.totalScore }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="项目编码" align="center" prop="projectCode" /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template slot-scope="scope">
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="getDetail(scope.row)"
            >查询详情</el-button
          > -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:assessment:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            style="color: red"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:assessment:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全生产责任制考核对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="assessmentProjectName">
              <selectPeopleTree
                v-model="form.assessmentProjectName"
                :people-list="projectList"
                placeholder="请选择项目名称"
                @change="handleProjectChange"
                :disabled="isCheck"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属公司" prop="company">
              <!-- <el-input v-model="form.company" placeholder="请输入所属公司" /> -->
              <selectPeopleTree
                ref="chargePersonName"
                v-model="form.company"
                :people-list="companyList"
                placeholder="请搜索或选择所属公司"
                @change="handleChange"
                :disabled="isCheck"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="考核周期" prop="assessmentCycle">
              <el-input
                v-model="form.assessmentCycle"
                placeholder="请输入考核周期"
                :disabled="isCheck"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核时间" prop="assessmentTime">
              <el-input
                v-model="form.assessmentTime"
                placeholder="请输入考核时间"
                :disabled="isCheck"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目经理姓名" prop="projectManagerName">
              <el-input
                v-model="form.projectManagerName"
                placeholder="请输入项目经理姓名"
                :disabled="isCheck"
                @input="handleInputProductManagerName(form.projectManagerName)"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24">
          <el-form-item label="安全员姓名" prop="safetyOfficerName">
            <el-button type="text" @click="addItem" :disabled="isCheck"
              >+ 添加安全员</el-button
            >
            <el-row :gutter="10" style="margin-top: 10px">
              <el-col
                :span="8"
                v-for="(item, index) in form.safetyOfficerName"
                :key="index"
                style="
                  display: flex;
                  flex-direction: row;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div
                  style="
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    width: 100%;
                  "
                >
                  <el-input
                    :disabled="isCheck"
                    v-model="form.safetyOfficerName[index]"
                    placeholder=""
                    style="margin-top: 10px; width: 100%"
                    @input="
                      handleInputSafetyOfficerName(
                        form.safetyOfficerName[index],
                        index
                      )
                    "
                  />
                  <el-button
                    type="text"
                    @click="removeItem(index)"
                    v-if="form.safetyOfficerName.length > 1"
                    style="color: #f56c6c"
                    :disabled="isCheck"
                  >
                    删除
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-table
          ref="tableRef"
          :data="tableData"
          border
          style="width: 100%"
          height="400"
        >
          <el-table-column label="安全生产责任制考核表" align="center">
            <el-table-column type="index" width="50" label="序号">
            </el-table-column>
            <el-table-column
              label="安全生产考核指标"
              prop="type"
              align="center"
              width="200"
            />
            <el-table-column
              label="落实情况"
              prop="implementationStatus"
              align="center"
            >
              <template slot-scope="scope">
                <el-input
                  :disabled="isCheck"
                  v-model="scope.row.implementationStatus"
                  placeholder="请输入"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="总分"
              prop="totalScore"
              align="center"
              width="80"
            />
            <el-table-column label="项目经理得分" align="center">
              <template slot-scope="scope">
                <div
                  style="
                    display: flex;
                    flex-direction: row;
                    justify-content: space-around;
                    align-items: center;
                  "
                >
                  <!-- <div>
                    <el-input
                      v-model="scope.row.projectManager.userName"
                      placeholder="项目经理姓名"
                      style="marign-right: 10px; width: 85%"
                    />
                    {{ form.projectManagerName }}
                  </div> -->
                  <div
                    style="
                      marign-left: 10px;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    "
                  >
                    <div style="margin-right: 20px">
                      {{
                        form.projectManagerName ? form.projectManagerName : ""
                      }}
                    </div>
                    <el-input
                      :disabled="isCheck"
                      v-model="scope.row.projectManager.totalScore"
                      placeholder="项目经理得分"
                      style="width: 65%; margin-left: 10px"
                      @input="handleInput(scope.row)"
                      @blur="handleBlur(scope.row)"
                    />
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="安全员得分" align="center">
              <template slot-scope="scope">
                <!-- <el-button
                  type="primary"
                  @click="addRowSafetyOfficer(scope.row)"
                  >新增安全员</el-button
                > -->
                <div
                  style="
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    margin-top: 10px;
                    align-items: center;
                  "
                  v-for="(item, index) in scope.row.safetyOfficer"
                  :key="index"
                >
                  <div style="width: 120px">
                    <!-- <el-input
                      v-model="item.userName"
                      placeholder="安全员姓名"
                      style="margin-right: 10px"
                    /> -->
                    {{ item.userName }}
                  </div>
                  <div>
                    <el-input
                      :disabled="isCheck"
                      v-model="item.totalScore"
                      placeholder="安全员得分"
                      style="width: 200px"
                      @input="
                        handleSafetyOfficerInput(
                          scope.row,
                          item.totalScore,
                          index
                        )
                      "
                      @blur="
                        handleSafetyOfficerBlur(
                          scope.row,
                          item.totalScore,
                          index
                        )
                      "
                    />
                  </div>
                  <!-- <el-button
                    type="danger"
                    style="margin-left: 20px"
                    @click="deleteRowSafetyOfficer(scope.row, index)"
                    >删除</el-button
                  > -->
                </div>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="查看详情"
      :visible.sync="openDetailVisable"
      width="1400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-table
        ref="tableRef"
        :data="tableDataDetail"
        border
        style="width: 100%"
        height="400"
      >
        <el-table-column label="安全生产责任制考核表" align="center">
          <el-table-column type="index" width="50" label="序号">
          </el-table-column>
          <el-table-column
            label="安全生产考核指标"
            prop="type"
            align="center"
            width="200"
          />
          <el-table-column
            label="落实情况"
            prop="implementationStatus"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.implementationStatus }}
            </template>
          </el-table-column>
          <el-table-column
            label="总分"
            prop="totalScore"
            align="center"
            width="80"
          />
          <el-table-column label="项目经理得分" align="center">
            <template slot-scope="scope">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  justify-content: space-around;
                "
              >
                <div>
                  {{ scope.row.projectManager }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="安全员得分" align="center">
            <template slot-scope="scope">
              <div
                style="
                  display: flex;
                  flex-direction: row;
                  justify-content: space-around;
                  margin-top: 10px;
                "
              >
                <div>
                  {{ scope.row.safetyOfficer }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDetail">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAssessment,
  getAssessment,
  getAssessmentDetail,
  delAssessment,
  addAssessment,
  updateAssessment,
} from "@/api/system/safetyAssessmentInfo/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { querytree, getEnterpriseInfo } from "@/api/system/info";
export default {
  name: "Assessment",
  components: {
    selectPeopleTree,
  },
  data() {
    return {
      isCheck: false,
      projectList: [],
      companyList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全生产责任制考核表格数据
      assessmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openDetailVisable: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assessmentProjectName: null,
        company: null,
        assessmentCycle: null,
        assessmentTime: null,
        pmScore: null,
        safetyOfficerScore: null,
        projectCode: null,
      },
      // 表单参数
      form: {
        projectManagerName: "",
        safetyOfficerName: [],
      },
      // 表单校验
      rules: {
        assessmentProjectName: [
          { required: true, message: "请输入项目名称", trigger: "change" },
        ],
        company: [
          { required: true, message: "请选择所属公司", trigger: "change" },
        ],
        assessmentCycle: [
          { required: true, message: "请输入考核周期", trigger: "blur" },
        ],
        assessmentTime: [
          { required: true, message: "请输入考核时间", trigger: "blur" },
        ],
        projectManagerName: [
          { required: true, message: "请输入项目经理姓名", trigger: "blur" },
        ],
        safetyOfficerName: [
          { required: true, message: "请输入安全员姓名", trigger: "blur" },
        ],
      },
      tableData: [
        {
          index: "1",
          type: "安全管理10%",
          totalScore: "15",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "2",
          type: "文明施工15%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "3",
          type: "脚手架15%",
          totalScore: "15",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "4",
          type: "基坑支护5%",
          totalScore: "5",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "5",
          type: "模板支架10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "6",
          type: "高处作业10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "7",
          type: "施工用电10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "8",
          type: "施工升降机10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "9",
          type: "塔式起重机与起重吊装10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "10",
          type: "施工机具5%",
          totalScore: "5",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
      ],
      tableDataDetail: [
        {
          index: "1",
          type: "安全管理10%",
          totalScore: "15",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "2",
          type: "文明施工15%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "3",
          type: "脚手架15%",
          totalScore: "15",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "4",
          type: "基坑支护5%",
          totalScore: "5",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "5",
          type: "模板支架10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "6",
          type: "高处作业10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "7",
          type: "施工用电10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "8",
          type: "施工升降机10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "9",
          type: "塔式起重机与起重吊装10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
        {
          index: "10",
          type: "施工机具5%",
          totalScore: "5",
          implementationStatus: "",
          projectManager: "",
          safetyOfficer: "",
        },
      ],
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    this.getCompanyList();
  },
  methods: {
    handleInput(row) {
      let value = row.projectManager.totalScore;
      // 清除除数字和小数点外的所有字符
      let val = value.replace(/[^\d.]/g, "");
      // 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");
      // 确保小数点不在开头
      val = val.replace(/^\./g, "");
      // 只保留两位小数
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      this.tableData.forEach((item) => {
        if (item.index == row.index) {
          item.projectManager.totalScore = val;
        }
      });
    },
    handleBlur(row) {
      let val = row.projectManager.totalScore;
      if (val && val[val.length - 1] === ".") {
        val = val.slice(0, -1);
      }
      this.tableData.forEach((item) => {
        if (item.index == row.index) {
          item.projectManager.totalScore = val;
        }
      });
    },
    handleSafetyOfficerInput(row, value, index) {
      // 清除除数字和小数点外的所有字符
      let val = value.replace(/[^\d.]/g, "");
      // 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");
      // 确保小数点不在开头
      val = val.replace(/^\./g, "");
      // 只保留两位小数
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      this.tableData.forEach((item) => {
        if (item.index == row.index) {
          item.safetyOfficer[index].totalScore = val;
        }
      });
    },
    handleSafetyOfficerBlur(row, value, index) {
      // 清除除数字和小数点外的所有字符
      let val = value;
      if (val && val[val.length - 1] === ".") {
        val = val.slice(0, -1); // 截取除最后一位外的所有字符
      }
      this.tableData.forEach((item) => {
        if (item.index == row.index) {
          item.safetyOfficer[index].totalScore = val;
        }
      });
    },
    handleInputProductManagerName(data) {
      this.form.projectManagerName = data;
      this.$forceUpdate();
    },
    handleInputSafetyOfficerName(data, index) {
      //   console.log(data, index);
      this.tableData.forEach((item) => {
        item.safetyOfficer[index].userName = data;
      });
      this.$forceUpdate();
    },
    // 新增输入框
    addItem() {
      //   this.form.safetyOfficerName.push("");
      this.$set(
        this.form.safetyOfficerName,
        this.form.safetyOfficerName.length,
        ""
      );

      this.tableData.forEach((item) => {
        item.safetyOfficer.push({ type: 2, userName: "", totalScore: "" });
      });
      this.$nextTick(() => {
        this.$refs.tableRef.doLayout();
      });
      this.$forceUpdate(); // 强制组件重新渲染
      //   console.log("新增的安全员", this.form.safetyOfficerName);
    },
    // 删除输入框
    removeItem(index) {
      this.form.safetyOfficerName.splice(index, 1);
      this.tableData.forEach((item) => {
        item.safetyOfficer.splice(index, 1);
      });
      this.$forceUpdate();
    },
    addRowSafetyOfficer(row) {
      this.tableData.forEach((item) => {
        if (item.index === row.index) {
          item.safetyOfficer.push({ type: 2, userName: "", totalScore: "" });
        }
      });
      this.$nextTick(() => {
        // 调用doLayout()重新计算布局
        this.$refs.tableRef.doLayout();
      });
      //   console.log("新增安全员", row);
    },
    deleteRowSafetyOfficer(row, deleteIndex) {
      if (row.safetyOfficer.length == 1) {
        this.$message({
          showClose: true,
          message: "至少保留一位安全员",
          type: "warning",
        });
        return;
      }
      this.tableData.forEach((item) => {
        if (item.index === row.index) {
          item.safetyOfficer.splice(deleteIndex, 1);
        }
      });
      //   console.log("删除安全员", deleteIndex);
    },
    /** 查询安全生产责任制考核列表 */
    getList() {
      this.loading = true;
      listAssessment(this.queryParams).then((response) => {
        this.assessmentList = response.rows;
        this.total = response.total;
        this.loading = false;
        // console.log("查询列表", this.assessmentList);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        assessmentProjectName: null,
        company: null,
        assessmentCycle: null,
        assessmentTime: null,
        projectManagerName: null,
        safetyOfficerName: [],
        pmScore: null,
        safetyOfficerScore: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        projectCode: null,
        projectAssessmentDetailVOList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.initTableData();
      this.reset();
      this.open = true;
      this.title = "添加安全生产责任制考核";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      this.form = row;
      let projectAssessmentDetailRespVOList =
        row.projectAssessmentDetailRespVOList;
      let projectAssessmentUserScoreRespVOList =
        projectAssessmentDetailRespVOList[0]
          .projectAssessmentUserScoreRespVOList;
      let projectManagerList = projectAssessmentUserScoreRespVOList.filter(
        (item) => item.type == 1
      );
      let safetyAssessmentList = projectAssessmentUserScoreRespVOList.filter(
        (item) => item.type == 2
      );
      //   console.log("aaaaaaaa", projectManagerList);
      //   console.log("bbbbbbbb", safetyAssessmentList);
      this.form.projectManagerName =
        projectManagerList.length > 0 ? projectManagerList[0].userName : "";
      this.form.safetyOfficerName =
        safetyAssessmentList.length > 0
          ? safetyAssessmentList.map((item) => item.userName)
          : [];
      this.initTableData();
      //   this.tableData.forEach(item => {
      //     item.implementationStatus = safetyAssessmentList.map(item => item.userName)
      //   });
      for (let i = 0; i < this.tableData.length; i++) {
        let tempProjectManagerList = projectAssessmentDetailRespVOList[
          i
        ].projectAssessmentUserScoreRespVOList.filter((item) => item.type == 1);
        let tempSafetyAssessmentList = projectAssessmentDetailRespVOList[
          i
        ].projectAssessmentUserScoreRespVOList.filter((item) => item.type == 2);
        this.tableData[i].implementationStatus =
          projectAssessmentDetailRespVOList[i].implementationStatus;
        this.tableData[i].implementationStatus;
        this.tableData[i].projectManager = tempProjectManagerList[0];
        this.tableData[i].safetyOfficer = tempSafetyAssessmentList;
      }
      this.open = true;
      this.isCheck = false;
      this.title = "修改安全生产责任制考核";
      console.log("999", this.isCheck);
      //   getAssessment(id).then((response) => {
      //     this.form = response.data;
      //     this.open = true;
      //     this.title = "修改安全生产责任制考核";
      //   });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      this.form = row;
      let projectAssessmentDetailRespVOList =
        row.projectAssessmentDetailRespVOList;
      let projectAssessmentUserScoreRespVOList =
        projectAssessmentDetailRespVOList[0]
          .projectAssessmentUserScoreRespVOList;
      let projectManagerList = projectAssessmentUserScoreRespVOList.filter(
        (item) => item.type == 1
      );
      let safetyAssessmentList = projectAssessmentUserScoreRespVOList.filter(
        (item) => item.type == 2
      );
      this.form.projectManagerName =
        projectManagerList.length > 0 ? projectManagerList[0].userName : "";
      this.form.safetyOfficerName =
        safetyAssessmentList.length > 0
          ? safetyAssessmentList.map((item) => item.userName)
          : [];
      this.initTableData();
      for (let i = 0; i < this.tableData.length; i++) {
        let tempProjectManagerList = projectAssessmentDetailRespVOList[
          i
        ].projectAssessmentUserScoreRespVOList.filter((item) => item.type == 1);
        let tempSafetyAssessmentList = projectAssessmentDetailRespVOList[
          i
        ].projectAssessmentUserScoreRespVOList.filter((item) => item.type == 2);
        this.tableData[i].implementationStatus =
          projectAssessmentDetailRespVOList[i].implementationStatus;
        this.tableData[i].implementationStatus;
        this.tableData[i].projectManager = tempProjectManagerList[0];
        this.tableData[i].safetyOfficer = tempSafetyAssessmentList;
      }
      this.open = true;
      this.isCheck = true;
      this.title = "查看安全生产责任制考核";
    },
    // handleUpdate(row) {
    //   this.reset();
    //   const id = row.id || this.ids;
    //   this.form = row;
    //   let projectAssessmentDetailRespVOList =
    //     row.projectAssessmentDetailRespVOList;
    //   let projectAssessmentUserScoreRespVOList =
    //     projectAssessmentDetailRespVOList[0]
    //       .projectAssessmentUserScoreRespVOList;
    //   let projectManagerList = projectAssessmentUserScoreRespVOList.filter(
    //     (item) => item.type == 1
    //   );
    //   let safetyAssessmentList = projectAssessmentUserScoreRespVOList.filter(
    //     (item) => item.type == 2
    //   );
    //   //   console.log("aaaaaaaa", projectManagerList);
    //   //   console.log("bbbbbbbb", safetyAssessmentList);
    //   this.form.projectManagerName =
    //     projectManagerList.length > 0 ? projectManagerList[0].userName : "";
    //   this.form.safetyOfficerName =
    //     safetyAssessmentList.length > 0
    //       ? safetyAssessmentList.map((item) => item.userName)
    //       : [];
    //   this.initTableData();
    //   //   this.tableData.forEach(item => {
    //   //     item.implementationStatus = safetyAssessmentList.map(item => item.userName)
    //   //   });
    //   for (let i = 0; i < this.tableData.length; i++) {
    //     let tempProjectManagerList = projectAssessmentDetailRespVOList[
    //       i
    //     ].projectAssessmentUserScoreRespVOList.filter((item) => item.type == 1);
    //     let tempSafetyAssessmentList = projectAssessmentDetailRespVOList[
    //       i
    //     ].projectAssessmentUserScoreRespVOList.filter((item) => item.type == 2);
    //     this.tableData[i].implementationStatus =
    //       projectAssessmentDetailRespVOList[i].implementationStatus;
    //     this.tableData[i].implementationStatus;
    //     this.tableData[i].projectManager = tempProjectManagerList[0];
    //     this.tableData[i].safetyOfficer = tempSafetyAssessmentList;
    //   }
    //   this.open = true;
    //   this.title = "修改安全生产责任制考核";
    //   //   getAssessment(id).then((response) => {
    //   //     this.form = response.data;
    //   //     this.open = true;
    //   //     this.title = "修改安全生产责任制考核";
    //   //   });
    // },
    getDetail(row) {
      //   this.reset();
      const id = row.id;
      getAssessmentDetail(id).then((response) => {
        let projectAssessmentDetailList = response.data;
        for (let i = 0; i < this.tableDataDetail.length; i++) {
          this.tableDataDetail[i].implementationStatus =
            projectAssessmentDetailList[i].implementationStatus;
          this.tableDataDetail[i].projectManager =
            projectAssessmentDetailList[i].projectManagerScore;
          this.tableDataDetail[i].safetyOfficer =
            projectAssessmentDetailList[i].safetyOfficerScores;
        }
        setTimeout(() => {
          this.openDetailVisable = true;
        }, 200);
      });
    },
    cancelDetail() {
      this.openDetailVisable = false;
    },
    /** 提交按钮 */
    submitForm() {
      if (
        this.form.safetyOfficerName.length == 0 ||
        this.form.safetyOfficerName[0] == ""
      ) {
        this.$message.error("安全员至少一个");
        return;
      }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let flag = true;
          this.tableData.forEach((item) => {
            let projectManager = item.projectManager;
            let safetyOfficer = item.safetyOfficer;
            if (projectManager.totalScore == "") {
              if (flag) {
                flag = false;
                this.$message.error(item.type + "的项目经理信息尚未完善");
              }
            }
            if (!flag) {
              return;
            }
            safetyOfficer.forEach((safetyOfficerItem) => {
              if (safetyOfficerItem.totalScore == "") {
                if (flag) {
                  flag = false;
                  this.$message.error(item.type + "的安全员信息尚未完善");
                }
              }
            });
          });
          if (!flag) {
            return;
          }

          let projectAssessmentDetailVOList = [];

          this.tableData.forEach((item) => {
            let tempProjectManager = item.projectManager;
            tempProjectManager.userName = this.form.projectManagerName;
            tempProjectManager.userIndex = 0;
            // console.log("bbbb", item.safetyOfficer);
            for (let i = 0; i < item.safetyOfficer.length; i++) {
              //   console.log("aaauuuu", item.safetyOfficer[i]);
              item.safetyOfficer[i].userIndex = i + 1;
            }
            let projectAssessmentUserScoreVOList = [
              tempProjectManager,
              ...item.safetyOfficer,
            ];
            projectAssessmentDetailVOList.push({
              implementationStatus: item.implementationStatus,
              projectAssessmentUserScoreVOList,
            });
          });
          this.form.projectAssessmentDetailVOList =
            projectAssessmentDetailVOList;

          if (this.form.id != null) {
            updateAssessment(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.initTableData();
              this.getList();
            });
          } else {
            addAssessment(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.initTableData();
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全生产责任制考核编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAssessment(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/projectAssessment/export",
        {
          ...this.queryParams,
        },
        `assessment_${new Date().getTime()}.xlsx`
      );
    },
    initTableData() {
      this.tableData = [
        {
          index: "1",
          type: "安全管理10%",
          totalScore: "15",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "2",
          type: "文明施工15%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "3",
          type: "脚手架15%",
          totalScore: "15",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "4",
          type: "基坑支护5%",
          totalScore: "5",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "5",
          type: "模板支架10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "6",
          type: "高处作业10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "7",
          type: "施工用电10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "8",
          type: "施工升降机10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "9",
          type: "塔式起重机与起重吊装10%",
          totalScore: "10",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
        {
          index: "10",
          type: "施工机具5%",
          totalScore: "5",
          implementationStatus: "",
          projectManager: { type: 1, userName: "", totalScore: "" },
          safetyOfficer: [],
        },
      ];
    },
    /** 获取项目列表 */
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    /** 项目选择变化处理 */
    handleProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.assessmentProjectName = selectedItem.label;
        this.form.projectId = selectedItem.id;
      } else {
        this.form.assessmentProjectName = null;
        this.form.projectId = null;
      }
    },
    handleChange(selectedItem) {
      if (selectedItem) {
        this.form.company = selectedItem.label;
      } else {
        this.form.company = null;
      }
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
  },
};
</script>
