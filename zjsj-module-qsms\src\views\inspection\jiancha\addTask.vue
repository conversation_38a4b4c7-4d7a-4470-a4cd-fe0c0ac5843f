<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left" v-if="!taskDetail.id">新增任务</div>
            <div class="top-left" v-else>编辑任务</div>
            <div class="top-right">
              <el-form
                :model="taskDetail"
                ref="form"
                :rules="rules"
                size="small"
                :inline="true"
              >
                <el-form-item v-if="taskDetail.id" label="单号" prop="taskType">
                  <span>{{ taskDetail.taskNumber }}</span>
                </el-form-item>
                <!-- <el-form-item label="任务下发街镇" prop="belongStreet">
                                    <el-select v-model="taskDetail.belongStreet" placeholder="请选择所属街镇" clearable>
                                        <el-option v-for="dict in dict.type.pz_street" :key="dict.value" :label="dict.label"
                                        :value="dict.value" />
                                    </el-select>
                                </el-form-item> -->
                <el-form-item label="巡检时间" prop="inspectTime">
                  <el-date-picker
                    v-model="taskDetail.inspectTime"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="巡检类型" prop="inspectType">
                  <el-select
                    v-model="taskDetail.inspectType"
                    placeholder="请选择巡检类型"
                    clearable
                    style="width: 150px"
                  >
                    <el-option
                      v-for="dict in dict.type.inspect_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            <!-- <div class="top-right" v-if="isAdmin">
                            <el-form :model="taskDetail" ref="form" :rules="rules" size="small" :inline="true">
                                <el-form-item v-if="taskDetail.id" label="单号" prop="taskType">
                                    <span>{{ taskDetail.taskNumber }}</span>
                                </el-form-item>
                            </el-form>  
                        </div> -->
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            label-width="80px"
          >
            <!-- <el-form-item label="所属街镇" prop="street">
                            <el-select v-model="queryParams.street" placeholder="请选择所属街镇" clearable>
                                <el-option v-for="dict in dict.type.pz_street" :key="dict.value" :label="dict.label"
                                :value="dict.value" />
                            </el-select>
                        </el-form-item> -->
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="queryParams.projectName"
                placeholder="请输入项目名称"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="项目类型" prop="useStatus">
              <el-select
                v-model="queryParams.useStatus"
                placeholder="请选择项目类型"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="dict in dict.type.gc_use_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >查询</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <el-table
            v-loading="loading"
            :data="list"
            @selection-change="handleSelectionChange"
            ref="projectTable"
          >
            <el-table-column type="selection" width="55" align="left" />

            <el-table-column
              label="项目名称"
              align="left"
              prop="corporatename"
            />
            <el-table-column
              label="项目地址"
              align="left"
              prop="companyaddress"
            />
            <el-table-column label="项目负责人" align="left" prop="liaison" />
            <el-table-column label="所属公司" align="left" prop="id" />
            <!-- <el-table-column label="项目类型" align="center" prop="useStatus">
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.gc_use_status"
                  :value="scope.row.useStatus"
                />
              </template>
            </el-table-column> -->
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
        <!-- <el-card class="box-card" v-if="isAdmin">
                  <div class="task">
                    <p>任务描述:</p>
                    <el-input
                      type="textarea"
                      autosize
                      placeholder="请输入描述"
                      v-model="taskDesc">
                    </el-input>
                  </div>
                </el-card> -->
      </el-col>
    </el-row>

    <!-- 修改底部按钮区域的位置 -->
    <div class="fixed-bottom">
      <div class="button-group">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading"
          >提交</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import { listProjectinspection } from "@/api/system/projectinspection.js";
import { formatDate } from "@/utils/index";
import {
  addJianChaTask,
  getJianChaTaskInfo,
  editJianChaTask,
} from "@/api/inspection/jiancha";
export default {
  name: "Checkdeinspectrecords",
  dicts: ["pz_street", "inspect_type", "gc_use_status"],
  data() {
    return {
      //权限区分
      isAdmin: false,
      //任务描述
      taskDesc: "",
      // 编辑表单内容
      editForm: {},
      // 详情表单内容
      detailForm: {},
      // 显示详情表单
      showDetailForm: false,
      // 显示编辑表单
      showEditForm: false,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 5,
      // 项目检查表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,

        taskType: "",
        useStatus: "",
        projectName: "",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 检查周期选项
      periodOptions: [{ label: "日常检查", value: "1" }],
      // 提交按钮加载状态
      submitLoading: false,
      // 选中的项目
      selectedProjects: [],
      allSelectedProjects: [],
      // 初始化时复制一份数据用于筛选
      originalList: [],
      taskDetail: {
        inspectTime: [],
        inspectType: "1",
        belongStreet: "",
      },
      isInitializing: false, // 添加初始化标记
      rules: {
        inspectTime: [
          { required: true, message: "请选择巡检时间", trigger: "change" },
        ],
        inspectType: [
          { required: true, message: "请选择巡检类型", trigger: "change" },
        ],
        // belongStreet: [
        //     { required: true, message: '请选择所属街镇', trigger: 'change' }
        // ]
      },
    };
  },
  created() {
    const role = store.getters.roles;
    if (
      role.includes("admin") ||
      role.includes("qujiadmin") ||
      role.includes("pqwgy")
    ) {
      this.isAdmin = true;
    } else {
      this.isAdmin = false;
    }
    this.getList();
    // 获取任务单号
    if (this.$route.query.id) {
      // 编辑
      this.taskDetail.id = this.$route.query.id;
      this.getTaskDetail();
    }
  },
  mounted() {},
  methods: {
    getTaskDetail() {
      getJianChaTaskInfo(this.taskDetail.id).then((res) => {
        this.taskDetail = res.data;
        this.allSelectedProjects = this.taskDetail.detailList;
        this.initPageSelect();
      });
    },
    getList() {
      this.loading = true;
      listProjectinspection(this.queryParams).then((response) => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
        this.initPageSelect();
      });
    },
    initPageSelect() {
      this.selectedProjects = [];
      this.isInitializing = true; // 设置初始化标记
      this.$nextTick(() => {
        this.list.forEach((row) => {
          let index = this.allSelectedProjects.findIndex(
            (item) => item.projectId == row.id
          );
          if (index !== -1) {
            this.$refs.projectTable.toggleRowSelection(row, true);
            this.selectedProjects.push(row);
          }
        });
        this.$nextTick(() => {
          this.isInitializing = false; // 初始化完成后重置标记
        });
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.street = store.getters.belongStreet;
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        street: "",
        taskType: "",
        useStatus: "",
        projectName: "",
      };
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // 如果是初始化过程，不执行数据更新逻辑
      if (this.isInitializing) {
        return;
      }

      // 获取当前页面所有数据的ID
      const currentPageIds = this.list.map((item) => item.id);

      // 保留非本页的已选数据
      this.allSelectedProjects = this.allSelectedProjects.filter(
        (item) => !currentPageIds.includes(item.projectId)
      );

      // 将当前页面选中的数据添加到 allSelectedProjects
      selection.forEach((row) => {
        const projectData = {
          projectId: row.id,
          ...row,
        };

        const exists = this.allSelectedProjects.some(
          (item) => item.projectId === row.id
        );
        if (!exists) {
          this.allSelectedProjects.push(projectData);
        }
      });

      // 更新当前页选中数据
      this.selectedProjects = selection;
    },
    // 取消按钮
    handleCancel() {
      this.$router.go(-1);
    },

    // 提交按钮
    handleSubmit() {
      if (this.allSelectedProjects.length === 0) {
        this.$message({
          message: "请选择要添加的项目",
          type: "warning",
        });
        return;
      }

      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            belongStreet: store.getters.belongStreet,
            inspectTime: formatDate(this.taskDetail.inspectTime[0]),
            deadlineTime: formatDate(this.taskDetail.inspectTime[1]),
            projectIds: this.allSelectedProjects
              .map((item) => item.projectId)
              .join(","),
            inspectType: this.taskDetail.inspectType,
          };
          console.log(params);
          this.submitLoading = true;

          if (this.taskDetail.id) {
            params.id = this.taskDetail.id;
            editJianChaTask(params)
              .then((res) => {
                this.submitLoading = false;
                if (res.code == 200) {
                  this.$modal.msgSuccess("修改成功");
                  this.$router.go(-1);
                } else {
                  this.$modal.msgError(res.msg);
                }
              })
              .finally(() => {
                this.submitLoading = false;
              });
          } else {
            params.taskStatus = 1;
            addJianChaTask(params)
              .then((res) => {
                this.submitLoading = false;
                if (res.code == 200) {
                  this.$modal.msgSuccess("提交成功");
                  this.$router.go(-1);
                } else {
                  this.$modal.msgError(res.msg);
                }
              })
              .finally(() => {
                this.submitLoading = false;
              });
          }
        }
      });

      // if (!this.taskDetail.inspectTime) {
      //     this.$message({
      //         message: '请选择巡检时间',
      //         type: 'warning'
      //     });
      //     return;
      // }

      // if (!this.taskDetail.inspectType) {
      //     this.$message({
      //         message: '请选择巡检类型',
      //         type: 'warning'
      //     });
      //     return;
      // }
    },
    handleSubmitAdmin() {},
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  // overflow-y: auto;
  height: calc(100vh - 100px);

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      .detail-center {
        display: flex;

        .detail-center-left {
          .detail-center-item {
            display: flex;
            line-height: 24px;
            margin: 12px 0;

            .detail-center-item-left {
              width: 70px;
            }

            .detail-center-item-right {
              flex: 1;
            }
          }
        }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 60%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  // justify-content: space-between;
  gap: 20px;

  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
    align-items: center;
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  width: calc(100% - 215px); // 减去左侧菜单的宽度
  height: 51px;
  background-color: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.12);
  z-index: 100;

  .button-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    padding-right: 20px;

    .el-button {
      padding: 12px 35px;
      margin-left: 10px;
    }
  }
}

.box-card {
  height: calc(100vh - 240px); // 减去顶部卡片高度和底部按钮高度
  overflow-y: auto;
  font-size: 14px;
  margin-bottom: 60px; // 为固定底部留出空间
  .task {
    width: 100%;
    display: flex;
    align-items: center;
    p {
      flex-shrink: 0;
      margin-right: 10px;
      font-family: 500;
      font-size: 16px;
    }
  }
}

.el-table {
  height: calc(100% - 120px); // 减去表格上方筛选区域的高度
}
</style>
