import request from '@/utils/request'

// 查询评价配置列表
export function listZjEvaluationConfigInfo(query) {
  return request({
    url: '/contractor/zjEvaluationConfigInfo/list',
    method: 'get',
    params: query
  })
}

// 查询评价配置详细
export function getZjEvaluationConfigInfo(id) {
  return request({
    url: '/contractor/zjEvaluationConfigInfo/' + id,
    method: 'get'
  })
}

// 新增评价配置
export function addZjEvaluationConfigInfo(data) {
  return request({
    url: '/contractor/zjEvaluationConfigInfo',
    method: 'post',
    data: data
  })
}

// 修改评价配置
export function updateZjEvaluationConfigInfo(data) {
  return request({
    url: '/contractor/zjEvaluationConfigInfo',
    method: 'put',
    data: data
  })
}

// 删除评价配置
export function delZjEvaluationConfigInfo(id) {
  return request({
    url: '/contractor/zjEvaluationConfigInfo/' + id,
    method: 'delete'
  })
}
