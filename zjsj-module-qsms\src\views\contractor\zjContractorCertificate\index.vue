<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="证书名称" prop="certificateName">
        <el-input
          v-model="queryParams.certificateName"
          placeholder="请输入证书名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业绩周期开始时间" prop="cycleBeginTime">
        <el-date-picker
          clearable
          v-model="queryParams.cycleBeginTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择业绩周期开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="业绩周期结束时间" prop="cycleEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.cycleEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择业绩周期结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="业绩概述" prop="performanceOverview">
        <el-input
          v-model="queryParams.performanceOverview"
          placeholder="请输入业绩概述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="承包商id" prop="contractorId">
        <el-input
          v-model="queryParams.contractorId"
          placeholder="请输入承包商id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['contractor:zjContractorCertificate:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['contractor:zjContractorCertificate:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:zjContractorCertificate:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['contractor:zjContractorCertificate:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjContractorCertificateList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="证书名称" align="center" prop="certificateName" />
      <el-table-column
        label="业绩周期开始时间"
        align="center"
        prop="cycleBeginTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.cycleBeginTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="业绩周期结束时间"
        align="center"
        prop="cycleEndTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.cycleEndTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="业绩概述"
        align="center"
        prop="performanceOverview"
      />
      <el-table-column
        label="附件"
        align="center"
        prop="attachmentUrl"
        width="100"
      >
        <template slot-scope="scope">
          <image-preview
            :src="scope.row.attachmentUrl"
            :width="50"
            :height="50"
          />
        </template>
      </el-table-column>
      <el-table-column label="承包商id" align="center" prop="contractorId" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:zjContractorCertificate:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:zjContractorCertificate:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改承包商业绩证书对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="证书名称" prop="certificateName">
          <el-input
            v-model="form.certificateName"
            placeholder="请输入证书名称"
          />
        </el-form-item>
        <el-form-item label="业绩周期开始时间" prop="cycleBeginTime">
          <el-date-picker
            clearable
            v-model="form.cycleBeginTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择业绩周期开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="业绩周期结束时间" prop="cycleEndTime">
          <el-date-picker
            clearable
            v-model="form.cycleEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择业绩周期结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="业绩概述" prop="performanceOverview">
          <el-input
            v-model="form.performanceOverview"
            placeholder="请输入业绩概述"
          />
        </el-form-item>
        <el-form-item label="附件" prop="attachmentUrl">
          <image-upload v-model="form.attachmentUrl" />
        </el-form-item>
        <el-form-item label="承包商id" prop="contractorId">
          <el-input v-model="form.contractorId" placeholder="请输入承包商id" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjContractorCertificate,
  getZjContractorCertificate,
  delZjContractorCertificate,
  addZjContractorCertificate,
  updateZjContractorCertificate,
} from "@/api/contractor/zjContractorCertificate";

export default {
  name: "ZjContractorCertificate",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 承包商业绩证书表格数据
      zjContractorCertificateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        certificateName: null,
        cycleBeginTime: null,
        cycleEndTime: null,
        performanceOverview: null,
        attachmentUrl: null,
        contractorId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询承包商业绩证书列表 */
    getList() {
      this.loading = true;
      listZjContractorCertificate(this.queryParams).then((response) => {
        this.zjContractorCertificateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        certificateName: null,
        cycleBeginTime: null,
        cycleEndTime: null,
        performanceOverview: null,
        attachmentUrl: null,
        contractorId: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加承包商业绩证书";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjContractorCertificate(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改承包商业绩证书";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjContractorCertificate(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjContractorCertificate(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除承包商业绩证书编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjContractorCertificate(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "contractor/zjContractorCertificate/export",
        {
          ...this.queryParams,
        },
        `zjContractorCertificate_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
