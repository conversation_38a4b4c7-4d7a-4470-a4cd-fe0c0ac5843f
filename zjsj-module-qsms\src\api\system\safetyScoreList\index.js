import request from '@/utils/request'

// 查询安全积分排行榜（记录个人和部门积分及行为）列表
export function listRanking(query) {
  return request({
    url: '/system/ranking/list',
    method: 'get',
    params: query
  })
}

// 查询安全积分排行榜（记录个人和部门积分及行为）详细
export function getRanking(id) {
  return request({
    url: '/system/ranking/' + id,
    method: 'get'
  })
}

// 新增安全积分排行榜（记录个人和部门积分及行为）
export function addRanking(data) {
  return request({
    url: '/system/ranking',
    method: 'post',
    data: data
  })
}

// 修改安全积分排行榜（记录个人和部门积分及行为）
export function updateRanking(data) {
  return request({
    url: '/system/ranking',
    method: 'put',
    data: data
  })
}

// 删除安全积分排行榜（记录个人和部门积分及行为）
export function delRanking(id) {
  return request({
    url: '/system/ranking/' + id,
    method: 'delete'
  })
}
