<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <!-- <el-form-item label="关联的风险点ID" prop="riskPointId">
        <el-input
          v-model="queryParams.riskPointId"
          placeholder="请输入关联的风险点ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="关联的风险类型ID" prop="riskTypeId">
        <el-input
          v-model="queryParams.riskTypeId"
          placeholder="请输入关联的风险类型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="措施编号" prop="measureCode">
        <el-input
          v-model="queryParams.measureCode"
          placeholder="请输入措施编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任部门" prop="responsibleDept">
        <el-input
          v-model="queryParams.responsibleDept"
          placeholder="请输入责任部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任人" prop="responsiblePerson">
        <el-input
          v-model="queryParams.responsiblePerson"
          placeholder="请输入责任人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="措施落实期限" prop="deadline">
        <el-date-picker
          clearable
          v-model="queryParams.deadline"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择措施落实期限"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:measures:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:measures:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:measures:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:measures:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="measuresList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="措施ID" align="center" prop="id" /> -->
      <!-- <el-table-column
        label="关联的风险点ID"
        align="center"
        prop="riskPointId"
      />
      <el-table-column
        label="关联的风险类型ID"
        align="center"
        prop="riskTypeId"
      /> -->
      <el-table-column label="措施编号" align="center" prop="measureCode" />
      <el-table-column label="措施类型" align="center" prop="measureType" />
      <el-table-column
        label="管控措施具体内容"
        align="center"
        prop="measureContent"
      />
      <el-table-column
        label="措施执行标准及要求"
        align="center"
        prop="implementationStandard"
      />
      <el-table-column label="责任部门" align="center" prop="responsibleDept" />
      <el-table-column label="责任人" align="center" prop="responsiblePerson" />
      <el-table-column
        label="措施落实期限"
        align="center"
        prop="deadline"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deadline, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="措施状态" align="center" prop="status" />
      <el-table-column
        label="效果评价"
        align="center"
        prop="effectEvaluation"
      />
      <el-table-column
        label="评价说明"
        align="center"
        prop="evaluationRemark"
      />
      <el-table-column
        label="相关文件附件地址"
        align="center"
        prop="attachmentUrl"
      />
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:measures:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:measures:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改风险控制措施管理（关联风险点与管控措施）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="关联的风险点ID" prop="riskPointId">
          <el-input
            v-model="form.riskPointId"
            placeholder="请输入关联的风险点ID"
          />
        </el-form-item>
        <!-- <el-form-item label="关联的风险类型ID" prop="riskTypeId">
          <el-input
            v-model="form.riskTypeId"
            placeholder="请输入关联的风险类型ID"
          />
        </el-form-item> -->
        <el-form-item label="措施编号" prop="measureCode">
          <el-input v-model="form.measureCode" placeholder="请输入措施编号" />
        </el-form-item>
        <el-form-item label="管控措施具体内容" prop="measureContent">
          <editor v-model="form.measureContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="措施执行标准及要求" prop="implementationStandard">
          <el-input
            v-model="form.implementationStandard"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="责任部门" prop="responsibleDept">
          <el-input
            v-model="form.responsibleDept"
            placeholder="请输入责任部门"
          />
        </el-form-item>
        <el-form-item label="责任人" prop="responsiblePerson">
          <el-input
            v-model="form.responsiblePerson"
            placeholder="请输入责任人"
          />
        </el-form-item>
        <el-form-item label="措施落实期限" prop="deadline">
          <el-date-picker
            clearable
            v-model="form.deadline"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择措施落实期限"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="评价说明" prop="evaluationRemark">
          <el-input
            v-model="form.evaluationRemark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="相关文件附件地址" prop="attachmentUrl">
          <el-input
            v-model="form.attachmentUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMeasures,
  getMeasures,
  delMeasures,
  addMeasures,
  updateMeasures,
} from "@/api/system/controlMeasuresManage/index";

export default {
  name: "Measures",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 风险控制措施管理（关联风险点与管控措施）表格数据
      measuresList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskPointId: null,
        riskTypeId: null,
        measureCode: null,
        measureType: null,
        measureContent: null,
        implementationStandard: null,
        responsibleDept: null,
        responsiblePerson: null,
        deadline: null,
        status: null,
        effectEvaluation: null,
        evaluationRemark: null,
        attachmentUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        riskPointId: [
          {
            required: true,
            message: "关联的风险点ID不能为空",
            trigger: "blur",
          },
        ],
        measureCode: [
          { required: true, message: "措施编号不能为空", trigger: "blur" },
        ],
        measureType: [
          {
            required: true,
            message: "措施类型：技术/管理/教育/个体防护不能为空",
            trigger: "change",
          },
        ],
        measureContent: [
          {
            required: true,
            message: "管控措施具体内容不能为空",
            trigger: "change",
          },
        ],
        responsibleDept: [
          { required: true, message: "责任部门不能为空", trigger: "blur" },
        ],
        responsiblePerson: [
          { required: true, message: "责任人不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询风险控制措施管理（关联风险点与管控措施）列表 */
    getList() {
      this.loading = true;
      listMeasures(this.queryParams).then((response) => {
        this.measuresList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        riskPointId: null,
        riskTypeId: null,
        measureCode: null,
        measureType: null,
        measureContent: null,
        implementationStandard: null,
        responsibleDept: null,
        responsiblePerson: null,
        deadline: null,
        status: null,
        effectEvaluation: null,
        evaluationRemark: null,
        attachmentUrl: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加风险控制措施管理（关联风险点与管控措施）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMeasures(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改风险控制措施管理（关联风险点与管控措施）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateMeasures(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMeasures(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除风险控制措施管理（关联风险点与管控措施）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delMeasures(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/measures/export",
        {
          ...this.queryParams,
        },
        `measures_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
