import request from '@/utils/request'

// 查询安全生产技术方案审批列表
export function listApproval(query) {
  return request({
    url: '/system/techPlanApproval/list',
    method: 'get',
    params: query
  })
}

// 查询安全生产技术方案审批详细
export function getApproval(id) {
  return request({
    url: '/system/techPlanApproval/' + id,
    method: 'get'
  })
}

// 新增安全生产技术方案审批
export function addApproval(data) {
  return request({
    url: '/system/techPlanApproval',
    method: 'post',
    data: data
  })
}

// 修改安全生产技术方案审批
export function updateApproval(data) {
  return request({
    url: '/system/techPlanApproval',
    method: 'put',
    data: data
  })
}

// 删除安全生产技术方案审批
export function delApproval(id) {
  return request({
    url: '/system/techPlanApproval/' + id,
    method: 'delete'
  })
}
