import request from '@/utils/request'

// 查询隐患举报列表
export function listHiddenDangerReport(query) {
  return request({
    url: '/system/hiddenDangerReport/list',
    method: 'get',
    params: query
  })
}

// 查询隐患举报详细
export function getHiddenDangerReport(id) {
  return request({
    url: '/system/hiddenDangerReport/' + id,
    method: 'get'
  })
}

// 新增隐患举报
export function addHiddenDangerReport(data) {
  return request({
    url: '/system/hiddenDangerReport',
    method: 'post',
    data: data
  })
}

// 修改隐患举报
export function updateHiddenDangerReport(data) {
  return request({
    url: '/system/hiddenDangerReport',
    method: 'put',
    data: data
  })
}

// 删除隐患举报
export function delHiddenDangerReport(id) {
  return request({
    url: '/system/hiddenDangerReport/' + id,
    method: 'delete'
  })
}
