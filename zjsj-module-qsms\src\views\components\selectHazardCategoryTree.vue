<template>
  <el-select
    ref="hazardSelectRef"
    :value="displayValue"
    :placeholder="placeholder"
    :disabled="disabled"
    clearable
    filterable
    style="width: 100%"
    class="hazard-category-select"
    popper-class="hazard-category-dropdown"
    @input="handleInput"
    @change="handleSelectChange"
    @visible-change="handleVisibleChange"
    @clear="handleClear"
  >
    <el-option :value="displayValue" style="height: auto; padding: 0">
      <el-tree
        ref="hazardTree"
        :data="treeData"
        :props="treeProps"
        :expand-on-click-node="false"
        node-key="id"
        highlight-current
        style="padding: 5px 0"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <span class="tree-label">{{ node.label }}</span>
        </span>
      </el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'SelectHazardCategoryTree',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '请选择隐患类别',
    },
    categoryList: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      displayValue: '',
      treeProps: {
        label: 'label',
        children: 'children',
      },
    }
  },

  computed: {
    treeData() {
      return this.processTreeData(this.categoryList)
    },
  },

  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        console.log('value 变化:', newVal)
        this.displayValue = newVal || ''
        // 当值改变时，延迟设置树的选中状态
        setTimeout(() => {
          this.setTreeSelection()
        }, 100)
      },
    },
    categoryList: {
      immediate: true,
      handler(newData) {
        console.log('categoryList 变化:', newData)
        // 当分类列表变化时，延迟设置树的选中状态
        if (newData && newData.length > 0 && this.displayValue) {
          setTimeout(() => {
            this.setTreeSelection()
          }, 200)
        }
      },
    },
  },

  mounted() {
    // 组件挂载后初始化设置
    this.$nextTick(() => {
      this.setTreeSelection()
    })
  },

  methods: {
    handleVisibleChange(isVisible) {
      if (isVisible) {
        // 下拉框打开时，设置当前选中的节点
        this.$nextTick(() => {
          if (this.value && this.$refs.hazardTree) {
            const selectedNode = this.findNodeById(this.treeData, this.value)
            if (selectedNode) {
              this.$refs.hazardTree.setCurrentKey(this.value)
              this.expandToNode(selectedNode)
            }
          }
        })
      }
    },

    handleNodeClick(data, node) {
      console.log('点击隐患类别节点', data, node)
      
      // 如果有子节点，切换展开状态
      if (data.children && data.children.length > 0) {
        node.expanded = !node.expanded
        return
      }
      
      // 叶子节点，触发选择
      this.selectNode(data)
    },

    selectNode(data) {
      this.displayValue = data.label
      this.$emit('input', data.label)
      
      // 触发 change 事件，传递完整的节点信息
      this.$emit('change', {
        id: data.id,
        label: data.label,
        value: data.label,
      })
      
      // 更新树的高亮选择
      this.$refs.hazardTree.setCurrentKey(data.id)
      
      // 关闭下拉框
      this.$nextTick(() => {
        this.closeDropdown()
      })
    },

    handleInput(value) {
      this.displayValue = value
      this.$emit('input', value)
    },

    handleSelectChange(value) {
      if (!value) {
        this.$emit('change', null)
      }
    },

    handleClear() {
      this.displayValue = ''
      this.$emit('input', '')
      this.$emit('change', null)
    },

    closeDropdown() {
      this.$refs.hazardSelectRef.blur()
    },

    // 处理树形数据
    processTreeData(data) {
      if (!data || !Array.isArray(data)) return []
      
      return data.map((item) => ({
        ...item,
        id: item.id || item.hazardId,
        label: item.label || item.hazardName || item.name,
        children: item.children ? this.processTreeData(item.children) : [],
      }))
    },

    // 根据ID查找节点
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) return node
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },

    // 展开到指定节点
    expandToNode(targetNode) {
      if (!targetNode || !this.$refs.hazardTree) return
      
      // 递归展开父节点
      const expandParents = (node) => {
        const parent = this.findParentNode(this.treeData, node.id)
        if (parent) {
          this.$refs.hazardTree.setExpandedKey(parent.id, true)
          expandParents(parent)
        }
      }
      
      expandParents(targetNode)
    },

    // 查找父节点
    findParentNode(nodes, childId, parent = null) {
      for (const node of nodes) {
        if (node.id === childId) {
          return parent
        }
        if (node.children && node.children.length) {
          const found = this.findParentNode(node.children, childId, node)
          if (found) return found
        }
      }
      return null
    },

    // 根据标签路径查找节点（用于回显）
    findNodeByLabelPath(labelPath) {
      console.log('=== 开始查找节点路径 ===')
      console.log('查找路径:', labelPath)

      if (!labelPath || !this.treeData.length) {
        console.log('路径为空或树数据为空')
        return null
      }

      // 分割路径，例如 "安全管理-安全生产责任制" -> ["安全管理", "安全生产责任制"]
      const pathParts = labelPath.split('-').map(part => part.trim())
      console.log('路径分割结果:', pathParts)

      let currentNodes = this.treeData
      let targetNode = null

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i]
        console.log(`\n--- 查找第${i+1}级: "${part}" ---`)
        console.log('当前层级可选节点:', currentNodes.map(n => `"${n.label}" (id: ${n.id})`))

        targetNode = currentNodes.find(node => node.label === part)

        if (!targetNode) {
          console.log(`❌ 未找到匹配的节点: "${part}"`)
          console.log('可能的原因：标签不匹配或节点不存在')
          return null
        }

        console.log(`✅ 找到节点: "${part}" (id: ${targetNode.id})`)

        if (i < pathParts.length - 1) {
          // 不是最后一级，继续查找子节点
          if (targetNode.children && targetNode.children.length > 0) {
            currentNodes = targetNode.children
            console.log(`进入下一级，子节点数量: ${currentNodes.length}`)
          } else {
            console.log(`❌ 节点 "${part}" 没有子节点，但路径还未结束`)
            return null
          }
        }
      }

      console.log('\n=== 查找完成 ===')
      console.log('最终找到的目标节点:', {
        id: targetNode.id,
        label: targetNode.label,
        hasChildren: !!(targetNode.children && targetNode.children.length > 0)
      })

      return targetNode
    },

    // 设置树形选择回显
    setTreeSelection() {
      console.log('=== setTreeSelection 开始 ===')
      console.log('displayValue:', this.displayValue)
      console.log('treeData 长度:', this.treeData?.length)
      console.log('hazardTree ref:', this.$refs.hazardTree)

      if (!this.displayValue) {
        console.log('displayValue 为空，退出')
        return
      }

      if (!this.$refs.hazardTree) {
        console.log('hazardTree ref 不存在，退出')
        return
      }

      if (!this.treeData || this.treeData.length === 0) {
        console.log('treeData 为空，退出')
        return
      }

      this.$nextTick(() => {
        const targetNode = this.findNodeByLabelPath(this.displayValue)

        if (targetNode) {
          console.log('找到目标节点:', targetNode)

          // 设置当前选中的节点
          this.$refs.hazardTree.setCurrentKey(targetNode.id)

          // 展开到目标节点
          this.expandToNode(targetNode)

          console.log('回显设置成功')
        } else {
          console.warn('未找到匹配的节点:', this.displayValue)
          console.log('树数据结构:', JSON.stringify(this.treeData, null, 2))
        }
      })

      console.log('=== setTreeSelection 结束 ===')
    },

    // 设置树的选中状态
    setTreeSelection() {
      if (!this.value || !this.$refs.hazardTree || !this.treeData.length) {
        return
      }

      // 根据displayValue查找对应的节点
      const selectedNode = this.findNodeByLabel(this.treeData, this.value)
      if (selectedNode) {
        this.$refs.hazardTree.setCurrentKey(selectedNode.id)
        this.expandToNode(selectedNode)
      }
    },

    // 根据标签查找节点
    findNodeByLabel(nodes, label) {
      for (const node of nodes) {
        if (node.label === label) {
          return node
        }
        if (node.children && node.children.length) {
          const found = this.findNodeByLabel(node.children, label)
          if (found) return found
        }
      }
      return null
    },
  },
}
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

::v-deep .hazard-category-dropdown {
  max-height: 400px;
}

::v-deep .hazard-category-dropdown .el-tree-node__content {
  height: auto;
  padding: 4px 0;
}

::v-deep .hazard-category-dropdown .el-tree-node__content:hover {
  background-color: #f5f7fa;
}

::v-deep .hazard-category-dropdown .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}
</style>
