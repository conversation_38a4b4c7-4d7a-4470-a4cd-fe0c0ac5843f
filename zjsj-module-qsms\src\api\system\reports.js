import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listReports(query) {
  return request({
    url: '/pc/bx/reports/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getReports(reportsid) {
  return request({
    url: '/pc/bx/reports/' + reportsid,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addReports(data) {
  return request({
    url: '/system/reports',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateReports(data) {
  return request({
    url: '/system/reports',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delReports(reportsid) {
  return request({
    url: '/pc/bx/reports/' + reportsid,
    method: 'delete'
  })
}

// 上传文件
// /api/updatephotos
export function uploadFile(data) {
  return request({
    url: '/api/updatephotos',
    method: 'post',
    data,
    hearder: {
      type: 'multipart/form-data'
    }
  })
}