<template>
    <div class="menhu-container">
        <div class="dashboard-grid">
            <!-- 第一行 -->
            <div class="grid-row">
                <!-- 第一列：安全管理总览 + 质量管理 -->
                <div class="grid-col anquanguanlizonglan-card">
                    <!-- 安全管理总览 -->
                    <el-card class="overview-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">安全管理总览</span>
                        </div>
                        <div class="overview-grid">
                            <div class="overview-item">
                                <img class="overview-icon" src="@/assets/images/menhu/xms.png" alt="项目">
                                <div class="overview-content">
                                    <div class="overview-label">在建项目数</div>
                                    <div class="overview-value">78 <span class="unit">个</span></div>
                                </div>
                            </div>
                            <div class="overview-item">
                                <img class="overview-icon" src="@/assets/images/menhu/jcs.png" alt="项目">

                                <div class="overview-content">
                                    <div class="overview-label">安全检查数</div>
                                    <div class="overview-value">487 <span class="unit">个</span></div>
                                </div>
                            </div>
                            <div class="overview-item">
                                <img class="overview-icon" src="@/assets/images/menhu/zgl.png" alt="项目">

                                <div class="overview-content">
                                    <div class="overview-label">隐患整改率</div>
                                    <div class="overview-value">80 <span class="unit">%</span></div>
                                </div>
                            </div>
                            <div class="overview-item">
                                <img class="overview-icon" src="@/assets/images/menhu/flfg.png" alt="项目">

                                <div class="overview-content">
                                    <div class="overview-label">法律法规</div>
                                    <div class="overview-value">48 <span class="unit">条</span></div>
                                </div>
                            </div>
                            <div class="overview-item">
                                <img class="overview-icon" src="@/assets/images/menhu/pxs.png" alt="项目">

                                <div class="overview-content">
                                    <div class="overview-label">安全教育培训数</div>
                                    <div class="overview-value">48 <span class="unit">次</span></div>
                                </div>
                            </div>
                            <div class="overview-item">
                                <img class="overview-icon" src="@/assets/images/menhu/gcs.png" alt="项目">

                                <div class="overview-content">
                                    <div class="overview-label">危大工程数</div>
                                    <div class="overview-value">15 <span class="unit">个</span></div>
                                </div>
                            </div>
                        </div>
                    </el-card>

                    <!-- 质量管理 -->
                    <el-card class="quality-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">质量管理</span>
                        </div>
                        <div class="quality-content">
                            <div class="quality-stats">
                                <div class="stat-item">
                                    <img class="stat-bg" src="@/assets/images/menhu/zhiliangbg.png" alt="">
                                    <div class="stat-content">
                                        <div class="stat-label"><img class="stat-icon"
                                                src="@/assets/images/menhu/zhiliang.png" alt="">检查总数</div>
                                        <div class="stat-value">38 <span class="unit">个</span></div>
                                        <div class="stat-detail">
                                            <div>已完成数量<span style="margin: 0 6px;">32</span>个</div>
                                            <div>可完成数量<span style="margin: 0 6px;">4</span>个</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <img class="stat-bg" src="@/assets/images/menhu/zhiliangbg.png" alt="">
                                    <div class="stat-content">
                                        <div class="stat-label"><img class="stat-icon"
                                                src="@/assets/images/menhu/zhiliang.png" alt="">按时整改率</div>
                                        <div class="stat-value">86.45 <span class="unit">%</span></div>
                                        <div class="stat-detail">
                                            <div>按时整改<span style="margin: 0 6px;">31</span>个</div>
                                            <div>未按时整改<span style="margin: 0 6px; color: #E85164;">7</span>个</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <img class="stat-bg" src="@/assets/images/menhu/zhiliangbg.png" alt="">
                                    <div class="stat-content">
                                        <div class="stat-label"><img class="stat-icon"
                                                src="@/assets/images/menhu/zhiliang.png" alt="">一次性整改通过率</div>
                                        <div class="stat-value">72.45 <span class="unit">%</span></div>
                                        <div class="stat-detail">
                                            <div>整改通过<span style="margin: 0 6px;">27</span>个</div>
                                            <div>整改未通过<span style="margin: 0 6px; color: #E85164;">9</span>个</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 第二列：安全管理 -->
                <div class="grid-col anquanguanli-card">
                    <el-card class="sidebar-card safety-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">安全管理</span>
                        </div>
                        <div class="safety-management">
                            <div class="safety-header">
                                <span class="safety-title">企业检查</span>
                            </div>

                            <div class="qiye-content">
                                <div class="qiye-content-top">
                                    <div class="qiye-content-total">检查次数<span class="qiye-content-total-num">8</span>次
                                    </div>
                                    <img class="qiye-content-total-img" src="@/assets/images/menhu/qiye.png" alt="">
                                </div>
                                <div class="qiye-content-item">
                                    <div class="qiye-content-item-item"><span class="qiye-dot">·</span><span
                                            class="qiye-label">隐患数</span><span class="qiye-value">10</span><span
                                            class="qiye-unit">个</span></div>
                                    <div class="qiye-content-item-item"><span class="qiye-dot">·</span><span
                                            class="qiye-label">整改率</span><span class="qiye-value">50</span><span
                                            class="qiye-unit">%</span></div>
                                </div>
                                <div class="qiye-content-item">
                                    <div class="qiye-content-item-item"><span class="qiye-dot">·</span><span
                                            class="qiye-label">已整改</span><span class="qiye-value">10</span><span
                                            class="qiye-unit">个</span></div>
                                    <div class="qiye-content-item-item"><span class="qiye-dot">·</span><span
                                            class="qiye-label">未整改</span><span class="qiye-value">10</span><span
                                            class="qiye-unit">个</span></div>
                                </div>
                            </div>

                            <!-- 安全检查类型分析 -->
                            <div class="analysis-section">
                                <div class="analysis-title">安全检查类型分析</div>
                                <div class="analysis-charts">
                                    <div class="analysis-chart-item">
                                        <div id="generalChart" style="width: 100px; height: 100px;"></div>
                                        <div class="analysis-info">
                                            <div class="analysis-number"><span style="color: #4A90E2; font-size: 24px;">4</span>/<span class="analysis-unit">28</span><span style="font-size: 10px;"> 个</span></div>
                                            <div class="analysis-label">一般记录</div>
                                        </div>
                                    </div>
                                    <div class="analysis-chart-item">
                                        <div id="hiddenChart" style="width: 100px; height: 100px;"></div>
                                        <div class="analysis-info">
                                            <div class="analysis-number"><span style="color: #FF8C42; font-size: 24px;">10</span>/<span class="analysis-unit">28</span><span style="font-size: 10px;"> 个</span></div>
                                            <div class="analysis-label">隐患整改</div>
                                        </div>
                                    </div>
                                    <div class="analysis-chart-item">
                                        <div id="stopChart" style="width: 100px; height: 100px;"></div>
                                        <div class="analysis-info">
                                            <div class="analysis-number"><span style="color: #FF4757; font-size: 24px;">14</span>/<span class="analysis-unit">28</span><span style="font-size: 10px;"> 个</span></div>
                                            <div class="analysis-label">停工整改</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 第三列：法律法规 + EHS动态 -->
                <div class="grid-col lalv-ehs-card">
                    <!-- 法律法规 -->
                    <el-card class="sidebar-card lalv-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">法律法规</span>
                            <el-button type="text" class="more-btn">更多</el-button>
                        </div>
                        <div class="law-list">
                            <div class="law-item" v-for="(law, index) in lawList" :key="index">
                                <div class="law-content">
                                    <div class="law-title">{{ law.title }}</div>
                                    <div class="law-time">{{ law.time }}</div>
                                </div>
                            </div>
                        </div>
                    </el-card>

                    <!-- EHS动态 -->
                    <el-card class="sidebar-card ehs-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">EHS动态</span>
                            <el-button type="text" class="more-btn">更多</el-button>
                        </div>
                        <div class="ehs-list">
                            <div class="ehs-table">
                                <div class="ehs-header">
                                    <div class="ehs-col-title">标题</div>
                                    <div class="ehs-col-source">来源</div>
                                    <div class="ehs-col-time">接收时间</div>
                                </div>
                                <div class="ehs-body">
                                    <div class="ehs-row">
                                        <div class="ehs-col-title">中江集团安坑监测...</div>
                                        <div class="ehs-col-source">总部EHS部</div>
                                        <div class="ehs-col-time">2025-05-11 15:02</div>
                                    </div>
                                    <div class="ehs-row">
                                        <div class="ehs-col-title">《高处坠落事故案例...</div>
                                        <div class="ehs-col-source">子公司EHS部</div>
                                        <div class="ehs-col-time">2025-05-11 15:02</div>
                                    </div>
                                    <div class="ehs-row">
                                        <div class="ehs-col-title">安全管理及隐患整...</div>
                                        <div class="ehs-col-source">总部EHS部</div>
                                        <div class="ehs-col-time">2025-05-11 15:02</div>
                                    </div>
                                    <div class="ehs-row">
                                        <div class="ehs-col-title">安全教育活动通知...</div>
                                        <div class="ehs-col-source">总部EHS部</div>
                                        <div class="ehs-col-time">2025-05-11 15:02</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 第一列：安全管理KPI指标 -->
                <div class="grid-col anquanguanlikpi-card">
                    <el-card class="kpi-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">安全管理KPI指标</span>
                            <div class="card-tabs">
                                <el-button type="text" :class="{ active: activeTab === 'hidden' }"
                                    @click="activeTab = 'hidden'">隐患数</el-button>
                                <el-button type="text" :class="{ active: activeTab === 'rectify' }"
                                    @click="activeTab = 'rectify'">整改闭环率</el-button>
                                <el-button type="text" :class="{ active: activeTab === 'training' }"
                                    @click="activeTab = 'training'">培训次数</el-button>
                            </div>
                            <div class="header-controls">
                                    
                                <div class="time-tabs">
                                    <el-button type="text" :class="{ active: timeRange === 'day' }"
                                        @click="timeRange = 'day'">日</el-button>
                                    <el-button type="text" :class="{ active: timeRange === 'week' }"
                                        @click="timeRange = 'week'">周</el-button>
                                    <el-button type="text" :class="{ active: timeRange === 'month' }"
                                        @click="timeRange = 'month'">月</el-button>
                                </div>
                            </div>
                        </div>
                        <div class="kpi-chart-container">
                            <div id="kpiChart" style="width: 100%; height: 350px;"></div>
                        </div>
                    </el-card>
                </div>

                <!-- 第二列：创优的奖项 -->
                <div class="grid-col chuangyou-card">
                    <el-card class="awards-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">创优的奖项</span>
                        </div>
                        <div class="awards-list">
                            <div class="award-item">
                                <div class="award-content">
                                    <div class="award-title">国家优质工程奖杯</div>
                                    <div class="award-count">2 <span class="award-unit">个</span></div>
                                </div>
                            </div>
                            <div class="award-item">
                                <div class="award-content">
                                    <div class="award-title">中国建设工程鲁班奖</div>
                                    <div class="award-count">4 <span class="award-unit">个</span></div>
                                </div>
                            </div>
                            <div class="award-item">
                                <div class="award-content">
                                    <div class="award-title">中国土木工程詹天佑奖</div>
                                    <div class="award-count">9 <span class="award-unit">个</span></div>
                                </div>
                            </div>
                            <div class="award-item">
                                <div class="award-content">
                                    <div class="award-title">省级优质工程奖</div>
                                    <div class="award-count">12 <span class="award-unit">个</span></div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 第三列：安全教育 -->
                <div class="grid-col anquanjiaoyu-card">
                    <el-card class="sidebar-card">
                        <div slot="header" class="card-header">
                            <span class="card-title">安全教育</span>
                        </div>
                        <div class="education-content">
                            <div class="education-charts">
                                <div class="edu-chart">
                                    <div id="examChart" style="width: 150px; height: 150px;"></div>
                                    <div class="chart-label">月度考试合格率</div>
                                </div>
                                <div class="edu-chart">
                                    <div id="courseChart" style="width: 150px; height: 150px;"></div>
                                    <div class="chart-label">课程完成率</div>
                                </div>
                            </div>
                        </div>
                    </el-card>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    name: "Menhu",
    data() {
        return {
            activeTab: 'hidden',
            timeRange: 'day',
            chartInstance: null,
            examChartInstance: null,
            courseChartInstance: null,
            generalChartInstance: null,
            hiddenChartInstance: null,
            stopChartInstance: null,
            lawList: [
                { title: '国家优质工程管理办法（住建部法规）', time: '05-12 09:45' },
                { title: '地方性法规颁发政策（地方政府颁发、政策）', time: '05-12 09:45' },
                { title: '行业技术标准', time: '05-12 09:45' },
                { title: '企业内部管理制度', time: '05-12 09:45' },
                { title: '应急管理相关法规', time: '05-12 09:45' }
            ],
            ehsList: [
                { status: '检查', title: '中江集团安质量监督...', time: '2025-05-11 15:02' },
                { status: '子公司EHS部', title: '《施工现场管理条例...》', time: '2025-05-11 15:02' },
                { status: '危岗EHS部', title: '安全管理及隐患排查...', time: '2025-05-11 15:02' },
                { status: '危岗EHS部', title: '安全教育活动组织...', time: '2025-05-11 15:02' }
            ],
            kpiData: {
                hidden: {
                    day: [
                        { date: '05-11', serious: 2, general: 5 },
                        { date: '05-12', serious: 10, general: 1 },
                        { date: '05-13', serious: 2, general: 18 },
                        { date: '05-14', serious: 3, general: 1 },
                        { date: '05-15', serious: 2, general: 1 },
                        { date: '05-16', serious: 6, general: 6 },
                        { date: '05-17', serious: 5, general: 10 }
                    ],
                    week: [
                        { date: '第1周', serious: 8, general: 12 },
                        { date: '第2周', serious: 15, general: 8 },
                        { date: '第3周', serious: 6, general: 20 },
                        { date: '第4周', serious: 10, general: 5 }
                    ],
                    month: [
                        { date: '1月', serious: 25, general: 45 },
                        { date: '2月', serious: 30, general: 38 },
                        { date: '3月', serious: 22, general: 52 },
                        { date: '4月', serious: 28, general: 42 },
                        { date: '5月', serious: 20, general: 35 }
                    ]
                },
                rectify: {
                    day: [
                        { date: '05-11', serious: 3, general: 4 },
                        { date: '05-12', serious: 12, general: 2 },
                        { date: '05-13', serious: 1, general: 15 },
                        { date: '05-14', serious: 2, general: 2 },
                        { date: '05-15', serious: 3, general: 2 },
                        { date: '05-16', serious: 8, general: 7 },
                        { date: '05-17', serious: 6, general: 8 }
                    ],
                    week: [
                        { date: '第1周', serious: 10, general: 15 },
                        { date: '第2周', serious: 18, general: 10 },
                        { date: '第3周', serious: 8, general: 22 },
                        { date: '第4周', serious: 12, general: 8 }
                    ],
                    month: [
                        { date: '1月', serious: 28, general: 50 },
                        { date: '2月', serious: 35, general: 42 },
                        { date: '3月', serious: 25, general: 55 },
                        { date: '4月', serious: 32, general: 45 },
                        { date: '5月', serious: 22, general: 38 }
                    ]
                },
                training: {
                    day: [
                        { date: '05-11', serious: 1, general: 3 },
                        { date: '05-12', serious: 8, general: 1 },
                        { date: '05-13', serious: 1, general: 12 },
                        { date: '05-14', serious: 2, general: 1 },
                        { date: '05-15', serious: 1, general: 1 },
                        { date: '05-16', serious: 4, general: 4 },
                        { date: '05-17', serious: 3, general: 6 }
                    ],
                    week: [
                        { date: '第1周', serious: 5, general: 8 },
                        { date: '第2周', serious: 12, general: 6 },
                        { date: '第3周', serious: 4, general: 15 },
                        { date: '第4周', serious: 8, general: 4 }
                    ],
                    month: [
                        { date: '1月', serious: 18, general: 32 },
                        { date: '2月', serious: 22, general: 28 },
                        { date: '3月', serious: 15, general: 38 },
                        { date: '4月', serious: 20, general: 30 },
                        { date: '5月', serious: 12, general: 25 }
                    ]
                }
            }
        };
    },
    mounted() {
        this.initChart();
        this.initEducationCharts();
        this.initAnalysisCharts();
    },
    watch: {
        activeTab() {
            this.updateChart();
        },
        timeRange() {
            this.updateChart();
        }
    },
    beforeDestroy() {
        if (this.chartInstance) {
            this.chartInstance.dispose();
        }
        if (this.examChartInstance) {
            this.examChartInstance.dispose();
        }
        if (this.courseChartInstance) {
            this.courseChartInstance.dispose();
        }
        if (this.generalChartInstance) {
            this.generalChartInstance.dispose();
        }
        if (this.hiddenChartInstance) {
            this.hiddenChartInstance.dispose();
        }
        if (this.stopChartInstance) {
            this.stopChartInstance.dispose();
        }
    },
    methods: {
        getCurrentData() {
            return this.kpiData[this.activeTab][this.timeRange];
        },
        initChart() {
            this.$nextTick(() => {
                const chartDom = document.getElementById('kpiChart');
                if (chartDom) {
                    this.chartInstance = echarts.init(chartDom);
                    this.updateChart();
                    
                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (this.chartInstance) {
                            this.chartInstance.resize();
                        }
                    });
                }
            });
        },
        updateChart() {
            if (!this.chartInstance) return;
            
            const data = this.getCurrentData();
            const dates = data.map(item => item.date);
            const seriousData = data.map(item => item.serious);
            const generalData = data.map(item => item.general);
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + '：' + param.value + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['严重', '一般'],
                    bottom: 10,
                    itemGap: 30,
                    textStyle: {
                        fontSize: 14,
                        color: '#333'
                    }
                },
                grid: {
                    left: '2%',
                    right: '2%',
                    top: '2%',
                    bottom: '10%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    axisLine: {
                        lineStyle: {
                            color: '#E4E7ED'
                        }
                    },
                    axisLabel: {
                        color: '#666',
                        fontSize: 12
                    },
                    axisTick: {
                        show: false
                    }
                },
                yAxis: {
                    type: 'value',
                    max: 25,
                    min: 0,
                    interval: 5,
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#666',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#F0F0F0',
                            type: 'solid'
                        }
                    }
                },
                series: [
                    {
                        name: '严重',
                        type: 'bar',
                        data: seriousData,
                        barWidth: 20,
                        itemStyle: {
                            color: '#FF8C42',
                            borderRadius: [2, 2, 0, 0]
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#FF7A28'
                            }
                        }
                    },
                    {
                        name: '一般',
                        type: 'bar',
                        data: generalData,
                        barWidth: 20,
                        itemStyle: {
                            color: '#4A90E2',
                            borderRadius: [2, 2, 0, 0]
                        },
                        emphasis: {
                            itemStyle: {
                                color: '#357ABD'
                            }
                        }
                    }
                ]
            };
            
            this.chartInstance.setOption(option);
        },
        initEducationCharts() {
            this.$nextTick(() => {
                this.initExamChart();
                this.initCourseChart();
            });
        },
        initExamChart() {
            const chartDom = document.getElementById('examChart');
            if (chartDom) {
                this.examChartInstance = echarts.init(chartDom);
                const option = this.createPieChartOption(78.12, '78.12%');
                this.examChartInstance.setOption(option);
            }
        },
        initCourseChart() {
            const chartDom = document.getElementById('courseChart');
            if (chartDom) {
                this.courseChartInstance = echarts.init(chartDom);
                const option = this.createPieChartOption(92.23, '92.23%');
                this.courseChartInstance.setOption(option);
            }
        },
        createPieChartOption(percentage, displayText) {
            return {
                series: [
                    {
                        type: 'pie',
                        radius: ['60%', '80%'],
                        center: ['50%', '50%'],
                        data: [
                            {
                                value: percentage,
                                name: '完成',
                                itemStyle: {
                                    color: 'rgba(38,86,245,0.8)'
                                }
                            },
                            {
                                value: 100 - percentage,
                                name: '未完成',
                                itemStyle: {
                                    color: 'rgba(255,150,89,0.8)'
                                }
                            }
                        ],
                        labelLine: {
                            show: false
                        },
                        label: {
                            show: true,
                            position: 'center',
                            fontSize: 18,
                            fontWeight: 'bold',
                            color: '#333',
                            formatter: displayText
                        },
                        emphasis: {
                            scale: false
                        },
                        silent: true
                    }
                ]
            };
        },
        initAnalysisCharts() {
            this.$nextTick(() => {
                this.initGeneralChart();
                this.initHiddenChart();
                this.initStopChart();
            });
        },
        initGeneralChart() {
            const chartDom = document.getElementById('generalChart');
            if (chartDom) {
                this.generalChartInstance = echarts.init(chartDom);
                const option = this.createAnalysisChartOption(12, '#4A90E2');
                this.generalChartInstance.setOption(option);
            }
        },
        initHiddenChart() {
            const chartDom = document.getElementById('hiddenChart');
            if (chartDom) {
                this.hiddenChartInstance = echarts.init(chartDom);
                const option = this.createAnalysisChartOption(38, '#FF8C42');
                this.hiddenChartInstance.setOption(option);
            }
        },
        initStopChart() {
            const chartDom = document.getElementById('stopChart');
            if (chartDom) {
                this.stopChartInstance = echarts.init(chartDom);
                const option = this.createAnalysisChartOption(50, '#FF4757');
                this.stopChartInstance.setOption(option);
            }
        },
        createAnalysisChartOption(percentage, color) {
            // 创建背景刻度数据（40个均匀间隔）
            const backgroundData = [];
            for (let i = 0; i < 40; i++) {
                backgroundData.push({
                    value: 0.8,
                    name: 'bg' + i,
                    itemStyle: {
                        color: '#D7DEE6',
                        borderWidth: 1,
                        borderColor: 'transparent'
                    }
                });
                backgroundData.push({
                    value: 0.2,
                    name: 'gap' + i,
                    itemStyle: {
                        color: 'transparent'
                    }
                });
            }

            // 创建进度数据（有间隔的进度条）
            const progressData = [];
            const totalSegments = 40; // 总段数
            const activeSegments = Math.round((percentage / 100) * totalSegments); // 激活的段数
            
            for (let i = 0; i < totalSegments; i++) {
                if (i < activeSegments) {
                    // 激活的进度段
                    progressData.push({
                        value: 0.8,
                        name: 'progress' + i,
                        itemStyle: {
                            color: color
                        }
                    });
                } else {
                    // 未激活的进度段
                    progressData.push({
                        value: 0.8,
                        name: 'inactive' + i,
                        itemStyle: {
                            color: 'transparent'
                        }
                    });
                }
                // 添加间隔
                progressData.push({
                    value: 0.4,
                    name: 'pgap' + i,
                    itemStyle: {
                        color: 'transparent'
                    }
                });
            }

            return {
                series: [
                    // 背景刻度系列
                    {
                        type: 'pie',
                        radius: ['70%', '90%'],
                        center: ['50%', '50%'],
                        startAngle: 90,
                        data: backgroundData,
                        labelLine: {
                            show: false
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            scale: false
                        },
                        silent: true
                    },
                    // 进度系列（有间隔）
                    {
                        type: 'pie',
                        radius: ['70%', '90%'],
                        center: ['50%', '50%'],
                        startAngle: 90,
                        data: progressData,
                        labelLine: {
                            show: false
                        },
                        label: {
                            show: true,
                            position: 'center',
                            fontSize: 16,
                            fontWeight: 'bold',
                            color: '#333',
                            formatter: percentage + '%'
                        },
                        emphasis: {
                            scale: false
                        },
                        silent: true
                    }
                ]
            };
        }
    }
};
</script>

<style scoped lang="scss">
.menhu-container {
    height: calc(100vh - 60px);
    background: #f5f7fa;
    padding: 20px;
    overflow-y: auto;
}

.dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    margin: 0 auto;
}

.quality-content {
    height: 140px;
    overflow-y: auto;
}

.grid-row {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
}

.grid-col {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.anquanguanlizonglan-card {
    width: calc(50% - 20px);
    height: 530px;
    margin-right: 20px;
}

.lalv-card {
    margin-bottom: 0!important;
}

.anquanguanlikpi-card {
    width: calc(50% - 20px);
    height: 450px;
    margin-right: 20px;
}

.anquanguanli-card {
    width: calc(30% - 20px);
    height: 530px;
    margin-right: 20px;
}

.chuangyou-card {
    width: calc(30% - 20px);
    height: 450px;
    margin-right: 20px;
}

.lalv-ehs-card {
    width: 20%;
    height: 530px;
}

.anquanjiaoyu-card {
    width: 20%;
    height: 470px;
}

.qiye-content {
    width: 100%;
    height: 172px;
    background: url('../../../assets/images/menhu/qiye-bg.png') no-repeat center center / 100% 100%;
    padding: 19px 24px;
    box-sizing: border-box;

    .qiye-content-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        .qiye-content-total {
            font-weight: 400;
            font-size: 16px;
            color: #040814;

            .qiye-content-total-num {
                margin: 0 6px;
                font-weight: bold;
                font-size: 32px;
                color: #1479FC;
            }
        }

        .qiye-content-total-img {}
    }

    .qiye-content-item {
        width: 100%;
        height: 38px;
        background: rgba(#F5F8FE, 0.6);
        border-radius: 6px 6px 6px 6px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-bottom: 4px;

        .qiye-content-item-item {
            font-size: 14px;
            color: #686C7F;
            font-weight: 400;

            .qiye-dot {
                font-weight: 900;
                margin-right: 10px;
            }

            .qiye-label {
                margin-right: 30px;
            }

            .qiye-value {
                font-weight: 500;
                font-size: 16px;
                color: #1F2026;
                margin-right: 4px;
            }

            .qiye-unit {}
        }
    }
}

// 卡片通用样式
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
        font-weight: bold;
        font-size: 16px;
        color: #333;
    }

    .more-btn {
        font-weight: 400;
        font-size: 14px;
        color: #394D6F;
        line-height: 12px;
    }
}

// 安全管理总览
.overview-card {
    .overview-grid {
        // display: grid;
        // grid-template-columns: repeat(4, 1fr);
        // gap: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        height: 188px;
        overflow-y: auto;
    }

    .overview-item {
        width: 182px;
        height: 86px;
        display: flex;
        align-items: center;
        padding: 20px;
        border-radius: 10px;

        background: linear-gradient(180deg, rgba(20, 121, 252, 0.1) 0%, rgba(20, 121, 252, 0) 100%), #FFFFFF;

        .overview-icon {
            width: 44px;
            height: 44px;
            margin-right: 14px;
        }

        .overview-content {
            .overview-label {
                font-size: 16px;
                color: #262B48;
                margin-bottom: 4px;
            }

            .overview-value {
                font-weight: bold;
                font-size: 24px;
                color: #101623;

                .unit {
                    font-weight: 400;
                    font-size: 14px;
                    color: #444444;
                }
            }
        }
    }
}

// 质量管理
.quality-card {
    padding-bottom: 6px;

    .quality-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;

        .stat-item {
            position: relative;
            width: 252px;
            display: flex;
            align-items: center;


            background: linear-gradient(180deg, rgba(#2656F5, 0.1) 0%, #FFFFFF 54%);
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #F5F5F6;
            padding: 25px 20px;
            box-sizing: border-box;

            .stat-bg {
                position: absolute;
                top: 13px;
                right: 1px;
            }

            .stat-icon {
                font-size: 30px;
                margin-right: 15px;
            }

            .stat-content {
                width: 100%;

                .stat-label {
                    margin-bottom: 5px;
                    display: flex;
                    align-items: center;

                    font-weight: 500;
                    font-size: 16px;
                    color: #232428;
                }

                .stat-value {
                    margin-bottom: 5px;
                    font-weight: bold;
                    font-size: 32px;
                    color: #2656F5;

                    .unit {
                        font-weight: 400;
                        font-size: 12px;
                        color: #232428;
                    }
                }

                .stat-detail {
                    width: 100%;
                    font-weight: 400;
                    font-size: 12px;
                    color: #686C7F;
                    display: flex;
                    justify-content: space-between;
                }
            }
        }
    }
}

// 第二行通用卡片样式
.anquanguanlikpi-card .kpi-card,
.chuangyou-card .awards-card,
.anquanjiaoyu-card .sidebar-card {
    height: 100%;
}

// 调整KPI卡片的内边距，使其与第一行视觉对齐
.kpi-card .el-card__body {
    padding: 10px;
}

// KPI卡片
.kpi-card {
    .card-header {
        .card-tabs {
            display: flex;
            gap: 20px;

            .el-button {
                padding: 8px 0;
                font-size: 14px;
                border: none;
                border-radius: 0;
                background: transparent;
                color: #666;
                position: relative;
                margin: 0;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background: #1479FC;
                    transform: scaleX(0);
                    transition: transform 0.3s ease;
                }

                &.active {
                    color: #1479FC;
                    font-weight: 500;

                    &::after {
                        transform: scaleX(1);
                    }
                }

                &:hover:not(.active) {
                    color: #1479FC;
                }

                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }
        }
        .header-controls {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .time-tabs {
            display: flex;

            .el-button {
                padding: 6px 16px;
                font-size: 14px;
                border-radius: 0;
                border: 1px solid #E4E7ED;
                color: #606266;
                margin: 0;
                position: relative;

                &:first-child {
                    border-radius: 4px 0 0 4px;
                }

                &:last-child {
                    border-radius: 0 4px 4px 0;
                }

                &:not(:first-child) {
                    border-left: none;
                }

                &.active {
                    color: #1479FC;
                    background: #F0F7FF;
                    border-color: #1479FC;
                    z-index: 1;
                }

                &:hover:not(.active) {
                    color: #1479FC;
                    border-color: #1479FC;
                    z-index: 1;
                }
            }
        }
    }

    .kpi-chart-container {
        height: 360px;
    }
}

// 侧边栏卡片
.sidebar-card {
    margin-bottom: 20px;
}

// 安全管理
.safety-management {
    .safety-header {
        margin-bottom: 15px;

        .safety-title {
            font-weight: bold;
            color: #333;
        }
    }

    .safety-stats {
        display: flex;
        align-items: center;
        gap: 20px;

        .safety-chart {
            .chart-circle {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: linear-gradient(135deg, #409EFF 0%, #66B1FF 100%);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                color: white;

                .chart-value {
                    font-size: 18px;
                    font-weight: bold;

                    .unit {
                        font-size: 12px;
                    }
                }

                .chart-label {
                    font-size: 10px;
                    margin-top: 2px;
                }
            }
        }

        .safety-details {
            flex: 1;

            .detail-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                font-size: 12px;

                .detail-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 8px;

                    &.blue {
                        background: #409EFF;
                    }

                    &.orange {
                        background: #E6A23C;
                    }

                    &.gray {
                        background: #909399;
                    }
                }

                .detail-label {
                    margin-right: 5px;
                    color: #666;
                }

                .detail-value {
                    font-weight: bold;
                    color: #333;
                    margin-right: 2px;
                }

                .detail-unit {
                    color: #666;
                }
            }
        }
    }
}

// 法律法规
.law-list {
    height: 150px;
    overflow-y: auto;

    .law-item {
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }

        .law-content {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .law-title {
                width: calc(100% - 80px);
                font-weight: 500;
                font-size: 14px;
                color: #353A59;
                margin-bottom: 5px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .law-time {
                width: 70px;
                font-weight: 400;
                font-size: 12px;
                color: #394D6F;
            }
        }
    }
}

// EHS动态
.ehs-list {
    height: 190px;
    overflow-y: auto;

    .ehs-table {
        width: 100%;
        position: relative;

        .ehs-header {
            position: sticky;
            top: 0;
            z-index: 10;
            display: flex;
            background: #FFFFFF;
            border-radius: 4px 4px 0 0;
            padding: 8px 0;
            font-weight: 500;
            font-size: 14px;
            color: #353A59;
            border-bottom: 1px solid #E9ECEF;

            .ehs-col-title {
                width: 40%;
                padding: 0 12px;
                text-align: left;
                font-weight: 500;
            }

            .ehs-col-source {
                width: 30%;
                padding: 0 12px;
                text-align: center;
            }

            .ehs-col-time {
                width: 30%;
                padding: 0 12px;
                text-align: center;
            }
        }

        .ehs-body {
            .ehs-row {
                display: flex;
                padding: 12px 0;
                border-bottom: 1px solid #F0F0F0;

                &:last-child {
                    border-bottom: none;
                }

                &:hover {
                    background: #F8F9FA;
                }

                .ehs-col-title {
                    width: 40%;
                    padding: 0 12px;
                    font-size: 14px;
                    color: #333;
                    line-height: 1.4;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .ehs-col-source {
                    width: 30%;
                    padding: 0 12px;
                    font-size: 12px;
                    color: #666;
                    text-align: center;
                    line-height: 1.4;
                }

                .ehs-col-time {
                    width: 30%;
                    padding: 0 12px;
                    font-size: 12px;
                    color: #999;
                    text-align: center;
                    line-height: 1.4;
                }
            }
        }
    }
}

// 安全教育
.education-content {
    .education-charts {
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 100%;
        padding: 70px 0;

        .edu-chart {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            position: relative;

            #examChart,
            #courseChart {
                background: url('../../../assets/images/menhu/pie-bg.png') no-repeat center center / 80% 80%;
            }

            .chart-label {
                font-size: 14px;
                color: #666;
                margin-top: 10px;
                font-weight: 500;
            }
        }
    }
}

// 安全检查类型分析
.analysis-section {
    margin-top: 20px;
    padding: 15px 0;

    .analysis-title {
        font-weight: bold;
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
    }

    .analysis-charts {
        display: flex;
        justify-content: space-around;
        align-items: center;

        .analysis-chart-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;

            .analysis-info {
                margin-top: 8px;

                .analysis-number {
                    font-size: 12px;
                    font-weight: bold;
                    color: #9B9DA9;

                    .analysis-unit {
                        font-size: 16px;
                        color: #232428;
                        margin-left: 2px;
                    }
                }

                .analysis-label {
                    font-size: 12px;
                    color: #686C7F;
                    margin-top: 2px;
                }
            }
        }
    }
}

// 创优奖项
.awards-card {
    .awards-list {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        height: 100%;
        
        .award-item {
            width: 100%;
            height: 64px;
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            background: url('../../../assets/images/menhu/award-bg.png') no-repeat center center / 100% 100%;
            margin-bottom: 15px;

            &:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }

            .award-content {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .award-title {
                    margin-left: 83px;
                    font-weight: 500;
                    font-size: 16px;
                    color: #181E2C;
                }

                .award-count {
                    font-weight: bold;
                    font-size: 24px;
                    color: #2656F5;
                }

                .award-unit {
                    font-weight: 400;
                    font-size: 12px;
                    color: #686C7F;
                    margin-right: 15px;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .dashboard-grid {
        flex-direction: column;
    }

    .right-sidebar {
        width: 100%;
    }

    .overview-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (max-width: 768px) {
    .overview-grid {
        grid-template-columns: 1fr !important;
    }

    .quality-stats {
        flex-direction: column !important;
    }
}
</style>
