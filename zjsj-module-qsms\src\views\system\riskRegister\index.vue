<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="130px"
    >
      <el-form-item label="项目名称" prop="projectName" label-width="68px">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="风险类别" prop="riskCode" label-width="80px">
        <el-select
          v-model="queryParams.riskCode"
          placeholder="请选择风险类别"
          clearable
          style="width: 180px"
          @change="handleQuery"
        >
          <el-option
            v-for="item in riskTypeList"
            :key="item.id"
            :label="item.riskName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="风险所处施工阶段" prop="riskStage">
        <el-input
          v-model="queryParams.riskStage"
          placeholder="请输入风险所处施工阶段"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任部门" prop="responsibleDept" label-width="80px">
        <el-input
          v-model="queryParams.responsibleDept"
          placeholder="请输入责任部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任人" prop="responsiblePerson" label-width="80px">
        <el-input
          v-model="queryParams.responsiblePerson"
          placeholder="请输入责任人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任人联系方式" prop="personContact">
        <el-input
          v-model="queryParams.personContact"
          placeholder="请输入责任人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="整改完成期限" prop="deadline">
        <el-date-picker
          clearable
          v-model="queryParams.deadline"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择整改完成期限"
          style="width: 300px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="监控频率" prop="monitorFrequency" label-width="80px">
        <el-input
          v-model="queryParams.monitorFrequency"
          placeholder="请输入监控频率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:checklist:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:checklist:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:checklist:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:checklist:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 表格横向滚动容器 -->
    <div class="table-scroll-container">
      <el-table
        v-loading="loading"
        :data="checklistList"
        class="scroll-table"
        height="calc(100vh - 230px)"
        @selection-change="handleSelectionChange"
        :cell-style="{ 'vertical-align': 'middle' }"
        :header-cell-style="{ 'vertical-align': 'middle' }"
      >
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="风险ID" align="center" prop="id" /> -->
        <el-table-column
          label="项目名称"
          align="center"
          prop="projectName"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="风险类别"
          align="center"
          prop="riskName"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="风险所处施工阶段"
          align="center"
          prop="riskStage"
          width="160"
          show-overflow-tooltip
        />
        <el-table-column
          label="风险描述"
          align="center"
          prop="riskContent"
          width="300"
        >
          <template slot-scope="scope">
            <!-- 
            <el-tooltip
              effect="light"
              placement="top"
              :disabled="!isContentOverflow(scope.row.riskContent)"
              popper-class="custom-tooltip"
            >
              <div
                slot="content"
                class="tooltip-content"
                v-html="scope.row.riskContent"
              ></div> -->
            <div
              :ref="'riskContentContainer_' + scope.row.id"
              class="rich-text-content rich-text-ellipsis"
              v-html="scope.row.riskContent"
            />
            <!-- </el-tooltip> -->
          </template>
        </el-table-column>
        <el-table-column
          label="风险等级"
          align="center"
          prop="riskLevel"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <dict-tag :options="dict.type.fxdj" :value="scope.row.riskLevel" />
          </template>
        </el-table-column>
        <el-table-column
          label="责任部门"
          align="center"
          prop="responsibleDept"
          width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="事故类型"
          align="center"
          prop="accidentType"
          width="300"
          show-overflow-tooltip
        />

        <el-table-column
          label="管控措施"
          align="center"
          prop="responsiblePerson"
          width="300"
        >
          <template slot-scope="scope">
            <!-- <el-tooltip
              effect="light"
              placement="top"
              :disabled="!isContentOverflow(scope.row.responsiblePerson)"
              popper-class="custom-tooltip"
            >
              <div
                slot="content"
                class="tooltip-content"
                v-html="scope.row.responsiblePerson"
              ></div> -->
            <div
              :ref="'richTextContainer_' + scope.row.id"
              class="rich-text-content rich-text-ellipsis"
              v-html="scope.row.responsiblePerson"
            />
            <!-- </el-tooltip> -->
          </template>
        </el-table-column>
        <!-- <el-table-column label="责任人联系方式" align="center" prop="personContact" width="160" show-overflow-tooltip />
        <el-table-column label="整改完成期限" align="center" prop="deadline" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.deadline, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="风险状态" align="center" prop="status" />
        <el-table-column
          label="监控频率"
          align="center"
          prop="monitorFrequency"
        /> -->
        <!-- <el-table-column label="备注" align="center" prop="remark" width="200" show-overflow-tooltip /> -->
        <!-- <el-table-column
          label="风险管控措施附件"
          align="center"
          prop="riskControlAttachment"
          width="160"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.riskControlAttachment"
              size="mini"
              type="text"
              @click="handleViewAttachment(scope.row.riskControlAttachment)"
              >查看</el-button
            >
            <span v-else>-</span>
          </template>
        </el-table-column> -->
        <el-table-column
          label="操作"
          fixed="right"
          width="180"
          align="center"
          class-name="small-padding"
        >
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['system:checklist:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              v-hasPermi="['system:checklist:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              v-hasPermi="['system:checklist:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目施工风险清单及管理措施对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <div>
                <el-radio-group
                  v-model="projectInputType"
                  size="small"
                  style="margin-bottom: 8px"
                >
                  <el-radio-button label="select"
                    >从项目列表选择</el-radio-button
                  >
                  <el-radio-button label="input">手动输入</el-radio-button>
                </el-radio-group>
                <selectPeopleTree
                  v-if="projectInputType === 'select'"
                  v-model="form.projectName"
                  :people-list="projectList"
                  placeholder="请选择项目名称"
                  @change="handleFormProjectChange"
                />
                <el-input
                  v-else
                  v-model="form.projectName"
                  placeholder="请输入项目名称"
                  @input="handleManualInput"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="风险类别" prop="riskCode">
              <el-select
                v-model="form.riskCode"
                placeholder="请选择风险类别"
                style="width: 100%"
                clearable
                @change="handleRiskTypeChange"
              >
                <el-option
                  v-for="item in riskTypeList"
                  :key="item.id"
                  :label="item.riskName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="风险所处施工阶段" prop="riskStage">
              <el-input
                v-model="form.riskStage"
                placeholder="请输入风险所处施工阶段"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="风险等级" prop="riskLevel">
              <el-select
                v-model="form.riskLevel"
                placeholder="请选择风险等级"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in dict.type.fxdj"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="风险描述" prop="riskContent">
              <editor v-model="form.riskContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="责任部门" prop="responsibleDept">
              <el-input
                v-model="form.responsibleDept"
                placeholder="请输入责任部门"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="事故类型" prop="accidentType">
              <el-input
                type="textarea"
                v-model="form.accidentType"
                placeholder="请输入事故类型"
                rows="4"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="管控措施" prop="responsiblePerson">
              <editor v-model="form.responsiblePerson" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="责任人联系方式" prop="personContact">
              <el-input
                v-model="form.personContact"
                placeholder="请输入责任人联系方式"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改完成期限" prop="deadline">
              <el-date-picker
                v-model="form.deadline"
                clearable
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择整改完成期限"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 查看 -->
    <el-dialog
      title="查看项目施工风险清单"
      :visible.sync="viewOpen"
      width="1000px"
      append-to-body
    >
      <el-form ref="form" :model="viewForm" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名：" prop="projectName">
              <span>{{ viewForm.projectName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="风险类别：" prop="riskCode">
              <span>{{ viewForm.riskName }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="风险所处施工阶段：" prop="riskStage">
              <span>{{ viewForm.riskStage }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="风险等级：" prop="riskLevel">
              <span>{{ getDictLabel(viewForm.riskLevel) }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="风险描述：" prop="riskContent">
              <span
                v-html="viewForm.riskContent"
                class="rich-text-content"
              ></span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="责任部门：" prop="responsibleDept">
              <span>{{ viewForm.responsibleDept }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="事故类型：" prop="accidentType">
              <span>{{ viewForm.accidentType || "-" }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="管控措施：" prop="responsiblePerson">
              <span
                v-html="viewForm.responsiblePerson"
                class="rich-text-content"
              ></span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="责任人联系方式：" prop="personContact">
              <span>{{ viewForm.personContact || "-" }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改完成期限：" prop="deadline">
              <span>{{ viewForm.deadline || "-" }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注：" prop="remark">
              <span>{{ viewForm.remark || "-" }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row>
          <el-col :span="24">
            <el-form-item label="风险管控措施附件：" prop="remark">
              <div class="document-list">
                <div
                  v-for="(doc, index) in getDocumentList(
                    viewForm.riskControlAttachment
                  )"
                  :key="'doc-' + index"
                  class="document-item"
                >
                  <span
                    class="attachment-item"
                    @click="handleDocumentPreview(doc)"
                  >
                    <i class="el-icon-document" />
                    {{ getFileName(doc) || `相关文档${index + 1}` }}
                  </span>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row> -->
      </el-form>
    </el-dialog>
    <AttachmentDialog
      v-model="attachmentDialogVisible"
      :attachment-list="attachmentList"
    />
    <!-- 图片预览组件 -->
    <!-- <el-dialog title="图片预览" :visible.sync="previewImageVisible">
      <el-image
        :src="previewImageUrl"
        :preview-src-list="allImageUrls"
        fit="contain"
        @close="previewImageVisible = false"
      ></el-image>
    </el-dialog> -->
  </div>
</template>

<script>
import {
  listChecklist,
  getChecklist,
  delChecklist,
  addChecklist,
  updateChecklist,
} from "@/api/system/riskRegister/index";
import { getRiskTypeManagementList } from "@/api/system/riskTypeManage/index";
import { querytree } from "@/api/system/info";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";

export default {
  name: "Checklist",
  dicts: ["fxdj"],
  components: {
    selectPeopleTree,
    AttachmentDialog,
  },
  data() {
    return {
      previewImageUrl: "",
      previewImageVisible: false,
      allImageUrls: [], // 存储所有图片URL
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目施工风险清单及管理措施表格数据
      checklistList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        riskCode: null,
        riskStage: null,
        riskContent: null,
        riskLevel: null,
        managementMeasures: null,
        responsibleDept: null,
        responsiblePerson: null,
        personContact: null,
        deadline: null,
        status: null,
        monitorFrequency: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        riskCode: [
          { required: true, message: "风险编号不能为空", trigger: "blur" },
        ],
        riskStage: [
          {
            required: true,
            message: "风险所处施工阶段不能为空",
            trigger: "blur",
          },
        ],
        riskContent: [
          { required: true, message: "风险描述不能为空", trigger: "change" },
        ],
        riskLevel: [
          { required: true, message: "风险等级不能为空", trigger: "change" },
        ],
        responsibleDept: [
          { required: true, message: "责任部门不能为空", trigger: "blur" },
        ],
        responsiblePerson: [
          { required: true, message: "管控措施不能为空", trigger: "blur" },
        ],
      },
      // 风险类型管理列表
      riskTypeList: [],
      // 项目列表
      projectList: [],
      // 附件对话框
      attachmentDialogVisible: false,
      attachmentList: [],
      // 项目输入类型
      projectInputType: "select",
      viewForm: {},
      viewOpen: false,
    };
  },
  created() {
    this.getList();
    this.getRiskTypeList();
    this.getProjectList();
  },
  mounted() {
    // this.replaceImagesWithElImage();
  },
  updated() {
    // this.replaceImagesWithElImage();
  },
  methods: {
    /** 解析文档列表 */
    getDocumentList(documentUrls) {
      if (!documentUrls) return [];
      return documentUrls.split(",").filter((item) => item.trim());
    },
    /** 文档预览 */
    async handleDocumentPreview(docUrl) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + docUrl;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
            cancelButtonText: "关闭",
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    /** 获取文件名 */
    getFileName(url) {
      if (!url) return "";
      const parts = url.split("/");
      return parts[parts.length - 1] || "";
    },
    getDictLabel(level) {
      const arr = this.dict.type.fxdj.find((item) => item.value == level);
      return arr ? arr.label : "-";
    },
    // replaceImagesWithElImage() {
    //   Object.keys(this.$refs).forEach((refKey) => {
    //     // console.log(refKey, "refKey");
    //     if (refKey.startsWith("richTextContainer_")) {
    //       const container = this.$refs[refKey];
    //       // console.log(container, "container");
    //       if (!container) return;

    //       // 收集当前行所有图片URL
    //       const images = container.querySelectorAll("img");
    //       const imageUrls = Array.from(images).map((img) => img.src);

    //       // 反向迭代避免DOM节点变动导致的索引问题
    //       Array.from(images)
    //         .reverse()
    //         .forEach((img) => {
    //           // 创建组件构造器
    //           const ImageComponent = Vue.extend({
    //             render: (h) =>
    //               h("el-image", {
    //                 props: {
    //                   src: img.src,
    //                   previewSrcList: imageUrls,
    //                   fit: "contain",
    //                 },
    //                 class: img.className,
    //                 style: img.style.cssText,
    //                 // nativeOn: {
    //                 //   // 手动绑定点击事件（可选）
    //                 //   click: () => {
    //                 //     this.previewImageUrl = img.src;
    //                 //     this.previewImageVisible = true;
    //                 //   },
    //                 // },
    //               }),
    //           });
    //           // 创建实例并挂载
    //           const instance = new ImageComponent().$mount();

    //           // 确保获取到DOM节点
    //           if (instance.$el && img.parentNode) {
    //             img.parentNode.replaceChild(instance.$el, img);
    //           }
    //         });
    //     }
    //   });
    // },
    // handleImageClick(e) {
    //   // 判断点击的是否是图片元素
    //   if (e.target.tagName === "IMG") {
    //     e.stopPropagation();
    //     // 获取当前富文本容器内所有图片
    //     const imgElements = e.currentTarget.querySelectorAll("img");
    //     console.log(e.target.src, "imgElements");
    //     this.allImageUrls = Array.from(imgElements).map((img) => img.src);
    //     // 设置当前预览图片
    //     this.previewImageUrl = e.target.src;
    //     this.previewImageVisible = true;
    //   }
    // },
    /** 查询项目施工风险清单及管理措施列表 */
    getList() {
      this.loading = true;
      listChecklist(this.queryParams).then((response) => {
        this.checklistList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取风险类型列表 */
    getRiskTypeList() {
      return getRiskTypeManagementList().then((response) => {
        this.riskTypeList = response.data || [];
        return this.riskTypeList;
      });
    },
    /** 获取项目列表 */
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    /** 处理手动输入 */
    handleManualInput(value) {
      this.form.projectName = value;
      // 手动输入时，projectId 设为空
      this.form.projectId = null;
    },
    /** 处理项目名称变化（保留旧方法以兼容） */
    handleFormProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectName = selectedItem.label;
        this.form.projectId = String(selectedItem.id);
      }
    },
    /** 处理风险类型变化 */
    handleRiskTypeChange(value) {
      const selectedRisk = this.riskTypeList.find((item) => item.id === value);
      if (selectedRisk) {
        // riskCode 保持为选择的 id
        this.form.riskCode = value;
        this.form.riskName = selectedRisk.riskName;
      }
    },
    /** 同步风险类型数据，处理修改时的回显 */
    syncRiskTypeData() {
      if (!this.form.riskCode) return;

      // 如果风险类型列表还没有加载完成，等待加载
      if (this.riskTypeList.length === 0) {
        this.getRiskTypeList().then(() => {
          this.setRiskTypeEcho();
        });
      } else {
        this.setRiskTypeEcho();
      }
    },
    /** 设置风险类型回显 */
    setRiskTypeEcho() {
      if (this.form.riskCode && this.riskTypeList.length > 0) {
        // 处理数据类型转换问题，确保能正确匹配
        const riskCode = String(this.form.riskCode);
        const selectedRisk = this.riskTypeList.find(
          (item) => String(item.id) === riskCode
        );
        if (selectedRisk) {
          // 确保riskCode是正确的类型
          this.form.riskCode = selectedRisk.id;
          this.form.riskName = selectedRisk.riskName;
        }
      }
    },
    /** 查看附件 */
    handleViewAttachment(value) {
      this.attachmentDialogVisible = true;
      this.attachmentList = value.split(",");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        projectId: null,
        riskCode: null,
        riskName: null,
        riskStage: null,
        riskContent: null,
        riskLevel: null,
        managementMeasures: null,
        responsibleDept: null,
        responsiblePerson: null,
        personContact: null,
        deadline: null,
        status: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.projectInputType = "select";
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;

      this.title = "添加项目施工风险清单及管理措施";
    },
    // 查看按钮
    handleView(row) {
      const id = row.id || this.ids;
      getChecklist(id).then((response) => {
        this.viewForm = response.data;
        // 确保风险类型列表已加载，然后设置风险类别回显
        this.syncRiskTypeData();
        this.viewOpen = true;
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();

      const id = row.id || this.ids;
      getChecklist(id).then((response) => {
        this.form = response.data;
        // 确保风险类型列表已加载，然后设置风险类别回显
        this.syncRiskTypeData();
        this.open = true;
        this.title = "修改项目施工风险清单及管理措施";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateChecklist(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addChecklist(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除项目施工风险清单及管理措施编号为"' + ids + '"的数据项？'
        )
        .then(function () {
          return delChecklist(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/checklist/export",
        {
          ...this.queryParams,
        },
        `checklist_${new Date().getTime()}.xlsx`
      );
    },
    /** 判断内容是否超过四行 */
    isContentOverflow(content) {
      if (!content) return false;

      // 创建一个临时div来测量内容高度
      const tempDiv = document.createElement("div");
      tempDiv.style.cssText = `
        position: absolute;
        visibility: hidden;
        height: auto;
        width: 268px;
        padding: 12px 8px;
        font-size: 14px;
        line-height: 1.6;
        word-break: break-word;
        white-space: normal;
      `;
      tempDiv.innerHTML = content;
      document.body.appendChild(tempDiv);

      const height = tempDiv.offsetHeight;
      document.body.removeChild(tempDiv);

      // 四行的高度约为：行高1.6 * 字体大小14px * 4行 = 89.6px，加上容差约为96px
      return height > 96;
    },
  },
};
</script>
<style scoped lang="scss">
// 表格横向滚动容器样式
.table-scroll-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  // 美化滚动条
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // 确保表格能够横向滚动
  ::v-deep .el-table {
    min-width: 100%;

    .el-table__body-wrapper {
      overflow-x: visible;
    }

    // 固定列阴影效果
    .el-table__fixed-right {
      box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// 富文本内容样式
::v-deep .rich-text-content {
  text-align: left;
  word-break: break-word;
  line-height: 1.5;
  overflow: visible;

  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 10px auto;
    display: block;
  }

  p {
    margin: 8px 0;
    span {
      color: #606266 !important;
    }
  }
}

// 四行省略样式
::v-deep .rich-text-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 96px; // 增加高度确保四行完整显示：1.5 * 16px * 4 = 96px
  line-height: 1.6; // 稍微增加行高
  word-break: break-word;
  font-size: 14px;

  // 处理富文本中的段落间距
  p {
    margin: 2px 0; // 减小段落间距
    line-height: 1.6;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 确保图片不影响行数计算
  img {
    display: none; // 在省略模式下隐藏图片
  }
}

// 自定义tooltip样式
::v-deep .custom-tooltip {
  max-width: 500px;
  border: 1px solid #e4e7ed !important;

  .tooltip-content {
    max-height: 400px;
    overflow-y: auto;
    line-height: 1.6;
    word-break: break-word;
    color: #303133; // 深灰色文字，确保在白色背景下清晰可见
    font-size: 14px;
    padding: 8px;

    img {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      margin: 8px 0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    p {
      margin: 8px 0;
      color: #303133;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 处理其他HTML标签的颜色
    * {
      color: #303133 !important;
    }
  }
}

// Element UI tooltip样式覆盖
::v-deep .el-tooltip__popper.is-light {
  background: #ffffff !important;
  border: 1px solid #e4e7ed !important;
  color: #303133 !important;
}

// 表格单元格样式优化
::v-deep .el-table {
  .el-table__cell {
    padding: 12px 8px;
    vertical-align: top;

    .cell {
      word-break: break-word;
      white-space: normal;
      overflow: visible;
    }
  }

  // 优化tooltip显示
  .el-tooltip {
    max-width: 300px;
  }
}
.document-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

// 响应式设计
@media (max-width: 1200px) {
  .table-scroll-container {
    ::v-deep .el-table {
      .el-table-column--selection {
        width: 50px !important;
      }
    }
  }
}
</style>
