import request from '@/utils/request'

// 查询员工情况列表
export function listZjEmployeeInfo(query) {
  return request({
    url: '/inspection/zjEmployeeInfo/list',
    method: 'get',
    params: query
  })
}

// 查询员工情况详细
export function getZjEmployeeInfo(id) {
  return request({
    url: '/inspection/zjEmployeeInfo/' + id,
    method: 'get'
  })
}

// 新增员工情况
export function addZjEmployeeInfo(data) {
  return request({
    url: '/inspection/zjEmployeeInfo',
    method: 'post',
    data: data
  })
}

// 修改员工情况
export function updateZjEmployeeInfo(data) {
  return request({
    url: '/inspection/zjEmployeeInfo',
    method: 'put',
    data: data
  })
}

// 删除员工情况
export function delZjEmployeeInfo(id) {
  return request({
    url: '/inspection/zjEmployeeInfo/' + id,
    method: 'delete'
  })
}

// 导出员工情况预警数据
export function exportyjZjEmployeeInfo(data) {
  return request({
    url: '/inspection/zjEmployeeInfo/exportyj',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}