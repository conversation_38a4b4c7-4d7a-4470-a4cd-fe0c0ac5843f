import request from '@/utils/request'

// 查询作业区域列表
export function listZjWorkArea(query) {
  return request({
    url: '/work/zjWorkArea/list',
    method: 'get',
    params: query
  })
}

// 查询作业区域详细
export function getZjWorkArea(areaId) {
  return request({
    url: '/work/zjWorkArea/' + areaId,
    method: 'get'
  })
}

// 新增作业区域
export function addZjWorkArea(data) {
  return request({
    url: '/work/zjWorkArea',
    method: 'post',
    data: data
  })
}

// 修改作业区域
export function updateZjWorkArea(data) {
  return request({
    url: '/work/zjWorkArea',
    method: 'put',
    data: data
  })
}

// 删除作业区域
export function delZjWorkArea(areaId) {
  return request({
    url: '/work/zjWorkArea/' + areaId,
    method: 'delete'
  })
}
