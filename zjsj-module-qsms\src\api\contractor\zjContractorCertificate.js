import request from '@/utils/request'

// 查询承包商业绩证书列表
export function listZjContractorCertificate(query) {
  return request({
    url: '/contractor/zjContractorCertificate/list',
    method: 'get',
    params: query
  })
}

// 查询承包商业绩证书详细
export function getZjContractorCertificate(id) {
  return request({
    url: '/contractor/zjContractorCertificate/' + id,
    method: 'get'
  })
}

// 新增承包商业绩证书
export function addZjContractorCertificate(data) {
  return request({
    url: '/contractor/zjContractorCertificate',
    method: 'post',
    data: data
  })
}

// 修改承包商业绩证书
export function updateZjContractorCertificate(data) {
  return request({
    url: '/contractor/zjContractorCertificate',
    method: 'put',
    data: data
  })
}

// 删除承包商业绩证书
export function delZjContractorCertificate(id) {
  return request({
    url: '/contractor/zjContractorCertificate/' + id,
    method: 'delete'
  })
}
