import request from '@/utils/request'

// 查询承包商评价列表
export function listZjContractorEvaluation(query) {
  return request({
    url: '/contractor/zjContractorEvaluation/list',
    method: 'get',
    params: query
  })
}

// 查询承包商评价详细
export function getZjContractorEvaluation(id) {
  return request({
    url: '/contractor/zjContractorEvaluation/' + id,
    method: 'get'
  })
}

// 新增承包商评价
export function addZjContractorEvaluation(data) {
  return request({
    url: '/contractor/zjContractorEvaluation',
    method: 'post',
    data: data
  })
}

// 修改承包商评价
export function updateZjContractorEvaluation(data) {
  return request({
    url: '/contractor/zjContractorEvaluation',
    method: 'put',
    data: data
  })
}

// 删除承包商评价
export function delZjContractorEvaluation(id) {
  return request({
    url: '/contractor/zjContractorEvaluation/' + id,
    method: 'delete'
  })
}
