import request from '@/utils/request'

// 查询考核管理列表
export function listZjAssessmentManagement(query) {
  return request({
    url: '/inspection/zjAssessmentManagement/list',
    method: 'get',
    params: query
  })
}

// 查询考核管理详细
export function getZjAssessmentManagement(id) {
  return request({
    url: '/inspection/zjAssessmentManagement/' + id,
    method: 'get'
  })
}

// 新增考核管理
export function addZjAssessmentManagement(data) {
  return request({
    url: '/inspection/zjAssessmentManagement',
    method: 'post',
    data: data
  })
}

// 修改考核管理
export function updateZjAssessmentManagement(data) {
  return request({
    url: '/inspection/zjAssessmentManagement',
    method: 'put',
    data: data
  })
}

// 删除考核管理
export function delZjAssessmentManagement(id) {
  return request({
    url: '/inspection/zjAssessmentManagement/' + id,
    method: 'delete'
  })
}
