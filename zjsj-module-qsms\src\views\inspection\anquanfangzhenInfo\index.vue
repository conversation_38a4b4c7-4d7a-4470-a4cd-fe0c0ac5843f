<template>
  <div class="app-container">
    <el-row>
      <el-col :span="3">
        <orgTree :type="'1'" @node-click="handleOrgTreeNodeClick"></orgTree>
      </el-col>
      <el-col :span="21">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
        >
          <el-form-item label="安全目标名称" prop="indexName">
            <el-input
              v-model="queryParams.indexName"
              placeholder="请输入安全目标名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:anquanfangzhenInfo:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:anquanfangzhenInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:anquanfangzhenInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:anquanfangzhenInfo:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="anquanfangzhenInfoList"
          @selection-change="handleSelectionChange"
          height="calc(100vh - 250px)"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="主键" align="center" prop="id" /> -->
          <el-table-column
            label="安全目标名称"
            align="center"
            prop="indexName"
          />
          <!-- <el-table-column
        label="目标指标内容"
        align="center"
        prop="indexContent"
      /> -->
          <el-table-column label="下发单位" align="center" prop="belongingUnit">
            <template slot-scope="scope">
              <span v-if="scope.row.belongingUnit">
                {{ getDeptName(scope.row.belongingUnit) }}
              </span>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- <el-table-column
            label="指标值"
            align="center"
            prop="indicatorValue"
          /> -->
          <el-table-column label="状态" align="center" prop="indexStatus">
            <template slot-scope="scope">
              {{
                indexStatusOptions.find(
                  (item) => item.value == scope.row.indexStatus
                ).label
              }}
            </template>
          </el-table-column>
          <el-table-column label="下发时间" align="center" prop="updateTime">
            <template slot-scope="scope">
              <span v-if="scope.row.updateTime">{{
                scope.row.updateTime
              }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="完成率"
            align="center"
            prop="completionRate"
          /> -->
          <el-table-column label="附件" align="center" prop="attachment">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.attachment"
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="handleSafetyTrainingRecords(scope.row.attachment)"
                >查看</el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:anquanfangzhenInfo:edit']"
                >编辑</el-button
              >
              <!-- <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:anquanfangzhenInfo:edit']"
                >查看</el-button
              > -->
              <el-button
                v-show="scope.row.indexStatus == '0'"
                size="mini"
                type="text"
                @click="handleRelease(scope.row)"
                >下发</el-button
              >
              <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:anquanfangzhenInfo:remove']"
            >删除</el-button
          > -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改安全方针目标对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="安全目标名称" prop="indexName">
          <el-input v-model="form.indexName" placeholder="请输入目标指标名称" />
        </el-form-item>
        <el-form-item label="附件">
          <file-upload v-model="form.attachment"></file-upload>
        </el-form-item>
        <!-- <el-form-item label="目标指标内容">
          <editor v-model="form.indexContent" :min-height="192" />
        </el-form-item> -->

        <!-- <el-form-item label="指标值">
          <el-input v-model="form.indicatorValue" placeholder="请输入指标值" />
        </el-form-item> -->
        <!-- <el-form-item label="状态">
          <el-select
            v-model="form.indexStatus"
            placeholder="请选择状态"
            style="width: 100%"
          >
            <el-option label="未下发" value="0" />
            <el-option label="已下发" value="1" />
            <el-option label="进行中" value="2" />
            <el-option label="已完成" value="3" />
          </el-select>
        </el-form-item> -->

        <!-- <el-form-item label="完成时间">
          <el-date-picker
            v-model="form.targetCompleteTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择完成时间"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('0')">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 下发安全方针目标对话框 -->
    <el-dialog
      title="选择下发单位"
      :visible.sync="releaseDialog"
      width="500px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="releaseRules"
        label-width="100px"
      >
        <el-form-item label="下发单位" prop="belongingUnit">
          <el-select
            v-model="form.belongingUnit"
            placeholder="请选择下发单位"
            clearable
            filterable
            multiple
            style="width: 100%"
          >
            <template>
              <div v-for="group in deptList" :key="group.id">
                <el-option
                  :label="group.enterpriseName"
                  :value="group.id"
                  style="padding-left: 20px"
                />
                <template v-if="group.children && group.children.length">
                  <el-option
                    v-for="child in renderDeptOptions(group.children)"
                    :key="child.id"
                    :label="child.enterpriseName"
                    :value="child.id"
                    :style="{ paddingLeft: child.level * 20 + 'px' }"
                  />
                </template>
              </div>
            </template>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="下发时间">
          <el-date-picker
            v-model="form.releaseTime"
            type="datetime"
            value-format="yyyy-MM-dd  HH:mm:ss"
            placeholder="请选择下发时间"
            style="width: 100%"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('1')">确 定</el-button>
        <el-button @click="cancelReleaseDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAnquanfangzhenInfo,
  getAnquanfangzhenInfo,
  delAnquanfangzhenInfo,
  addAnquanfangzhenInfo,
  updateAnquanfangzhenInfo,
} from "@/api/inspection/anquanfangzhenInfo";
import orgTree from "../../components/orgTree.vue";
import { listInfo } from "@/api/system/info";
export default {
  name: "AnquanfangzhenInfo",
  components: {
    orgTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全方针目标表格数据
      anquanfangzhenInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        indexName: null,
        indexContent: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      deptList: [],
      deptParams: {
        enterpriseName: undefined,
        status: undefined,
      },
      indexStatusOptions: [
        {
          label: "未下发",
          value: "0",
        },
        {
          label: "已下发",
          value: "1",
        },
        {
          label: "进行中",
          value: "2",
        },
        {
          label: "已完成",
          value: "3",
        },
      ],
      releaseDialog: false,
    };
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    async handleSafetyTrainingRecords(value) {
      // if (value) {
      //   window.open(this.baseUrl + value);
      // } else {
      //   this.$message.warning("该记录没有附件");
      // }
      if (!value) {
        this.$message.warning("该记录没有附件");
        return;
      }
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const response = await fetch(fileUrl);
          const buffer = await response.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    handleOrgTreeNodeClick(nodeData) {
      // 在这里处理接收到的子组件数据
      // console.log("接收到子组件数据:", nodeData);
      // 可以根据nodeData更新查询参数或其他状态
      this.queryParams.belongingUnit = nodeData.id; // 假设nodeData中有id字段

      this.handleQuery(); // 触发查询
    },
    // 不扁平化
    renderDeptOptions(children, level = 1) {
      let result = [];
      children.forEach((child) => {
        result.push({
          ...child,
          level,
          enterpriseName: " ".repeat(level * 2) + child.enterpriseName,
        });
        if (child.children && child.children.length) {
          result = result.concat(
            this.renderDeptOptions(child.children, level + 1)
          );
        }
      });
      return result;
    },
    // 添加扁平化部门的方法
    flattenDept(children) {
      let result = [];
      children.forEach((child) => {
        result.push(child);
        if (child.children && child.children.length) {
          result = result.concat(this.flattenDept(child.children));
        }
      });
      return result;
    },
    getDeptName(deptId) {
      const dept = this.flattenDept(this.deptList).find(
        (item) => item.id == deptId
      );
      return dept ? dept.enterpriseName : "";
    },
    /** 查询部门列表 */
    getDeptList() {
      listInfo(this.deptParams).then((response) => {
        this.deptList = this.handleTree(response.data, "id");
        this.loading = false;
      });
    },
    /** 查询安全方针目标列表 */
    getList() {
      this.loading = true;
      listAnquanfangzhenInfo(this.queryParams).then((response) => {
        this.anquanfangzhenInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelReleaseDialog() {
      this.releaseDialog = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        indexName: null,
        // indexContent: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.belongingUnit = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全方针目标";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAnquanfangzhenInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全方针目标";
      });
    },
    handleRelease(row) {
      this.releaseDialog = true;
      this.form.id = row.id;
      //   this.$modal
      //     .confirm("是否确认下发安全方针目标编号为" + row.id + "的数据项？")
      //     .then(function () {
      //       return releaseAnquanfangzhenInfo(row.id);
      //     })
      //     .then(() => {
      //       this.$modal.msgSuccess("下发成功");
      //       this.getList();
      //     })
      //     .catch(() => {});
    },

    /** 提交按钮 */
    submitForm(type) {
      if (type) {
        this.form.indexStatus = type;
      }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            console.log(this.form, "this.form");
            updateAnquanfangzhenInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
            this.releaseDialog = false;
            this.reset();
          } else {
            addAnquanfangzhenInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全方针目标编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAnquanfangzhenInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/anquanfangzhenInfo/export",
        {
          ...this.queryParams,
        },
        `anquanfangzhenInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
