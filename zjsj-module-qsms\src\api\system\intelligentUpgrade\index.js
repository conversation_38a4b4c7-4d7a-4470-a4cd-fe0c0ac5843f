import request from '@/utils/request'

// 查询智能化升级项目列表
export function listSmartUpgradeProjects(query) {
  return request({
    url: '/system/smartUpgradeProjects/list',
    method: 'get',
    params: query
  })
}

// 查询智能化升级项目详细
export function getSmartUpgradeProjects(id) {
  return request({
    url: '/system/smartUpgradeProjects/' + id,
    method: 'get'
  })
}

// 新增智能化升级项目
export function addSmartUpgradeProjects(data) {
  return request({
    url: '/system/smartUpgradeProjects',
    method: 'post',
    data: data
  })
}

// 修改智能化升级项目
export function updateSmartUpgradeProjects(data) {
  return request({
    url: '/system/smartUpgradeProjects',
    method: 'put',
    data: data
  })
}

// 删除智能化升级项目
export function delSmartUpgradeProjects(id) {
  return request({
    url: '/system/smartUpgradeProjects/' + id,
    method: 'delete'
  })
}
