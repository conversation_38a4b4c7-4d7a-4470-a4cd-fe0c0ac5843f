<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="配置id" prop="configId">
        <el-input
          v-model="queryParams.configId"
          placeholder="请输入配置id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评价项目" prop="evaluationProject">
        <el-input
          v-model="queryParams.evaluationProject"
          placeholder="请输入评价项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评分标准" prop="gradingCriteria">
        <el-input
          v-model="queryParams.gradingCriteria"
          placeholder="请输入评分标准"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目分值" prop="projectScore">
        <el-input
          v-model="queryParams.projectScore"
          placeholder="请输入项目分值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['contractor:zjEvaluationConfigContent:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['contractor:zjEvaluationConfigContent:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:zjEvaluationConfigContent:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['contractor:zjEvaluationConfigContent:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjEvaluationConfigContentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="修改时间" align="center" prop="id" />
      <el-table-column label="配置id" align="center" prop="configId" />
      <el-table-column
        label="评价项目"
        align="center"
        prop="evaluationProject"
      />
      <el-table-column label="评分标准" align="center" prop="gradingCriteria" />
      <el-table-column label="项目分值" align="center" prop="projectScore" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:zjEvaluationConfigContent:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:zjEvaluationConfigContent:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评价配置内容对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="配置id" prop="configId">
          <el-input v-model="form.configId" placeholder="请输入配置id" />
        </el-form-item>
        <el-form-item label="评价项目" prop="evaluationProject">
          <el-input
            v-model="form.evaluationProject"
            placeholder="请输入评价项目"
          />
        </el-form-item>
        <el-form-item label="评分标准" prop="gradingCriteria">
          <el-input
            v-model="form.gradingCriteria"
            placeholder="请输入评分标准"
          />
        </el-form-item>
        <el-form-item label="项目分值" prop="projectScore">
          <el-input v-model="form.projectScore" placeholder="请输入项目分值" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjEvaluationConfigContent,
  getZjEvaluationConfigContent,
  delZjEvaluationConfigContent,
  addZjEvaluationConfigContent,
  updateZjEvaluationConfigContent,
} from "@/api/contractor/zjEvaluationConfigContent";

export default {
  name: "ZjEvaluationConfigContent",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评价配置内容表格数据
      zjEvaluationConfigContentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        configId: null,
        evaluationProject: null,
        gradingCriteria: null,
        projectScore: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询评价配置内容列表 */
    getList() {
      this.loading = true;
      listZjEvaluationConfigContent(this.queryParams).then((response) => {
        this.zjEvaluationConfigContentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        configId: null,
        evaluationProject: null,
        gradingCriteria: null,
        projectScore: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加评价配置内容";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjEvaluationConfigContent(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改评价配置内容";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjEvaluationConfigContent(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEvaluationConfigContent(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除评价配置内容编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjEvaluationConfigContent(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "contractor/zjEvaluationConfigContent/export",
        {
          ...this.queryParams,
        },
        `zjEvaluationConfigContent_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
