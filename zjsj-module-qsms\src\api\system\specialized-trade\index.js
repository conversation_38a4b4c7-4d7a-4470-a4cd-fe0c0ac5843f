import request from '@/utils/request'

// 查询特种作业人员列表
export function listSpecializedTrade(query) {
  return request({
    url: '/system/specializedTrade/list',
    method: 'get',
    params: query
  })
}

// getSpecializedTrade, delSpecializedTrade, addSpecializedTrade, updateSpecializedTrade
export function getSpecializedTrade(id) {
  return request({
    url: '/system/specializedTrade/' + id,
    method: 'get'
  })
}

export function delSpecializedTrade(id) {
  return request({
    url: '/system/specializedTrade/' + id,
    method: 'delete'
  })
}

export function addSpecializedTrade(data) {
  return request({
    url: '/system/specializedTrade',
    method: 'post',
    data: data
  })
}

export function updateSpecializedTrade(data) {
  return request({
    url: '/system/specializedTrade',
    method: 'put',
    data: data
  })
}