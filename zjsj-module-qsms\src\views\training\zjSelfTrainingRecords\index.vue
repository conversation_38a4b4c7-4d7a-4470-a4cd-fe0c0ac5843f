<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="培训名称" prop="trainingName">
        <el-input
          v-model="queryParams.trainingName"
          placeholder="请输入培训名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">  
        <el-select
          v-model="querySelectedCompanyName"
          placeholder="请选择公司名称"
          clearable
          filterable
          :loading="companyLoading"
          :filter-method="filterQueryCompany"
          @change="handleQueryCompanyChange"
          @visible-change="handleQueryVisibleChange"
          style="width: 200px"
          class="company-tree-select"
          popper-class="company-tree-dropdown"
        >
          <el-option :value="querySelectedCompanyName" style="height: auto; padding: 0">
            <el-tree
              ref="queryCompanyTree"
              :data="companyList"
              :props="{ label: 'label', children: 'children' }"
              :filter-node-method="filterCompanyNode"
              :expand-on-click-node="false"
              node-key="value"
              highlight-current
              style="padding: 5px 0"
              @node-click="handleQueryNodeClick"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span class="tree-label" v-html="highlightSearchText(node.label, querySearchText)"/>
              </span>
            </el-tree>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['training:zjSelfTrainingRecords:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['training:zjSelfTrainingRecords:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['training:zjSelfTrainingRecords:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['training:zjSelfTrainingRecords:export']"
        >{{ ids.length > 0 ? '导出选中' : '导出' }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="zjSelfTrainingRecordsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="培训名称" align="center" prop="trainingName" show-overflow-tooltip />
      <el-table-column label="培训日期" align="center" prop="trainingDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.trainingDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公司名称" align="center" prop="companyName" show-overflow-tooltip />
      <el-table-column
        label="培训内容"
        align="center"
        prop="trainingContent"
        width="300"
      >
        <template slot-scope="scope">
          <div
            :ref="'trainingContentContainer_' + scope.row.id"
            class="rich-text-content rich-text-ellipsis"
            v-html="scope.row.trainingContent"
          />
        </template>
      </el-table-column>
      <el-table-column label="参加人员" align="center" prop="participantsName" show-overflow-tooltip />
      <el-table-column label="备注" align="center" prop="trainingRemarks" />
      <el-table-column label="附件" align="center" prop="attachmentUpload">
        <template slot-scope="scope">
          <div v-if="scope.row.attachmentUpload" class="contract-file">
            <div
              v-for="(item, index) in scope.row.attachmentUpload.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ item.split("/").pop() }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['training:zjSelfTrainingRecords:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['training:zjSelfTrainingRecords:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
      class="pagination"
    />

    <!-- 添加或修改安全教育培训体系-自培记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="培训名称" prop="trainingName">
          <el-input v-model="form.trainingName" placeholder="请输入培训名称" />
        </el-form-item>
        <el-form-item label="培训日期" prop="trainingDate">
          <el-date-picker clearable
            v-model="form.trainingDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择培训日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-select
            v-model="formSelectedCompanyName"
            placeholder="请选择公司名称"
            clearable
            filterable
            :loading="companyLoading"
            :filter-method="filterFormCompany"
            @change="handleFormCompanyChange"
            @visible-change="handleFormVisibleChange"
            style="width: 100%"
            class="company-tree-select"
            popper-class="company-tree-dropdown"
          >
            <el-option :value="formSelectedCompanyName" style="height: auto; padding: 0">
              <el-tree
                ref="formCompanyTree"
                :data="companyList"
                :props="{ label: 'label', children: 'children' }"
                :filter-node-method="filterCompanyNode"
                :expand-on-click-node="false"
                node-key="value"
                highlight-current
                style="padding: 5px 0"
                @node-click="handleFormNodeClick"
              >
                <span slot-scope="{ node, data }" class="custom-tree-node">
                  <span class="tree-label" v-html="highlightSearchText(node.label, formSearchText)"/>
                </span>
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="培训内容">
          <editor v-model="form.trainingContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="参加部门" prop="participantsId">
          <el-select ref="participantsSelect" v-model="participantsDisplay" placeholder="请选择参加部门" clearable
            style="width: 100%" popper-class="org-tree-select-dropdown" @clear="clearParticipants">
            <el-option :value="participantsDisplay" :label="participantsDisplay"
              style="height: auto; padding: 0; border: none;">
              <div class="tree-select-wrapper">
                <el-tree
                  v-loading="orgTreeLoading"
                  :data="orgTreeData"
                  :props="orgTreeProps"
                  highlight-current
                  @node-click="handleParticipantsNodeClick"
                  :expand-on-click-node="false"
                  node-key="id"
                  style="padding: 5px 0"
                >
                  <template #default="{ node, data }">
                    <el-tooltip effect="dark" :content="data.label" placement="top">
                      <span class="custom-tree-node-label">
                        {{ node.label }}
                      </span>
                    </el-tooltip>
                  </template>
                </el-tree>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="trainingRemarks">
          <el-input v-model="form.trainingRemarks" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="附件上传" prop="attachmentUpload">
          <file-upload v-model="form.attachmentUpload" :file-type="['pdf', 'docx', 'png', 'jpg', 'doc', 'xls', 'xlsx', 'txt']"></file-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listZjSelfTrainingRecords, getZjSelfTrainingRecords, delZjSelfTrainingRecords, addZjSelfTrainingRecords, updateZjSelfTrainingRecords } from "@/api/training/zjSelfTrainingRecords";
import { getEnterpriseInfo } from "@/api/system/info";
import { listZjEmployeeInfo } from "@/api/inspection/zjEmployeeInfo";

export default {
  name: "ZjSelfTrainingRecords",
  data() {
    return {
      // 基础URL
      baseUrl: process.env.VUE_APP_BASE_API,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全教育培训体系-自培记录表格数据
      zjSelfTrainingRecordsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 公司列表数据
      companyList: [],
      // 公司列表加载状态
      companyLoading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        trainingName: null,
        companyName: null,
        companyId: null,
        trainingContent: null,
        trainingRemarks: null,
        attachmentUpload: null,
      },
      // 查询表单选中的公司名称（显示用）
      querySelectedCompanyName: "",
      // 表单选中的公司名称（显示用）
      formSelectedCompanyName: "",
      // 查询搜索文本
      querySearchText: "",
      // 表单搜索文本
      formSearchText: "",
      // 参与部门显示文本
      participantsDisplay: "",
      // 当前选择的参与部门ID
      selectedDeptId: null,
      // 当前选择的参与部门名称
      selectedDeptName: null,
      // 组织架构树数据
      orgTreeData: [],
      // 组织架构树加载状态
      orgTreeLoading: false,
      // 组织架构树属性配置
      orgTreeProps: {
        children: 'children',
        label: 'label',
        id: 'id',
        isLeaf: 'isLeaf'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        trainingName: [
          { required: true, message: "培训名称不能为空", trigger: "blur" }
        ],
        trainingDate: [
          { required: true, message: "培训日期不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getCompanyList();
    this.getOrgTreeData();
  },
  methods: {
    /** 获取组织架构数据 */
    getOrgTreeData() {
      this.orgTreeLoading = true;
      getEnterpriseInfo()
        .then((response) => {
          const deptList = response.data || [];
          this.orgTreeData = [];
          deptList.forEach((item) => {
            this.orgTreeData.push({
              label: item.label,
              id: item.id,
              children: item.children
            });
          });
          this.orgTreeLoading = false;
        })
        .catch((error) => {
          this.orgTreeLoading = false;
          console.error('获取组织架构数据失败:', error);
        });
    },
    /** 查询公司列表 */
    getCompanyList() {
      this.companyLoading = true;
      getEnterpriseInfo().then(response => {
        this.companyList = this.processCompanyTree(response.data || []);
        this.companyLoading = false;
      }).catch(() => {
        this.companyLoading = false;
      });
    },
    /** 处理公司树形数据 */
    processCompanyTree(tree) {
      return tree.map(node => ({
        value: node.id,
        label: node.label,
        children: node.children && node.children.length > 0 ? this.processCompanyTree(node.children) : undefined
      }));
    },
    /** 在树形数据中查找节点 */
    findNodeInTree(tree, id) {
      for (let node of tree) {
        if (node.value === id) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeInTree(node.children, id);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    /** 查询下拉框显示状态变化 */
    handleQueryVisibleChange(isVisible) {
      if (isVisible) {
        // 下拉框打开时，如果有搜索文本，立即触发过滤
        if (this.querySearchText) {
          this.$nextTick(() => {
            this.filterQueryCompany(this.querySearchText);
          });
        }
      } else {
        // 关闭下拉框时清空搜索
        this.querySearchText = "";
        if (this.$refs.queryCompanyTree) {
          this.$refs.queryCompanyTree.filter("");
        }
      }
    },
    /** 表单下拉框显示状态变化 */
    handleFormVisibleChange(isVisible) {
      if (isVisible) {
        // 下拉框打开时，如果有搜索文本，立即触发过滤
        if (this.formSearchText) {
          this.$nextTick(() => {
            this.filterFormCompany(this.formSearchText);
          });
        }
      } else {
        // 关闭下拉框时清空搜索
        this.formSearchText = "";
        if (this.$refs.formCompanyTree) {
          this.$refs.formCompanyTree.filter("");
        }
      }
    },
    /** 查询树节点点击 */
    handleQueryNodeClick(data) {
      this.querySelectedCompanyName = data.label;  // 显示名称
      this.queryParams.companyId = data.value;      // 保存ID
      this.queryParams.companyName = data.label;    // 保存名称
      
      // 更新树的高亮选择并关闭下拉框
      this.$refs.queryCompanyTree.setCurrentKey(data.value);
      this.$nextTick(() => {
        // 模拟点击下拉框外部区域来关闭下拉框
        document.body.click();
      });
    },
    /** 表单树节点点击 */
    handleFormNodeClick(data) {
      this.formSelectedCompanyName = data.label;   // 显示名称
      this.form.companyId = data.value;            // 保存ID
      this.form.companyName = data.label;          // 保存名称
      
      // 更新树的高亮选择并关闭下拉框
      this.$refs.formCompanyTree.setCurrentKey(data.value);
      this.$nextTick(() => {
        // 模拟点击下拉框外部区域来关闭下拉框
        document.body.click();
      });
    },
    /** 查询表单公司选择变化 */
    handleQueryCompanyChange(value) {
      if (!value) {
        this.querySelectedCompanyName = "";
        this.queryParams.companyId = null;
        this.queryParams.companyName = null;
      }
    },
    /** 表单公司选择变化 */
    handleFormCompanyChange(value) {
      if (!value) {
        this.formSelectedCompanyName = "";
        this.form.companyId = null;
        this.form.companyName = null;
      }
    },
    /** 查询公司过滤方法 */
    filterQueryCompany(value) {
      this.querySearchText = value;
      if (this.$refs.queryCompanyTree) {
        this.$refs.queryCompanyTree.filter(value);
        // 如果有搜索值，展开匹配的节点
        if (value && value.trim()) {
          setTimeout(() => {
            this.expandMatchedNodes(this.$refs.queryCompanyTree, value.trim());
          }, 50);
        }
      }
      return true;
    },
    /** 表单公司过滤方法 */
    filterFormCompany(value) {
      this.formSearchText = value;
      if (this.$refs.formCompanyTree) {
        this.$refs.formCompanyTree.filter(value);
        // 如果有搜索值，展开匹配的节点
        if (value && value.trim()) {
          setTimeout(() => {
            this.expandMatchedNodes(this.$refs.formCompanyTree, value.trim());
          }, 50);
        }
      }
      return true;
    },
    /** 公司节点过滤方法 */
    filterCompanyNode(value, data) {
      if (!value) return true;
      const searchValue = value.toLowerCase().trim();
      const label = data.label ? data.label.toLowerCase() : "";
      return label.includes(searchValue);
    },
    /** 展开匹配的节点 */
    expandMatchedNodes(treeRef, searchValue) {
      if (!treeRef || !searchValue) return;
      
      const expandedKeys = [];
      this.collectExpandedNodes(this.companyList, searchValue, expandedKeys);
      
      // 展开节点
      const uniqueKeys = [...new Set(expandedKeys)];
      uniqueKeys.forEach((key) => {
        const node = treeRef.store.nodesMap[key];
        if (node && !node.expanded) {
          node.expand();
        }
      });
    },
    /** 收集需要展开的节点 */
    collectExpandedNodes(nodes, searchValue, expandedKeys, parentKey = null) {
      let hasMatchedChild = false;
      
      for (const node of nodes) {
        // 检查当前节点是否匹配
        if (this.filterCompanyNode(searchValue, node)) {
          hasMatchedChild = true;
          if (parentKey) {
            expandedKeys.push(parentKey);
          }
        }
        
        // 递归检查子节点
        if (node.children && node.children.length > 0) {
          const childMatched = this.collectExpandedNodes(
            node.children,
            searchValue,
            expandedKeys,
            node.value
          );
          
          if (childMatched) {
            hasMatchedChild = true;
            if (node.value) {
              expandedKeys.push(node.value);
            }
            if (parentKey) {
              expandedKeys.push(parentKey);
            }
          }
        }
      }
      
      return hasMatchedChild;
    },
    /** 高亮搜索文本 */
    highlightSearchText(text, searchValue) {
      if (!text) return "";
      if (!searchValue || !searchValue.trim()) return text;
      
      const searchText = searchValue.trim();
      // 防止XSS攻击，转义HTML特殊字符
      const escapedText = text.replace(/[&<>"']/g, function (match) {
        const escapeMap = {
          "&": "&amp;",
          "<": "&lt;",
          ">": "&gt;",
          '"': "&quot;",
          "'": "&#x27;",
        };
        return escapeMap[match];
      });
      
      // 如果搜索文本包含在显示文本中，高亮显示
      if (escapedText.toLowerCase().includes(searchText.toLowerCase())) {
        const regex = new RegExp(
          `(${searchText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
          "gi"
        );
        return escapedText.replace(
          regex,
          '<span class="search-highlight">$1</span>'
        );
      }
      
      return escapedText;
    },
    /** 查询安全教育培训体系-自培记录列表 */
    getList() {
      this.loading = true;
      listZjSelfTrainingRecords(this.queryParams).then(response => {
        this.zjSelfTrainingRecordsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        trainingName: null,
        trainingDate: null,
        companyName: null,
        companyId: null,
        trainingContent: null,
        participantsId: null,
        participantsDeptName: null,
        participantsName: '',
        trainingRemarks: null,
        attachmentUpload: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.formSelectedCompanyName = "";
      this.formSearchText = "";
      this.participantsDisplay = "";
      this.selectedDeptId = null;
      this.selectedDeptName = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.querySelectedCompanyName = "";
      this.querySearchText = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全教育培训体系-自培记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getZjSelfTrainingRecords(id).then(response => {
        this.form = response.data;
        // 如果有公司ID，找到对应的公司并设置选中状态
        if (this.form.companyId) {
          this.formSelectedCompanyName = this.form.companyName || "";
          // 设置树的高亮选择
          this.$nextTick(() => {
            if (this.$refs.formCompanyTree) {
              this.$refs.formCompanyTree.setCurrentKey(this.form.companyId);
            }
          });
        }
        
        // 处理参与部门数据回显
        if (this.form.participantsId && this.form.participantsDeptName) {
          this.selectedDeptId = this.form.participantsId;
          this.selectedDeptName = this.form.participantsDeptName;
          // 如果participantsName已经是部门名称(人数)格式，直接使用
          if (this.form.participantsName && this.form.participantsName.includes('(') && this.form.participantsName.includes('人)')) {
            this.participantsDisplay = this.form.participantsName;
          } else if (this.form.participantsName) {
            // 如果是老数据（人员名称列表），计算人数并转换格式
            const participantCount = this.form.participantsName.split('、').filter(name => name.trim()).length;
            const displayText = `${this.form.participantsDeptName}(${participantCount}人)`;
            this.participantsDisplay = displayText;
            this.form.participantsName = displayText; // 更新为新格式
          } else {
            this.participantsDisplay = this.form.participantsDeptName;
            this.form.participantsName = this.form.participantsDeptName;
          }
        }
        
        this.open = true;
        this.title = "修改安全教育培训体系-自培记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保participantsName是部门名称(人数)的格式
          const submitData = { ...this.form };
          if (submitData.participantsId && this.participantsDisplay) {
            submitData.participantsName = this.participantsDisplay;
          } else {
            submitData.participantsName = '';
          }
          
          if (this.form.id != null) {
            updateZjSelfTrainingRecords(submitData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjSelfTrainingRecords(submitData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除安全教育培训体系-自培记录编号为"' + ids + '"的数据项？').then(function() {
        return delZjSelfTrainingRecords(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 如果有选中记录，导出选中的；否则导出全部
      if (this.ids.length > 0) {
        // 导出选中记录
        this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条记录？').then(() => {
          this.download(
            'training/zjSelfTrainingRecords/export',
            {
              ids: this.ids.join(',')
            },
            `zjSelfTrainingRecords_selected_${new Date().getTime()}.xlsx`
          )
        })
      } else {
        // 导出全部记录（根据查询条件，但不包含分页参数）
        const exportParams = { ...this.queryParams }
        // 移除分页参数，导出全部符合条件的数据
        delete exportParams.pageNum
        delete exportParams.pageSize
        
        this.download(
          'training/zjSelfTrainingRecords/export',
          exportParams,
          `zjSelfTrainingRecords_${new Date().getTime()}.xlsx`
        )
      }
    },
    /** 处理参与部门选择 */
    handleParticipantsNodeClick(nodeData) {
      if (nodeData && nodeData.label && nodeData.id) {
        this.selectedDeptId = nodeData.id;
        this.selectedDeptName = nodeData.label;
        // 设置表单中的部门ID和名称
        this.form.participantsId = nodeData.id;
        this.form.participantsDeptName = nodeData.label;
        // 获取该部门的所有人员用于显示
        this.getParticipantsByDept(nodeData.id, nodeData.label);
        // 选择后关闭下拉框
        this.$nextTick(() => {
          if (this.$refs.participantsSelect) {
            this.$refs.participantsSelect.blur();
          }
        });
      }
    },
    /** 根据部门ID获取参与人员（用于显示） */
    getParticipantsByDept(deptId, deptName) {
      const queryParams = {
        deptId: deptId,
        pageNum: 1,
        pageSize: 1000 // 获取所有人员
      };

      listZjEmployeeInfo(queryParams).then(response => {
        if (response.rows && response.rows.length > 0) {
          // 设置选择器显示的文本为部门名称+人数
          const displayText = `${deptName}(${response.rows.length}人)`;
          this.participantsDisplay = displayText;
          // 将部门名称(人数)保存到表单的participantsName字段
          this.form.participantsName = displayText;
        } else {
          this.$modal.msgWarning(`${deptName}部门暂无人员`);
          this.participantsDisplay = deptName;
          this.form.participantsName = deptName;
        }
      }).catch(error => {
        console.error('获取部门人员失败:', error);
        this.$modal.msgError('获取部门人员失败');
        this.participantsDisplay = deptName;
        this.form.participantsName = deptName;
      });
    },
    /** 清空参与部门 */
    clearParticipants() {
      this.form.participantsId = null;
      this.form.participantsDeptName = null;
      this.form.participantsName = '';
      this.participantsDisplay = '';
      this.selectedDeptId = null;
      this.selectedDeptName = null;
    },
    /** 附件查看 */
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const response = await fetch(fileUrl);
          const buffer = await response.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    }
  }
};
</script>

<style scoped>
/* 页面布局 - 固定底部分页 */
.app-container {
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  padding: 20px;
}

/* 搜索表单区域 */
.app-container .el-form {
  flex-shrink: 0;
  margin-bottom: 10px;
}

/* 工具栏区域 */
.app-container .el-row.mb8 {
  flex-shrink: 0;
  margin-bottom: 8px;
}

/* 表格区域 - 占据剩余空间 */
.app-container .el-table {
  flex: 1;
  margin-bottom: 20px;
}

/* 分页样式 - 固定在底部 */
.pagination {
  flex-shrink: 0;
  text-align: center;
  padding: 10px 0;
  margin-top: auto;
  background-color: #fff;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 调整树组件样式 */
:deep(.company-tree-select) {
  min-width: 180px;
  width: auto;
}

/* 控制下拉框宽度 */
:deep(.company-tree-dropdown) {
  max-width: 300px !important;
  min-width: 200px !important;
  width: auto !important;
}

:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item) {
  padding: 0;
  height: auto;
}

/* 搜索高亮样式 */
:deep(.search-highlight) {
  background-color: #fffacd;
  color: #d32f2f;
  font-weight: bold;
  padding: 1px 2px;
  border-radius: 2px;
}

/* 附件样式 */
.contract-file {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attachment-item {
  color: #409EFF;
  cursor: pointer;
  text-decoration: underline;
  font-size: 12px;
  word-break: break-all;
}

.attachment-item:hover {
  color: #66b1ff;
}

/* 树形下拉选择器样式 */
.tree-select-wrapper {
  width: 300px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

:deep(.org-tree-select-dropdown .el-select-dropdown__item) {
  padding: 0 !important;
  height: auto !important;
  line-height: normal !important;
}

:deep(.org-tree-select-dropdown .el-select-dropdown__item:hover) {
  background-color: transparent !important;
}

/* 树选择器标签样式 */
.custom-tree-node-label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.tree-select-wrapper .el-tree-node__label) {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.tree-select-wrapper .el-tree-node.is-current > .el-tree-node__content) {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}
</style>

<style>
/* 全局样式 - 不受scoped限制，确保能够覆盖Element UI的默认样式 */
.company-tree-dropdown.el-select-dropdown {
  max-width: 350px !important;
  min-width: 200px !important;
  width: auto !important;
}

.company-tree-dropdown .el-select-dropdown__item {
  padding: 0;
  height: auto;
}

/* 组织架构树选择器全局样式 */
.org-tree-select-dropdown.el-select-dropdown {
  max-width: 350px !important;
  min-width: 200px !important;
  width: auto !important;
}

.org-tree-select-dropdown .el-select-dropdown__item {
  padding: 0;
  height: auto;
}

/* 富文本内容样式 */
.rich-text-content {
  text-align: center;
  word-break: break-word;
  line-height: 1.5;
  overflow: visible;
}

.rich-text-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 10px auto;
  display: block;
}

.rich-text-content p {
  margin: 8px 0;
}

.rich-text-content p span {
  color: #606266 !important;
}

/* 四行省略样式 */
.rich-text-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 96px; /* 1.5 * 16px * 4 = 96px */
  line-height: 1.6;
  word-break: break-word;
  font-size: 14px;
}

/* 处理富文本中的段落间距 */
.rich-text-ellipsis p {
  margin: 2px 0; /* 减小段落间距 */
  line-height: 1.6;
}

.rich-text-ellipsis p:first-child {
  margin-top: 0;
}

.rich-text-ellipsis p:last-child {
  margin-bottom: 0;
}

/* 确保图片不影响行数计算 */
.rich-text-ellipsis img {
  display: none; /* 在省略模式下隐藏图片 */
}
</style>
