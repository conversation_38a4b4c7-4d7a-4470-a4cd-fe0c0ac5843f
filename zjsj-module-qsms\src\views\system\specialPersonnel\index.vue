<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="人员姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属项目" prop="projectName">
        <selectPeopleTree
          v-model="queryParams.projectName"
          :people-list="projectList"
          placeholder="请选择所属项目"
          @change="handleSearchProjectChange"
          style="width: 400px"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:workers:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:workers:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:workers:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="workersList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="人员姓名" align="center" prop="name" />
      <!-- <el-table-column label="身份证号码" align="center" prop="idCardNum" /> -->
      <el-table-column label="手机号码" align="center" prop="phone" />
      <el-table-column label="作业类型" align="center" prop="workType" />
      <el-table-column label="所属项目" align="center" prop="projectName" />
      <el-table-column
        label="在岗状态"
        align="center"
        prop="status"
        :formatter="formatStatus"
      />
      <!-- 
      <el-table-column
        label="操作证编号"
        align="center"
        prop="certificateNum"
      />
      <el-table-column
        label="发证机关"
        align="center"
        prop="certificateIssueOrg"
      />
      <el-table-column
        label="发证日期"
        align="center"
        prop="certificateIssueDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.certificateIssueDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="证书有效期"
        align="center"
        prop="certificateExpiryDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.certificateExpiryDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作证正面照片"
        align="center"
        prop="certificateFrontPic"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.certificateFrontPic" class="contract-file">
            <div
              v-for="(item, index) in scope.row.certificateFrontPic.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作证反面照片"
        align="center"
        prop="certificateBackPic"
      >
        <template slot-scope="scope">
          <div v-if="scope.row.certificateBackPic" class="contract-file">
            <div
              v-for="(item, index) in scope.row.certificateBackPic.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="280"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:workers:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdateStatus(scope.row)"
            v-hasPermi="['system:workers:edit']"
            >修改在岗状态</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:workers:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改特种作业人员对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="人员姓名" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入人员姓名"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="所属项目" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择所属项目"
            @change="handleFormSearchProjectChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="身份证号码" prop="idCardNum">
          <el-input
            v-model="form.idCardNum"
            placeholder="请输入身份证号码"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入手机号码"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="作业类型" prop="workType">
          <el-input
            v-model="form.workType"
            placeholder="请输入作业类型"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="操作证编号" prop="certificateNum">
          <el-input
            v-model="form.certificateNum"
            placeholder="请输入操作证编号"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="发证机关" prop="certificateIssueOrg">
          <el-input
            v-model="form.certificateIssueOrg"
            placeholder="请输入发证机关"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="发证日期" prop="certificateIssueDate">
          <el-date-picker
            clearable
            v-model="form.certificateIssueDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择发证日期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="证书有效期" prop="certificateExpiryDate">
          <el-date-picker
            clearable
            v-model="form.certificateExpiryDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择证书有效期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="操作证正面照片" prop="certificateFrontPic">
          <file-upload
            v-model="form.certificateFrontPic"
            :file-type="['png', 'jpg', 'gif', 'jpeg', 'svg']"
            :disabled="isCheck"
            :limit="1"
          />
        </el-form-item>
        <el-form-item label="操作证反面照片" prop="certificateBackPic">
          <file-upload
            v-model="form.certificateBackPic"
            :file-type="['png', 'jpg', 'gif', 'jpeg', 'svg']"
            :disabled="isCheck"
            :limit="1"
          />
        </el-form-item>
        <el-form-item label="在岗状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择在岗状态"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="修改在岗状态"
      :visible.sync="openStatusDialog"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="在岗状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择在岗状态"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormStatus">确 定</el-button>
        <el-button @click="cancelStatus">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWorkers,
  getWorkers,
  delWorkers,
  addWorkers,
  updateWorkers,
} from "@/api/system/specialPersonnel/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { getFileOrignalName } from "@/utils/common.js";
import { querytree } from "@/api/system/info";
export default {
  name: "Workers",
  components: {
    selectPeopleTree,
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      isCheck: false,
      statusOptions: [
        {
          label: "离场",
          value: 0,
        },
        {
          label: "在岗",
          value: 1,
        },
        {
          label: "黑名单",
          value: 2,
        },
      ],
      projectList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 特种作业人员表格数据
      workersList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openStatusDialog: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        idCardNum: null,
        phone: null,
        workType: null,
        certificateNum: null,
        certificateIssueOrg: null,
        certificateIssueDate: null,
        certificateExpiryDate: null,
        certificateFrontPic: null,
        certificateBackPic: null,
        projectId: null,
        projectName: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "人员姓名不能为空", trigger: "blur" },
        ],
        idCardNum: [
          { required: true, validator: this.validateIdCard, trigger: "blur" },
        ],
        workType: [
          { required: true, message: "作业类型不能为空", trigger: "change" },
        ],
        certificateNum: [
          { required: true, message: "操作证编号不能为空", trigger: "blur" },
        ],
        certificateIssueOrg: [
          { required: true, message: "发证机关不能为空", trigger: "blur" },
        ],
        certificateIssueDate: [
          { required: true, message: "发证日期不能为空", trigger: "blur" },
        ],
        certificateExpiryDate: [
          { required: true, message: "证书有效期不能为空", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "所属项目不能为空", trigger: "change" },
        ],
        status: [
          { required: true, message: "在岗状态不能为空", trigger: "change" },
        ],
        workType: [
          { required: true, message: "作业类型不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProjectList();
  },
  methods: {
    formatStatus(row, column, value) {
      const option = this.statusOptions.find((item) => item.value == value);
      return option ? option.label : value;
    },
    validateIdCard(rule, value, callback) {
      const reg = /(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!reg.test(value)) {
        return callback(new Error("身份证格式错误"));
      }
      callback();
    },
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    handleSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectId = selectedItem.id;
        this.queryParams.projectName = selectedItem.label;
      } else {
        this.queryParams.projectId = null;
      }
    },
    handleFormSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectId = selectedItem.id;
        this.form.projectName = selectedItem.label;
      } else {
        this.form.projectId = null;
      }
    },
    getFileOrignalName,
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    /** 查询特种作业人员列表 */
    getList() {
      this.loading = true;
      listWorkers(this.queryParams).then((response) => {
        this.workersList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelStatus() {
      this.openStatusDialog = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        idCardNum: null,
        phone: null,
        workType: null,
        certificateNum: null,
        certificateIssueOrg: null,
        certificateIssueDate: null,
        certificateExpiryDate: null,
        certificateFrontPic: null,
        certificateBackPic: null,
        projectId: null,
        projectName: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.projectId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加特种作业人员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getWorkers(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改特种作业人员";
      });
    },
    handleUpdateStatus(row) {
      this.reset();
      const id = row.id || this.ids;
      getWorkers(id).then((response) => {
        this.form = response.data;
        this.openStatusDialog = true;
        this.isCheck = false;
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getWorkers(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "查看特种作业人员";
      });
    },
    /** 提交按钮 */
    submitFormStatus() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateWorkers(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.openStatusDialog = false;
              this.getList();
            });
          }
        }
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateWorkers(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWorkers(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除特种作业人员编号为"' + ids + '"的数据项？')
        .then(function () {
          return delWorkers(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/specialWorkers/export",
        {
          ...this.queryParams,
        },
        `特种作业人员管理.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>