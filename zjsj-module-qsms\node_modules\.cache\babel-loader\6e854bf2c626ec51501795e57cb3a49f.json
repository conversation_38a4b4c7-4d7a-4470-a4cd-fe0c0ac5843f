{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue", "mtime": 1757497419375}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_bar<PERSON><PERSON>", "_interopRequireDefault", "_<PERSON><PERSON><PERSON>", "_statistics", "name", "components", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "data", "qualityTimeType", "safetyTimeType", "statsData", "domesticProjects", "internationalProjects", "safetyInvestment", "largeEquipment", "dangerousProjects", "superDangerousProjects", "domesticSafetyInvestment", "internationalSafetyInvestment", "companyBranches", "casualties", "majorH<PERSON><PERSON>", "qualityManagement", "inspectionPlans", "dailyInspections", "specialInspections", "foundProblems", "fixedProblems", "pendingProblems", "onTimeRate", "onTimeFixed", "overdueFixed", "selfReportProblems", "selfReportFixed", "selfReportPending", "selfReportRate", "selfReportOnTime", "selfReportOverdue", "safetyManagement", "inspectionCount", "hazardFound", "hazardCount", "reportOnTimeRate", "hazardTypeChart1", "colorList", "value", "option", "series", "center", "radius", "label", "show", "position", "formatter", "fontSize", "color", "lineHeight", "labelLine", "length", "length2", "lineStyle", "width", "hazardTypeChart2", "selfReport", "reportCount", "completed", "pending", "onTimeCompleted", "overdueCompleted", "statisticsAnalysis", "<PERSON><PERSON><PERSON>", "dangerousProjectsList", "progress", "dangerousProjectChart", "grid", "top", "left", "right", "bottom", "xAxis", "type", "max", "axisLabel", "axisTick", "axisLine", "splitLine", "yAxis", "interval", "itemStyle", "borderRadius", "<PERSON><PERSON><PERSON><PERSON>", "largeEquipmentChart", "rotate", "stack", "safetyInvestmentPieChart", "safetyInvestmentTotal", "unit", "safetyInvestmentProjects", "lineData", "xAxisData", "seriesData", "mainData", "yearCount", "dangerList", "echartData", "cateBarData", "yearBarData", "chart2Lengend", "echartType1", "echartType2", "echartTypeList1", "echartTypeList2", "queryParams", "pageNum", "pageSize", "tooltip", "visible", "x", "y", "title", "loading", "error", "dangerousProjectChartInstance", "equipmentDetailsCache", "equipmentTooltip", "requestQueue", "Set", "created", "loadManagementOverview", "loadQualityStatistics", "loadSafetyStatistics", "loadDangerTypeStatistics", "loadSafetyProductionStatistics", "loadDangerousProStatistics", "loadLargeEquipmentStatistics", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "methods", "changeQualityTimeType", "timeType", "changeSafetyTimeType", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "p", "n", "console", "log", "getManagementOverview", "v", "code", "wdgcs", "aqtz<PERSON><PERSON>", "gnzjxms", "gjzjxms", "dxsbs", "warn", "handleDataLoadError", "a", "$modal", "msgWarning", "_this2", "_callee2", "_t2", "_context2", "getQualityStatistics", "jczs", "ywcsl", "kwcsl", "aszgl", "aszg", "waszg", "handleQualityDataLoadError", "_this3", "_callee3", "_t3", "_context3", "getSafetyStatistics", "toFixed", "safetyFixedProblems", "safetyPendingProblems", "safetyOnTimeFixed", "safetyOverdueFixed", "handleSafetyDataLoadError", "_this4", "_callee4", "defaultDataForInspection", "colorList1", "colorList2", "sortedData", "_t4", "_context4", "getDangerTypeStatistics", "Array", "isArray", "sort", "b", "slice", "chart1", "chart2", "handleDangerTypeDataLoadError", "defaultDataForReport", "_this5", "_callee5", "fullCompanyNames", "budgetData", "actualData", "maxValue", "yAxisMax", "totalInvestment", "_t5", "_context5", "getSafetyProductionStatistics", "map", "item", "companyName", "company", "substring", "index", "parseFloat", "annualBudgetAmount", "fullName", "actualInputAmount", "Math", "apply", "_toConsumableArray2", "concat", "ceil", "reduce", "sum", "round", "toString", "safetyInvestmentChart", "handleSafetyProductionDataLoadError", "_this6", "_callee6", "topProjects", "projectNames", "projectData", "_t6", "_context6", "getDangerousProStatistics", "detalList", "reverse", "originalName", "$nextTick", "initDangerousProjectChart", "handleDangerousProDataLoadError", "_this7", "_this8", "$refs", "init", "trigger", "backgroundColor", "borderColor", "borderWidth", "textStyle", "extraCssText", "point", "params", "dom", "rect", "size", "tooltipWidth", "contentSize", "tooltipHeight", "chartWidth", "chartHeight", "height", "rightSpace", "leftSpace", "min", "_typeof2", "_params$data", "html", "for<PERSON>ach", "setOption", "window", "addEventListener", "resize", "_this9", "_callee7", "equipmentNames", "runningData", "_t7", "_context7", "getLargeEquipmentStatistics", "legend", "undefined", "fill", "handleLargeEquipmentDataLoadError", "loadEquipmentDetails", "equipmentName", "_this0", "_callee8", "_t8", "_context8", "getLargeEquipmentByNameStatistics", "equipment_name", "handleEquipmentMouseMove", "event", "_this$largeEquipmentC", "currentTarget", "getBoundingClientRect", "clientX", "equipmentIndex", "floor", "clientY", "cachedData", "handleEquipmentMouseLeave", "truncateCompanyName", "max<PERSON><PERSON><PERSON>", "arguments"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container home bg\">\r\n    <!-- 顶部统计卡片 -->\r\n    <div class=\"top-stats\">\r\n      <!-- 超危大工程 -->\r\n      <div class=\"stat-card stat-card-1\">\r\n        <div class=\"stat-header\">\r\n          <h3>超危大工程</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">超危工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.superDangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">危大工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.dangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 安全生产 -->\r\n      <div class=\"stat-card stat-card-2\">\r\n        <div class=\"stat-header\">\r\n          <h3>安全生产</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 在建项目 -->\r\n      <div class=\"stat-card stat-card-3\">\r\n        <div class=\"stat-header\">\r\n          <h3>在建项目</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 第二行统计卡片 -->\r\n    <div class=\"second-stats\">\r\n      <div class=\"stat-card-small stat-card-small-1\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-office-building\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">企业分支</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.companyBranches }}</span>\r\n            <span class=\"stat-unit-small\">个</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-2\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">伤亡事故人数</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.casualties }}</span>\r\n            <span class=\"stat-unit-small\">人</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-3\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning-outline\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">重大危险源项目</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.majorHazards }}</span>\r\n            <span class=\"stat-unit-small\">项</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-4\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-cpu\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">大型设备数量</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.largeEquipment }}</span>\r\n            <span class=\"stat-unit-small\">台</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 第三行：超危大工程和安全生产投入 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>超危大工程</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"dangerous-chart-container\">\r\n              <div\r\n                ref=\"dangerousProjectChart\"\r\n                class=\"chart-container\"\r\n                style=\"height: 280px\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>安全生产投入</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"safety-investment-container\">\r\n              <!-- 左侧环形图 -->\r\n              <div class=\"pie-chart-section\">\r\n                <div class=\"chart-wrapper\">\r\n                  <pieChart \r\n                    height=\"280px\" \r\n                    :data=\"safetyInvestmentPieChart\" \r\n                    :showCenterText=\"true\"\r\n                    :centerText=\"safetyInvestmentTotal\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <!-- 右侧项目列表 -->\r\n              <div class=\"project-list-section\">\r\n                <div class=\"project-list\">\r\n                  <div \r\n                    v-for=\"(item, index) in safetyInvestmentProjects\" \r\n                    :key=\"index\"\r\n                    class=\"project-item\"\r\n                  >\r\n                    <div class=\"project-dot\" :style=\"{backgroundColor: item.color}\"></div>\r\n                    <div class=\"project-info\">\r\n                      <div class=\"project-name\">{{ item.name }}</div>\r\n                      <div class=\"project-amount\">{{ item.value }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 底部：质量管理和安全管理 -->\r\n    <el-row :gutter=\"20\" class=\"management-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>质量管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <!-- timeType=1  月  2：年 -->\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 2 }\"\r\n                  @click=\"changeQualityTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 1 }\"\r\n                  @click=\"changeQualityTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div style=\"background: #f5f7f9; padding: 10px 0px 1px 20px\">\r\n            <!-- 检查下发部分 -->\r\n            <div class=\"section-header\">\r\n              <span class=\"section-title\">检查下发</span>\r\n            </div>\r\n            <div class=\"quality-stats\">\r\n              <div class=\"stat-group\">\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"检查计划数\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">检查计划数</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.inspectionPlans }}\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >日常巡检\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.dailyInspections\r\n                      }}</span>\r\n                      次</span\r\n                    >\r\n                    <span\r\n                      >专项巡检\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.specialInspections\r\n                      }}</span>\r\n                      次</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"发现问题数\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">发现问题数</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.foundProblems }}\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >已整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.fixedProblems\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >待整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.pendingProblems\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"按时整改率\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">按时整改率</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.onTimeRate }}%\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.onTimeFixed\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >未按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.overdueFixed\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            style=\"\r\n              background: #f5f7f9;\r\n              padding: 10px 0px 1px 20px;\r\n              margin-top: 20px;\r\n            \"\r\n          >\r\n            <!-- 自主上报部分 -->\r\n            <div class=\"section-header\">\r\n              <span class=\"section-title\">自主上报</span>\r\n            </div>\r\n            <div class=\"quality-stats\">\r\n              <div class=\"stat-group\">\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"自主上报问题数\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">自主上报问题数</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.selfReportProblems }}\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >已整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportFixed\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >待整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportPending\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"按时整改率\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">按时整改率</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.selfReportRate }}%\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportOnTime\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >未按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportOverdue\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n                <!-- 空占位符，用于对齐 -->\r\n                <div class=\"stat-item stat-placeholder\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>安全管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 2 }\"\r\n                  @click=\"changeSafetyTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 1 }\"\r\n                  @click=\"changeSafetyTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 顶部统计卡片 -->\r\n          <div class=\"safety-top-stats\">\r\n            <div class=\"safety-stat-card-new\">\r\n              <div class=\"safety-stat-icon\">\r\n                <i class=\"el-icon-warning-outline\"></i>\r\n              </div>\r\n              <div class=\"safety-stat-content-new\">\r\n                <div class=\"safety-stat-label\">安全隐患数</div>\r\n                <div class=\"safety-stat-number-new\">\r\n                  <span class=\"number\">{{ safetyManagement.hazardCount }}</span>\r\n                  <span class=\"unit\">个</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"safety-stat-card-new\">\r\n              <div class=\"safety-stat-icon rate-icon\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n              </div>\r\n              <div class=\"safety-stat-content-new\">\r\n                <div class=\"safety-stat-label\">按时整改率</div>\r\n                <div class=\"safety-stat-number-new\">\r\n                  <span class=\"number\">{{ safetyManagement.reportOnTimeRate }}</span>\r\n                  <span class=\"unit\">%</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 饼状图区域 -->\r\n          <div class=\"safety-chart-area\">\r\n            <pieChart\r\n              :data=\"safetyManagement.hazardTypeChart2\"\r\n              height=\"180px\"\r\n              :showCenterText=\"true\"\r\n              :centerText=\"{\r\n                value: safetyManagement.hazardCount.toString(),\r\n                unit: '安全隐患总数',\r\n                label: ''\r\n              }\"\r\n            />\r\n          </div>\r\n\r\n          <!-- 底部统计数据 -->\r\n          <div class=\"safety-bottom-stats\">\r\n            <div class=\"stats-row\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #2656F5;\"></div>\r\n                <span class=\"stat-label\">待整改</span>\r\n                <span class=\"stat-value\">4</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #FF920D;\"></div>\r\n                <span class=\"stat-label\">已整改</span>\r\n                <span class=\"stat-value\">4</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"stats-row\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #54C255;\"></div>\r\n                <span class=\"stat-label\">已合格</span>\r\n                <span class=\"stat-value\">50</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #8EE98F;\"></div>\r\n                <span class=\"stat-label\">整改率</span>\r\n                <span class=\"stat-value\">50</span>\r\n                <span class=\"stat-unit\">%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 底部大型设备图表 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"24\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>大型设备</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div\r\n              class=\"equipment-chart-container\"\r\n              @mousemove=\"handleEquipmentMouseMove\"\r\n              @mouseleave=\"handleEquipmentMouseLeave\"\r\n            >\r\n              <barChart\r\n                height=\"300px\"\r\n                :data=\"largeEquipmentChart\"\r\n                :show-tooltip=\"false\"\r\n              />\r\n\r\n              <!-- 大型设备悬浮框 -->\r\n              <div\r\n                v-show=\"equipmentTooltip.visible\"\r\n                class=\"project-tooltip\"\r\n                :style=\"{\r\n                  left: equipmentTooltip.x + 'px',\r\n                  top: equipmentTooltip.y + 'px',\r\n                }\"\r\n              >\r\n                <div class=\"tooltip-header\">{{ equipmentTooltip.title }}</div>\r\n                <div v-if=\"equipmentTooltip.loading\" class=\"tooltip-loading\">\r\n                  加载中...\r\n                </div>\r\n                <div v-else-if=\"equipmentTooltip.error\" class=\"tooltip-error\">\r\n                  {{ equipmentTooltip.error }}\r\n                </div>\r\n                <div\r\n                  v-else-if=\"equipmentTooltip.data.length > 0\"\r\n                  class=\"tooltip-content\"\r\n                >\r\n                  <div\r\n                    v-for=\"item in equipmentTooltip.data\"\r\n                    :key=\"item.name\"\r\n                    class=\"tooltip-item\"\r\n                  >\r\n                    <span class=\"tooltip-name\">{{ item.name }}</span>\r\n                    <span class=\"tooltip-value\">{{ item.value }}</span>\r\n                  </div>\r\n                </div>\r\n                <div v-else class=\"tooltip-empty\">暂无详细数据</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport barChart from \"./components/barChart.vue\";\r\nimport pieChart from \"./components/pieChart.vue\";\r\nimport {\r\n  getManagementOverview,\r\n  getQualityStatistics,\r\n  getSafetyStatistics,\r\n  getDangerTypeStatistics,\r\n  getSafetyProductionStatistics,\r\n  getDangerousProStatistics,\r\n  getLargeEquipmentStatistics,\r\n  getLargeEquipmentByNameStatistics,\r\n} from \"@/api/statistics\";\r\nexport default {\r\n  name: \"Index\",\r\n  components: {\r\n    barChart,\r\n    pieChart,\r\n  },\r\n  data() {\r\n    return {\r\n      // 时间类型选择 (1-月, 2-年)\r\n      qualityTimeType: 2, // 默认为年\r\n      safetyTimeType: 2, // 默认为年\r\n      // 顶部统计数据\r\n      statsData: {\r\n        // 原有数据\r\n        domesticProjects: 1126,\r\n        internationalProjects: 1126,\r\n        safetyInvestment: 1500,\r\n        largeEquipment: 1126,\r\n        dangerousProjects: 1126,\r\n        // 新增数据\r\n        superDangerousProjects: 1126,\r\n        domesticSafetyInvestment: 1126,\r\n        internationalSafetyInvestment: 1126,\r\n        companyBranches: 1126,\r\n        casualties: 0,\r\n        majorHazards: 1126,\r\n      },\r\n      // 质量管理数据\r\n      qualityManagement: {\r\n        // 检查下发\r\n        inspectionPlans: 122,\r\n        dailyInspections: 89,\r\n        specialInspections: 33,\r\n        foundProblems: 31,\r\n        fixedProblems: 29,\r\n        pendingProblems: 2,\r\n        onTimeRate: 96,\r\n        onTimeFixed: 28,\r\n        overdueFixed: 1,\r\n        // 自主上报\r\n        selfReportProblems: 103,\r\n        selfReportFixed: 100,\r\n        selfReportPending: 3,\r\n        selfReportRate: 86.45,\r\n        selfReportOnTime: 31,\r\n        selfReportOverdue: 7,\r\n      },\r\n      // 安全管理数据\r\n      safetyManagement: {\r\n        // 检查下发\r\n        inspectionCount: 58,\r\n        hazardFound: 20,\r\n        onTimeRate: 89,\r\n        // 自主上报\r\n        hazardCount: 103,\r\n        reportOnTimeRate: 86.45,\r\n        // 隐患类别统计图表\r\n        hazardTypeChart1: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#E54545\",\r\n            \"#FECF77\",\r\n            \"#FF7730\",\r\n            \"#B38DFF\",\r\n            \"#A1FFEB\",\r\n          ],\r\n          data: [\r\n            { value: 35, name: \"基础设施\" },\r\n            { value: 28, name: \"设备维护\" },\r\n            { value: 25, name: \"消防安全\" },\r\n            { value: 22, name: \"电气安全\" },\r\n            { value: 18, name: \"高空作业\" },\r\n            { value: 15, name: \"机械操作\" },\r\n            { value: 12, name: \"化学品管理\" },\r\n            { value: 10, name: \"个人防护\" },\r\n            { value: 8, name: \"环境卫生\" },\r\n            { value: 5, name: \"其他\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n        hazardTypeChart2: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n          ],\r\n          data: [\r\n            { value: 15, name: \"基础设施\" },\r\n            { value: 12, name: \"设备维护\" },\r\n            { value: 11, name: \"消防安全\" },\r\n            { value: 10, name: \"电气安全\" },\r\n            { value: 10, name: \"高空作业\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n      },\r\n      // 自主上报数据\r\n      selfReport: {\r\n        reportCount: 103,\r\n        completed: 100,\r\n        pending: 3,\r\n        onTimeRate: 86.45,\r\n        onTimeCompleted: 31,\r\n        overdueCompleted: 7,\r\n      },\r\n      // 统计分析数据\r\n      statisticsAnalysis: {\r\n        overallChart: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#A1FFEB\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#FF920D\",\r\n          ],\r\n          data: [\r\n            { value: 25, name: \"安全基础管理\" },\r\n            { value: 20, name: \"消防安全\" },\r\n            { value: 18, name: \"电气安全\" },\r\n            { value: 15, name: \"特种设备\" },\r\n            { value: 12, name: \"危化品\" },\r\n            { value: 10, name: \"其他\" },\r\n          ],\r\n        },\r\n      },\r\n      // 危大工程项目列表数据\r\n      dangerousProjectsList: [\r\n        { name: \"苏电产业科创园(NO.2010G32)07-13地块项目\", progress: 100 },\r\n        { name: \"未来出行产业园项目（直流分公司）\", progress: 80 },\r\n        { name: \"华为网络石代表处项目\", progress: 70 },\r\n        { name: \"年产3001万件汽车底盘等部件生产线项目\", progress: 30 },\r\n        { name: \"泪源城土壤生产及新客体验中心二期建设项目\", progress: 30 },\r\n      ],\r\n      // 危大工程图表配置\r\n      dangerousProjectChart: {\r\n        colorList: [\"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\"],\r\n        grid: {\r\n          top: 30,\r\n          left: \"35%\",\r\n          right: \"10%\",\r\n          bottom: \"5%\",\r\n        },\r\n        xAxis: {\r\n          type: \"value\",\r\n          max: 10,\r\n          axisLabel: {\r\n            show: false,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"苏电产业科创园(NO.2010G32) 07-13地块项目\",\r\n            \"未来出行产业园项目（直流分公司）\",\r\n            \"华为网络石代表处项目\",\r\n            \"年产3001万件汽车底盘等部件生产线项目\",\r\n            \"泪源城土壤生产及新客体验中心二期建设项目\",\r\n          ],\r\n          axisLabel: {\r\n            fontSize: 12,\r\n            color: \"#333\",\r\n            interval: 0,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"危大工程数量\",\r\n            type: \"bar\",\r\n            data: [10, 8, 7, 3, 3],\r\n            itemStyle: {\r\n              color: \"#5990FD\",\r\n              borderRadius: [0, 4, 4, 0],\r\n            },\r\n            barWidth: \"60%\",\r\n            label: {\r\n              show: true,\r\n              position: \"right\",\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n              formatter: \"{c}\",\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      largeEquipmentChart: {\r\n        colorList: [\r\n          \"#FF920D\",\r\n          \"#FECF77\",\r\n          \"#FF7730\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#2C2C2C\",\r\n        ],\r\n        grid: {\r\n          top: 30,\r\n          left: \"8%\",\r\n          right: \"8%\",\r\n          bottom: \"25%\",\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"设备分类1\",\r\n            \"设备分类2\",\r\n            \"设备分类3\",\r\n            \"设备分类4\",\r\n            \"设备分类5\",\r\n            \"设备分类6\",\r\n          ],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0,\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"value\",\r\n          max: 210,\r\n          axisLabel: {\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"未安装\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF920D\" },\r\n            data: [35, 0, 20, 0, 0, 35],\r\n          },\r\n          {\r\n            name: \"安装中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FECF77\" },\r\n            data: [0, 0, 0, 0, 0, 5],\r\n          },\r\n          {\r\n            name: \"验收中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF7730\" },\r\n            data: [0, 0, 10, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"运行中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#54C255\" },\r\n            data: [175, 120, 150, 30, 180, 150],\r\n          },\r\n          {\r\n            name: \"维修中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2656F5\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"已报废\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2C2C2C\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n        ],\r\n      },\r\n      // 安全生产投入环形图数据\r\n      safetyInvestmentPieChart: {\r\n        colorList: [\r\n          \"#2656F5\",\r\n          \"#FF920D\", \r\n          \"#54C255\",\r\n          \"#E54545\",\r\n          \"#8EE98F\",\r\n          \"#A1CDFF\"\r\n        ],\r\n        data: [\r\n          { value: 2000, name: \"海外工程一公司\" },\r\n          { value: 2000, name: \"海外工程二公司\" },\r\n          { value: 2000, name: \"海外工程三公司\" },\r\n          { value: 2000, name: \"中江国际集团\" },\r\n          { value: 2000, name: \"第五建设分公司\" }\r\n        ],\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"50%\"],\r\n              radius: [\"55%\", \"75%\"],\r\n              label: {\r\n                show: false\r\n              },\r\n              labelLine: {\r\n                show: false\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      // 安全生产投入总金额\r\n      safetyInvestmentTotal: {\r\n        value: \"6000\",\r\n        unit: \"总投入(万元)\",\r\n        label: \"安全生产投入\"\r\n      },\r\n      // 安全生产投入项目列表\r\n      safetyInvestmentProjects: [\r\n        { name: \"海外工程一公司\", value: \"2000\", color: \"#2656F5\" },\r\n        { name: \"海外工程二公司\", value: \"2000\", color: \"#FF920D\" },\r\n        { name: \"海外工程三公司\", value: \"2000\", color: \"#54C255\" },\r\n        { name: \"中江国际集团\", value: \"2000\", color: \"#E54545\" },\r\n        { name: \"第五建设分公司\", value: \"2000\", color: \"#8EE98F\" }\r\n      ],\r\n      // 保留这些旧数据结构以防止报错，后续可以逐步清理\r\n      lineData: {\r\n        grid: {\r\n          top: 10,\r\n          left: \"6%\",\r\n          right: \"6%\",\r\n          bottom: \"12%\",\r\n        },\r\n        xAxisData: [],\r\n        seriesData: [],\r\n      },\r\n      mainData: {},\r\n      yearCount: {},\r\n      dangerList: [],\r\n      echartData: { colorList: [], data: [] },\r\n      cateBarData: { colorList: [], series: [] },\r\n      yearBarData: { series: [] },\r\n      chart2Lengend: [],\r\n      echartType1: 1,\r\n      echartType2: 1,\r\n      echartTypeList1: [],\r\n      echartTypeList2: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 悬浮框相关数据\r\n      tooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      // 危大工程图表实例\r\n      dangerousProjectChartInstance: null,\r\n      // 设备详细信息缓存\r\n      equipmentDetailsCache: {},\r\n      // 设备悬浮框相关数据\r\n      equipmentTooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      requestQueue: new Set(), // 正在请求的项目名称队列\r\n    };\r\n  },\r\n  created() {\r\n    this.loadManagementOverview();\r\n    this.loadQualityStatistics();\r\n    this.loadSafetyStatistics();\r\n    this.loadDangerTypeStatistics();\r\n    this.loadSafetyProductionStatistics();\r\n    this.loadDangerousProStatistics();\r\n    this.loadLargeEquipmentStatistics();\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁图表实例\r\n    if (this.dangerousProjectChartInstance) {\r\n      this.dangerousProjectChartInstance.dispose();\r\n      this.dangerousProjectChartInstance = null;\r\n    }\r\n\r\n    // 清理请求队列\r\n    this.requestQueue.clear();\r\n  },\r\n  methods: {\r\n    // 切换质量管理时间类型\r\n    changeQualityTimeType(timeType) {\r\n      if (this.qualityTimeType !== timeType) {\r\n        this.qualityTimeType = timeType;\r\n        this.loadQualityStatistics();\r\n      }\r\n    },\r\n\r\n    // 切换安全管理时间类型\r\n    changeSafetyTimeType(timeType) {\r\n      if (this.safetyTimeType !== timeType) {\r\n        this.safetyTimeType = timeType;\r\n        this.loadSafetyStatistics();\r\n        this.loadDangerTypeStatistics();\r\n      }\r\n    },\r\n\r\n    // 获取安全管理总览数据\r\n    async loadManagementOverview() {\r\n      try {\r\n        console.log(\"开始加载安全管理总览数据...\");\r\n        const response = await getManagementOverview();\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的数据结构\r\n          this.statsData.dangerousProjects = data.wdgcs || 0; // 危大工程数\r\n          this.statsData.safetyInvestment = data.aqtzzje || 0; // 安全投资总金额\r\n          this.statsData.domesticProjects = data.gnzjxms || 0; // 国内在建项目\r\n          this.statsData.internationalProjects = data.gjzjxms || 0; // 国际在建项目\r\n          this.statsData.largeEquipment = data.dxsbs || 0; // 大型设备数\r\n\r\n          console.log(\"安全管理总览数据加载成功:\", this.statsData);\r\n        } else {\r\n          console.warn(\"接口返回数据格式异常:\", response);\r\n          this.handleDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理总览数据失败:\", error);\r\n        this.handleDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理数据加载错误\r\n    handleDataLoadError() {\r\n      console.log(\"使用默认数据\");\r\n      // 保持原有的默认数据，确保页面正常显示\r\n      this.$modal.msgWarning(\"数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取质量管理统计数据\r\n    async loadQualityStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载质量管理统计数据...\",\r\n          \"timeType:\",\r\n          this.qualityTimeType\r\n        );\r\n        const response = await getQualityStatistics({\r\n          timeType: this.qualityTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的质量管理数据结构（自主上报部分）\r\n          this.qualityManagement.selfReportProblems = data.jczs || 0; // 自主上报问题数\r\n          this.qualityManagement.selfReportFixed = data.ywcsl || 0; // 已整改数量\r\n          this.qualityManagement.selfReportPending = data.kwcsl || 0; // 待整改数量\r\n          this.qualityManagement.selfReportRate = data.aszgl\r\n            ? data.aszgl * 100\r\n            : 0; // 按时整改率（转换为百分比）\r\n          this.qualityManagement.selfReportOnTime = data.aszg || 0; // 按时整改\r\n          this.qualityManagement.selfReportOverdue = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"质量管理统计数据加载成功:\", this.qualityManagement);\r\n        } else {\r\n          console.warn(\"质量管理统计接口返回数据格式异常:\", response);\r\n          this.handleQualityDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取质量管理统计数据失败:\", error);\r\n        this.handleQualityDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理质量管理数据加载错误\r\n    handleQualityDataLoadError() {\r\n      console.log(\"使用质量管理默认数据\");\r\n      this.$modal.msgWarning(\"质量管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全管理统计数据\r\n    async loadSafetyStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载安全管理统计数据...\",\r\n          \"timeType:\",\r\n          this.safetyTimeType\r\n        );\r\n        const response = await getSafetyStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的安全管理数据结构（自主上报部分）\r\n          this.safetyManagement.hazardCount = data.jczs || 0; // 隐患数（问题数）\r\n          this.safetyManagement.reportOnTimeRate = data.aszgl\r\n            ? (data.aszgl * 100).toFixed(1)\r\n            : 0; // 按时整改率（转换为百分比）\r\n          // 为了保持数据一致性，也可以存储更多详细数据备用\r\n          this.safetyManagement.safetyFixedProblems = data.ywcsl || 0; // 已整改数量\r\n          this.safetyManagement.safetyPendingProblems = data.kwcsl || 0; // 待整改数量\r\n          this.safetyManagement.safetyOnTimeFixed = data.aszg || 0; // 按时整改\r\n          this.safetyManagement.safetyOverdueFixed = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"安全管理统计数据加载成功:\", this.safetyManagement);\r\n        } else {\r\n          console.warn(\"安全管理统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理统计数据失败:\", error);\r\n        this.handleSafetyDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全管理数据加载错误\r\n    handleSafetyDataLoadError() {\r\n      console.log(\"使用安全管理默认数据\");\r\n      this.$modal.msgWarning(\"安全管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取隐患类别统计数据\r\n    async loadDangerTypeStatistics() {\r\n      try {\r\n        console.log(\"开始加载隐患类别统计数据...\");\r\n        const response = await getDangerTypeStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        // 检查下发的图表使用默认数据，总和等于检查次数58\r\n        const defaultDataForInspection = [\r\n          { value: 15, name: \"基础设施\" },\r\n          { value: 12, name: \"设备维护\" },\r\n          { value: 11, name: \"消防安全\" },\r\n          { value: 10, name: \"电气安全\" },\r\n          { value: 10, name: \"高空作业\" },\r\n        ];\r\n\r\n        // 定义颜色数组\r\n        const colorList1 = [\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n          \"#FF920D\",\r\n          \"#54C255\",\r\n          \"#A1CDFF\",\r\n        ];\r\n        const colorList2 = [\r\n          \"#FF920D\",\r\n          \"#E54545\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n        ];\r\n\r\n        // 更新检查下发的隐患类别统计图表（使用默认数据）\r\n        this.safetyManagement.hazardTypeChart1 = {\r\n          colorList: colorList1,\r\n          data: defaultDataForInspection,\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        };\r\n\r\n        // 自主上报的图表使用接口数据\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n          // 对数据按value值降序排序，只取前5个\r\n          const sortedData = data\r\n            .sort((a, b) => (b.value || 0) - (a.value || 0))\r\n            .slice(0, 5);\r\n\r\n          // 更新自主上报的隐患类别统计图表（使用接口数据）\r\n          this.safetyManagement.hazardTypeChart2 = {\r\n            colorList: colorList2,\r\n            data: sortedData,\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"52%\"],\r\n                  radius: [\"45%\", \"75%\"],\r\n                  label: {\r\n                    show: true,\r\n                    position: \"outside\",\r\n                    formatter: \"{b}\\n{c}\",\r\n                    fontSize: 10,\r\n                    color: \"#666\",\r\n                    lineHeight: 14,\r\n                  },\r\n                  labelLine: {\r\n                    show: true,\r\n                    length: 8,\r\n                    length2: 15,\r\n                    lineStyle: {\r\n                      color: \"#666\",\r\n                      width: 1,\r\n                    },\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n          };\r\n\r\n          console.log(\"隐患类别统计数据加载成功:\", {\r\n            chart1: \"使用默认数据\",\r\n            chart2: this.safetyManagement.hazardTypeChart2,\r\n          });\r\n        } else {\r\n          console.warn(\"隐患类别统计接口返回数据格式异常:\", response);\r\n          this.handleDangerTypeDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取隐患类别统计数据失败:\", error);\r\n        this.handleDangerTypeDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理隐患类别统计数据加载错误\r\n    handleDangerTypeDataLoadError() {\r\n      console.log(\"自主上报图表使用默认数据\");\r\n      // 如果接口失败，自主上报图表也使用默认数据\r\n      const defaultDataForReport = [\r\n        { value: 8, name: \"基础设施\" },\r\n        { value: 7, name: \"设备维护\" },\r\n        { value: 6, name: \"消防安全\" },\r\n        { value: 5, name: \"电气安全\" },\r\n        { value: 4, name: \"高空作业\" },\r\n      ];\r\n\r\n      const colorList2 = [\r\n        \"#FF920D\",\r\n        \"#E54545\",\r\n        \"#54C255\",\r\n        \"#2656F5\",\r\n        \"#8EE98F\",\r\n      ];\r\n\r\n      this.safetyManagement.hazardTypeChart2 = {\r\n        colorList: colorList2,\r\n        data: defaultDataForReport,\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"52%\"],\r\n              radius: [\"45%\", \"75%\"],\r\n              label: {\r\n                show: true,\r\n                position: \"outside\",\r\n                formatter: \"{b}\\n{c}\",\r\n                fontSize: 10,\r\n                color: \"#666\",\r\n                lineHeight: 14,\r\n              },\r\n              labelLine: {\r\n                show: true,\r\n                length: 8,\r\n                length2: 15,\r\n                lineStyle: {\r\n                  color: \"#666\",\r\n                  width: 1,\r\n                },\r\n              },\r\n            },\r\n          ],\r\n        },\r\n      };\r\n      this.$modal.msgWarning(\"隐患类别统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全生产投入统计数据\r\n    async loadSafetyProductionStatistics() {\r\n      try {\r\n        console.log(\"开始加载安全生产投入统计数据...\");\r\n        const response = await getSafetyProductionStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取公司名称作为X轴标签（截取显示）\r\n          const xAxisData = data.map((item) => {\r\n            // 截取公司名称用于X轴显示，保持图表美观\r\n            const companyName = item.company || \"未知公司\";\r\n            return companyName.length > 10\r\n              ? companyName.substring(0, 10) + \"...\"\r\n              : companyName;\r\n          });\r\n\r\n          // 保存完整的公司名称用于tooltip显示\r\n          const fullCompanyNames = data.map(\r\n            (item) => item.company || \"未知公司\"\r\n          );\r\n\r\n          // 提取年度预算金额数据，并保存完整公司名称\r\n          const budgetData = data.map((item, index) => ({\r\n            value: parseFloat(item.annualBudgetAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n          // 提取实际投入金额数据，并保存完整公司名称\r\n          const actualData = data.map((item, index) => ({\r\n            value: parseFloat(item.actualInputAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n\r\n          // 动态计算Y轴最大值\r\n          const maxValue = Math.max(\r\n            ...budgetData.map((item) => item.value),\r\n            ...actualData.map((item) => item.value)\r\n          );\r\n          const yAxisMax = Math.ceil((maxValue * 1.2) / 1000) * 1000; // 向上取整到千位\r\n\r\n          // 计算总投入\r\n          const totalInvestment = data.reduce((sum, item) => sum + parseFloat(item.actualInputAmount || 0), 0);\r\n\r\n          // 更新环形图数据\r\n          const colorList = [\"#2656F5\", \"#FF920D\", \"#54C255\", \"#E54545\", \"#8EE98F\", \"#A1CDFF\"];\r\n          \r\n          this.safetyInvestmentPieChart = {\r\n            colorList: colorList,\r\n            data: data.map((item, index) => ({\r\n              value: parseFloat(item.actualInputAmount || 0),\r\n              name: item.company || \"未知公司\"\r\n            })),\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"50%\"],\r\n                  radius: [\"55%\", \"75%\"],\r\n                  label: {\r\n                    show: false\r\n                  },\r\n                  labelLine: {\r\n                    show: false\r\n                  }\r\n                }\r\n              ]\r\n            }\r\n          };\r\n\r\n          // 更新中心文字\r\n          this.safetyInvestmentTotal = {\r\n            value: Math.round(totalInvestment).toString(),\r\n            unit: \"总投入(万元)\",\r\n            label: \"安全生产投入\"\r\n          };\r\n\r\n          // 更新项目列表\r\n          this.safetyInvestmentProjects = data.map((item, index) => ({\r\n            name: item.company || \"未知公司\",\r\n            value: Math.round(parseFloat(item.actualInputAmount || 0)).toString(),\r\n            color: colorList[index % colorList.length]\r\n          }));\r\n\r\n          console.log(\r\n            \"安全生产投入统计数据加载成功:\",\r\n            this.safetyInvestmentChart\r\n          );\r\n        } else {\r\n          console.warn(\"安全生产投入统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyProductionDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全生产投入统计数据失败:\", error);\r\n        this.handleSafetyProductionDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全生产投入统计数据加载错误\r\n    handleSafetyProductionDataLoadError() {\r\n      console.log(\"使用安全生产投入统计默认数据\");\r\n      this.$modal.msgWarning(\"安全生产投入统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取危大工程统计数据\r\n    async loadDangerousProStatistics() {\r\n      try {\r\n        console.log(\"开始加载危大工程统计数据...\");\r\n        const response = await getDangerousProStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 获取前5个项目数据\r\n          const topProjects = data.slice(0, 5);\r\n\r\n          // 找出最大的value值，用于计算X轴最大值\r\n          const maxValue = Math.max(...data.map((item) => item.value));\r\n\r\n          // 提取项目名称和数值，保留完整数据用于tooltip\r\n          const projectNames = topProjects.map((item) => {\r\n            // 截取项目名称，避免过长\r\n            return item.name && item.name.length > 10\r\n              ? item.name.substring(0, 10) + \"...\"\r\n              : item.name || \"未知项目\";\r\n          });\r\n\r\n          // 构建包含详细信息的数据\r\n          const projectData = topProjects.map((item, index) => ({\r\n            name: projectNames[index],\r\n            value: item.value || 0,\r\n            fullName: item.name || \"未知项目\",\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 更新图表配置\r\n          this.dangerousProjectChart.yAxis.data = projectNames.reverse();\r\n          this.dangerousProjectChart.series[0].data = projectData.reverse();\r\n          this.dangerousProjectChart.xAxis.max = maxValue;\r\n\r\n          // 保存原始数据供其他用途使用\r\n          this.dangerousProjectsList = topProjects.map((item) => ({\r\n            name: item.name || \"未知项目\",\r\n            value: item.value || 0,\r\n            originalName: item.name,\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 初始化图表\r\n          this.$nextTick(() => {\r\n            this.initDangerousProjectChart();\r\n          });\r\n\r\n          console.log(\"危大工程统计数据加载成功:\", this.dangerousProjectChart);\r\n        } else {\r\n          console.warn(\"危大工程统计接口返回数据格式异常:\", response);\r\n          this.handleDangerousProDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取危大工程统计数据失败:\", error);\r\n        this.handleDangerousProDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理危大工程统计数据加载错误\r\n    handleDangerousProDataLoadError() {\r\n      console.log(\"使用危大工程统计默认数据\");\r\n      this.$modal.msgWarning(\"危大工程统计数据加载失败，显示默认数据\");\r\n      // 使用默认数据初始化图表\r\n      this.$nextTick(() => {\r\n        this.initDangerousProjectChart();\r\n      });\r\n    },\r\n\r\n    // 初始化危大工程图表\r\n    initDangerousProjectChart() {\r\n      if (this.$refs.dangerousProjectChart) {\r\n        // 销毁现有实例\r\n        if (this.dangerousProjectChartInstance) {\r\n          this.dangerousProjectChartInstance.dispose();\r\n        }\r\n\r\n        // 创建新的图表实例\r\n        this.dangerousProjectChartInstance = echarts.init(\r\n          this.$refs.dangerousProjectChart\r\n        );\r\n\r\n        // 设置图表选项\r\n        const option = {\r\n          grid: this.dangerousProjectChart.grid,\r\n          xAxis: this.dangerousProjectChart.xAxis,\r\n          yAxis: this.dangerousProjectChart.yAxis,\r\n          series: this.dangerousProjectChart.series,\r\n          tooltip: {\r\n            show: true,\r\n            trigger: \"item\",\r\n            backgroundColor: \"#fff\",\r\n            borderColor: \"#ccc\",\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n            },\r\n            extraCssText:\r\n              \"box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 400px; white-space: normal;\",\r\n            position: function (point, params, dom, rect, size) {\r\n              // 获取 tooltip 的实际尺寸\r\n              const tooltipWidth = size.contentSize[0];\r\n              const tooltipHeight = size.contentSize[1];\r\n\r\n              // 图表容器尺寸\r\n              const chartWidth = rect.width;\r\n              const chartHeight = rect.height;\r\n\r\n              // 计算右边和左边的可用空间\r\n              const rightSpace = chartWidth - point[0] - 10;\r\n              const leftSpace = point[0] - 10;\r\n\r\n              let x, y;\r\n\r\n              // 优先选择空间更大的一边，但确保不会被遮挡\r\n              if (rightSpace >= tooltipWidth || rightSpace > leftSpace) {\r\n                // 显示在右边\r\n                x = point[0] + 10;\r\n                // 如果右边真的放不下，尝试调整到图表右边界内\r\n                if (x + tooltipWidth > chartWidth) {\r\n                  x = chartWidth - tooltipWidth - 5;\r\n                }\r\n              } else if (leftSpace >= tooltipWidth) {\r\n                // 显示在左边，但确保有足够空间\r\n                x = point[0] - tooltipWidth - 10;\r\n                // 确保不会超出左边界\r\n                if (x < 0) {\r\n                  x = 5;\r\n                }\r\n              } else {\r\n                // 如果两边都放不下，选择右边并强制显示在图表内\r\n                x = Math.max(\r\n                  5,\r\n                  Math.min(point[0] + 10, chartWidth - tooltipWidth - 5)\r\n                );\r\n              }\r\n\r\n              // 垂直居中，但确保不超出边界\r\n              y = point[1] - tooltipHeight / 2;\r\n              if (y < 10) {\r\n                y = 10;\r\n              } else if (y + tooltipHeight > chartHeight - 10) {\r\n                y = chartHeight - tooltipHeight - 10;\r\n              }\r\n\r\n              return [x, y];\r\n            },\r\n            formatter: (params) => {\r\n              if (params.data && typeof params.data === \"object\") {\r\n                const { fullName, value, detalList } = params.data;\r\n\r\n                let html = `<div style=\"font-weight: bold; margin-bottom: 8px; color: #333;\">${fullName}</div>`;\r\n                html += `<div style=\"margin-bottom: 8px;\">总数量: <span style=\"color: #1890ff; font-weight: bold;\">${value}</span></div>`;\r\n\r\n                if (detalList && detalList.length > 0) {\r\n                  html +=\r\n                    '<div style=\"border-top: 1px solid #eee; padding-top: 8px;\">';\r\n                  html +=\r\n                    '<div style=\"font-weight: bold; margin-bottom: 6px; color: #666;\">详细信息:</div>';\r\n                  detalList.forEach((item) => {\r\n                    html += `<div style=\"margin-bottom: 4px; padding-left: 8px; border-left: 2px solid #1890ff;\">\r\n                      <div style=\"font-weight: 500;\">${\r\n                        item.name || \"未知类型\"\r\n                      }</div>\r\n                      <div style=\"color: #666; font-size: 11px;\">数量: ${\r\n                        item.value || 0\r\n                      }</div>\r\n                    </div>`;\r\n                  });\r\n                  html += \"</div>\";\r\n                } else {\r\n                  html +=\r\n                    '<div style=\"color: #999; font-style: italic;\">暂无详细信息</div>';\r\n                }\r\n\r\n                return html;\r\n              }\r\n\r\n              // 兜底显示\r\n              return `项目: ${params.name}<br/>数量: ${params.value}`;\r\n            },\r\n          },\r\n        };\r\n\r\n        this.dangerousProjectChartInstance.setOption(option);\r\n\r\n        // 自适应大小\r\n        window.addEventListener(\"resize\", () => {\r\n          if (this.dangerousProjectChartInstance) {\r\n            this.dangerousProjectChartInstance.resize();\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 获取大型设备统计数据\r\n    async loadLargeEquipmentStatistics() {\r\n      try {\r\n        console.log(\"开始加载大型设备统计数据...\");\r\n        const response = await getLargeEquipmentStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取设备名称作为X轴类别\r\n          const equipmentNames = data.map((item) => item.name || \"未知设备\");\r\n\r\n          // 提取设备数量，全部设为\"运行中\"状态\r\n          const runningData = data.map((item) => item.value || 0);\r\n          // 动态计算Y轴最大值，直接使用接口数据的最大值\r\n          const maxValue = Math.max(...runningData);\r\n          const yAxisMax = maxValue > 0 ? maxValue : 10; // Y轴上限就是接口里最大的数据\r\n\r\n          // 更新大型设备图表配置\r\n          this.largeEquipmentChart = {\r\n            colorList: [\r\n              \"#FF920D\",\r\n              \"#FECF77\",\r\n              \"#FF7730\",\r\n              \"#54C255\",\r\n              \"#2656F5\",\r\n              \"#2C2C2C\",\r\n            ],\r\n            grid: {\r\n              top: 30,\r\n              left: \"8%\",\r\n              right: \"8%\",\r\n              bottom: \"25%\",\r\n            },\r\n            legend: undefined, // 显式移除图例\r\n            xAxis: {\r\n              type: \"category\",\r\n              data: equipmentNames,\r\n              axisLabel: {\r\n                interval: 0,\r\n                rotate: 0,\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            yAxis: {\r\n              type: \"value\",\r\n              max: yAxisMax > 0 ? yAxisMax : 10,\r\n              min: 0,\r\n              interval: yAxisMax > 0 ? Math.ceil(yAxisMax / 5) : 2, // 将Y轴等分为5个区间，确保间隔为整数\r\n              axisLabel: {\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            series: [\r\n              {\r\n                name: \"未安装\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF920D\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"安装中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FECF77\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"验收中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF7730\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"运行中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#54C255\" },\r\n                data: runningData, // 使用真实数据\r\n              },\r\n              {\r\n                name: \"维修中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2656F5\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"已报废\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2C2C2C\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n            ],\r\n          };\r\n\r\n          console.log(\"大型设备统计数据加载成功:\", this.largeEquipmentChart);\r\n        } else {\r\n          console.warn(\"大型设备统计接口返回数据格式异常:\", response);\r\n          this.handleLargeEquipmentDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取大型设备统计数据失败:\", error);\r\n        this.handleLargeEquipmentDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理大型设备统计数据加载错误\r\n    handleLargeEquipmentDataLoadError() {\r\n      console.log(\"使用大型设备统计默认数据\");\r\n      this.$modal.msgWarning(\"大型设备统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 异步加载设备详细信息\r\n    async loadEquipmentDetails(equipmentName) {\r\n      // 如果已经有缓存，直接返回\r\n      if (this.equipmentDetailsCache[equipmentName]) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(\"开始加载设备详细信息:\", equipmentName);\r\n        const response = await getLargeEquipmentByNameStatistics(equipmentName);\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          // 缓存详细数据\r\n          this.equipmentDetailsCache[equipmentName] = response.data.map(\r\n            (item) => ({\r\n              name: item.name,\r\n              value: item.value,\r\n              equipment_name: item.equipment_name,\r\n            })\r\n          );\r\n\r\n          console.log(\r\n            \"设备详细信息加载成功:\",\r\n            equipmentName,\r\n            this.equipmentDetailsCache[equipmentName]\r\n          );\r\n\r\n          // 更新当前悬浮框的数据\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.data =\r\n              this.equipmentDetailsCache[equipmentName];\r\n          }\r\n        } else {\r\n          // 设置空数据避免重复请求\r\n          this.equipmentDetailsCache[equipmentName] = [];\r\n          console.warn(\"设备详细信息接口返回数据格式异常:\", response);\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.error = \"数据格式异常\";\r\n          }\r\n        }\r\n      } catch (error) {\r\n        // 设置空数据避免重复请求\r\n        this.equipmentDetailsCache[equipmentName] = [];\r\n        console.error(\"获取设备详细信息失败:\", error);\r\n        if (\r\n          this.equipmentTooltip.visible &&\r\n          this.equipmentTooltip.title === equipmentName\r\n        ) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"加载失败\";\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标移动\r\n    handleEquipmentMouseMove(event) {\r\n      // 获取鼠标位置\r\n      const rect = event.currentTarget.getBoundingClientRect();\r\n      const x = event.clientX - rect.left;\r\n      const chartWidth = rect.width;\r\n\r\n      // 根据鼠标位置推断设备（简化实现）\r\n      const equipmentNames = this.largeEquipmentChart.xAxis?.data || [];\r\n      if (equipmentNames.length === 0) return;\r\n\r\n      const equipmentIndex = Math.floor(\r\n        (x / chartWidth) * equipmentNames.length\r\n      );\r\n      if (equipmentIndex >= 0 && equipmentIndex < equipmentNames.length) {\r\n        const equipmentName = equipmentNames[equipmentIndex];\r\n\r\n        // 显示悬浮框\r\n        this.equipmentTooltip.visible = true;\r\n        this.equipmentTooltip.x = event.clientX + 10;\r\n        this.equipmentTooltip.y = event.clientY - 10;\r\n        this.equipmentTooltip.title = equipmentName;\r\n\r\n        // 检查缓存数据\r\n        const cachedData = this.equipmentDetailsCache[equipmentName];\r\n        if (cachedData) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = cachedData;\r\n        } else {\r\n          this.equipmentTooltip.loading = true;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = [];\r\n\r\n          // 加载详细数据\r\n          this.loadEquipmentDetails(equipmentName);\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标离开\r\n    handleEquipmentMouseLeave() {\r\n      this.equipmentTooltip.visible = false;\r\n      this.equipmentTooltip.data = [];\r\n      this.equipmentTooltip.error = \"\";\r\n      this.equipmentTooltip.loading = false;\r\n    },\r\n\r\n    // 截取公司名称显示\r\n    truncateCompanyName(companyName, maxLength = 20) {\r\n      if (!companyName) return \"\";\r\n      if (companyName.length <= maxLength) return companyName;\r\n      return companyName.substring(0, maxLength) + \"...\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home {\r\n  padding: 15px; // 默认固定内边距，避免大屏幕下过大\r\n  background: #f5f7fa;\r\n  font-size: 14px; // 默认固定基础字体大小\r\n  /* 响应式基础字体大小 */\r\n\r\n  @media (max-width: 1199px) {\r\n    padding: 1%;\r\n    font-size: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n.el-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 顶部统计卡片样式（第一行 - 3个大卡片）\r\n.top-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card {\r\n    flex: 1;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 20px;\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n    min-height: 120px;\r\n    min-width: 30%;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 3% 2%;\r\n    }\r\n\r\n    .stat-header {\r\n      margin-bottom: 16px;\r\n      \r\n      h3 {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        margin: 0;\r\n        \r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(1rem, 1.8vw, 1.125rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-dual {\r\n      display: flex;\r\n      gap: 20px;\r\n      \r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n      \r\n      .stat-item {\r\n        flex: 1;\r\n        \r\n        .stat-title {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n          line-height: 1.2;\r\n          \r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n\r\n        .stat-number-row {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 4px;\r\n\r\n          .stat-number {\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            color: #1479fc;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(1rem, 2.2vw, 1.75rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 14px;\r\n            color: #666;\r\n            font-weight: normal;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.1vw, 0.875rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-1 {\r\n      background: linear-gradient(120deg, #F74A34 0%, #FF7C5E 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-2 {\r\n      background: linear-gradient(137deg, #1688E6 0%, #46ABFF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-3 {\r\n      background: linear-gradient(137deg, #F5873E 0%, #F5A645 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 第二行统计卡片样式（4个小卡片）\r\n.second-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card-small {\r\n    display: flex;\r\n    align-items: center;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 16px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n    flex: 1;\r\n    min-width: 22%;\r\n    min-height: 110px;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 2% 1.5%;\r\n    }\r\n\r\n    .stat-icon {\r\n      margin-right: 12px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 40px;\r\n      height: 40px;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 8px;\r\n\r\n      @media (max-width: 1199px) {\r\n        width: 35px;\r\n        height: 35px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      i {\r\n        font-size: 20px;\r\n        color: #666;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-small {\r\n      flex: 1;\r\n\r\n      .stat-title-small {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-bottom: 4px;\r\n        line-height: 1.2;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n\r\n      .stat-number-row-small {\r\n        display: flex;\r\n        align-items: baseline;\r\n        gap: 4px;\r\n\r\n        .stat-number-small {\r\n          font-size: 20px;\r\n          font-weight: bold;\r\n          color: #333;\r\n          line-height: 1;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(1rem, 1.6vw, 1.25rem);\r\n          }\r\n        }\r\n\r\n        .stat-unit-small {\r\n          font-size: 12px;\r\n          color: #666;\r\n          font-weight: normal;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-1 {\r\n      background: linear-gradient(137deg, #156BF6 0%, #4681FF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-2 {\r\n      background: linear-gradient(137deg, #FC9920 0%, #F5AC45 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-3 {\r\n      background: linear-gradient(137deg, #9D59FF 0%, #CA79F5 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-4 {\r\n      background: linear-gradient(147deg, #18C68C 0%, #2BD181 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 管理卡片样式\r\n.management-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 15px; // 减少内边距，默认固定内边距\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 370px;\r\n    overflow: hidden; // 防止内容超出\r\n    box-sizing: border-box; // 确保内边距计算在内\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 1%;\r\n    }\r\n\r\n    .management-header {\r\n      margin-bottom: 10px; // 减少间距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1%;\r\n      }\r\n\r\n      .header-content {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n\r\n      h3 {\r\n        margin: 0;\r\n        font-size: 16px; // 默认固定字体大小\r\n        font-weight: 400;\r\n        color: #333;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n        }\r\n      }\r\n\r\n      .time-tabs {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 4px;\r\n\r\n        .tab-item {\r\n          font-size: 12px;\r\n          color: #666;\r\n          cursor: pointer;\r\n          padding: 2px 6px;\r\n          border-radius: 4px;\r\n          transition: all 0.3s ease;\r\n          user-select: none;\r\n\r\n          &:hover {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n          }\r\n\r\n          &.active {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n            font-weight: 500;\r\n          }\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            padding: 1px 4px;\r\n          }\r\n        }\r\n\r\n        .tab-divider {\r\n          font-size: 12px;\r\n          color: #ccc;\r\n          margin: 0 2px;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 质量管理样式\r\n    .section-header {\r\n      margin-bottom: 1%;\r\n\r\n      .section-title {\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: #f5f7fa;\r\n        border-radius: 0.25rem;\r\n      }\r\n    }\r\n\r\n    .quality-stats {\r\n      margin-bottom: 1%;\r\n\r\n      .stat-group {\r\n        display: flex;\r\n        gap: 1.5%;\r\n        align-items: flex-start;\r\n\r\n        .stat-item {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          &.stat-placeholder {\r\n            visibility: hidden;\r\n          }\r\n\r\n          .stat-header {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 0.5rem;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 28px;\r\n\r\n            .stat-icon {\r\n              width: 1.25rem;\r\n              height: 1.25rem;\r\n              border-radius: 0.25rem;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              color: white;\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n              &.blue {\r\n                background: #2656f5;\r\n              }\r\n\r\n              &.orange {\r\n                background: #ff920d;\r\n              }\r\n\r\n              &.green {\r\n                background: #54c255;\r\n              }\r\n            }\r\n\r\n            .stat-label {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n              color: #666;\r\n              font-weight: 500;\r\n            }\r\n          }\r\n\r\n          .stat-number {\r\n            font-size: clamp(0.75rem, 1.3vw, 1.25rem);\r\n            font-weight: bold;\r\n            color: #2656f5;\r\n            line-height: 1;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 24px;\r\n            text-align: left;\r\n          }\r\n\r\n          .stat-detail {\r\n            display: flex;\r\n            gap: 1.5%;\r\n            align-items: flex-start;\r\n\r\n            span {\r\n              font-size: clamp(0.5rem, 0.7vw, 0.625rem);\r\n              color: #999;\r\n              line-height: 1.4;\r\n\r\n              .number {\r\n                color: #333;\r\n                font-weight: 500;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 安全管理专用样式\r\n    // 新的安全管理样式\r\n    .safety-top-stats {\r\n      display: flex;\r\n      gap: 15px;\r\n      margin-bottom: 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n        margin-bottom: 1.5%;\r\n      }\r\n\r\n      .safety-stat-card-new {\r\n        flex: 1;\r\n        background: #f8f9fa;\r\n        border-radius: 8px;\r\n        padding: 15px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12px;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.2%;\r\n          gap: 1%;\r\n        }\r\n\r\n        .safety-stat-icon {\r\n          width: 48px;\r\n          height: 48px;\r\n          border-radius: 50%;\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          flex-shrink: 0;\r\n\r\n          &.rate-icon {\r\n            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n          }\r\n\r\n          @media (max-width: 1199px) {\r\n            width: clamp(36px, 4vw, 48px);\r\n            height: clamp(36px, 4vw, 48px);\r\n          }\r\n\r\n          i {\r\n            font-size: 24px;\r\n            color: white;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(18px, 2vw, 24px);\r\n            }\r\n          }\r\n        }\r\n\r\n        .safety-stat-content-new {\r\n          flex: 1;\r\n\r\n          .safety-stat-label {\r\n            font-size: 14px;\r\n            color: #666;\r\n            margin-bottom: 6px;\r\n            line-height: 1.2;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .safety-stat-number-new {\r\n            display: flex;\r\n            align-items: baseline;\r\n            gap: 4px;\r\n\r\n            .number {\r\n              font-size: 28px;\r\n              font-weight: bold;\r\n              color: #1479fc;\r\n              line-height: 1;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(1.25rem, 2.5vw, 1.75rem);\r\n              }\r\n            }\r\n\r\n            .unit {\r\n              font-size: 16px;\r\n              color: #666;\r\n              font-weight: normal;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(0.875rem, 1.4vw, 1rem);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-chart-area {\r\n      margin-bottom: 20px;\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n    }\r\n\r\n    .safety-bottom-stats {\r\n      .stats-row {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-bottom: 12px;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 8px;\r\n          flex: 1;\r\n          justify-content: center;\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 0.8%;\r\n          }\r\n\r\n          .stat-dot {\r\n            width: 10px;\r\n            height: 10px;\r\n            border-radius: 50%;\r\n            flex-shrink: 0;\r\n\r\n            @media (max-width: 1199px) {\r\n              width: clamp(8px, 1vw, 10px);\r\n              height: clamp(8px, 1vw, 10px);\r\n            }\r\n          }\r\n\r\n          .stat-label {\r\n            font-size: 12px;\r\n            color: #666;\r\n            min-width: 40px;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n              min-width: clamp(30px, 3vw, 40px);\r\n            }\r\n          }\r\n\r\n          .stat-value {\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            color: #333;\r\n            min-width: 20px;\r\n            text-align: right;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n            color: #666;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-sections-container {\r\n      display: flex;\r\n      gap: 15px; // 减少间距，默认固定间距\r\n      width: 100%;\r\n      overflow: hidden; // 防止内容超出\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n    }\r\n\r\n    .safety-section-independent {\r\n      flex: 1;\r\n      background: #f5f7f9;\r\n      padding: 10px 15px 12px 15px; // 减少内边距，默认固定内边距\r\n      border-radius: 0.5rem;\r\n      min-width: 0; // 允许flex项目缩小到内容大小以下\r\n      box-sizing: border-box; // 确保内边距计算在内\r\n\r\n      @media (max-width: 1199px) {\r\n        padding: 0.8% 1.5% 1% 1.5%;\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px; // 默认固定间距\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .section-title {\r\n          font-size: 12px; // 默认固定字体大小\r\n          color: #666;\r\n          padding: 0.25rem 0.5rem;\r\n          background: rgba(245, 247, 250, 0.8);\r\n          border-radius: 0.25rem;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-stats-row {\r\n      display: flex;\r\n      gap: 8px; // 进一步减少间距，默认固定间距\r\n      margin-top: 10px; // 减少上边距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n        margin-top: 1%;\r\n      }\r\n\r\n      .safety-stat-card {\r\n        flex: 1;\r\n        background: white;\r\n        border-radius: 0.5rem;\r\n        padding: 10px 12px; // 大幅减少内边距，默认固定内边距\r\n        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);\r\n        min-width: 0; // 允许flex项目缩小\r\n        box-sizing: border-box; // 确保内边距计算在内\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.2% 1.6%;\r\n        }\r\n\r\n        .safety-stat-title {\r\n          font-size: 10px; // 默认固定字体大小\r\n          color: #666666;\r\n          margin-bottom: 0.5rem;\r\n          line-height: 1.2;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n          }\r\n        }\r\n\r\n        .safety-stat-content-row {\r\n          display: flex;\r\n          align-items: flex-end;\r\n          justify-content: space-between;\r\n          gap: 15px; // 默认固定间距\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 1.2%;\r\n          }\r\n        }\r\n\r\n        .safety-stat-main {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 0.25rem;\r\n          margin-bottom: 0;\r\n\r\n          .safety-stat-number {\r\n            font-size: 22px; // 调整默认固定字体大小，适合1300px+屏幕\r\n            font-weight: bold;\r\n            color: #1479fc;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.5vw, 1.5rem);\r\n              /* 减小响应式字体：12px-24px */\r\n            }\r\n          }\r\n\r\n          .safety-stat-unit {\r\n            font-size: 10px; // 默认固定字体大小\r\n            color: #666666;\r\n            font-weight: normal;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n            }\r\n          }\r\n        }\r\n\r\n        .safety-stat-subtitle {\r\n          font-size: 9px; // 默认固定字体大小\r\n          color: #999999;\r\n          line-height: 1.2;\r\n          white-space: nowrap;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.4375rem, 0.7vw, 0.5625rem);\r\n          }\r\n\r\n          .safety-highlight-number {\r\n            color: #ff920d;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 内嵌图表样式\r\n    .chart-section-in-section {\r\n      .chart-title {\r\n        font-size: 12px; // 默认固定字体大小\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: rgba(245, 247, 250, 0.8);\r\n        border-radius: 0.25rem;\r\n        margin-bottom: 0.5rem;\r\n        text-align: left;\r\n        display: inline-block;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .chart-section {\r\n      .chart-row {\r\n        display: flex;\r\n        gap: 1.5%;\r\n\r\n        .chart-item {\r\n          flex: 1;\r\n\r\n          .chart-title {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            color: #666;\r\n            margin-bottom: 0.5rem;\r\n            text-align: center;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .pie-chart-section {\r\n      .chart-title {\r\n        font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n        color: #666;\r\n        margin-bottom: 1rem;\r\n        text-align: center;\r\n      }\r\n\r\n      &.full-height {\r\n        height: calc(100% - 5rem);\r\n\r\n        .chart-container {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 底部图表卡片样式\r\n.charts-row {\r\n  .chart-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 2.5%;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 340px;\r\n\r\n    .chart-header {\r\n      margin-bottom: 1.5%;\r\n\r\n      h4 {\r\n        margin: 0;\r\n        font-size: clamp(0.875rem, 1.4vw, 1rem);\r\n        font-weight: 600;\r\n        color: #333;\r\n      }\r\n    }\r\n\r\n    .chart-content {\r\n      height: calc(100% - 4rem);\r\n    }\r\n  }\r\n}\r\n\r\n// 图表容器样式\r\n.chart-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n// 设备图表容器样式\r\n.equipment-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n// 危大工程图表容器样式\r\n.dangerous-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n// 安全生产投入容器样式\r\n.safety-investment-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  gap: 24px;\r\n  align-items: center;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 2%;\r\n  }\r\n\r\n  .pie-chart-section {\r\n    width: 280px;\r\n    height: 280px;\r\n    flex-shrink: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    @media (max-width: 1199px) {\r\n      width: 35%;\r\n      height: auto;\r\n      aspect-ratio: 1;\r\n    }\r\n    \r\n    .chart-wrapper {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: relative;\r\n    }\r\n  }\r\n\r\n  .project-list-section {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-left: 24px;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding-left: 2%;\r\n    }\r\n\r\n    .project-list {\r\n      width: 100%;\r\n      max-width: 300px;\r\n      \r\n      .project-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 18px;\r\n        padding: 10px 0;\r\n        transition: background-color 0.2s ease;\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1.8%;\r\n          padding: 1% 0;\r\n        }\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: rgba(20, 121, 252, 0.05);\r\n          border-radius: 4px;\r\n          padding-left: 8px;\r\n          padding-right: 8px;\r\n        }\r\n\r\n        .project-dot {\r\n          width: 14px;\r\n          height: 14px;\r\n          border-radius: 50%;\r\n          margin-right: 15px;\r\n          flex-shrink: 0;\r\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n\r\n          @media (max-width: 1199px) {\r\n            width: 1.4vw;\r\n            height: 1.4vw;\r\n            margin-right: 1.5%;\r\n          }\r\n        }\r\n\r\n        .project-info {\r\n          flex: 1;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          min-width: 0;\r\n\r\n          .project-name {\r\n            font-size: 15px;\r\n            color: #333;\r\n            font-weight: 500;\r\n            flex: 1;\r\n            margin-right: 12px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            line-height: 1.4;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.8rem, 1.5vw, 0.9375rem);\r\n            }\r\n          }\r\n\r\n          .project-amount {\r\n            font-size: 16px;\r\n            color: #1479fc;\r\n            font-weight: 700;\r\n            white-space: nowrap;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n            }\r\n\r\n            &::after {\r\n              content: '万';\r\n              font-size: 13px;\r\n              color: #666;\r\n              margin-left: 3px;\r\n              font-weight: 400;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(0.7rem, 1.3vw, 0.8125rem);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 悬浮框样式\r\n.project-tooltip {\r\n  position: fixed;\r\n  z-index: 1000;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  padding: 0;\r\n  min-width: 12.5rem;\r\n  max-width: 50rem;\r\n  width: auto;\r\n  font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n  .tooltip-header {\r\n    background: #f8f9fa;\r\n    padding: 0.75rem 1rem;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    font-weight: 600;\r\n    color: #374151;\r\n    border-radius: 0.5rem 0.5rem 0 0;\r\n    font-size: clamp(0.75rem, 1.1vw, 0.8125rem);\r\n    word-wrap: break-word;\r\n    word-break: break-all;\r\n    white-space: normal;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .tooltip-loading,\r\n  .tooltip-error,\r\n  .tooltip-empty {\r\n    padding: 1rem;\r\n    text-align: center;\r\n    color: #6b7280;\r\n  }\r\n\r\n  .tooltip-error {\r\n    color: #ef4444;\r\n  }\r\n\r\n  .tooltip-content {\r\n    padding: 0.5rem 0;\r\n    max-height: 18.75rem;\r\n    overflow-y: auto;\r\n\r\n    .tooltip-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0.5rem 1rem;\r\n      border-bottom: 1px solid #f3f4f6;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &:hover {\r\n        background: #f8f9fa;\r\n      }\r\n\r\n      .tooltip-name {\r\n        flex: 1;\r\n        color: #374151;\r\n        line-height: 1.4;\r\n        margin-right: 0.75rem;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        text-align: left;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .tooltip-value {\r\n        color: #2563eb;\r\n        font-weight: 600;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        min-width: 1.875rem;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式图标样式\r\n.stat-icon-img {\r\n  width: 40px; // 默认固定大小\r\n  height: 40px;\r\n  object-fit: contain;\r\n  flex-shrink: 0;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(24px, 3vw, 48px);\r\n    height: clamp(24px, 3vw, 48px);\r\n  }\r\n}\r\n\r\n// 小卡片图标样式（现在使用字体图标，此样式已不需要）\r\n// .stat-icon-img-small {\r\n//   width: 24px;\r\n//   height: 24px;\r\n//   object-fit: contain;\r\n//   flex-shrink: 0;\r\n\r\n//   @media (max-width: 1199px) {\r\n//     width: clamp(20px, 2.5vw, 28px);\r\n//     height: clamp(20px, 2.5vw, 28px);\r\n//   }\r\n// }\r\n\r\n.check-icon-img {\r\n  width: 14px; // 默认固定大小\r\n  height: 14px;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(12px, 1.5vw, 16px);\r\n    height: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n// 响应式媒体查询 - 针对1200px-1920px范围优化，让1200px也有1920px的效果\r\n@media (min-width: 1200px) and (max-width: 1920px) {\r\n  .home {\r\n    font-size: 14px;\r\n    /* 固定字体大小 */\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      .stat-icon {\r\n        width: 50px;\r\n        height: 50px;\r\n        min-width: 50px;\r\n        min-height: 50px;\r\n        max-width: 50px;\r\n        max-height: 50px;\r\n      }\r\n\r\n      .stat-content {\r\n        .stat-title {\r\n          font-size: 12px;\r\n        }\r\n\r\n        .stat-number-row {\r\n          .stat-number {\r\n            font-size: 24px;\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .management-card {\r\n    .management-header h3 {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .section-title {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .quality-stats .stat-group .stat-item {\r\n      .stat-header {\r\n        .stat-icon {\r\n          width: 18px;\r\n          height: 18px;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 13px;\r\n        }\r\n      }\r\n\r\n      .stat-number {\r\n        font-size: 20px;\r\n      }\r\n\r\n      .stat-detail span {\r\n        font-size: 10px;\r\n      }\r\n    }\r\n\r\n    // 添加安全管理数字字体大小设置\r\n    .safety-stat-card {\r\n      .safety-stat-main {\r\n        .safety-stat-number {\r\n          font-size: 22px;\r\n          /* 1300-1920px下适中的字体大小 */\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .check-icon-img {\r\n    width: 14px;\r\n    height: 14px;\r\n  }\r\n\r\n  .chart-card {\r\n    .chart-header h4 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .project-tooltip {\r\n    font-size: 12px;\r\n\r\n    .tooltip-header {\r\n      font-size: 13px;\r\n    }\r\n\r\n    .tooltip-content .tooltip-item {\r\n      .tooltip-name {\r\n        font-size: 12px;\r\n      }\r\n\r\n      .tooltip-value {\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 固定间距和布局\r\n  .top-stats {\r\n    gap: 20px;\r\n    /* 固定间距 */\r\n\r\n    .stat-card {\r\n      padding: 20px 15px;\r\n      /* 固定内边距 */\r\n      min-height: 120px;\r\n    }\r\n  }\r\n\r\n  .management-row {\r\n    margin-bottom: 20px;\r\n\r\n    .management-card {\r\n      padding: 15px;\r\n      /* 与默认样式保持一致 */\r\n      height: 370px;\r\n      /* 确保高度一致 */\r\n\r\n      .management-header {\r\n        margin-bottom: 10px;\r\n        /* 与默认样式保持一致 */\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .quality-stats {\r\n        margin-bottom: 15px;\r\n\r\n        .stat-group {\r\n          gap: 20px;\r\n          /* 固定间距 */\r\n        }\r\n      }\r\n\r\n      .safety-sections-container {\r\n        gap: 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定间距 */\r\n      }\r\n\r\n      .safety-section-independent {\r\n        padding: 10px 15px 12px 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定内边距 */\r\n\r\n        .safety-stats-row {\r\n          gap: 8px;\r\n          /* 与默认样式保持一致 */\r\n          /* 固定间距 */\r\n          margin-top: 10px;\r\n          /* 与默认样式保持一致 */\r\n\r\n          .safety-stat-card {\r\n            padding: 10px 12px;\r\n            /* 与默认样式保持一致 */\r\n            /* 固定内边距 */\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .charts-row {\r\n    .chart-card {\r\n      padding: 20px;\r\n      height: 340px;\r\n      /* 确保高度一致 */\r\n\r\n      .chart-header {\r\n        margin-bottom: 15px;\r\n      }\r\n\r\n      .chart-content {\r\n        height: calc(100% - 50px);\r\n        /* 固定计算高度 */\r\n      }\r\n    }\r\n  }\r\n\r\n  // 确保栅格系统间距一致\r\n  .el-row {\r\n    margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  // 统一进度条样式\r\n  .project-progress {\r\n    .progress-bar {\r\n      height: 12px;\r\n      /* 固定高度 */\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .home {\r\n    padding: 1%;\r\n    font-size: clamp(10px, 2vw, 14px);\r\n  }\r\n\r\n  .top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card {\r\n      min-height: 140px;\r\n      min-width: 100%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(1rem, 3vw, 1.25rem) !important;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1.25rem, 4vw, 2rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.875rem, 2.5vw, 1.125rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card-small {\r\n      min-width: 100%;\r\n      min-height: 70px;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(1rem, 3.5vw, 1.5rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1.125rem) !important;\r\n    /* 减小字体：12px-18px */\r\n  }\r\n\r\n  .management-row {\r\n    .el-col {\r\n      margin-bottom: 1%;\r\n    }\r\n  }\r\n\r\n  .safety-sections-container {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    height: 350px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 300px;\r\n  }\r\n\r\n  .project-tooltip {\r\n    max-width: 95vw;\r\n    min-width: 85vw;\r\n    width: auto;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .home {\r\n    font-size: clamp(8px, 3vw, 12px);\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      min-height: 120px;\r\n      padding: 3%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(0.875rem, 4vw, 1.125rem) !important;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1rem, 5vw, 1.75rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    .stat-card-small {\r\n      min-height: 60px;\r\n      padding: 2.5%;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(0.875rem, 4vw, 1.25rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 32px !important;\r\n    height: 32px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.625rem, 3vw, 1rem) !important;\r\n    /* 进一步减小：10px-16px */\r\n  }\r\n\r\n  // 新的安全管理移动端样式\r\n  .safety-top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .safety-stat-card-new {\r\n      padding: 3%;\r\n\r\n      .safety-stat-icon {\r\n        width: 40px !important;\r\n        height: 40px !important;\r\n\r\n        i {\r\n          font-size: 20px !important;\r\n        }\r\n      }\r\n\r\n      .safety-stat-content-new {\r\n        .safety-stat-label {\r\n          font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n        }\r\n\r\n        .safety-stat-number-new {\r\n          .number {\r\n            font-size: clamp(1rem, 4vw, 1.5rem) !important;\r\n          }\r\n\r\n          .unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .safety-bottom-stats {\r\n    .stats-row {\r\n      .stat-item {\r\n        gap: 1%;\r\n\r\n        .stat-label {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          min-width: 25px !important;\r\n        }\r\n\r\n        .stat-value {\r\n          font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-unit {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .management-card {\r\n    height: 320px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 280px;\r\n  }\r\n\r\n  .safety-stats-row {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .quality-stats .stat-group {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n}\r\n\r\n// 全局样式覆盖\r\n::v-deep .el-card {\r\n  border: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .el-card__header {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  padding: 1rem 1.25rem;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  padding: 1.25rem;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAojBA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAK,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,eAAA;MAAA;MACAC,cAAA;MAAA;MACA;MACAC,SAAA;QACA;QACAC,gBAAA;QACAC,qBAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,iBAAA;QACA;QACAC,sBAAA;QACAC,wBAAA;QACAC,6BAAA;QACAC,eAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA;MACAC,iBAAA;QACA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,aAAA;QACAC,eAAA;QACAC,UAAA;QACAC,WAAA;QACAC,YAAA;QACA;QACAC,kBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,iBAAA;MACA;MACA;MACAC,gBAAA;QACA;QACAC,eAAA;QACAC,WAAA;QACAX,UAAA;QACA;QACAY,WAAA;QACAC,gBAAA;QACA;QACAC,gBAAA;UACAC,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,UACA;UACArC,IAAA,GACA;YAAAsC,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,EACA;UACA2C,MAAA;YACAC,MAAA,GACA;cACAC,MAAA;cACAC,MAAA;cACAC,KAAA;gBACAC,IAAA;gBACAC,QAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,UAAA;cACA;cACAC,SAAA;gBACAN,IAAA;gBACAO,MAAA;gBACAC,OAAA;gBACAC,SAAA;kBACAL,KAAA;kBACAM,KAAA;gBACA;cACA;YACA;UAEA;QACA;QACAC,gBAAA;UACAlB,SAAA,GACA,WACA,WACA,WACA,WACA,UACA;UACArC,IAAA,GACA;YAAAsC,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,EACA;UACA2C,MAAA;YACAC,MAAA,GACA;cACAC,MAAA;cACAC,MAAA;cACAC,KAAA;gBACAC,IAAA;gBACAC,QAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,UAAA;cACA;cACAC,SAAA;gBACAN,IAAA;gBACAO,MAAA;gBACAC,OAAA;gBACAC,SAAA;kBACAL,KAAA;kBACAM,KAAA;gBACA;cACA;YACA;UAEA;QACA;MACA;MACA;MACAE,UAAA;QACAC,WAAA;QACAC,SAAA;QACAC,OAAA;QACArC,UAAA;QACAsC,eAAA;QACAC,gBAAA;MACA;MACA;MACAC,kBAAA;QACAC,YAAA;UACA1B,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;UACArC,IAAA,GACA;YAAAsC,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA,GACA;YAAA0C,KAAA;YAAA1C,IAAA;UAAA;QAEA;MACA;MACA;MACAoE,qBAAA,GACA;QAAApE,IAAA;QAAAqE,QAAA;MAAA,GACA;QAAArE,IAAA;QAAAqE,QAAA;MAAA,GACA;QAAArE,IAAA;QAAAqE,QAAA;MAAA,GACA;QAAArE,IAAA;QAAAqE,QAAA;MAAA,GACA;QAAArE,IAAA;QAAAqE,QAAA;MAAA,EACA;MACA;MACAC,qBAAA;QACA7B,SAAA;QACA8B,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACAC,GAAA;UACAC,SAAA;YACA/B,IAAA;UACA;UACAgC,QAAA;YACAhC,IAAA;UACA;UACAiC,QAAA;YACAjC,IAAA;UACA;UACAkC,SAAA;YACAlC,IAAA;UACA;QACA;QACAmC,KAAA;UACAN,IAAA;UACAzE,IAAA,GACA,iCACA,oBACA,cACA,wBACA,uBACA;UACA2E,SAAA;YACA5B,QAAA;YACAC,KAAA;YACAgC,QAAA;UACA;UACAJ,QAAA;YACAhC,IAAA;UACA;UACAiC,QAAA;YACAjC,IAAA;UACA;QACA;QACAJ,MAAA,GACA;UACA5C,IAAA;UACA6E,IAAA;UACAzE,IAAA;UACAiF,SAAA;YACAjC,KAAA;YACAkC,YAAA;UACA;UACAC,QAAA;UACAxC,KAAA;YACAC,IAAA;YACAC,QAAA;YACAG,KAAA;YACAD,QAAA;YACAD,SAAA;UACA;QACA;MAEA;MACAsC,mBAAA;QACA/C,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;QACA8B,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACAzE,IAAA,GACA,SACA,SACA,SACA,SACA,SACA,QACA;UACA2E,SAAA;YACAK,QAAA;YACAK,MAAA;YACAtC,QAAA;UACA;QACA;QACAgC,KAAA;UACAN,IAAA;UACAC,GAAA;UACAC,SAAA;YACA5B,QAAA;UACA;QACA;QACAP,MAAA,GACA;UACA5C,IAAA;UACA6E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAjC,KAAA;UAAA;UACAhD,IAAA;QACA,GACA;UACAJ,IAAA;UACA6E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAjC,KAAA;UAAA;UACAhD,IAAA;QACA,GACA;UACAJ,IAAA;UACA6E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAjC,KAAA;UAAA;UACAhD,IAAA;QACA,GACA;UACAJ,IAAA;UACA6E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAjC,KAAA;UAAA;UACAhD,IAAA;QACA,GACA;UACAJ,IAAA;UACA6E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAjC,KAAA;UAAA;UACAhD,IAAA;QACA,GACA;UACAJ,IAAA;UACA6E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAjC,KAAA;UAAA;UACAhD,IAAA;QACA;MAEA;MACA;MACAuF,wBAAA;QACAlD,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;QACArC,IAAA,GACA;UAAAsC,KAAA;UAAA1C,IAAA;QAAA,GACA;UAAA0C,KAAA;UAAA1C,IAAA;QAAA,GACA;UAAA0C,KAAA;UAAA1C,IAAA;QAAA,GACA;UAAA0C,KAAA;UAAA1C,IAAA;QAAA,GACA;UAAA0C,KAAA;UAAA1C,IAAA;QAAA,EACA;QACA2C,MAAA;UACAC,MAAA,GACA;YACAC,MAAA;YACAC,MAAA;YACAC,KAAA;cACAC,IAAA;YACA;YACAM,SAAA;cACAN,IAAA;YACA;UACA;QAEA;MACA;MACA;MACA4C,qBAAA;QACAlD,KAAA;QACAmD,IAAA;QACA9C,KAAA;MACA;MACA;MACA+C,wBAAA,GACA;QAAA9F,IAAA;QAAA0C,KAAA;QAAAU,KAAA;MAAA,GACA;QAAApD,IAAA;QAAA0C,KAAA;QAAAU,KAAA;MAAA,GACA;QAAApD,IAAA;QAAA0C,KAAA;QAAAU,KAAA;MAAA,GACA;QAAApD,IAAA;QAAA0C,KAAA;QAAAU,KAAA;MAAA,GACA;QAAApD,IAAA;QAAA0C,KAAA;QAAAU,KAAA;MAAA,EACA;MACA;MACA2C,QAAA;QACAxB,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAqB,SAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;QAAA5D,SAAA;QAAArC,IAAA;MAAA;MACAkG,WAAA;QAAA7D,SAAA;QAAAG,MAAA;MAAA;MACA2D,WAAA;QAAA3D,MAAA;MAAA;MACA4D,aAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,eAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA;MACAC,OAAA;QACAC,OAAA;QACAC,CAAA;QACAC,CAAA;QACAC,KAAA;QACAC,OAAA;QACAC,KAAA;QACAlH,IAAA;MACA;MACA;MACAmH,6BAAA;MACA;MACAC,qBAAA;MACA;MACAC,gBAAA;QACAR,OAAA;QACAC,CAAA;QACAC,CAAA;QACAC,KAAA;QACAC,OAAA;QACAC,KAAA;QACAlH,IAAA;MACA;MACAsH,YAAA,MAAAC,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,sBAAA;IACA,KAAAC,qBAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,wBAAA;IACA,KAAAC,8BAAA;IACA,KAAAC,0BAAA;IACA,KAAAC,4BAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAb,6BAAA;MACA,KAAAA,6BAAA,CAAAc,OAAA;MACA,KAAAd,6BAAA;IACA;;IAEA;IACA,KAAAG,YAAA,CAAAY,KAAA;EACA;EACAC,OAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,QAAA;MACA,SAAApI,eAAA,KAAAoI,QAAA;QACA,KAAApI,eAAA,GAAAoI,QAAA;QACA,KAAAX,qBAAA;MACA;IACA;IAEA;IACAY,oBAAA,WAAAA,qBAAAD,QAAA;MACA,SAAAnI,cAAA,KAAAmI,QAAA;QACA,KAAAnI,cAAA,GAAAmI,QAAA;QACA,KAAAV,oBAAA;QACA,KAAAC,wBAAA;MACA;IACA;IAEA;IACAH,sBAAA,WAAAA,uBAAA;MAAA,IAAAc,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAA7I,IAAA,EAAA8I,EAAA;QAAA,WAAAJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAEAE,OAAA,CAAAC,GAAA;cAAAJ,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAG,iCAAA;YAAA;cAAAR,QAAA,GAAAG,QAAA,CAAAM,CAAA;cAEA,IAAAT,QAAA,IAAAA,QAAA,CAAAU,IAAA,YAAAV,QAAA,CAAA7I,IAAA;gBACAA,IAAA,GAAA6I,QAAA,CAAA7I,IAAA,EAEA;gBACAuI,KAAA,CAAApI,SAAA,CAAAK,iBAAA,GAAAR,IAAA,CAAAwJ,KAAA;gBACAjB,KAAA,CAAApI,SAAA,CAAAG,gBAAA,GAAAN,IAAA,CAAAyJ,OAAA;gBACAlB,KAAA,CAAApI,SAAA,CAAAC,gBAAA,GAAAJ,IAAA,CAAA0J,OAAA;gBACAnB,KAAA,CAAApI,SAAA,CAAAE,qBAAA,GAAAL,IAAA,CAAA2J,OAAA;gBACApB,KAAA,CAAApI,SAAA,CAAAI,cAAA,GAAAP,IAAA,CAAA4J,KAAA;;gBAEAT,OAAA,CAAAC,GAAA,kBAAAb,KAAA,CAAApI,SAAA;cACA;gBACAgJ,OAAA,CAAAU,IAAA,gBAAAhB,QAAA;gBACAN,KAAA,CAAAuB,mBAAA;cACA;cAAAd,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAM,CAAA;cAEAH,OAAA,CAAAjC,KAAA,kBAAA4B,EAAA;cACAP,KAAA,CAAAuB,mBAAA;YAAA;cAAA,OAAAd,QAAA,CAAAe,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IAEA;IACAkB,mBAAA,WAAAA,oBAAA;MACAX,OAAA,CAAAC,GAAA;MACA;MACA,KAAAY,MAAA,CAAAC,UAAA;IACA;IAEA;IACAvC,qBAAA,WAAAA,sBAAA;MAAA,IAAAwC,MAAA;MAAA,WAAA1B,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAwB,SAAA;QAAA,IAAAtB,QAAA,EAAA7I,IAAA,EAAAoK,GAAA;QAAA,WAAA1B,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAsB,SAAA;UAAA,kBAAAA,SAAA,CAAApB,CAAA,GAAAoB,SAAA,CAAAnB,CAAA;YAAA;cAAAmB,SAAA,CAAApB,CAAA;cAEAE,OAAA,CAAAC,GAAA,CACA,mBACA,aACAc,MAAA,CAAAjK,eACA;cAAAoK,SAAA,CAAAnB,CAAA;cAAA,OACA,IAAAoB,gCAAA;gBACAjC,QAAA,EAAA6B,MAAA,CAAAjK;cACA;YAAA;cAFA4I,QAAA,GAAAwB,SAAA,CAAAf,CAAA;cAIA,IAAAT,QAAA,IAAAA,QAAA,CAAAU,IAAA,YAAAV,QAAA,CAAA7I,IAAA;gBACAA,IAAA,GAAA6I,QAAA,CAAA7I,IAAA,EAEA;gBACAkK,MAAA,CAAAnJ,iBAAA,CAAAU,kBAAA,GAAAzB,IAAA,CAAAuK,IAAA;gBACAL,MAAA,CAAAnJ,iBAAA,CAAAW,eAAA,GAAA1B,IAAA,CAAAwK,KAAA;gBACAN,MAAA,CAAAnJ,iBAAA,CAAAY,iBAAA,GAAA3B,IAAA,CAAAyK,KAAA;gBACAP,MAAA,CAAAnJ,iBAAA,CAAAa,cAAA,GAAA5B,IAAA,CAAA0K,KAAA,GACA1K,IAAA,CAAA0K,KAAA,SACA;gBACAR,MAAA,CAAAnJ,iBAAA,CAAAc,gBAAA,GAAA7B,IAAA,CAAA2K,IAAA;gBACAT,MAAA,CAAAnJ,iBAAA,CAAAe,iBAAA,GAAA9B,IAAA,CAAA4K,KAAA;;gBAEAzB,OAAA,CAAAC,GAAA,kBAAAc,MAAA,CAAAnJ,iBAAA;cACA;gBACAoI,OAAA,CAAAU,IAAA,sBAAAhB,QAAA;gBACAqB,MAAA,CAAAW,0BAAA;cACA;cAAAR,SAAA,CAAAnB,CAAA;cAAA;YAAA;cAAAmB,SAAA,CAAApB,CAAA;cAAAmB,GAAA,GAAAC,SAAA,CAAAf,CAAA;cAEAH,OAAA,CAAAjC,KAAA,kBAAAkD,GAAA;cACAF,MAAA,CAAAW,0BAAA;YAAA;cAAA,OAAAR,SAAA,CAAAN,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IAEA;IACAU,0BAAA,WAAAA,2BAAA;MACA1B,OAAA,CAAAC,GAAA;MACA,KAAAY,MAAA,CAAAC,UAAA;IACA;IAEA;IACAtC,oBAAA,WAAAA,qBAAA;MAAA,IAAAmD,MAAA;MAAA,WAAAtC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAoC,SAAA;QAAA,IAAAlC,QAAA,EAAA7I,IAAA,EAAAgL,GAAA;QAAA,WAAAtC,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAkC,SAAA;UAAA,kBAAAA,SAAA,CAAAhC,CAAA,GAAAgC,SAAA,CAAA/B,CAAA;YAAA;cAAA+B,SAAA,CAAAhC,CAAA;cAEAE,OAAA,CAAAC,GAAA,CACA,mBACA,aACA0B,MAAA,CAAA5K,cACA;cAAA+K,SAAA,CAAA/B,CAAA;cAAA,OACA,IAAAgC,+BAAA;gBACA7C,QAAA,EAAAyC,MAAA,CAAA5K;cACA;YAAA;cAFA2I,QAAA,GAAAoC,SAAA,CAAA3B,CAAA;cAIA,IAAAT,QAAA,IAAAA,QAAA,CAAAU,IAAA,YAAAV,QAAA,CAAA7I,IAAA;gBACAA,IAAA,GAAA6I,QAAA,CAAA7I,IAAA,EAEA;gBACA8K,MAAA,CAAA/I,gBAAA,CAAAG,WAAA,GAAAlC,IAAA,CAAAuK,IAAA;gBACAO,MAAA,CAAA/I,gBAAA,CAAAI,gBAAA,GAAAnC,IAAA,CAAA0K,KAAA,GACA,CAAA1K,IAAA,CAAA0K,KAAA,QAAAS,OAAA,MACA;gBACA;gBACAL,MAAA,CAAA/I,gBAAA,CAAAqJ,mBAAA,GAAApL,IAAA,CAAAwK,KAAA;gBACAM,MAAA,CAAA/I,gBAAA,CAAAsJ,qBAAA,GAAArL,IAAA,CAAAyK,KAAA;gBACAK,MAAA,CAAA/I,gBAAA,CAAAuJ,iBAAA,GAAAtL,IAAA,CAAA2K,IAAA;gBACAG,MAAA,CAAA/I,gBAAA,CAAAwJ,kBAAA,GAAAvL,IAAA,CAAA4K,KAAA;;gBAEAzB,OAAA,CAAAC,GAAA,kBAAA0B,MAAA,CAAA/I,gBAAA;cACA;gBACAoH,OAAA,CAAAU,IAAA,sBAAAhB,QAAA;gBACAiC,MAAA,CAAAU,yBAAA;cACA;cAAAP,SAAA,CAAA/B,CAAA;cAAA;YAAA;cAAA+B,SAAA,CAAAhC,CAAA;cAAA+B,GAAA,GAAAC,SAAA,CAAA3B,CAAA;cAEAH,OAAA,CAAAjC,KAAA,kBAAA8D,GAAA;cACAF,MAAA,CAAAU,yBAAA;YAAA;cAAA,OAAAP,SAAA,CAAAlB,CAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAEA;IAEA;IACAS,yBAAA,WAAAA,0BAAA;MACArC,OAAA,CAAAC,GAAA;MACA,KAAAY,MAAA,CAAAC,UAAA;IACA;IAEA;IACArC,wBAAA,WAAAA,yBAAA;MAAA,IAAA6D,MAAA;MAAA,WAAAjD,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+C,SAAA;QAAA,IAAA7C,QAAA,EAAA8C,wBAAA,EAAAC,UAAA,EAAAC,UAAA,EAAA7L,IAAA,EAAA8L,UAAA,EAAAC,GAAA;QAAA,WAAArD,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAiD,SAAA;UAAA,kBAAAA,SAAA,CAAA/C,CAAA,GAAA+C,SAAA,CAAA9C,CAAA;YAAA;cAAA8C,SAAA,CAAA/C,CAAA;cAEAE,OAAA,CAAAC,GAAA;cAAA4C,SAAA,CAAA9C,CAAA;cAAA,OACA,IAAA+C,mCAAA;gBACA5D,QAAA,EAAAoD,MAAA,CAAAvL;cACA;YAAA;cAFA2I,QAAA,GAAAmD,SAAA,CAAA1C,CAAA;cAIA;cACAqC,wBAAA,IACA;gBAAArJ,KAAA;gBAAA1C,IAAA;cAAA,GACA;gBAAA0C,KAAA;gBAAA1C,IAAA;cAAA,GACA;gBAAA0C,KAAA;gBAAA1C,IAAA;cAAA,GACA;gBAAA0C,KAAA;gBAAA1C,IAAA;cAAA,GACA;gBAAA0C,KAAA;gBAAA1C,IAAA;cAAA,EACA,EAEA;cACAgM,UAAA,IACA,WACA,WACA,WACA,WACA,UACA;cACAC,UAAA,IACA,WACA,WACA,WACA,WACA,UACA,EAEA;cACAJ,MAAA,CAAA1J,gBAAA,CAAAK,gBAAA;gBACAC,SAAA,EAAAuJ,UAAA;gBACA5L,IAAA,EAAA2L,wBAAA;gBACApJ,MAAA;kBACAC,MAAA,GACA;oBACAC,MAAA;oBACAC,MAAA;oBACAC,KAAA;sBACAC,IAAA;sBACAC,QAAA;sBACAC,SAAA;sBACAC,QAAA;sBACAC,KAAA;sBACAC,UAAA;oBACA;oBACAC,SAAA;sBACAN,IAAA;sBACAO,MAAA;sBACAC,OAAA;sBACAC,SAAA;wBACAL,KAAA;wBACAM,KAAA;sBACA;oBACA;kBACA;gBAEA;cACA;;cAEA;cACA,IACAuF,QAAA,IACAA,QAAA,CAAAU,IAAA,YACAV,QAAA,CAAA7I,IAAA,IACAkM,KAAA,CAAAC,OAAA,CAAAtD,QAAA,CAAA7I,IAAA,GACA;gBACAA,IAAA,GAAA6I,QAAA,CAAA7I,IAAA,EACA;gBACA8L,UAAA,GAAA9L,IAAA,CACAoM,IAAA,WAAArC,CAAA,EAAAsC,CAAA;kBAAA,QAAAA,CAAA,CAAA/J,KAAA,UAAAyH,CAAA,CAAAzH,KAAA;gBAAA,GACAgK,KAAA,QAEA;gBACAb,MAAA,CAAA1J,gBAAA,CAAAwB,gBAAA;kBACAlB,SAAA,EAAAwJ,UAAA;kBACA7L,IAAA,EAAA8L,UAAA;kBACAvJ,MAAA;oBACAC,MAAA,GACA;sBACAC,MAAA;sBACAC,MAAA;sBACAC,KAAA;wBACAC,IAAA;wBACAC,QAAA;wBACAC,SAAA;wBACAC,QAAA;wBACAC,KAAA;wBACAC,UAAA;sBACA;sBACAC,SAAA;wBACAN,IAAA;wBACAO,MAAA;wBACAC,OAAA;wBACAC,SAAA;0BACAL,KAAA;0BACAM,KAAA;wBACA;sBACA;oBACA;kBAEA;gBACA;gBAEA6F,OAAA,CAAAC,GAAA;kBACAmD,MAAA;kBACAC,MAAA,EAAAf,MAAA,CAAA1J,gBAAA,CAAAwB;gBACA;cACA;gBACA4F,OAAA,CAAAU,IAAA,sBAAAhB,QAAA;gBACA4C,MAAA,CAAAgB,6BAAA;cACA;cAAAT,SAAA,CAAA9C,CAAA;cAAA;YAAA;cAAA8C,SAAA,CAAA/C,CAAA;cAAA8C,GAAA,GAAAC,SAAA,CAAA1C,CAAA;cAEAH,OAAA,CAAAjC,KAAA,kBAAA6E,GAAA;cACAN,MAAA,CAAAgB,6BAAA;YAAA;cAAA,OAAAT,SAAA,CAAAjC,CAAA;UAAA;QAAA,GAAA2B,QAAA;MAAA;IAEA;IAEA;IACAe,6BAAA,WAAAA,8BAAA;MACAtD,OAAA,CAAAC,GAAA;MACA;MACA,IAAAsD,oBAAA,IACA;QAAApK,KAAA;QAAA1C,IAAA;MAAA,GACA;QAAA0C,KAAA;QAAA1C,IAAA;MAAA,GACA;QAAA0C,KAAA;QAAA1C,IAAA;MAAA,GACA;QAAA0C,KAAA;QAAA1C,IAAA;MAAA,GACA;QAAA0C,KAAA;QAAA1C,IAAA;MAAA,EACA;MAEA,IAAAiM,UAAA,IACA,WACA,WACA,WACA,WACA,UACA;MAEA,KAAA9J,gBAAA,CAAAwB,gBAAA;QACAlB,SAAA,EAAAwJ,UAAA;QACA7L,IAAA,EAAA0M,oBAAA;QACAnK,MAAA;UACAC,MAAA,GACA;YACAC,MAAA;YACAC,MAAA;YACAC,KAAA;cACAC,IAAA;cACAC,QAAA;cACAC,SAAA;cACAC,QAAA;cACAC,KAAA;cACAC,UAAA;YACA;YACAC,SAAA;cACAN,IAAA;cACAO,MAAA;cACAC,OAAA;cACAC,SAAA;gBACAL,KAAA;gBACAM,KAAA;cACA;YACA;UACA;QAEA;MACA;MACA,KAAA0G,MAAA,CAAAC,UAAA;IACA;IAEA;IACApC,8BAAA,WAAAA,+BAAA;MAAA,IAAA8E,MAAA;MAAA,WAAAnE,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiE,SAAA;QAAA,IAAA/D,QAAA,EAAA7I,IAAA,EAAA4F,SAAA,EAAAiH,gBAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,eAAA,EAAA7K,SAAA,EAAA8K,GAAA;QAAA,WAAAzE,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAqE,SAAA;UAAA,kBAAAA,SAAA,CAAAnE,CAAA,GAAAmE,SAAA,CAAAlE,CAAA;YAAA;cAAAkE,SAAA,CAAAnE,CAAA;cAEAE,OAAA,CAAAC,GAAA;cAAAgE,SAAA,CAAAlE,CAAA;cAAA,OACA,IAAAmE,yCAAA;YAAA;cAAAxE,QAAA,GAAAuE,SAAA,CAAA9D,CAAA;cAEA,IACAT,QAAA,IACAA,QAAA,CAAAU,IAAA,YACAV,QAAA,CAAA7I,IAAA,IACAkM,KAAA,CAAAC,OAAA,CAAAtD,QAAA,CAAA7I,IAAA,GACA;gBACAA,IAAA,GAAA6I,QAAA,CAAA7I,IAAA,EAEA;gBACA4F,SAAA,GAAA5F,IAAA,CAAAsN,GAAA,WAAAC,IAAA;kBACA;kBACA,IAAAC,WAAA,GAAAD,IAAA,CAAAE,OAAA;kBACA,OAAAD,WAAA,CAAArK,MAAA,QACAqK,WAAA,CAAAE,SAAA,kBACAF,WAAA;gBACA,IAEA;gBACAX,gBAAA,GAAA7M,IAAA,CAAAsN,GAAA,CACA,UAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAE,OAAA;gBAAA,CACA,GAEA;gBACAX,UAAA,GAAA9M,IAAA,CAAAsN,GAAA,WAAAC,IAAA,EAAAI,KAAA;kBAAA;oBACArL,KAAA,EAAAsL,UAAA,CAAAL,IAAA,CAAAM,kBAAA;oBACAC,QAAA,EAAAjB,gBAAA,CAAAc,KAAA;kBACA;gBAAA,IACA;gBACAZ,UAAA,GAAA/M,IAAA,CAAAsN,GAAA,WAAAC,IAAA,EAAAI,KAAA;kBAAA;oBACArL,KAAA,EAAAsL,UAAA,CAAAL,IAAA,CAAAQ,iBAAA;oBACAD,QAAA,EAAAjB,gBAAA,CAAAc,KAAA;kBACA;gBAAA,IAEA;gBACAX,QAAA,GAAAgB,IAAA,CAAAtJ,GAAA,CAAAuJ,KAAA,CAAAD,IAAA,MAAAE,mBAAA,CAAAzF,OAAA,EACAqE,UAAA,CAAAQ,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAjL,KAAA;gBAAA,IAAA6L,MAAA,KAAAD,mBAAA,CAAAzF,OAAA,EACAsE,UAAA,CAAAO,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAjL,KAAA;gBAAA,IACA;gBACA2K,QAAA,GAAAe,IAAA,CAAAI,IAAA,CAAApB,QAAA;gBAEA;gBACAE,eAAA,GAAAlN,IAAA,CAAAqO,MAAA,WAAAC,GAAA,EAAAf,IAAA;kBAAA,OAAAe,GAAA,GAAAV,UAAA,CAAAL,IAAA,CAAAQ,iBAAA;gBAAA,OAEA;gBACA1L,SAAA;gBAEAsK,MAAA,CAAApH,wBAAA;kBACAlD,SAAA,EAAAA,SAAA;kBACArC,IAAA,EAAAA,IAAA,CAAAsN,GAAA,WAAAC,IAAA,EAAAI,KAAA;oBAAA;sBACArL,KAAA,EAAAsL,UAAA,CAAAL,IAAA,CAAAQ,iBAAA;sBACAnO,IAAA,EAAA2N,IAAA,CAAAE,OAAA;oBACA;kBAAA;kBACAlL,MAAA;oBACAC,MAAA,GACA;sBACAC,MAAA;sBACAC,MAAA;sBACAC,KAAA;wBACAC,IAAA;sBACA;sBACAM,SAAA;wBACAN,IAAA;sBACA;oBACA;kBAEA;gBACA;;gBAEA;gBACA+J,MAAA,CAAAnH,qBAAA;kBACAlD,KAAA,EAAA0L,IAAA,CAAAO,KAAA,CAAArB,eAAA,EAAAsB,QAAA;kBACA/I,IAAA;kBACA9C,KAAA;gBACA;;gBAEA;gBACAgK,MAAA,CAAAjH,wBAAA,GAAA1F,IAAA,CAAAsN,GAAA,WAAAC,IAAA,EAAAI,KAAA;kBAAA;oBACA/N,IAAA,EAAA2N,IAAA,CAAAE,OAAA;oBACAnL,KAAA,EAAA0L,IAAA,CAAAO,KAAA,CAAAX,UAAA,CAAAL,IAAA,CAAAQ,iBAAA,QAAAS,QAAA;oBACAxL,KAAA,EAAAX,SAAA,CAAAsL,KAAA,GAAAtL,SAAA,CAAAc,MAAA;kBACA;gBAAA;gBAEAgG,OAAA,CAAAC,GAAA,CACA,mBACAuD,MAAA,CAAA8B,qBACA;cACA;gBACAtF,OAAA,CAAAU,IAAA,wBAAAhB,QAAA;gBACA8D,MAAA,CAAA+B,mCAAA;cACA;cAAAtB,SAAA,CAAAlE,CAAA;cAAA;YAAA;cAAAkE,SAAA,CAAAnE,CAAA;cAAAkE,GAAA,GAAAC,SAAA,CAAA9D,CAAA;cAEAH,OAAA,CAAAjC,KAAA,oBAAAiG,GAAA;cACAR,MAAA,CAAA+B,mCAAA;YAAA;cAAA,OAAAtB,SAAA,CAAArD,CAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IAEA;IAEA;IACA8B,mCAAA,WAAAA,oCAAA;MACAvF,OAAA,CAAAC,GAAA;MACA,KAAAY,MAAA,CAAAC,UAAA;IACA;IAEA;IACAnC,0BAAA,WAAAA,2BAAA;MAAA,IAAA6G,MAAA;MAAA,WAAAnG,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAiG,SAAA;QAAA,IAAA/F,QAAA,EAAA7I,IAAA,EAAA6O,WAAA,EAAA7B,QAAA,EAAA8B,YAAA,EAAAC,WAAA,EAAAC,GAAA;QAAA,WAAAtG,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAkG,SAAA;UAAA,kBAAAA,SAAA,CAAAhG,CAAA,GAAAgG,SAAA,CAAA/F,CAAA;YAAA;cAAA+F,SAAA,CAAAhG,CAAA;cAEAE,OAAA,CAAAC,GAAA;cAAA6F,SAAA,CAAA/F,CAAA;cAAA,OACA,IAAAgG,qCAAA;YAAA;cAAArG,QAAA,GAAAoG,SAAA,CAAA3F,CAAA;cAEA,IACAT,QAAA,IACAA,QAAA,CAAAU,IAAA,YACAV,QAAA,CAAA7I,IAAA,IACAkM,KAAA,CAAAC,OAAA,CAAAtD,QAAA,CAAA7I,IAAA,GACA;gBACAA,IAAA,GAAA6I,QAAA,CAAA7I,IAAA,EAEA;gBACA6O,WAAA,GAAA7O,IAAA,CAAAsM,KAAA,QAEA;gBACAU,QAAA,GAAAgB,IAAA,CAAAtJ,GAAA,CAAAuJ,KAAA,CAAAD,IAAA,MAAAE,mBAAA,CAAAzF,OAAA,EAAAzI,IAAA,CAAAsN,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAjL,KAAA;gBAAA,MAEA;gBACAwM,YAAA,GAAAD,WAAA,CAAAvB,GAAA,WAAAC,IAAA;kBACA;kBACA,OAAAA,IAAA,CAAA3N,IAAA,IAAA2N,IAAA,CAAA3N,IAAA,CAAAuD,MAAA,QACAoK,IAAA,CAAA3N,IAAA,CAAA8N,SAAA,kBACAH,IAAA,CAAA3N,IAAA;gBACA,IAEA;gBACAmP,WAAA,GAAAF,WAAA,CAAAvB,GAAA,WAAAC,IAAA,EAAAI,KAAA;kBAAA;oBACA/N,IAAA,EAAAkP,YAAA,CAAAnB,KAAA;oBACArL,KAAA,EAAAiL,IAAA,CAAAjL,KAAA;oBACAwL,QAAA,EAAAP,IAAA,CAAA3N,IAAA;oBACAuP,SAAA,EAAA5B,IAAA,CAAA4B,SAAA;kBACA;gBAAA,IAEA;gBACAR,MAAA,CAAAzK,qBAAA,CAAAa,KAAA,CAAA/E,IAAA,GAAA8O,YAAA,CAAAM,OAAA;gBACAT,MAAA,CAAAzK,qBAAA,CAAA1B,MAAA,IAAAxC,IAAA,GAAA+O,WAAA,CAAAK,OAAA;gBACAT,MAAA,CAAAzK,qBAAA,CAAAM,KAAA,CAAAE,GAAA,GAAAsI,QAAA;;gBAEA;gBACA2B,MAAA,CAAA3K,qBAAA,GAAA6K,WAAA,CAAAvB,GAAA,WAAAC,IAAA;kBAAA;oBACA3N,IAAA,EAAA2N,IAAA,CAAA3N,IAAA;oBACA0C,KAAA,EAAAiL,IAAA,CAAAjL,KAAA;oBACA+M,YAAA,EAAA9B,IAAA,CAAA3N,IAAA;oBACAuP,SAAA,EAAA5B,IAAA,CAAA4B,SAAA;kBACA;gBAAA;;gBAEA;gBACAR,MAAA,CAAAW,SAAA;kBACAX,MAAA,CAAAY,yBAAA;gBACA;gBAEApG,OAAA,CAAAC,GAAA,kBAAAuF,MAAA,CAAAzK,qBAAA;cACA;gBACAiF,OAAA,CAAAU,IAAA,sBAAAhB,QAAA;gBACA8F,MAAA,CAAAa,+BAAA;cACA;cAAAP,SAAA,CAAA/F,CAAA;cAAA;YAAA;cAAA+F,SAAA,CAAAhG,CAAA;cAAA+F,GAAA,GAAAC,SAAA,CAAA3F,CAAA;cAEAH,OAAA,CAAAjC,KAAA,kBAAA8H,GAAA;cACAL,MAAA,CAAAa,+BAAA;YAAA;cAAA,OAAAP,SAAA,CAAAlF,CAAA;UAAA;QAAA,GAAA6E,QAAA;MAAA;IAEA;IAEA;IACAY,+BAAA,WAAAA,gCAAA;MAAA,IAAAC,MAAA;MACAtG,OAAA,CAAAC,GAAA;MACA,KAAAY,MAAA,CAAAC,UAAA;MACA;MACA,KAAAqF,SAAA;QACAG,MAAA,CAAAF,yBAAA;MACA;IACA;IAEA;IACAA,yBAAA,WAAAA,0BAAA;MAAA,IAAAG,MAAA;MACA,SAAAC,KAAA,CAAAzL,qBAAA;QACA;QACA,SAAAiD,6BAAA;UACA,KAAAA,6BAAA,CAAAc,OAAA;QACA;;QAEA;QACA,KAAAd,6BAAA,GAAA9H,OAAA,CAAAuQ,IAAA,CACA,KAAAD,KAAA,CAAAzL,qBACA;;QAEA;QACA,IAAA3B,MAAA;UACA4B,IAAA,OAAAD,qBAAA,CAAAC,IAAA;UACAK,KAAA,OAAAN,qBAAA,CAAAM,KAAA;UACAO,KAAA,OAAAb,qBAAA,CAAAa,KAAA;UACAvC,MAAA,OAAA0B,qBAAA,CAAA1B,MAAA;UACAoE,OAAA;YACAhE,IAAA;YACAiN,OAAA;YACAC,eAAA;YACAC,WAAA;YACAC,WAAA;YACAC,SAAA;cACAjN,KAAA;cACAD,QAAA;YACA;YACAmN,YAAA,EACA;YACArN,QAAA,WAAAA,SAAAsN,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA;cACA;cACA,IAAAC,YAAA,GAAAD,IAAA,CAAAE,WAAA;cACA,IAAAC,aAAA,GAAAH,IAAA,CAAAE,WAAA;;cAEA;cACA,IAAAE,UAAA,GAAAL,IAAA,CAAAhN,KAAA;cACA,IAAAsN,WAAA,GAAAN,IAAA,CAAAO,MAAA;;cAEA;cACA,IAAAC,UAAA,GAAAH,UAAA,GAAAR,KAAA;cACA,IAAAY,SAAA,GAAAZ,KAAA;cAEA,IAAArJ,CAAA,EAAAC,CAAA;;cAEA;cACA,IAAA+J,UAAA,IAAAN,YAAA,IAAAM,UAAA,GAAAC,SAAA;gBACA;gBACAjK,CAAA,GAAAqJ,KAAA;gBACA;gBACA,IAAArJ,CAAA,GAAA0J,YAAA,GAAAG,UAAA;kBACA7J,CAAA,GAAA6J,UAAA,GAAAH,YAAA;gBACA;cACA,WAAAO,SAAA,IAAAP,YAAA;gBACA;gBACA1J,CAAA,GAAAqJ,KAAA,MAAAK,YAAA;gBACA;gBACA,IAAA1J,CAAA;kBACAA,CAAA;gBACA;cACA;gBACA;gBACAA,CAAA,GAAAkH,IAAA,CAAAtJ,GAAA,CACA,GACAsJ,IAAA,CAAAgD,GAAA,CAAAb,KAAA,UAAAQ,UAAA,GAAAH,YAAA,KACA;cACA;;cAEA;cACAzJ,CAAA,GAAAoJ,KAAA,MAAAO,aAAA;cACA,IAAA3J,CAAA;gBACAA,CAAA;cACA,WAAAA,CAAA,GAAA2J,aAAA,GAAAE,WAAA;gBACA7J,CAAA,GAAA6J,WAAA,GAAAF,aAAA;cACA;cAEA,QAAA5J,CAAA,EAAAC,CAAA;YACA;YACAjE,SAAA,WAAAA,UAAAsN,MAAA;cACA,IAAAA,MAAA,CAAApQ,IAAA,QAAAiR,QAAA,CAAAxI,OAAA,EAAA2H,MAAA,CAAApQ,IAAA;gBACA,IAAAkR,YAAA,GAAAd,MAAA,CAAApQ,IAAA;kBAAA8N,QAAA,GAAAoD,YAAA,CAAApD,QAAA;kBAAAxL,KAAA,GAAA4O,YAAA,CAAA5O,KAAA;kBAAA6M,SAAA,GAAA+B,YAAA,CAAA/B,SAAA;gBAEA,IAAAgC,IAAA,yEAAAhD,MAAA,CAAAL,QAAA;gBACAqD,IAAA,iHAAAhD,MAAA,CAAA7L,KAAA;gBAEA,IAAA6M,SAAA,IAAAA,SAAA,CAAAhM,MAAA;kBACAgO,IAAA,IACA;kBACAA,IAAA,IACA;kBACAhC,SAAA,CAAAiC,OAAA,WAAA7D,IAAA;oBACA4D,IAAA,sJAAAhD,MAAA,CAEAZ,IAAA,CAAA3N,IAAA,yGAAAuO,MAAA,CAGAZ,IAAA,CAAAjL,KAAA,4CAEA;kBACA;kBACA6O,IAAA;gBACA;kBACAA,IAAA,IACA;gBACA;gBAEA,OAAAA,IAAA;cACA;;cAEA;cACA,wBAAAhD,MAAA,CAAAiC,MAAA,CAAAxQ,IAAA,yBAAAuO,MAAA,CAAAiC,MAAA,CAAA9N,KAAA;YACA;UACA;QACA;QAEA,KAAA6E,6BAAA,CAAAkK,SAAA,CAAA9O,MAAA;;QAEA;QACA+O,MAAA,CAAAC,gBAAA;UACA,IAAA7B,MAAA,CAAAvI,6BAAA;YACAuI,MAAA,CAAAvI,6BAAA,CAAAqK,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAzJ,4BAAA,WAAAA,6BAAA;MAAA,IAAA0J,MAAA;MAAA,WAAAjJ,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+I,SAAA;QAAA,IAAA7I,QAAA,EAAA7I,IAAA,EAAA2R,cAAA,EAAAC,WAAA,EAAA5E,QAAA,EAAAC,QAAA,EAAA4E,GAAA;QAAA,WAAAnJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA+I,SAAA;UAAA,kBAAAA,SAAA,CAAA7I,CAAA,GAAA6I,SAAA,CAAA5I,CAAA;YAAA;cAAA4I,SAAA,CAAA7I,CAAA;cAEAE,OAAA,CAAAC,GAAA;cAAA0I,SAAA,CAAA5I,CAAA;cAAA,OACA,IAAA6I,uCAAA;YAAA;cAAAlJ,QAAA,GAAAiJ,SAAA,CAAAxI,CAAA;cAEA,IACAT,QAAA,IACAA,QAAA,CAAAU,IAAA,YACAV,QAAA,CAAA7I,IAAA,IACAkM,KAAA,CAAAC,OAAA,CAAAtD,QAAA,CAAA7I,IAAA,GACA;gBACAA,IAAA,GAAA6I,QAAA,CAAA7I,IAAA,EAEA;gBACA2R,cAAA,GAAA3R,IAAA,CAAAsN,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAA3N,IAAA;gBAAA,IAEA;gBACAgS,WAAA,GAAA5R,IAAA,CAAAsN,GAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAjL,KAAA;gBAAA,IACA;gBACA0K,QAAA,GAAAgB,IAAA,CAAAtJ,GAAA,CAAAuJ,KAAA,CAAAD,IAAA,MAAAE,mBAAA,CAAAzF,OAAA,EAAAmJ,WAAA;gBACA3E,QAAA,GAAAD,QAAA,OAAAA,QAAA;gBAEA;gBACAyE,MAAA,CAAArM,mBAAA;kBACA/C,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;kBACA8B,IAAA;oBACAC,GAAA;oBACAC,IAAA;oBACAC,KAAA;oBACAC,MAAA;kBACA;kBACAyN,MAAA,EAAAC,SAAA;kBAAA;kBACAzN,KAAA;oBACAC,IAAA;oBACAzE,IAAA,EAAA2R,cAAA;oBACAhN,SAAA;sBACAK,QAAA;sBACAK,MAAA;sBACAtC,QAAA;oBACA;kBACA;kBACAgC,KAAA;oBACAN,IAAA;oBACAC,GAAA,EAAAuI,QAAA,OAAAA,QAAA;oBACA+D,GAAA;oBACAhM,QAAA,EAAAiI,QAAA,OAAAe,IAAA,CAAAI,IAAA,CAAAnB,QAAA;oBAAA;oBACAtI,SAAA;sBACA5B,QAAA;oBACA;kBACA;kBACAP,MAAA,GACA;oBACA5C,IAAA;oBACA6E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAjC,KAAA;oBAAA;oBACAhD,IAAA,MAAAkM,KAAA,CAAAlM,IAAA,CAAAmD,MAAA,EAAA+O,IAAA;kBACA,GACA;oBACAtS,IAAA;oBACA6E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAjC,KAAA;oBAAA;oBACAhD,IAAA,MAAAkM,KAAA,CAAAlM,IAAA,CAAAmD,MAAA,EAAA+O,IAAA;kBACA,GACA;oBACAtS,IAAA;oBACA6E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAjC,KAAA;oBAAA;oBACAhD,IAAA,MAAAkM,KAAA,CAAAlM,IAAA,CAAAmD,MAAA,EAAA+O,IAAA;kBACA,GACA;oBACAtS,IAAA;oBACA6E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAjC,KAAA;oBAAA;oBACAhD,IAAA,EAAA4R,WAAA;kBACA,GACA;oBACAhS,IAAA;oBACA6E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAjC,KAAA;oBAAA;oBACAhD,IAAA,MAAAkM,KAAA,CAAAlM,IAAA,CAAAmD,MAAA,EAAA+O,IAAA;kBACA,GACA;oBACAtS,IAAA;oBACA6E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAjC,KAAA;oBAAA;oBACAhD,IAAA,MAAAkM,KAAA,CAAAlM,IAAA,CAAAmD,MAAA,EAAA+O,IAAA;kBACA;gBAEA;gBAEA/I,OAAA,CAAAC,GAAA,kBAAAqI,MAAA,CAAArM,mBAAA;cACA;gBACA+D,OAAA,CAAAU,IAAA,sBAAAhB,QAAA;gBACA4I,MAAA,CAAAU,iCAAA;cACA;cAAAL,SAAA,CAAA5I,CAAA;cAAA;YAAA;cAAA4I,SAAA,CAAA7I,CAAA;cAAA4I,GAAA,GAAAC,SAAA,CAAAxI,CAAA;cAEAH,OAAA,CAAAjC,KAAA,kBAAA2K,GAAA;cACAJ,MAAA,CAAAU,iCAAA;YAAA;cAAA,OAAAL,SAAA,CAAA/H,CAAA;UAAA;QAAA,GAAA2H,QAAA;MAAA;IAEA;IAEA;IACAS,iCAAA,WAAAA,kCAAA;MACAhJ,OAAA,CAAAC,GAAA;MACA,KAAAY,MAAA,CAAAC,UAAA;IACA;IAEA;IACAmI,oBAAA,WAAAA,qBAAAC,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9J,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA4J,SAAA;QAAA,IAAA1J,QAAA,EAAA2J,GAAA;QAAA,WAAA9J,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA0J,SAAA;UAAA,kBAAAA,SAAA,CAAAxJ,CAAA,GAAAwJ,SAAA,CAAAvJ,CAAA;YAAA;cAAA,KAEAoJ,MAAA,CAAAlL,qBAAA,CAAAiL,aAAA;gBAAAI,SAAA,CAAAvJ,CAAA;gBAAA;cAAA;cAAA,OAAAuJ,SAAA,CAAA1I,CAAA;YAAA;cAAA0I,SAAA,CAAAxJ,CAAA;cAKAE,OAAA,CAAAC,GAAA,gBAAAiJ,aAAA;cAAAI,SAAA,CAAAvJ,CAAA;cAAA,OACA,IAAAwJ,6CAAA,EAAAL,aAAA;YAAA;cAAAxJ,QAAA,GAAA4J,SAAA,CAAAnJ,CAAA;cAEA,IACAT,QAAA,IACAA,QAAA,CAAAU,IAAA,YACAV,QAAA,CAAA7I,IAAA,IACAkM,KAAA,CAAAC,OAAA,CAAAtD,QAAA,CAAA7I,IAAA,GACA;gBACA;gBACAsS,MAAA,CAAAlL,qBAAA,CAAAiL,aAAA,IAAAxJ,QAAA,CAAA7I,IAAA,CAAAsN,GAAA,CACA,UAAAC,IAAA;kBAAA;oBACA3N,IAAA,EAAA2N,IAAA,CAAA3N,IAAA;oBACA0C,KAAA,EAAAiL,IAAA,CAAAjL,KAAA;oBACAqQ,cAAA,EAAApF,IAAA,CAAAoF;kBACA;gBAAA,CACA;gBAEAxJ,OAAA,CAAAC,GAAA,CACA,eACAiJ,aAAA,EACAC,MAAA,CAAAlL,qBAAA,CAAAiL,aAAA,CACA;;gBAEA;gBACA,IACAC,MAAA,CAAAjL,gBAAA,CAAAR,OAAA,IACAyL,MAAA,CAAAjL,gBAAA,CAAAL,KAAA,KAAAqL,aAAA,EACA;kBACAC,MAAA,CAAAjL,gBAAA,CAAAJ,OAAA;kBACAqL,MAAA,CAAAjL,gBAAA,CAAArH,IAAA,GACAsS,MAAA,CAAAlL,qBAAA,CAAAiL,aAAA;gBACA;cACA;gBACA;gBACAC,MAAA,CAAAlL,qBAAA,CAAAiL,aAAA;gBACAlJ,OAAA,CAAAU,IAAA,sBAAAhB,QAAA;gBACA,IACAyJ,MAAA,CAAAjL,gBAAA,CAAAR,OAAA,IACAyL,MAAA,CAAAjL,gBAAA,CAAAL,KAAA,KAAAqL,aAAA,EACA;kBACAC,MAAA,CAAAjL,gBAAA,CAAAJ,OAAA;kBACAqL,MAAA,CAAAjL,gBAAA,CAAAH,KAAA;gBACA;cACA;cAAAuL,SAAA,CAAAvJ,CAAA;cAAA;YAAA;cAAAuJ,SAAA,CAAAxJ,CAAA;cAAAuJ,GAAA,GAAAC,SAAA,CAAAnJ,CAAA;cAEA;cACAgJ,MAAA,CAAAlL,qBAAA,CAAAiL,aAAA;cACAlJ,OAAA,CAAAjC,KAAA,gBAAAsL,GAAA;cACA,IACAF,MAAA,CAAAjL,gBAAA,CAAAR,OAAA,IACAyL,MAAA,CAAAjL,gBAAA,CAAAL,KAAA,KAAAqL,aAAA,EACA;gBACAC,MAAA,CAAAjL,gBAAA,CAAAJ,OAAA;gBACAqL,MAAA,CAAAjL,gBAAA,CAAAH,KAAA;cACA;YAAA;cAAA,OAAAuL,SAAA,CAAA1I,CAAA;UAAA;QAAA,GAAAwI,QAAA;MAAA;IAEA;IAEA;IACAK,wBAAA,WAAAA,yBAAAC,KAAA;MAAA,IAAAC,qBAAA;MACA;MACA,IAAAxC,IAAA,GAAAuC,KAAA,CAAAE,aAAA,CAAAC,qBAAA;MACA,IAAAlM,CAAA,GAAA+L,KAAA,CAAAI,OAAA,GAAA3C,IAAA,CAAAjM,IAAA;MACA,IAAAsM,UAAA,GAAAL,IAAA,CAAAhN,KAAA;;MAEA;MACA,IAAAqO,cAAA,KAAAmB,qBAAA,QAAA1N,mBAAA,CAAAZ,KAAA,cAAAsO,qBAAA,uBAAAA,qBAAA,CAAA9S,IAAA;MACA,IAAA2R,cAAA,CAAAxO,MAAA;MAEA,IAAA+P,cAAA,GAAAlF,IAAA,CAAAmF,KAAA,CACArM,CAAA,GAAA6J,UAAA,GAAAgB,cAAA,CAAAxO,MACA;MACA,IAAA+P,cAAA,SAAAA,cAAA,GAAAvB,cAAA,CAAAxO,MAAA;QACA,IAAAkP,aAAA,GAAAV,cAAA,CAAAuB,cAAA;;QAEA;QACA,KAAA7L,gBAAA,CAAAR,OAAA;QACA,KAAAQ,gBAAA,CAAAP,CAAA,GAAA+L,KAAA,CAAAI,OAAA;QACA,KAAA5L,gBAAA,CAAAN,CAAA,GAAA8L,KAAA,CAAAO,OAAA;QACA,KAAA/L,gBAAA,CAAAL,KAAA,GAAAqL,aAAA;;QAEA;QACA,IAAAgB,UAAA,QAAAjM,qBAAA,CAAAiL,aAAA;QACA,IAAAgB,UAAA;UACA,KAAAhM,gBAAA,CAAAJ,OAAA;UACA,KAAAI,gBAAA,CAAAH,KAAA;UACA,KAAAG,gBAAA,CAAArH,IAAA,GAAAqT,UAAA;QACA;UACA,KAAAhM,gBAAA,CAAAJ,OAAA;UACA,KAAAI,gBAAA,CAAAH,KAAA;UACA,KAAAG,gBAAA,CAAArH,IAAA;;UAEA;UACA,KAAAoS,oBAAA,CAAAC,aAAA;QACA;MACA;IACA;IAEA;IACAiB,yBAAA,WAAAA,0BAAA;MACA,KAAAjM,gBAAA,CAAAR,OAAA;MACA,KAAAQ,gBAAA,CAAArH,IAAA;MACA,KAAAqH,gBAAA,CAAAH,KAAA;MACA,KAAAG,gBAAA,CAAAJ,OAAA;IACA;IAEA;IACAsM,mBAAA,WAAAA,oBAAA/F,WAAA;MAAA,IAAAgG,SAAA,GAAAC,SAAA,CAAAtQ,MAAA,QAAAsQ,SAAA,QAAAxB,SAAA,GAAAwB,SAAA;MACA,KAAAjG,WAAA;MACA,IAAAA,WAAA,CAAArK,MAAA,IAAAqQ,SAAA,SAAAhG,WAAA;MACA,OAAAA,WAAA,CAAAE,SAAA,IAAA8F,SAAA;IACA;EACA;AACA", "ignoreList": []}]}