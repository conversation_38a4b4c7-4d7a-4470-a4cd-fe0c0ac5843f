{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue", "mtime": 1757499054501}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_bar<PERSON><PERSON>", "_interopRequireDefault", "_<PERSON><PERSON><PERSON>", "_statistics", "name", "components", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "data", "qualityTimeType", "safetyTimeType", "statsData", "domesticProjects", "internationalProjects", "safetyInvestment", "largeEquipment", "dangerousProjects", "superDangerousProjects", "domesticSafetyInvestment", "internationalSafetyInvestment", "companyBranches", "casualties", "majorH<PERSON><PERSON>", "qualityManagement", "inspectionPlans", "dailyInspections", "specialInspections", "foundProblems", "fixedProblems", "pendingProblems", "onTimeRate", "onTimeFixed", "overdueFixed", "selfReportProblems", "selfReportFixed", "selfReportPending", "selfReportRate", "selfReportOnTime", "selfReportOverdue", "qualityTypeChart", "colorList", "value", "option", "series", "center", "radius", "label", "show", "position", "formatter", "fontSize", "color", "lineHeight", "labelLine", "length", "length2", "lineStyle", "width", "safetyManagement", "inspectionCount", "hazardFound", "hazardCount", "reportOnTimeRate", "hazardTypeChart1", "hazardTypeChart2", "selfReport", "reportCount", "completed", "pending", "onTimeCompleted", "overdueCompleted", "statisticsAnalysis", "<PERSON><PERSON><PERSON>", "dangerousProjectsList", "progress", "dangerousProjectChart", "grid", "top", "left", "right", "bottom", "xAxis", "type", "max", "axisLabel", "axisTick", "axisLine", "splitLine", "yAxis", "interval", "itemStyle", "borderRadius", "<PERSON><PERSON><PERSON><PERSON>", "largeEquipmentChart", "rotate", "stack", "safetyInvestmentPieChart", "safetyInvestmentTotal", "unit", "safetyInvestmentProjects", "lineData", "xAxisData", "seriesData", "mainData", "yearCount", "dangerList", "echartData", "cateBarData", "yearBarData", "chart2Lengend", "echartType1", "echartType2", "echartTypeList1", "echartTypeList2", "queryParams", "pageNum", "pageSize", "tooltip", "visible", "x", "y", "title", "loading", "error", "dangerousProjectChartInstance", "equipmentDetailsCache", "equipmentTooltip", "requestQueue", "Set", "created", "loadManagementOverview", "loadQualityStatistics", "loadSafetyStatistics", "loadDangerTypeStatistics", "loadSafetyProductionStatistics", "loadDangerousProStatistics", "loadLargeEquipmentStatistics", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "methods", "handleItemHover", "item", "event", "console", "log", "hideTooltip", "changeQualityTimeType", "timeType", "changeSafetyTimeType", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "p", "n", "getManagementOverview", "v", "code", "wdgcs", "aqtz<PERSON><PERSON>", "gnzjxms", "gjzjxms", "dxsbs", "warn", "handleDataLoadError", "a", "$modal", "msgWarning", "_this2", "_callee2", "_t2", "_context2", "getQualityStatistics", "jczs", "ywcsl", "kwcsl", "aszgl", "aszg", "waszg", "handleQualityDataLoadError", "_this3", "_callee3", "_t3", "_context3", "getSafetyStatistics", "toFixed", "safetyFixedProblems", "safetyPendingProblems", "safetyOnTimeFixed", "safetyOverdueFixed", "handleSafetyDataLoadError", "_this4", "_callee4", "defaultDataForInspection", "colorList1", "colorList2", "sortedData", "_t4", "_context4", "getDangerTypeStatistics", "Array", "isArray", "sort", "b", "slice", "chart1", "chart2", "handleDangerTypeDataLoadError", "defaultDataForReport", "_this5", "_callee5", "fullCompanyNames", "budgetData", "actualData", "maxValue", "yAxisMax", "totalInvestment", "_t5", "_context5", "getSafetyProductionStatistics", "map", "companyName", "company", "substring", "index", "parseFloat", "annualBudgetAmount", "fullName", "actualInputAmount", "Math", "apply", "_toConsumableArray2", "concat", "ceil", "reduce", "sum", "round", "toString", "safetyInvestmentChart", "handleSafetyProductionDataLoadError", "_this6", "_callee6", "topProjects", "projectNames", "projectData", "_t6", "_context6", "getDangerousProStatistics", "detalList", "reverse", "originalName", "$nextTick", "initDangerousProjectChart", "handleDangerousProDataLoadError", "_this7", "_this8", "$refs", "init", "trigger", "backgroundColor", "borderColor", "borderWidth", "textStyle", "extraCssText", "point", "params", "dom", "rect", "size", "tooltipWidth", "contentSize", "tooltipHeight", "chartWidth", "chartHeight", "height", "rightSpace", "leftSpace", "min", "_typeof2", "_params$data", "html", "for<PERSON>ach", "setOption", "window", "addEventListener", "resize", "_this9", "_callee7", "equipmentNames", "runningData", "_t7", "_context7", "getLargeEquipmentStatistics", "legend", "undefined", "fill", "handleLargeEquipmentDataLoadError", "loadEquipmentDetails", "equipmentName", "_this0", "_callee8", "_t8", "_context8", "getLargeEquipmentByNameStatistics", "equipment_name", "handleEquipmentMouseMove", "_this$largeEquipmentC", "currentTarget", "getBoundingClientRect", "clientX", "equipmentIndex", "floor", "clientY", "cachedData", "handleEquipmentMouseLeave", "truncateCompanyName", "max<PERSON><PERSON><PERSON>", "arguments"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container home bg\">\r\n    <!-- 顶部统计卡片 -->\r\n    <div class=\"top-stats\">\r\n      <!-- 超危大工程 -->\r\n      <div class=\"stat-card stat-card-1\">\r\n        <div class=\"stat-header\">\r\n          <h3>超危大工程</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">超危工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.superDangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">危大工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.dangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 安全生产 -->\r\n      <div class=\"stat-card stat-card-2\">\r\n        <div class=\"stat-header\">\r\n          <h3>安全生产</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 在建项目 -->\r\n      <div class=\"stat-card stat-card-3\">\r\n        <div class=\"stat-header\">\r\n          <h3>在建项目</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 第二行统计卡片 -->\r\n    <div class=\"second-stats\">\r\n      <div class=\"stat-card-small stat-card-small-1\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-office-building\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">企业分支</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.companyBranches }}</span>\r\n            <span class=\"stat-unit-small\">个</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-2\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">伤亡事故人数</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.casualties }}</span>\r\n            <span class=\"stat-unit-small\">人</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-3\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning-outline\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">重大风险</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.majorHazards }}</span>\r\n            <span class=\"stat-unit-small\">项</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-4\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-cpu\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">大型设备数量</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.largeEquipment }}</span>\r\n            <span class=\"stat-unit-small\">台</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 第三行：超危大工程和安全生产投入 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>超危大工程</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"dangerous-chart-container\">\r\n              <div\r\n                ref=\"dangerousProjectChart\"\r\n                class=\"chart-container\"\r\n                style=\"height: 280px\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>安全生产投入</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"safety-investment-container investment-container-enhanced\">\r\n              <!-- 左侧环形图区域 -->\r\n              <div class=\"chart-section pie-chart-section\">\r\n                <div class=\"chart-wrapper\">\r\n                  <pieChart \r\n                    height=\"280px\" \r\n                    :data=\"safetyInvestmentPieChart\" \r\n                    :showCenterText=\"true\"\r\n                    :centerText=\"safetyInvestmentTotal\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <!-- 右侧数据列表区域 -->\r\n              <div class=\"data-list-section project-list-section\">\r\n                <div class=\"data-list project-list\">\r\n                  <div \r\n                    v-for=\"(item, index) in safetyInvestmentProjects\" \r\n                    :key=\"index\"\r\n                    class=\"safety-investment-item project-item\"\r\n                    @mouseenter=\"handleItemHover(item, $event)\"\r\n                    @mouseleave=\"hideTooltip\"\r\n                  >\r\n                    <div class=\"data-indicator project-dot\" :style=\"{backgroundColor: item.color}\"></div>\r\n                    <div class=\"data-info project-info\">\r\n                      <div class=\"data-label project-name\">{{ item.name }}</div>\r\n                      <div class=\"data-amount project-amount\">{{ item.value }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 底部：安全管理和质量管理 -->\r\n    <el-row :gutter=\"20\" class=\"management-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>安全管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 2 }\"\r\n                  @click=\"changeSafetyTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 1 }\"\r\n                  @click=\"changeSafetyTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 第一行统计数据 - 按设计稿样式 -->\r\n           <div style=\"background-color: #F5F7F9;padding: 10px;\" >\r\n          <div class=\"safety-top-stats-simple\">\r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"安全隐患数\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">安全隐患数</span>\r\n              <span class=\"safety-number\">{{ safetyManagement.hazardCount }}</span>\r\n              <span class=\"safety-unit\">个</span>\r\n            </div>\r\n            \r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"按时整改率\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">按时整改率</span>\r\n              <span class=\"safety-number\">{{ safetyManagement.reportOnTimeRate }}</span>\r\n              <span class=\"safety-unit\">%</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 第二行统计数据 - 一行显示 -->\r\n          <div class=\"safety-second-stats\" style=\"background-color: #fff;padding: 5px;\">\r\n            <div class=\"stats-row-single\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">待整改</span>\r\n                <span class=\"stat-value\">{{ safetyManagement.safetyPendingProblems || 4 }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">已整改</span>\r\n                <span class=\"stat-value\">{{ safetyManagement.safetyFixedProblems || 4 }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">已合格</span>\r\n                <span class=\"stat-value\">50</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">整改率</span>\r\n                <span class=\"stat-value\">{{ safetyManagement.reportOnTimeRate }}</span>\r\n                <span class=\"stat-unit\">%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 饼状图区域 -->\r\n          <div class=\"safety-chart-area\">\r\n            <pieChart\r\n              :data=\"safetyManagement.hazardTypeChart2\"\r\n              height=\"180px\"\r\n              :showCenterText=\"true\"\r\n              :centerText=\"{\r\n                value: safetyManagement.hazardCount.toString(),\r\n                unit: '安全隐患总数',\r\n                label: ''\r\n              }\"\r\n            />\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>质量管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <!-- timeType=1  月  2：年 -->\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 2 }\"\r\n                  @click=\"changeQualityTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 1 }\"\r\n                  @click=\"changeQualityTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div style=\"background-color: #F5F7F9;padding: 10px;\" >\r\n          <!-- 第一行统计数据 - 按安全管理样式 -->\r\n          <div class=\"safety-top-stats-simple\">\r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"质量问题数\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">质量问题数</span>\r\n              <span class=\"safety-number\">{{ qualityManagement.foundProblems + qualityManagement.selfReportProblems }}</span>\r\n              <span class=\"safety-unit\">个</span>\r\n            </div>\r\n            \r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"按时整改率\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">按时整改率</span>\r\n              <span class=\"safety-number\">{{ Math.round((qualityManagement.onTimeRate + qualityManagement.selfReportRate) / 2) }}</span>\r\n              <span class=\"safety-unit\">%</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 第二行统计数据 - 一行显示 -->\r\n          <div class=\"safety-second-stats\" style=\"background-color: #fff;padding: 5px;\">\r\n            <div class=\"stats-row-single\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">待整改</span>\r\n                <span class=\"stat-value\">{{ qualityManagement.pendingProblems + qualityManagement.selfReportPending }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">已整改</span>\r\n                <span class=\"stat-value\">{{ qualityManagement.fixedProblems + qualityManagement.selfReportFixed }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">检查计划</span>\r\n                <span class=\"stat-value\">{{ qualityManagement.inspectionPlans }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">整改率</span>\r\n                <span class=\"stat-value\">{{ Math.round((qualityManagement.onTimeRate + qualityManagement.selfReportRate) / 2) }}</span>\r\n                <span class=\"stat-unit\">%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 饼状图区域 - 质量管理图表 -->\r\n          <div class=\"safety-chart-area\">\r\n            <pieChart\r\n              :data=\"qualityManagement.qualityTypeChart\"\r\n              height=\"180px\"\r\n              :showCenterText=\"true\"\r\n              :centerText=\"{\r\n                value: (qualityManagement.foundProblems + qualityManagement.selfReportProblems).toString(),\r\n                unit: '质量问题总数',\r\n                label: ''\r\n              }\"\r\n            />\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 第五行：大型设备图表 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>大型设备</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div\r\n              class=\"equipment-chart-container\"\r\n              @mousemove=\"handleEquipmentMouseMove\"\r\n              @mouseleave=\"handleEquipmentMouseLeave\"\r\n            >\r\n              <barChart\r\n                height=\"300px\"\r\n                :data=\"largeEquipmentChart\"\r\n                :show-tooltip=\"false\"\r\n              />\r\n\r\n              <!-- 大型设备悬浮框 -->\r\n              <div\r\n                v-show=\"equipmentTooltip.visible\"\r\n                class=\"project-tooltip\"\r\n                :style=\"{\r\n                  left: equipmentTooltip.x + 'px',\r\n                  top: equipmentTooltip.y + 'px',\r\n                }\"\r\n              >\r\n                <div class=\"tooltip-header\">{{ equipmentTooltip.title }}</div>\r\n                <div v-if=\"equipmentTooltip.loading\" class=\"tooltip-loading\">\r\n                  加载中...\r\n                </div>\r\n                <div v-else-if=\"equipmentTooltip.error\" class=\"tooltip-error\">\r\n                  {{ equipmentTooltip.error }}\r\n                </div>\r\n                <div\r\n                  v-else-if=\"equipmentTooltip.data.length > 0\"\r\n                  class=\"tooltip-content\"\r\n                >\r\n                  <div\r\n                    v-for=\"item in equipmentTooltip.data\"\r\n                    :key=\"item.name\"\r\n                    class=\"tooltip-item\"\r\n                  >\r\n                    <span class=\"tooltip-name\">{{ item.name }}</span>\r\n                    <span class=\"tooltip-value\">{{ item.value }}</span>\r\n                  </div>\r\n                </div>\r\n                <div v-else class=\"tooltip-empty\">暂无详细数据</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport barChart from \"./components/barChart.vue\";\r\nimport pieChart from \"./components/pieChart.vue\";\r\nimport {\r\n  getManagementOverview,\r\n  getQualityStatistics,\r\n  getSafetyStatistics,\r\n  getDangerTypeStatistics,\r\n  getSafetyProductionStatistics,\r\n  getDangerousProStatistics,\r\n  getLargeEquipmentStatistics,\r\n  getLargeEquipmentByNameStatistics,\r\n} from \"@/api/statistics\";\r\nexport default {\r\n  name: \"Index\",\r\n  components: {\r\n    barChart,\r\n    pieChart,\r\n  },\r\n  data() {\r\n    return {\r\n      // 时间类型选择 (1-月, 2-年)\r\n      qualityTimeType: 2, // 默认为年\r\n      safetyTimeType: 2, // 默认为年\r\n      // 顶部统计数据\r\n      statsData: {\r\n        // 原有数据\r\n        domesticProjects: 1126,\r\n        internationalProjects: 1126,\r\n        safetyInvestment: 1500,\r\n        largeEquipment: 1126,\r\n        dangerousProjects: 1126,\r\n        // 新增数据\r\n        superDangerousProjects: 1126,\r\n        domesticSafetyInvestment: 1126,\r\n        internationalSafetyInvestment: 1126,\r\n        companyBranches: 1126,\r\n        casualties: 0,\r\n        majorHazards: 1126,\r\n      },\r\n      // 质量管理数据\r\n      qualityManagement: {\r\n        // 检查下发\r\n        inspectionPlans: 122,\r\n        dailyInspections: 89,\r\n        specialInspections: 33,\r\n        foundProblems: 31,\r\n        fixedProblems: 29,\r\n        pendingProblems: 2,\r\n        onTimeRate: 96,\r\n        onTimeFixed: 28,\r\n        overdueFixed: 1,\r\n        // 自主上报\r\n        selfReportProblems: 103,\r\n        selfReportFixed: 100,\r\n        selfReportPending: 3,\r\n        selfReportRate: 86.45,\r\n        selfReportOnTime: 31,\r\n        selfReportOverdue: 7,\r\n        // 质量类别统计图表\r\n        qualityTypeChart: {\r\n          colorList: ['#2656F5', '#FF920D', '#54C255', '#E54545', '#8EE98F'],\r\n          data: [\r\n            { value: 30, name: '施工质量' },\r\n            { value: 25, name: '材料质量' },\r\n            { value: 20, name: '工艺质量' },\r\n            { value: 15, name: '设备质量' },\r\n            { value: 10, name: '其他质量' }\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n      },\r\n      // 安全管理数据\r\n      safetyManagement: {\r\n        // 检查下发\r\n        inspectionCount: 58,\r\n        hazardFound: 20,\r\n        onTimeRate: 89,\r\n        // 自主上报\r\n        hazardCount: 103,\r\n        reportOnTimeRate: 86.45,\r\n        // 隐患类别统计图表\r\n        hazardTypeChart1: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#E54545\",\r\n            \"#FECF77\",\r\n            \"#FF7730\",\r\n            \"#B38DFF\",\r\n            \"#A1FFEB\",\r\n          ],\r\n          data: [\r\n            { value: 35, name: \"基础设施\" },\r\n            { value: 28, name: \"设备维护\" },\r\n            { value: 25, name: \"消防安全\" },\r\n            { value: 22, name: \"电气安全\" },\r\n            { value: 18, name: \"高空作业\" },\r\n            { value: 15, name: \"机械操作\" },\r\n            { value: 12, name: \"化学品管理\" },\r\n            { value: 10, name: \"个人防护\" },\r\n            { value: 8, name: \"环境卫生\" },\r\n            { value: 5, name: \"其他\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n        hazardTypeChart2: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n          ],\r\n          data: [\r\n            { value: 15, name: \"基础设施\" },\r\n            { value: 12, name: \"设备维护\" },\r\n            { value: 11, name: \"消防安全\" },\r\n            { value: 10, name: \"电气安全\" },\r\n            { value: 10, name: \"高空作业\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n      },\r\n      // 自主上报数据\r\n      selfReport: {\r\n        reportCount: 103,\r\n        completed: 100,\r\n        pending: 3,\r\n        onTimeRate: 86.45,\r\n        onTimeCompleted: 31,\r\n        overdueCompleted: 7,\r\n      },\r\n      // 统计分析数据\r\n      statisticsAnalysis: {\r\n        overallChart: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#A1FFEB\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#FF920D\",\r\n          ],\r\n          data: [\r\n            { value: 25, name: \"安全基础管理\" },\r\n            { value: 20, name: \"消防安全\" },\r\n            { value: 18, name: \"电气安全\" },\r\n            { value: 15, name: \"特种设备\" },\r\n            { value: 12, name: \"危化品\" },\r\n            { value: 10, name: \"其他\" },\r\n          ],\r\n        },\r\n      },\r\n      // 危大工程项目列表数据\r\n      dangerousProjectsList: [\r\n        { name: \"苏电产业科创园(NO.2010G32)07-13地块项目\", progress: 100 },\r\n        { name: \"未来出行产业园项目（直流分公司）\", progress: 80 },\r\n        { name: \"华为网络石代表处项目\", progress: 70 },\r\n        { name: \"年产3001万件汽车底盘等部件生产线项目\", progress: 30 },\r\n        { name: \"泪源城土壤生产及新客体验中心二期建设项目\", progress: 30 },\r\n      ],\r\n      // 危大工程图表配置\r\n      dangerousProjectChart: {\r\n        colorList: [\"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\"],\r\n        grid: {\r\n          top: 30,\r\n          left: \"35%\",\r\n          right: \"10%\",\r\n          bottom: \"5%\",\r\n        },\r\n        xAxis: {\r\n          type: \"value\",\r\n          max: 10,\r\n          axisLabel: {\r\n            show: false,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"苏电产业科创园(NO.2010G32) 07-13地块项目\",\r\n            \"未来出行产业园项目（直流分公司）\",\r\n            \"华为网络石代表处项目\",\r\n            \"年产3001万件汽车底盘等部件生产线项目\",\r\n            \"泪源城土壤生产及新客体验中心二期建设项目\",\r\n          ],\r\n          axisLabel: {\r\n            fontSize: 12,\r\n            color: \"#333\",\r\n            interval: 0,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"危大工程数量\",\r\n            type: \"bar\",\r\n            data: [10, 8, 7, 3, 3],\r\n            itemStyle: {\r\n              color: \"#5990FD\",\r\n              borderRadius: [0, 4, 4, 0],\r\n            },\r\n            barWidth: \"60%\",\r\n            label: {\r\n              show: true,\r\n              position: \"right\",\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n              formatter: \"{c}\",\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      largeEquipmentChart: {\r\n        colorList: [\r\n          \"#FF920D\",\r\n          \"#FECF77\",\r\n          \"#FF7730\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#2C2C2C\",\r\n        ],\r\n        grid: {\r\n          top: 30,\r\n          left: \"8%\",\r\n          right: \"8%\",\r\n          bottom: \"25%\",\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"设备分类1\",\r\n            \"设备分类2\",\r\n            \"设备分类3\",\r\n            \"设备分类4\",\r\n            \"设备分类5\",\r\n            \"设备分类6\",\r\n          ],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0,\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"value\",\r\n          max: 210,\r\n          axisLabel: {\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"未安装\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF920D\" },\r\n            data: [35, 0, 20, 0, 0, 35],\r\n          },\r\n          {\r\n            name: \"安装中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FECF77\" },\r\n            data: [0, 0, 0, 0, 0, 5],\r\n          },\r\n          {\r\n            name: \"验收中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF7730\" },\r\n            data: [0, 0, 10, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"运行中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#54C255\" },\r\n            data: [175, 120, 150, 30, 180, 150],\r\n          },\r\n          {\r\n            name: \"维修中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2656F5\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"已报废\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2C2C2C\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n        ],\r\n      },\r\n      // 安全生产投入环形图数据\r\n      safetyInvestmentPieChart: {\r\n        colorList: [\r\n          \"#2656F5\",\r\n          \"#FF920D\", \r\n          \"#54C255\",\r\n          \"#E54545\",\r\n          \"#8EE98F\",\r\n          \"#A1CDFF\"\r\n        ],\r\n        data: [\r\n          { value: 2000, name: \"海外工程一公司\" },\r\n          { value: 2000, name: \"海外工程二公司\" },\r\n          { value: 2000, name: \"海外工程三公司\" },\r\n          { value: 2000, name: \"中江国际集团\" },\r\n          { value: 2000, name: \"第五建设分公司\" }\r\n        ],\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"50%\"],\r\n              radius: [\"55%\", \"75%\"],\r\n              label: {\r\n                show: false\r\n              },\r\n              labelLine: {\r\n                show: false\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      // 安全生产投入总金额\r\n      safetyInvestmentTotal: {\r\n        value: \"6000\",\r\n        unit: \"总投入(万元)\",\r\n        label: \"安全生产投入\"\r\n      },\r\n      // 安全生产投入项目列表\r\n      safetyInvestmentProjects: [\r\n        { name: \"海外工程一公司\", value: \"2000\", color: \"#2656F5\" },\r\n        { name: \"海外工程二公司\", value: \"2000\", color: \"#FF920D\" },\r\n        { name: \"海外工程三公司\", value: \"2000\", color: \"#54C255\" },\r\n        { name: \"中江国际集团\", value: \"2000\", color: \"#E54545\" },\r\n        { name: \"第五建设分公司\", value: \"2000\", color: \"#8EE98F\" }\r\n      ],\r\n      // 保留这些旧数据结构以防止报错，后续可以逐步清理\r\n      lineData: {\r\n        grid: {\r\n          top: 10,\r\n          left: \"6%\",\r\n          right: \"6%\",\r\n          bottom: \"12%\",\r\n        },\r\n        xAxisData: [],\r\n        seriesData: [],\r\n      },\r\n      mainData: {},\r\n      yearCount: {},\r\n      dangerList: [],\r\n      echartData: { colorList: [], data: [] },\r\n      cateBarData: { colorList: [], series: [] },\r\n      yearBarData: { series: [] },\r\n      chart2Lengend: [],\r\n      echartType1: 1,\r\n      echartType2: 1,\r\n      echartTypeList1: [],\r\n      echartTypeList2: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 悬浮框相关数据\r\n      tooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      // 危大工程图表实例\r\n      dangerousProjectChartInstance: null,\r\n      // 设备详细信息缓存\r\n      equipmentDetailsCache: {},\r\n      // 设备悬浮框相关数据\r\n      equipmentTooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      requestQueue: new Set(), // 正在请求的项目名称队列\r\n    };\r\n  },\r\n  created() {\r\n    this.loadManagementOverview();\r\n    this.loadQualityStatistics();\r\n    this.loadSafetyStatistics();\r\n    this.loadDangerTypeStatistics();\r\n    this.loadSafetyProductionStatistics();\r\n    this.loadDangerousProStatistics();\r\n    this.loadLargeEquipmentStatistics();\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁图表实例\r\n    if (this.dangerousProjectChartInstance) {\r\n      this.dangerousProjectChartInstance.dispose();\r\n      this.dangerousProjectChartInstance = null;\r\n    }\r\n\r\n    // 清理请求队列\r\n    this.requestQueue.clear();\r\n  },\r\n  methods: {\r\n    // 处理安全生产投入项目hover事件\r\n    handleItemHover(item, event) {\r\n      // 可以在这里添加hover效果的逻辑\r\n      // 例如显示详细信息的tooltip等\r\n      console.log('Hovering over:', item.name, item.value);\r\n    },\r\n\r\n    // 隐藏tooltip\r\n    hideTooltip() {\r\n      // 隐藏tooltip的逻辑\r\n    },\r\n\r\n    // 切换质量管理时间类型\r\n    changeQualityTimeType(timeType) {\r\n      if (this.qualityTimeType !== timeType) {\r\n        this.qualityTimeType = timeType;\r\n        this.loadQualityStatistics();\r\n      }\r\n    },\r\n\r\n    // 切换安全管理时间类型\r\n    changeSafetyTimeType(timeType) {\r\n      if (this.safetyTimeType !== timeType) {\r\n        this.safetyTimeType = timeType;\r\n        this.loadSafetyStatistics();\r\n        this.loadDangerTypeStatistics();\r\n      }\r\n    },\r\n\r\n    // 获取安全管理总览数据\r\n    async loadManagementOverview() {\r\n      try {\r\n        console.log(\"开始加载安全管理总览数据...\");\r\n        const response = await getManagementOverview();\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的数据结构\r\n          this.statsData.dangerousProjects = data.wdgcs || 0; // 危大工程数\r\n          this.statsData.safetyInvestment = data.aqtzzje || 0; // 安全投资总金额\r\n          this.statsData.domesticProjects = data.gnzjxms || 0; // 国内在建项目\r\n          this.statsData.internationalProjects = data.gjzjxms || 0; // 国际在建项目\r\n          this.statsData.largeEquipment = data.dxsbs || 0; // 大型设备数\r\n\r\n          console.log(\"安全管理总览数据加载成功:\", this.statsData);\r\n        } else {\r\n          console.warn(\"接口返回数据格式异常:\", response);\r\n          this.handleDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理总览数据失败:\", error);\r\n        this.handleDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理数据加载错误\r\n    handleDataLoadError() {\r\n      console.log(\"使用默认数据\");\r\n      // 保持原有的默认数据，确保页面正常显示\r\n      this.$modal.msgWarning(\"数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取质量管理统计数据\r\n    async loadQualityStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载质量管理统计数据...\",\r\n          \"timeType:\",\r\n          this.qualityTimeType\r\n        );\r\n        const response = await getQualityStatistics({\r\n          timeType: this.qualityTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的质量管理数据结构（自主上报部分）\r\n          this.qualityManagement.selfReportProblems = data.jczs || 0; // 自主上报问题数\r\n          this.qualityManagement.selfReportFixed = data.ywcsl || 0; // 已整改数量\r\n          this.qualityManagement.selfReportPending = data.kwcsl || 0; // 待整改数量\r\n          this.qualityManagement.selfReportRate = data.aszgl\r\n            ? data.aszgl * 100\r\n            : 0; // 按时整改率（转换为百分比）\r\n          this.qualityManagement.selfReportOnTime = data.aszg || 0; // 按时整改\r\n          this.qualityManagement.selfReportOverdue = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"质量管理统计数据加载成功:\", this.qualityManagement);\r\n        } else {\r\n          console.warn(\"质量管理统计接口返回数据格式异常:\", response);\r\n          this.handleQualityDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取质量管理统计数据失败:\", error);\r\n        this.handleQualityDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理质量管理数据加载错误\r\n    handleQualityDataLoadError() {\r\n      console.log(\"使用质量管理默认数据\");\r\n      this.$modal.msgWarning(\"质量管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全管理统计数据\r\n    async loadSafetyStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载安全管理统计数据...\",\r\n          \"timeType:\",\r\n          this.safetyTimeType\r\n        );\r\n        const response = await getSafetyStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的安全管理数据结构（自主上报部分）\r\n          this.safetyManagement.hazardCount = data.jczs || 0; // 隐患数（问题数）\r\n          this.safetyManagement.reportOnTimeRate = data.aszgl\r\n            ? (data.aszgl * 100).toFixed(1)\r\n            : 0; // 按时整改率（转换为百分比）\r\n          // 为了保持数据一致性，也可以存储更多详细数据备用\r\n          this.safetyManagement.safetyFixedProblems = data.ywcsl || 0; // 已整改数量\r\n          this.safetyManagement.safetyPendingProblems = data.kwcsl || 0; // 待整改数量\r\n          this.safetyManagement.safetyOnTimeFixed = data.aszg || 0; // 按时整改\r\n          this.safetyManagement.safetyOverdueFixed = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"安全管理统计数据加载成功:\", this.safetyManagement);\r\n        } else {\r\n          console.warn(\"安全管理统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理统计数据失败:\", error);\r\n        this.handleSafetyDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全管理数据加载错误\r\n    handleSafetyDataLoadError() {\r\n      console.log(\"使用安全管理默认数据\");\r\n      this.$modal.msgWarning(\"安全管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取隐患类别统计数据\r\n    async loadDangerTypeStatistics() {\r\n      try {\r\n        console.log(\"开始加载隐患类别统计数据...\");\r\n        const response = await getDangerTypeStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        // 检查下发的图表使用默认数据，总和等于检查次数58\r\n        const defaultDataForInspection = [\r\n          { value: 15, name: \"基础设施\" },\r\n          { value: 12, name: \"设备维护\" },\r\n          { value: 11, name: \"消防安全\" },\r\n          { value: 10, name: \"电气安全\" },\r\n          { value: 10, name: \"高空作业\" },\r\n        ];\r\n\r\n        // 定义颜色数组\r\n        const colorList1 = [\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n          \"#FF920D\",\r\n          \"#54C255\",\r\n          \"#A1CDFF\",\r\n        ];\r\n        const colorList2 = [\r\n          \"#FF920D\",\r\n          \"#E54545\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n        ];\r\n\r\n        // 更新检查下发的隐患类别统计图表（使用默认数据）\r\n        this.safetyManagement.hazardTypeChart1 = {\r\n          colorList: colorList1,\r\n          data: defaultDataForInspection,\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        };\r\n\r\n        // 自主上报的图表使用接口数据\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n          // 对数据按value值降序排序，只取前5个\r\n          const sortedData = data\r\n            .sort((a, b) => (b.value || 0) - (a.value || 0))\r\n            .slice(0, 5);\r\n\r\n          // 更新自主上报的隐患类别统计图表（使用接口数据）\r\n          this.safetyManagement.hazardTypeChart2 = {\r\n            colorList: colorList2,\r\n            data: sortedData,\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"52%\"],\r\n                  radius: [\"45%\", \"75%\"],\r\n                  label: {\r\n                    show: true,\r\n                    position: \"outside\",\r\n                    formatter: \"{b}\\n{c}\",\r\n                    fontSize: 10,\r\n                    color: \"#666\",\r\n                    lineHeight: 14,\r\n                  },\r\n                  labelLine: {\r\n                    show: true,\r\n                    length: 8,\r\n                    length2: 15,\r\n                    lineStyle: {\r\n                      color: \"#666\",\r\n                      width: 1,\r\n                    },\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n          };\r\n\r\n          console.log(\"隐患类别统计数据加载成功:\", {\r\n            chart1: \"使用默认数据\",\r\n            chart2: this.safetyManagement.hazardTypeChart2,\r\n          });\r\n        } else {\r\n          console.warn(\"隐患类别统计接口返回数据格式异常:\", response);\r\n          this.handleDangerTypeDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取隐患类别统计数据失败:\", error);\r\n        this.handleDangerTypeDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理隐患类别统计数据加载错误\r\n    handleDangerTypeDataLoadError() {\r\n      console.log(\"自主上报图表使用默认数据\");\r\n      // 如果接口失败，自主上报图表也使用默认数据\r\n      const defaultDataForReport = [\r\n        { value: 8, name: \"基础设施\" },\r\n        { value: 7, name: \"设备维护\" },\r\n        { value: 6, name: \"消防安全\" },\r\n        { value: 5, name: \"电气安全\" },\r\n        { value: 4, name: \"高空作业\" },\r\n      ];\r\n\r\n      const colorList2 = [\r\n        \"#FF920D\",\r\n        \"#E54545\",\r\n        \"#54C255\",\r\n        \"#2656F5\",\r\n        \"#8EE98F\",\r\n      ];\r\n\r\n      this.safetyManagement.hazardTypeChart2 = {\r\n        colorList: colorList2,\r\n        data: defaultDataForReport,\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"52%\"],\r\n              radius: [\"45%\", \"75%\"],\r\n              label: {\r\n                show: true,\r\n                position: \"outside\",\r\n                formatter: \"{b}\\n{c}\",\r\n                fontSize: 10,\r\n                color: \"#666\",\r\n                lineHeight: 14,\r\n              },\r\n              labelLine: {\r\n                show: true,\r\n                length: 8,\r\n                length2: 15,\r\n                lineStyle: {\r\n                  color: \"#666\",\r\n                  width: 1,\r\n                },\r\n              },\r\n            },\r\n          ],\r\n        },\r\n      };\r\n      this.$modal.msgWarning(\"隐患类别统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全生产投入统计数据\r\n    async loadSafetyProductionStatistics() {\r\n      try {\r\n        console.log(\"开始加载安全生产投入统计数据...\");\r\n        const response = await getSafetyProductionStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取公司名称作为X轴标签（截取显示）\r\n          const xAxisData = data.map((item) => {\r\n            // 截取公司名称用于X轴显示，保持图表美观\r\n            const companyName = item.company || \"未知公司\";\r\n            return companyName.length > 10\r\n              ? companyName.substring(0, 10) + \"...\"\r\n              : companyName;\r\n          });\r\n\r\n          // 保存完整的公司名称用于tooltip显示\r\n          const fullCompanyNames = data.map(\r\n            (item) => item.company || \"未知公司\"\r\n          );\r\n\r\n          // 提取年度预算金额数据，并保存完整公司名称\r\n          const budgetData = data.map((item, index) => ({\r\n            value: parseFloat(item.annualBudgetAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n          // 提取实际投入金额数据，并保存完整公司名称\r\n          const actualData = data.map((item, index) => ({\r\n            value: parseFloat(item.actualInputAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n\r\n          // 动态计算Y轴最大值\r\n          const maxValue = Math.max(\r\n            ...budgetData.map((item) => item.value),\r\n            ...actualData.map((item) => item.value)\r\n          );\r\n          const yAxisMax = Math.ceil((maxValue * 1.2) / 1000) * 1000; // 向上取整到千位\r\n\r\n          // 计算总投入\r\n          const totalInvestment = data.reduce((sum, item) => sum + parseFloat(item.actualInputAmount || 0), 0);\r\n\r\n          // 更新环形图数据\r\n          const colorList = [\"#2656F5\", \"#FF920D\", \"#54C255\", \"#E54545\", \"#8EE98F\", \"#A1CDFF\"];\r\n          \r\n          this.safetyInvestmentPieChart = {\r\n            colorList: colorList,\r\n            data: data.map((item, index) => ({\r\n              value: parseFloat(item.actualInputAmount || 0),\r\n              name: item.company || \"未知公司\"\r\n            })),\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"50%\"],\r\n                  radius: [\"55%\", \"75%\"],\r\n                  label: {\r\n                    show: false\r\n                  },\r\n                  labelLine: {\r\n                    show: false\r\n                  }\r\n                }\r\n              ]\r\n            }\r\n          };\r\n\r\n          // 更新中心文字\r\n          this.safetyInvestmentTotal = {\r\n            value: Math.round(totalInvestment).toString(),\r\n            unit: \"总投入(万元)\",\r\n            label: \"安全生产投入\"\r\n          };\r\n\r\n          // 更新项目列表\r\n          this.safetyInvestmentProjects = data.map((item, index) => ({\r\n            name: item.company || \"未知公司\",\r\n            value: Math.round(parseFloat(item.actualInputAmount || 0)).toString(),\r\n            color: colorList[index % colorList.length]\r\n          }));\r\n\r\n          console.log(\r\n            \"安全生产投入统计数据加载成功:\",\r\n            this.safetyInvestmentChart\r\n          );\r\n        } else {\r\n          console.warn(\"安全生产投入统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyProductionDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全生产投入统计数据失败:\", error);\r\n        this.handleSafetyProductionDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全生产投入统计数据加载错误\r\n    handleSafetyProductionDataLoadError() {\r\n      console.log(\"使用安全生产投入统计默认数据\");\r\n      this.$modal.msgWarning(\"安全生产投入统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取危大工程统计数据\r\n    async loadDangerousProStatistics() {\r\n      try {\r\n        console.log(\"开始加载危大工程统计数据...\");\r\n        const response = await getDangerousProStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 获取前5个项目数据\r\n          const topProjects = data.slice(0, 5);\r\n\r\n          // 找出最大的value值，用于计算X轴最大值\r\n          const maxValue = Math.max(...data.map((item) => item.value));\r\n\r\n          // 提取项目名称和数值，保留完整数据用于tooltip\r\n          const projectNames = topProjects.map((item) => {\r\n            // 截取项目名称，避免过长\r\n            return item.name && item.name.length > 10\r\n              ? item.name.substring(0, 10) + \"...\"\r\n              : item.name || \"未知项目\";\r\n          });\r\n\r\n          // 构建包含详细信息的数据\r\n          const projectData = topProjects.map((item, index) => ({\r\n            name: projectNames[index],\r\n            value: item.value || 0,\r\n            fullName: item.name || \"未知项目\",\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 更新图表配置\r\n          this.dangerousProjectChart.yAxis.data = projectNames.reverse();\r\n          this.dangerousProjectChart.series[0].data = projectData.reverse();\r\n          this.dangerousProjectChart.xAxis.max = maxValue;\r\n\r\n          // 保存原始数据供其他用途使用\r\n          this.dangerousProjectsList = topProjects.map((item) => ({\r\n            name: item.name || \"未知项目\",\r\n            value: item.value || 0,\r\n            originalName: item.name,\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 初始化图表\r\n          this.$nextTick(() => {\r\n            this.initDangerousProjectChart();\r\n          });\r\n\r\n          console.log(\"危大工程统计数据加载成功:\", this.dangerousProjectChart);\r\n        } else {\r\n          console.warn(\"危大工程统计接口返回数据格式异常:\", response);\r\n          this.handleDangerousProDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取危大工程统计数据失败:\", error);\r\n        this.handleDangerousProDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理危大工程统计数据加载错误\r\n    handleDangerousProDataLoadError() {\r\n      console.log(\"使用危大工程统计默认数据\");\r\n      this.$modal.msgWarning(\"危大工程统计数据加载失败，显示默认数据\");\r\n      // 使用默认数据初始化图表\r\n      this.$nextTick(() => {\r\n        this.initDangerousProjectChart();\r\n      });\r\n    },\r\n\r\n    // 初始化危大工程图表\r\n    initDangerousProjectChart() {\r\n      if (this.$refs.dangerousProjectChart) {\r\n        // 销毁现有实例\r\n        if (this.dangerousProjectChartInstance) {\r\n          this.dangerousProjectChartInstance.dispose();\r\n        }\r\n\r\n        // 创建新的图表实例\r\n        this.dangerousProjectChartInstance = echarts.init(\r\n          this.$refs.dangerousProjectChart\r\n        );\r\n\r\n        // 设置图表选项\r\n        const option = {\r\n          grid: this.dangerousProjectChart.grid,\r\n          xAxis: this.dangerousProjectChart.xAxis,\r\n          yAxis: this.dangerousProjectChart.yAxis,\r\n          series: this.dangerousProjectChart.series,\r\n          tooltip: {\r\n            show: true,\r\n            trigger: \"item\",\r\n            backgroundColor: \"#fff\",\r\n            borderColor: \"#ccc\",\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n            },\r\n            extraCssText:\r\n              \"box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 400px; white-space: normal;\",\r\n            position: function (point, params, dom, rect, size) {\r\n              // 获取 tooltip 的实际尺寸\r\n              const tooltipWidth = size.contentSize[0];\r\n              const tooltipHeight = size.contentSize[1];\r\n\r\n              // 图表容器尺寸\r\n              const chartWidth = rect.width;\r\n              const chartHeight = rect.height;\r\n\r\n              // 计算右边和左边的可用空间\r\n              const rightSpace = chartWidth - point[0] - 10;\r\n              const leftSpace = point[0] - 10;\r\n\r\n              let x, y;\r\n\r\n              // 优先选择空间更大的一边，但确保不会被遮挡\r\n              if (rightSpace >= tooltipWidth || rightSpace > leftSpace) {\r\n                // 显示在右边\r\n                x = point[0] + 10;\r\n                // 如果右边真的放不下，尝试调整到图表右边界内\r\n                if (x + tooltipWidth > chartWidth) {\r\n                  x = chartWidth - tooltipWidth - 5;\r\n                }\r\n              } else if (leftSpace >= tooltipWidth) {\r\n                // 显示在左边，但确保有足够空间\r\n                x = point[0] - tooltipWidth - 10;\r\n                // 确保不会超出左边界\r\n                if (x < 0) {\r\n                  x = 5;\r\n                }\r\n              } else {\r\n                // 如果两边都放不下，选择右边并强制显示在图表内\r\n                x = Math.max(\r\n                  5,\r\n                  Math.min(point[0] + 10, chartWidth - tooltipWidth - 5)\r\n                );\r\n              }\r\n\r\n              // 垂直居中，但确保不超出边界\r\n              y = point[1] - tooltipHeight / 2;\r\n              if (y < 10) {\r\n                y = 10;\r\n              } else if (y + tooltipHeight > chartHeight - 10) {\r\n                y = chartHeight - tooltipHeight - 10;\r\n              }\r\n\r\n              return [x, y];\r\n            },\r\n            formatter: (params) => {\r\n              if (params.data && typeof params.data === \"object\") {\r\n                const { fullName, value, detalList } = params.data;\r\n\r\n                let html = `<div style=\"font-weight: bold; margin-bottom: 8px; color: #333;\">${fullName}</div>`;\r\n                html += `<div style=\"margin-bottom: 8px;\">总数量: <span style=\"color: #1890ff; font-weight: bold;\">${value}</span></div>`;\r\n\r\n                if (detalList && detalList.length > 0) {\r\n                  html +=\r\n                    '<div style=\"border-top: 1px solid #eee; padding-top: 8px;\">';\r\n                  html +=\r\n                    '<div style=\"font-weight: bold; margin-bottom: 6px; color: #666;\">详细信息:</div>';\r\n                  detalList.forEach((item) => {\r\n                    html += `<div style=\"margin-bottom: 4px; padding-left: 8px; border-left: 2px solid #1890ff;\">\r\n                      <div style=\"font-weight: 500;\">${\r\n                        item.name || \"未知类型\"\r\n                      }</div>\r\n                      <div style=\"color: #666; font-size: 11px;\">数量: ${\r\n                        item.value || 0\r\n                      }</div>\r\n                    </div>`;\r\n                  });\r\n                  html += \"</div>\";\r\n                } else {\r\n                  html +=\r\n                    '<div style=\"color: #999; font-style: italic;\">暂无详细信息</div>';\r\n                }\r\n\r\n                return html;\r\n              }\r\n\r\n              // 兜底显示\r\n              return `项目: ${params.name}<br/>数量: ${params.value}`;\r\n            },\r\n          },\r\n        };\r\n\r\n        this.dangerousProjectChartInstance.setOption(option);\r\n\r\n        // 自适应大小\r\n        window.addEventListener(\"resize\", () => {\r\n          if (this.dangerousProjectChartInstance) {\r\n            this.dangerousProjectChartInstance.resize();\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 获取大型设备统计数据\r\n    async loadLargeEquipmentStatistics() {\r\n      try {\r\n        console.log(\"开始加载大型设备统计数据...\");\r\n        const response = await getLargeEquipmentStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取设备名称作为X轴类别\r\n          const equipmentNames = data.map((item) => item.name || \"未知设备\");\r\n\r\n          // 提取设备数量，全部设为\"运行中\"状态\r\n          const runningData = data.map((item) => item.value || 0);\r\n          // 动态计算Y轴最大值，直接使用接口数据的最大值\r\n          const maxValue = Math.max(...runningData);\r\n          const yAxisMax = maxValue > 0 ? maxValue : 10; // Y轴上限就是接口里最大的数据\r\n\r\n          // 更新大型设备图表配置\r\n          this.largeEquipmentChart = {\r\n            colorList: [\r\n              \"#FF920D\",\r\n              \"#FECF77\",\r\n              \"#FF7730\",\r\n              \"#54C255\",\r\n              \"#2656F5\",\r\n              \"#2C2C2C\",\r\n            ],\r\n            grid: {\r\n              top: 30,\r\n              left: \"8%\",\r\n              right: \"8%\",\r\n              bottom: \"25%\",\r\n            },\r\n            legend: undefined, // 显式移除图例\r\n            xAxis: {\r\n              type: \"category\",\r\n              data: equipmentNames,\r\n              axisLabel: {\r\n                interval: 0,\r\n                rotate: 0,\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            yAxis: {\r\n              type: \"value\",\r\n              max: yAxisMax > 0 ? yAxisMax : 10,\r\n              min: 0,\r\n              interval: yAxisMax > 0 ? Math.ceil(yAxisMax / 5) : 2, // 将Y轴等分为5个区间，确保间隔为整数\r\n              axisLabel: {\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            series: [\r\n              {\r\n                name: \"未安装\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF920D\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"安装中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FECF77\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"验收中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF7730\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"运行中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#54C255\" },\r\n                data: runningData, // 使用真实数据\r\n              },\r\n              {\r\n                name: \"维修中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2656F5\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"已报废\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2C2C2C\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n            ],\r\n          };\r\n\r\n          console.log(\"大型设备统计数据加载成功:\", this.largeEquipmentChart);\r\n        } else {\r\n          console.warn(\"大型设备统计接口返回数据格式异常:\", response);\r\n          this.handleLargeEquipmentDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取大型设备统计数据失败:\", error);\r\n        this.handleLargeEquipmentDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理大型设备统计数据加载错误\r\n    handleLargeEquipmentDataLoadError() {\r\n      console.log(\"使用大型设备统计默认数据\");\r\n      this.$modal.msgWarning(\"大型设备统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 异步加载设备详细信息\r\n    async loadEquipmentDetails(equipmentName) {\r\n      // 如果已经有缓存，直接返回\r\n      if (this.equipmentDetailsCache[equipmentName]) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(\"开始加载设备详细信息:\", equipmentName);\r\n        const response = await getLargeEquipmentByNameStatistics(equipmentName);\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          // 缓存详细数据\r\n          this.equipmentDetailsCache[equipmentName] = response.data.map(\r\n            (item) => ({\r\n              name: item.name,\r\n              value: item.value,\r\n              equipment_name: item.equipment_name,\r\n            })\r\n          );\r\n\r\n          console.log(\r\n            \"设备详细信息加载成功:\",\r\n            equipmentName,\r\n            this.equipmentDetailsCache[equipmentName]\r\n          );\r\n\r\n          // 更新当前悬浮框的数据\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.data =\r\n              this.equipmentDetailsCache[equipmentName];\r\n          }\r\n        } else {\r\n          // 设置空数据避免重复请求\r\n          this.equipmentDetailsCache[equipmentName] = [];\r\n          console.warn(\"设备详细信息接口返回数据格式异常:\", response);\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.error = \"数据格式异常\";\r\n          }\r\n        }\r\n      } catch (error) {\r\n        // 设置空数据避免重复请求\r\n        this.equipmentDetailsCache[equipmentName] = [];\r\n        console.error(\"获取设备详细信息失败:\", error);\r\n        if (\r\n          this.equipmentTooltip.visible &&\r\n          this.equipmentTooltip.title === equipmentName\r\n        ) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"加载失败\";\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标移动\r\n    handleEquipmentMouseMove(event) {\r\n      // 获取鼠标位置\r\n      const rect = event.currentTarget.getBoundingClientRect();\r\n      const x = event.clientX - rect.left;\r\n      const chartWidth = rect.width;\r\n\r\n      // 根据鼠标位置推断设备（简化实现）\r\n      const equipmentNames = this.largeEquipmentChart.xAxis?.data || [];\r\n      if (equipmentNames.length === 0) return;\r\n\r\n      const equipmentIndex = Math.floor(\r\n        (x / chartWidth) * equipmentNames.length\r\n      );\r\n      if (equipmentIndex >= 0 && equipmentIndex < equipmentNames.length) {\r\n        const equipmentName = equipmentNames[equipmentIndex];\r\n\r\n        // 显示悬浮框\r\n        this.equipmentTooltip.visible = true;\r\n        this.equipmentTooltip.x = event.clientX + 10;\r\n        this.equipmentTooltip.y = event.clientY - 10;\r\n        this.equipmentTooltip.title = equipmentName;\r\n\r\n        // 检查缓存数据\r\n        const cachedData = this.equipmentDetailsCache[equipmentName];\r\n        if (cachedData) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = cachedData;\r\n        } else {\r\n          this.equipmentTooltip.loading = true;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = [];\r\n\r\n          // 加载详细数据\r\n          this.loadEquipmentDetails(equipmentName);\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标离开\r\n    handleEquipmentMouseLeave() {\r\n      this.equipmentTooltip.visible = false;\r\n      this.equipmentTooltip.data = [];\r\n      this.equipmentTooltip.error = \"\";\r\n      this.equipmentTooltip.loading = false;\r\n    },\r\n\r\n    // 截取公司名称显示\r\n    truncateCompanyName(companyName, maxLength = 20) {\r\n      if (!companyName) return \"\";\r\n      if (companyName.length <= maxLength) return companyName;\r\n      return companyName.substring(0, maxLength) + \"...\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home {\r\n  padding: 15px; // 默认固定内边距，避免大屏幕下过大\r\n  background: #f5f7fa;\r\n  font-size: 14px; // 默认固定基础字体大小\r\n  /* 响应式基础字体大小 */\r\n\r\n  @media (max-width: 1199px) {\r\n    padding: 1%;\r\n    font-size: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n.el-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 顶部统计卡片样式（第一行 - 3个大卡片）\r\n.top-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card {\r\n    flex: 1;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 20px;\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n    min-height: 120px;\r\n    min-width: 30%;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 3% 2%;\r\n    }\r\n\r\n    .stat-header {\r\n      margin-bottom: 16px;\r\n      \r\n      h3 {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        margin: 0;\r\n        \r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(1rem, 1.8vw, 1.125rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-dual {\r\n      display: flex;\r\n      gap: 20px;\r\n      \r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n      \r\n      .stat-item {\r\n        flex: 1;\r\n        \r\n        .stat-title {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n          line-height: 1.2;\r\n          \r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n\r\n        .stat-number-row {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 4px;\r\n\r\n          .stat-number {\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            color: #2656F5;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(1rem, 2.2vw, 1.75rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 14px;\r\n            color: #666;\r\n            font-weight: normal;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.1vw, 0.875rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-1 {\r\n      background: linear-gradient(120deg, #F74A34 0%, #FF7C5E 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-2 {\r\n      background: linear-gradient(137deg, #1688E6 0%, #46ABFF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-3 {\r\n      background: linear-gradient(137deg, #F5873E 0%, #F5A645 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 第二行统计卡片样式（4个小卡片）\r\n.second-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card-small {\r\n    display: flex;\r\n    align-items: center;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 16px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n    flex: 1;\r\n    min-width: 22%;\r\n    min-height: 110px;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 2% 1.5%;\r\n    }\r\n\r\n    .stat-icon {\r\n      margin-right: 12px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 40px;\r\n      height: 40px;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 8px;\r\n\r\n      @media (max-width: 1199px) {\r\n        width: 35px;\r\n        height: 35px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      i {\r\n        font-size: 20px;\r\n        color: #666;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-small {\r\n      flex: 1;\r\n\r\n      .stat-title-small {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-bottom: 4px;\r\n        line-height: 1.2;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n\r\n      .stat-number-row-small {\r\n        display: flex;\r\n        align-items: baseline;\r\n        gap: 4px;\r\n\r\n        .stat-number-small {\r\n          font-size: 20px;\r\n          font-weight: bold;\r\n          color: #333;\r\n          line-height: 1;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(1rem, 1.6vw, 1.25rem);\r\n          }\r\n        }\r\n\r\n        .stat-unit-small {\r\n          font-size: 12px;\r\n          color: #666;\r\n          font-weight: normal;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-1 {\r\n      background: linear-gradient(137deg, #156BF6 0%, #4681FF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-2 {\r\n      background: linear-gradient(137deg, #FC9920 0%, #F5AC45 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-3 {\r\n      background: linear-gradient(137deg, #9D59FF 0%, #CA79F5 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-4 {\r\n      background: linear-gradient(147deg, #18C68C 0%, #2BD181 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 管理卡片样式\r\n.management-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 15px; // 减少内边距，默认固定内边距\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 35  0px;\r\n    overflow: hidden; // 防止内容超出\r\n    box-sizing: border-box; // 确保内边距计算在内\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 1%;\r\n    }\r\n\r\n    .management-header {\r\n      margin-bottom: 10px; // 减少间距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1%;\r\n      }\r\n\r\n      .header-content {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n\r\n      h3 {\r\n        margin: 0;\r\n        font-size: 16px; // 默认固定字体大小\r\n        font-weight: 400;\r\n        color: #333;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n        }\r\n      }\r\n\r\n      .time-tabs {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 4px;\r\n\r\n        .tab-item {\r\n          font-size: 12px;\r\n          color: #666;\r\n          cursor: pointer;\r\n          padding: 2px 6px;\r\n          border-radius: 4px;\r\n          transition: all 0.3s ease;\r\n          user-select: none;\r\n\r\n          &:hover {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n          }\r\n\r\n          &.active {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n            font-weight: 500;\r\n          }\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            padding: 1px 4px;\r\n          }\r\n        }\r\n\r\n        .tab-divider {\r\n          font-size: 12px;\r\n          color: #ccc;\r\n          margin: 0 2px;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 质量管理样式\r\n    .section-header {\r\n      margin-bottom: 1%;\r\n\r\n      .section-title {\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: #f5f7fa;\r\n        border-radius: 0.25rem;\r\n      }\r\n    }\r\n\r\n    .quality-stats {\r\n      margin-bottom: 1%;\r\n\r\n      .stat-group {\r\n        display: flex;\r\n        gap: 1.5%;\r\n        align-items: flex-start;\r\n\r\n        .stat-item {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          &.stat-placeholder {\r\n            visibility: hidden;\r\n          }\r\n\r\n          .stat-header {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 0.5rem;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 28px;\r\n\r\n            .stat-icon {\r\n              width: 1.25rem;\r\n              height: 1.25rem;\r\n              border-radius: 0.25rem;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              color: white;\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n              &.blue {\r\n                background: #2656f5;\r\n              }\r\n\r\n              &.orange {\r\n                background: #ff920d;\r\n              }\r\n\r\n              &.green {\r\n                background: #54c255;\r\n              }\r\n            }\r\n\r\n            .stat-label {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n              color: #666;\r\n              font-weight: 500;\r\n            }\r\n          }\r\n\r\n          .stat-number {\r\n            font-size: clamp(0.75rem, 1.3vw, 1.25rem);\r\n            font-weight: bold;\r\n            color: #2656f5;\r\n            line-height: 1;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 24px;\r\n            text-align: left;\r\n          }\r\n\r\n          .stat-detail {\r\n            display: flex;\r\n            gap: 1.5%;\r\n            align-items: flex-start;\r\n\r\n            span {\r\n              font-size: clamp(0.5rem, 0.7vw, 0.625rem);\r\n              color: #999;\r\n              line-height: 1.4;\r\n\r\n              .number {\r\n                color: #333;\r\n                font-weight: 500;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 安全管理专用样式 - 简单版本按设计稿\r\n    .safety-top-stats-simple {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20px;\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n\r\n      .safety-stat-item-simple {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        @media (max-width: 1199px) {\r\n          gap: 0.8%;\r\n        }\r\n\r\n        .safety-icon-simple {\r\n          width: 20px;\r\n          height: 20px;\r\n          object-fit: contain;\r\n          flex-shrink: 0;\r\n\r\n          @media (max-width: 1199px) {\r\n            width: clamp(16px, 2vw, 20px);\r\n            height: clamp(16px, 2vw, 20px);\r\n          }\r\n        }\r\n\r\n        .safety-label {\r\n          font-size: 14px;\r\n          color: #666;\r\n          margin-right: 4px;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n          }\r\n        }\r\n\r\n        .safety-number {\r\n          font-size: 24px;\r\n          font-weight: bold;\r\n          color: #2656F5;\r\n          line-height: 1;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(1.25rem, 2vw, 1.5rem);\r\n          }\r\n        }\r\n\r\n        .safety-unit {\r\n          font-size: 16px;\r\n          color: #666;\r\n          font-weight: normal;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.875rem, 1.2vw, 1rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 新的第二行统计数据样式 - 一行显示\r\n    .safety-second-stats {\r\n      margin-bottom: 20px;\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n\r\n      .stats-row-single {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 6px;\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 0.6%;\r\n          }\r\n\r\n          .stat-dot {\r\n            width: 6px;\r\n            height: 6px;\r\n            border-radius: 50%;\r\n            background-color: #686C7F;\r\n            flex-shrink: 0;\r\n\r\n            @media (max-width: 1199px) {\r\n              width: clamp(4px, 0.6vw, 6px);\r\n              height: clamp(4px, 0.6vw, 6px);\r\n            }\r\n          }\r\n\r\n          .stat-label {\r\n            font-size: 12px;\r\n            color: #686C7F;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n\r\n          .stat-value {\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            color: #000;\r\n            margin-left: 2px;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n            color: #686C7F;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-chart-area {\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n    }\r\n\r\n    .safety-bottom-stats {\r\n      .stats-row {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-bottom: 12px;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 8px;\r\n          flex: 1;\r\n          justify-content: center;\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 0.8%;\r\n          }\r\n\r\n          .stat-dot {\r\n            width: 10px;\r\n            height: 10px;\r\n            border-radius: 50%;\r\n            flex-shrink: 0;\r\n\r\n            @media (max-width: 1199px) {\r\n              width: clamp(8px, 1vw, 10px);\r\n              height: clamp(8px, 1vw, 10px);\r\n            }\r\n          }\r\n\r\n          .stat-label {\r\n            font-size: 12px;\r\n            color: #666;\r\n            min-width: 40px;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n              min-width: clamp(30px, 3vw, 40px);\r\n            }\r\n          }\r\n\r\n          .stat-value {\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            color: #333;\r\n            min-width: 20px;\r\n            text-align: right;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n            color: #666;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-sections-container {\r\n      display: flex;\r\n      gap: 15px; // 减少间距，默认固定间距\r\n      width: 100%;\r\n      overflow: hidden; // 防止内容超出\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n    }\r\n\r\n    .safety-section-independent {\r\n      flex: 1;\r\n      background: #f5f7f9;\r\n      padding: 10px 15px 12px 15px; // 减少内边距，默认固定内边距\r\n      border-radius: 0.5rem;\r\n      min-width: 0; // 允许flex项目缩小到内容大小以下\r\n      box-sizing: border-box; // 确保内边距计算在内\r\n\r\n      @media (max-width: 1199px) {\r\n        padding: 0.8% 1.5% 1% 1.5%;\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px; // 默认固定间距\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .section-title {\r\n          font-size: 12px; // 默认固定字体大小\r\n          color: #666;\r\n          padding: 0.25rem 0.5rem;\r\n          background: rgba(245, 247, 250, 0.8);\r\n          border-radius: 0.25rem;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-stats-row {\r\n      display: flex;\r\n      gap: 8px; // 进一步减少间距，默认固定间距\r\n      margin-top: 10px; // 减少上边距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n        margin-top: 1%;\r\n      }\r\n\r\n      .safety-stat-card {\r\n        flex: 1;\r\n        background: white;\r\n        border-radius: 0.5rem;\r\n        padding: 10px 12px; // 大幅减少内边距，默认固定内边距\r\n        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);\r\n        min-width: 0; // 允许flex项目缩小\r\n        box-sizing: border-box; // 确保内边距计算在内\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.2% 1.6%;\r\n        }\r\n\r\n        .safety-stat-title {\r\n          font-size: 10px; // 默认固定字体大小\r\n          color: #666666;\r\n          margin-bottom: 0.5rem;\r\n          line-height: 1.2;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n          }\r\n        }\r\n\r\n        .safety-stat-content-row {\r\n          display: flex;\r\n          align-items: flex-end;\r\n          justify-content: space-between;\r\n          gap: 15px; // 默认固定间距\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 1.2%;\r\n          }\r\n        }\r\n\r\n        .safety-stat-main {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 0.25rem;\r\n          margin-bottom: 0;\r\n\r\n          .safety-stat-number {\r\n            font-size: 22px; // 调整默认固定字体大小，适合1300px+屏幕\r\n            font-weight: bold;\r\n            color: #2656F5;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.5vw, 1.5rem);\r\n              /* 减小响应式字体：12px-24px */\r\n            }\r\n          }\r\n\r\n          .safety-stat-unit {\r\n            font-size: 10px; // 默认固定字体大小\r\n            color: #666666;\r\n            font-weight: normal;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n            }\r\n          }\r\n        }\r\n\r\n        .safety-stat-subtitle {\r\n          font-size: 9px; // 默认固定字体大小\r\n          color: #999999;\r\n          line-height: 1.2;\r\n          white-space: nowrap;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.4375rem, 0.7vw, 0.5625rem);\r\n          }\r\n\r\n          .safety-highlight-number {\r\n            color: #ff920d;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 内嵌图表样式\r\n    .chart-section-in-section {\r\n      .chart-title {\r\n        font-size: 12px; // 默认固定字体大小\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: rgba(245, 247, 250, 0.8);\r\n        border-radius: 0.25rem;\r\n        margin-bottom: 0.5rem;\r\n        text-align: left;\r\n        display: inline-block;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .chart-section {\r\n      .chart-row {\r\n        display: flex;\r\n        gap: 1.5%;\r\n\r\n        .chart-item {\r\n          flex: 1;\r\n\r\n          .chart-title {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            color: #666;\r\n            margin-bottom: 0.5rem;\r\n            text-align: center;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .pie-chart-section {\r\n      .chart-title {\r\n        font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n        color: #666;\r\n        margin-bottom: 1rem;\r\n        text-align: center;\r\n      }\r\n\r\n      &.full-height {\r\n        height: calc(100% - 5rem);\r\n\r\n        .chart-container {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 底部图表卡片样式\r\n.charts-row {\r\n  .chart-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 2.5%;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 340px;\r\n\r\n    .chart-header {\r\n      margin-bottom: 1.5%;\r\n\r\n      h4 {\r\n        margin: 0;\r\n        font-size: clamp(0.875rem, 1.4vw, 1rem);\r\n        font-weight: 600;\r\n        color: #333;\r\n      }\r\n    }\r\n\r\n    .chart-content {\r\n      height: calc(100% - 4rem);\r\n    }\r\n  }\r\n}\r\n\r\n// 图表容器样式\r\n.chart-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n// 设备图表容器样式\r\n.equipment-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n// 危大工程图表容器样式\r\n.dangerous-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* ===========================================\r\n   投入管理样式模块 - 可扩展设计\r\n   支持多种投入类型的统一样式管理\r\n   =========================================== */\r\n\r\n// 基础投入容器增强样式\r\n.investment-container-enhanced {\r\n  border-radius: 12px;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(135deg, rgba(20, 121, 252, 0.02) 0%, transparent 50%);\r\n    pointer-events: none;\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: 0 8px 24px rgba(20, 121, 252, 0.15);\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n// 安全生产投入容器样式\r\n.safety-investment-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  gap: 32px;\r\n  align-items: center;\r\n  padding: 20px;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 2%;\r\n    padding: 2%;\r\n  }\r\n\r\n  // 图表区域增强样式\r\n  .chart-section {\r\n    width: 280px;\r\n    height: 280px;\r\n    flex-shrink: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: relative;\r\n    \r\n    // 添加装饰性边框效果\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: -4px;\r\n      left: -4px;\r\n      right: -4px;\r\n      bottom: -4px;\r\n      background: linear-gradient(45deg, #1479FC, #54C255, #FF920D, #E54545);\r\n      border-radius: 50%;\r\n      opacity: 0;\r\n      transition: opacity 0.3s ease;\r\n      z-index: 0;\r\n    }\r\n\r\n    &:hover::before {\r\n      opacity: 0.1;\r\n    }\r\n    \r\n    @media (max-width: 1199px) {\r\n      width: 35%;\r\n      height: auto;\r\n      aspect-ratio: 1;\r\n    }\r\n    \r\n    .chart-wrapper {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: relative;\r\n      z-index: 1;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n      background: rgba(255, 255, 255, 0.9);\r\n      backdrop-filter: blur(10px);\r\n    }\r\n  }\r\n\r\n  // 数据列表区域增强样式\r\n  .data-list-section {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-left: 32px;\r\n    position: relative;\r\n\r\n    // 添加分割线装饰\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      left: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      width: 3px;\r\n      height: 60%;\r\n      background: linear-gradient(to bottom, #1479FC, rgba(20, 121, 252, 0.3), transparent);\r\n      border-radius: 2px;\r\n    }\r\n\r\n    @media (max-width: 1199px) {\r\n      padding-left: 3.2%;\r\n      \r\n      &::before {\r\n        left: 1.6%;\r\n        width: 0.3vw;\r\n      }\r\n    }\r\n\r\n    .data-list {\r\n      width: 100%;\r\n      max-width: 320px;\r\n      \r\n      .project-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 18px;\r\n        padding: 10px 0;\r\n        transition: background-color 0.2s ease;\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1.8%;\r\n          padding: 1% 0;\r\n        }\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: rgba(20, 121, 252, 0.05);\r\n          border-radius: 4px;\r\n          padding-left: 8px;\r\n          padding-right: 8px;\r\n        }\r\n\r\n        .project-dot {\r\n          width: 14px;\r\n          height: 14px;\r\n          border-radius: 50%;\r\n          margin-right: 15px;\r\n          flex-shrink: 0;\r\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n\r\n          @media (max-width: 1199px) {\r\n            width: 1.4vw;\r\n            height: 1.4vw;\r\n            margin-right: 1.5%;\r\n          }\r\n        }\r\n\r\n        .project-info {\r\n          flex: 1;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          min-width: 0;\r\n\r\n          .project-name {\r\n            font-size: 15px;\r\n            color: #333;\r\n            font-weight: 500;\r\n            flex: 1;\r\n            margin-right: 12px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            line-height: 1.4;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.8rem, 1.5vw, 0.9375rem);\r\n            }\r\n          }\r\n\r\n          .project-amount {\r\n            font-size: 16px;\r\n            color: #2656F5;\r\n            font-weight: 700;\r\n            white-space: nowrap;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n            }\r\n\r\n            &::after {\r\n              content: '万';\r\n              font-size: 13px;\r\n              color: #666;\r\n              margin-left: 3px;\r\n              font-weight: 400;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(0.7rem, 1.3vw, 0.8125rem);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 悬浮框样式\r\n.project-tooltip {\r\n  position: fixed;\r\n  z-index: 1000;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  padding: 0;\r\n  min-width: 12.5rem;\r\n  max-width: 50rem;\r\n  width: auto;\r\n  font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n  .tooltip-header {\r\n    background: #f8f9fa;\r\n    padding: 0.75rem 1rem;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    font-weight: 600;\r\n    color: #374151;\r\n    border-radius: 0.5rem 0.5rem 0 0;\r\n    font-size: clamp(0.75rem, 1.1vw, 0.8125rem);\r\n    word-wrap: break-word;\r\n    word-break: break-all;\r\n    white-space: normal;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .tooltip-loading,\r\n  .tooltip-error,\r\n  .tooltip-empty {\r\n    padding: 1rem;\r\n    text-align: center;\r\n    color: #6b7280;\r\n  }\r\n\r\n  .tooltip-error {\r\n    color: #ef4444;\r\n  }\r\n\r\n  .tooltip-content {\r\n    padding: 0.5rem 0;\r\n    max-height: 18.75rem;\r\n    overflow-y: auto;\r\n\r\n    .tooltip-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0.5rem 1rem;\r\n      border-bottom: 1px solid #f3f4f6;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &:hover {\r\n        background: #f8f9fa;\r\n      }\r\n\r\n      .tooltip-name {\r\n        flex: 1;\r\n        color: #374151;\r\n        line-height: 1.4;\r\n        margin-right: 0.75rem;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        text-align: left;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .tooltip-value {\r\n        color: #2563eb;\r\n        font-weight: 600;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        min-width: 1.875rem;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式图标样式\r\n.stat-icon-img {\r\n  width: 40px; // 默认固定大小\r\n  height: 40px;\r\n  object-fit: contain;\r\n  flex-shrink: 0;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(24px, 3vw, 48px);\r\n    height: clamp(24px, 3vw, 48px);\r\n  }\r\n}\r\n\r\n// 小卡片图标样式（现在使用字体图标，此样式已不需要）\r\n// .stat-icon-img-small {\r\n//   width: 24px;\r\n//   height: 24px;\r\n//   object-fit: contain;\r\n//   flex-shrink: 0;\r\n\r\n//   @media (max-width: 1199px) {\r\n//     width: clamp(20px, 2.5vw, 28px);\r\n//     height: clamp(20px, 2.5vw, 28px);\r\n//   }\r\n// }\r\n\r\n.check-icon-img {\r\n  width: 14px; // 默认固定大小\r\n  height: 14px;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(12px, 1.5vw, 16px);\r\n    height: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n// 响应式媒体查询 - 针对1200px-1920px范围优化，让1200px也有1920px的效果\r\n@media (min-width: 1200px) and (max-width: 1920px) {\r\n  .home {\r\n    font-size: 14px;\r\n    /* 固定字体大小 */\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      .stat-icon {\r\n        width: 50px;\r\n        height: 50px;\r\n        min-width: 50px;\r\n        min-height: 50px;\r\n        max-width: 50px;\r\n        max-height: 50px;\r\n      }\r\n\r\n      .stat-content {\r\n        .stat-title {\r\n          font-size: 12px;\r\n        }\r\n\r\n        .stat-number-row {\r\n          .stat-number {\r\n            font-size: 24px;\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .management-card {\r\n    .management-header h3 {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .section-title {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .quality-stats .stat-group .stat-item {\r\n      .stat-header {\r\n        .stat-icon {\r\n          width: 18px;\r\n          height: 18px;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 13px;\r\n        }\r\n      }\r\n\r\n      .stat-number {\r\n        font-size: 20px;\r\n      }\r\n\r\n      .stat-detail span {\r\n        font-size: 10px;\r\n      }\r\n    }\r\n\r\n    // 添加安全管理数字字体大小设置\r\n    .safety-stat-card {\r\n      .safety-stat-main {\r\n        .safety-stat-number {\r\n          font-size: 22px;\r\n          /* 1300-1920px下适中的字体大小 */\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .check-icon-img {\r\n    width: 14px;\r\n    height: 14px;\r\n  }\r\n\r\n  .chart-card {\r\n    .chart-header h4 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .project-tooltip {\r\n    font-size: 12px;\r\n\r\n    .tooltip-header {\r\n      font-size: 13px;\r\n    }\r\n\r\n    .tooltip-content .tooltip-item {\r\n      .tooltip-name {\r\n        font-size: 12px;\r\n      }\r\n\r\n      .tooltip-value {\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 固定间距和布局\r\n  .top-stats {\r\n    gap: 20px;\r\n    /* 固定间距 */\r\n\r\n    .stat-card {\r\n      padding: 20px 15px;\r\n      /* 固定内边距 */\r\n      min-height: 120px;\r\n    }\r\n  }\r\n\r\n  .management-row {\r\n    margin-bottom: 20px;\r\n\r\n    .management-card {\r\n      padding: 15px;\r\n      /* 与默认样式保持一致 */\r\n      height: 350px;\r\n      /* 确保高度一致 */\r\n\r\n      .management-header {\r\n        margin-bottom: 10px;\r\n        /* 与默认样式保持一致 */\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .quality-stats {\r\n        margin-bottom: 15px;\r\n\r\n        .stat-group {\r\n          gap: 20px;\r\n          /* 固定间距 */\r\n        }\r\n      }\r\n\r\n      .safety-sections-container {\r\n        gap: 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定间距 */\r\n      }\r\n\r\n      .safety-section-independent {\r\n        padding: 10px 15px 12px 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定内边距 */\r\n\r\n        .safety-stats-row {\r\n          gap: 8px;\r\n          /* 与默认样式保持一致 */\r\n          /* 固定间距 */\r\n          margin-top: 10px;\r\n          /* 与默认样式保持一致 */\r\n\r\n          .safety-stat-card {\r\n            padding: 10px 12px;\r\n            /* 与默认样式保持一致 */\r\n            /* 固定内边距 */\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .charts-row {\r\n    .chart-card {\r\n      padding: 20px;\r\n      height: 340px;\r\n      /* 确保高度一致 */\r\n\r\n      .chart-header {\r\n        margin-bottom: 15px;\r\n      }\r\n\r\n      .chart-content {\r\n        height: calc(100% - 50px);\r\n        /* 固定计算高度 */\r\n      }\r\n    }\r\n  }\r\n\r\n  // 确保栅格系统间距一致\r\n  .el-row {\r\n    margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  // 统一进度条样式\r\n  .project-progress {\r\n    .progress-bar {\r\n      height: 12px;\r\n      /* 固定高度 */\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .home {\r\n    padding: 1%;\r\n    font-size: clamp(10px, 2vw, 14px);\r\n  }\r\n\r\n  .top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card {\r\n      min-height: 140px;\r\n      min-width: 100%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(1rem, 3vw, 1.25rem) !important;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1.25rem, 4vw, 2rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.875rem, 2.5vw, 1.125rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card-small {\r\n      min-width: 100%;\r\n      min-height: 70px;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(1rem, 3.5vw, 1.5rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1.125rem) !important;\r\n    /* 减小字体：12px-18px */\r\n  }\r\n\r\n  .management-row {\r\n    .el-col {\r\n      margin-bottom: 1%;\r\n    }\r\n  }\r\n\r\n  .safety-sections-container {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    height: 350px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 300px;\r\n  }\r\n\r\n  .project-tooltip {\r\n    max-width: 95vw;\r\n    min-width: 85vw;\r\n    width: auto;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .home {\r\n    font-size: clamp(8px, 3vw, 12px);\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      min-height: 120px;\r\n      padding: 3%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(0.875rem, 4vw, 1.125rem) !important;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1rem, 5vw, 1.75rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    .stat-card-small {\r\n      min-height: 60px;\r\n      padding: 2.5%;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(0.875rem, 4vw, 1.25rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 32px !important;\r\n    height: 32px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.625rem, 3vw, 1rem) !important;\r\n    /* 进一步减小：10px-16px */\r\n  }\r\n\r\n  // 新的安全管理移动端样式\r\n  .safety-top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .safety-stat-card-new {\r\n      padding: 3%;\r\n\r\n      .safety-stat-icon {\r\n        width: 40px !important;\r\n        height: 40px !important;\r\n\r\n        i {\r\n          font-size: 20px !important;\r\n        }\r\n      }\r\n\r\n      .safety-stat-content-new {\r\n        .safety-stat-label {\r\n          font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n        }\r\n\r\n        .safety-stat-number-new {\r\n          .number {\r\n            font-size: clamp(1rem, 4vw, 1.5rem) !important;\r\n          }\r\n\r\n          .unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .safety-bottom-stats {\r\n    .stats-row {\r\n      .stat-item {\r\n        gap: 1%;\r\n\r\n        .stat-label {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          min-width: 25px !important;\r\n        }\r\n\r\n        .stat-value {\r\n          font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-unit {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .management-card {\r\n    height: 320px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 280px;\r\n  }\r\n\r\n  .safety-stats-row {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .quality-stats .stat-group {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n}\r\n\r\n// 全局样式覆盖\r\n::v-deep .el-card {\r\n  border: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .el-card__header {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  padding: 1rem 1.25rem;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  padding: 1.25rem;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8aA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAK,IAAA;EACAC,UAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,eAAA;MAAA;MACAC,cAAA;MAAA;MACA;MACAC,SAAA;QACA;QACAC,gBAAA;QACAC,qBAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,iBAAA;QACA;QACAC,sBAAA;QACAC,wBAAA;QACAC,6BAAA;QACAC,eAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA;MACAC,iBAAA;QACA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,aAAA;QACAC,aAAA;QACAC,eAAA;QACAC,UAAA;QACAC,WAAA;QACAC,YAAA;QACA;QACAC,kBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,iBAAA;QACA;QACAC,gBAAA;UACAC,SAAA;UACAhC,IAAA,GACA;YAAAiC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,EACA;UACAsC,MAAA;YACAC,MAAA,GACA;cACAC,MAAA;cACAC,MAAA;cACAC,KAAA;gBACAC,IAAA;gBACAC,QAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,UAAA;cACA;cACAC,SAAA;gBACAN,IAAA;gBACAO,MAAA;gBACAC,OAAA;gBACAC,SAAA;kBACAL,KAAA;kBACAM,KAAA;gBACA;cACA;YACA;UAEA;QACA;MACA;MACA;MACAC,gBAAA;QACA;QACAC,eAAA;QACAC,WAAA;QACA9B,UAAA;QACA;QACA+B,WAAA;QACAC,gBAAA;QACA;QACAC,gBAAA;UACAvB,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,WACA,UACA;UACAhC,IAAA,GACA;YAAAiC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,EACA;UACAsC,MAAA;YACAC,MAAA,GACA;cACAC,MAAA;cACAC,MAAA;cACAC,KAAA;gBACAC,IAAA;gBACAC,QAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,UAAA;cACA;cACAC,SAAA;gBACAN,IAAA;gBACAO,MAAA;gBACAC,OAAA;gBACAC,SAAA;kBACAL,KAAA;kBACAM,KAAA;gBACA;cACA;YACA;UAEA;QACA;QACAO,gBAAA;UACAxB,SAAA,GACA,WACA,WACA,WACA,WACA,UACA;UACAhC,IAAA,GACA;YAAAiC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,EACA;UACAsC,MAAA;YACAC,MAAA,GACA;cACAC,MAAA;cACAC,MAAA;cACAC,KAAA;gBACAC,IAAA;gBACAC,QAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,UAAA;cACA;cACAC,SAAA;gBACAN,IAAA;gBACAO,MAAA;gBACAC,OAAA;gBACAC,SAAA;kBACAL,KAAA;kBACAM,KAAA;gBACA;cACA;YACA;UAEA;QACA;MACA;MACA;MACAQ,UAAA;QACAC,WAAA;QACAC,SAAA;QACAC,OAAA;QACAtC,UAAA;QACAuC,eAAA;QACAC,gBAAA;MACA;MACA;MACAC,kBAAA;QACAC,YAAA;UACAhC,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;UACAhC,IAAA,GACA;YAAAiC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA,GACA;YAAAqC,KAAA;YAAArC,IAAA;UAAA;QAEA;MACA;MACA;MACAqE,qBAAA,GACA;QAAArE,IAAA;QAAAsE,QAAA;MAAA,GACA;QAAAtE,IAAA;QAAAsE,QAAA;MAAA,GACA;QAAAtE,IAAA;QAAAsE,QAAA;MAAA,GACA;QAAAtE,IAAA;QAAAsE,QAAA;MAAA,GACA;QAAAtE,IAAA;QAAAsE,QAAA;MAAA,EACA;MACA;MACAC,qBAAA;QACAnC,SAAA;QACAoC,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACAC,GAAA;UACAC,SAAA;YACArC,IAAA;UACA;UACAsC,QAAA;YACAtC,IAAA;UACA;UACAuC,QAAA;YACAvC,IAAA;UACA;UACAwC,SAAA;YACAxC,IAAA;UACA;QACA;QACAyC,KAAA;UACAN,IAAA;UACA1E,IAAA,GACA,iCACA,oBACA,cACA,wBACA,uBACA;UACA4E,SAAA;YACAlC,QAAA;YACAC,KAAA;YACAsC,QAAA;UACA;UACAJ,QAAA;YACAtC,IAAA;UACA;UACAuC,QAAA;YACAvC,IAAA;UACA;QACA;QACAJ,MAAA,GACA;UACAvC,IAAA;UACA8E,IAAA;UACA1E,IAAA;UACAkF,SAAA;YACAvC,KAAA;YACAwC,YAAA;UACA;UACAC,QAAA;UACA9C,KAAA;YACAC,IAAA;YACAC,QAAA;YACAG,KAAA;YACAD,QAAA;YACAD,SAAA;UACA;QACA;MAEA;MACA4C,mBAAA;QACArD,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;QACAoC,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACA1E,IAAA,GACA,SACA,SACA,SACA,SACA,SACA,QACA;UACA4E,SAAA;YACAK,QAAA;YACAK,MAAA;YACA5C,QAAA;UACA;QACA;QACAsC,KAAA;UACAN,IAAA;UACAC,GAAA;UACAC,SAAA;YACAlC,QAAA;UACA;QACA;QACAP,MAAA,GACA;UACAvC,IAAA;UACA8E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAvC,KAAA;UAAA;UACA3C,IAAA;QACA,GACA;UACAJ,IAAA;UACA8E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAvC,KAAA;UAAA;UACA3C,IAAA;QACA,GACA;UACAJ,IAAA;UACA8E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAvC,KAAA;UAAA;UACA3C,IAAA;QACA,GACA;UACAJ,IAAA;UACA8E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAvC,KAAA;UAAA;UACA3C,IAAA;QACA,GACA;UACAJ,IAAA;UACA8E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAvC,KAAA;UAAA;UACA3C,IAAA;QACA,GACA;UACAJ,IAAA;UACA8E,IAAA;UACAa,KAAA;UACAL,SAAA;YAAAvC,KAAA;UAAA;UACA3C,IAAA;QACA;MAEA;MACA;MACAwF,wBAAA;QACAxD,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;QACAhC,IAAA,GACA;UAAAiC,KAAA;UAAArC,IAAA;QAAA,GACA;UAAAqC,KAAA;UAAArC,IAAA;QAAA,GACA;UAAAqC,KAAA;UAAArC,IAAA;QAAA,GACA;UAAAqC,KAAA;UAAArC,IAAA;QAAA,GACA;UAAAqC,KAAA;UAAArC,IAAA;QAAA,EACA;QACAsC,MAAA;UACAC,MAAA,GACA;YACAC,MAAA;YACAC,MAAA;YACAC,KAAA;cACAC,IAAA;YACA;YACAM,SAAA;cACAN,IAAA;YACA;UACA;QAEA;MACA;MACA;MACAkD,qBAAA;QACAxD,KAAA;QACAyD,IAAA;QACApD,KAAA;MACA;MACA;MACAqD,wBAAA,GACA;QAAA/F,IAAA;QAAAqC,KAAA;QAAAU,KAAA;MAAA,GACA;QAAA/C,IAAA;QAAAqC,KAAA;QAAAU,KAAA;MAAA,GACA;QAAA/C,IAAA;QAAAqC,KAAA;QAAAU,KAAA;MAAA,GACA;QAAA/C,IAAA;QAAAqC,KAAA;QAAAU,KAAA;MAAA,GACA;QAAA/C,IAAA;QAAAqC,KAAA;QAAAU,KAAA;MAAA,EACA;MACA;MACAiD,QAAA;QACAxB,IAAA;UACAC,GAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;QACA;QACAqB,SAAA;QACAC,UAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;QAAAlE,SAAA;QAAAhC,IAAA;MAAA;MACAmG,WAAA;QAAAnE,SAAA;QAAAG,MAAA;MAAA;MACAiE,WAAA;QAAAjE,MAAA;MAAA;MACAkE,aAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,eAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA;MACAC,OAAA;QACAC,OAAA;QACAC,CAAA;QACAC,CAAA;QACAC,KAAA;QACAC,OAAA;QACAC,KAAA;QACAnH,IAAA;MACA;MACA;MACAoH,6BAAA;MACA;MACAC,qBAAA;MACA;MACAC,gBAAA;QACAR,OAAA;QACAC,CAAA;QACAC,CAAA;QACAC,KAAA;QACAC,OAAA;QACAC,KAAA;QACAnH,IAAA;MACA;MACAuH,YAAA,MAAAC,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,sBAAA;IACA,KAAAC,qBAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,wBAAA;IACA,KAAAC,8BAAA;IACA,KAAAC,0BAAA;IACA,KAAAC,4BAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAAb,6BAAA;MACA,KAAAA,6BAAA,CAAAc,OAAA;MACA,KAAAd,6BAAA;IACA;;IAEA;IACA,KAAAG,YAAA,CAAAY,KAAA;EACA;EACAC,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA,EAAAC,KAAA;MACA;MACA;MACAC,OAAA,CAAAC,GAAA,mBAAAH,IAAA,CAAA1I,IAAA,EAAA0I,IAAA,CAAArG,KAAA;IACA;IAEA;IACAyG,WAAA,WAAAA,YAAA;MACA;IAAA,CACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAC,QAAA;MACA,SAAA3I,eAAA,KAAA2I,QAAA;QACA,KAAA3I,eAAA,GAAA2I,QAAA;QACA,KAAAjB,qBAAA;MACA;IACA;IAEA;IACAkB,oBAAA,WAAAA,qBAAAD,QAAA;MACA,SAAA1I,cAAA,KAAA0I,QAAA;QACA,KAAA1I,cAAA,GAAA0I,QAAA;QACA,KAAAhB,oBAAA;QACA,KAAAC,wBAAA;MACA;IACA;IAEA;IACAH,sBAAA,WAAAA,uBAAA;MAAA,IAAAoB,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAApJ,IAAA,EAAAqJ,EAAA;QAAA,WAAAJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA,GAAAD,QAAA,CAAAE,CAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAEAhB,OAAA,CAAAC,GAAA;cAAAc,QAAA,CAAAE,CAAA;cAAA,OACA,IAAAC,iCAAA;YAAA;cAAAN,QAAA,GAAAG,QAAA,CAAAI,CAAA;cAEA,IAAAP,QAAA,IAAAA,QAAA,CAAAQ,IAAA,YAAAR,QAAA,CAAApJ,IAAA;gBACAA,IAAA,GAAAoJ,QAAA,CAAApJ,IAAA,EAEA;gBACA8I,KAAA,CAAA3I,SAAA,CAAAK,iBAAA,GAAAR,IAAA,CAAA6J,KAAA;gBACAf,KAAA,CAAA3I,SAAA,CAAAG,gBAAA,GAAAN,IAAA,CAAA8J,OAAA;gBACAhB,KAAA,CAAA3I,SAAA,CAAAC,gBAAA,GAAAJ,IAAA,CAAA+J,OAAA;gBACAjB,KAAA,CAAA3I,SAAA,CAAAE,qBAAA,GAAAL,IAAA,CAAAgK,OAAA;gBACAlB,KAAA,CAAA3I,SAAA,CAAAI,cAAA,GAAAP,IAAA,CAAAiK,KAAA;;gBAEAzB,OAAA,CAAAC,GAAA,kBAAAK,KAAA,CAAA3I,SAAA;cACA;gBACAqI,OAAA,CAAA0B,IAAA,gBAAAd,QAAA;gBACAN,KAAA,CAAAqB,mBAAA;cACA;cAAAZ,QAAA,CAAAE,CAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,CAAA;cAAAH,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAnB,OAAA,CAAArB,KAAA,kBAAAkC,EAAA;cACAP,KAAA,CAAAqB,mBAAA;YAAA;cAAA,OAAAZ,QAAA,CAAAa,CAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IAEA;IAEA;IACAgB,mBAAA,WAAAA,oBAAA;MACA3B,OAAA,CAAAC,GAAA;MACA;MACA,KAAA4B,MAAA,CAAAC,UAAA;IACA;IAEA;IACA3C,qBAAA,WAAAA,sBAAA;MAAA,IAAA4C,MAAA;MAAA,WAAAxB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAsB,SAAA;QAAA,IAAApB,QAAA,EAAApJ,IAAA,EAAAyK,GAAA;QAAA,WAAAxB,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAoB,SAAA;UAAA,kBAAAA,SAAA,CAAAlB,CAAA,GAAAkB,SAAA,CAAAjB,CAAA;YAAA;cAAAiB,SAAA,CAAAlB,CAAA;cAEAhB,OAAA,CAAAC,GAAA,CACA,mBACA,aACA8B,MAAA,CAAAtK,eACA;cAAAyK,SAAA,CAAAjB,CAAA;cAAA,OACA,IAAAkB,gCAAA;gBACA/B,QAAA,EAAA2B,MAAA,CAAAtK;cACA;YAAA;cAFAmJ,QAAA,GAAAsB,SAAA,CAAAf,CAAA;cAIA,IAAAP,QAAA,IAAAA,QAAA,CAAAQ,IAAA,YAAAR,QAAA,CAAApJ,IAAA;gBACAA,IAAA,GAAAoJ,QAAA,CAAApJ,IAAA,EAEA;gBACAuK,MAAA,CAAAxJ,iBAAA,CAAAU,kBAAA,GAAAzB,IAAA,CAAA4K,IAAA;gBACAL,MAAA,CAAAxJ,iBAAA,CAAAW,eAAA,GAAA1B,IAAA,CAAA6K,KAAA;gBACAN,MAAA,CAAAxJ,iBAAA,CAAAY,iBAAA,GAAA3B,IAAA,CAAA8K,KAAA;gBACAP,MAAA,CAAAxJ,iBAAA,CAAAa,cAAA,GAAA5B,IAAA,CAAA+K,KAAA,GACA/K,IAAA,CAAA+K,KAAA,SACA;gBACAR,MAAA,CAAAxJ,iBAAA,CAAAc,gBAAA,GAAA7B,IAAA,CAAAgL,IAAA;gBACAT,MAAA,CAAAxJ,iBAAA,CAAAe,iBAAA,GAAA9B,IAAA,CAAAiL,KAAA;;gBAEAzC,OAAA,CAAAC,GAAA,kBAAA8B,MAAA,CAAAxJ,iBAAA;cACA;gBACAyH,OAAA,CAAA0B,IAAA,sBAAAd,QAAA;gBACAmB,MAAA,CAAAW,0BAAA;cACA;cAAAR,SAAA,CAAAjB,CAAA;cAAA;YAAA;cAAAiB,SAAA,CAAAlB,CAAA;cAAAiB,GAAA,GAAAC,SAAA,CAAAf,CAAA;cAEAnB,OAAA,CAAArB,KAAA,kBAAAsD,GAAA;cACAF,MAAA,CAAAW,0BAAA;YAAA;cAAA,OAAAR,SAAA,CAAAN,CAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IAEA;IAEA;IACAU,0BAAA,WAAAA,2BAAA;MACA1C,OAAA,CAAAC,GAAA;MACA,KAAA4B,MAAA,CAAAC,UAAA;IACA;IAEA;IACA1C,oBAAA,WAAAA,qBAAA;MAAA,IAAAuD,MAAA;MAAA,WAAApC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAkC,SAAA;QAAA,IAAAhC,QAAA,EAAApJ,IAAA,EAAAqL,GAAA;QAAA,WAAApC,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAgC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,CAAA,GAAA8B,SAAA,CAAA7B,CAAA;YAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAEAhB,OAAA,CAAAC,GAAA,CACA,mBACA,aACA0C,MAAA,CAAAjL,cACA;cAAAoL,SAAA,CAAA7B,CAAA;cAAA,OACA,IAAA8B,+BAAA;gBACA3C,QAAA,EAAAuC,MAAA,CAAAjL;cACA;YAAA;cAFAkJ,QAAA,GAAAkC,SAAA,CAAA3B,CAAA;cAIA,IAAAP,QAAA,IAAAA,QAAA,CAAAQ,IAAA,YAAAR,QAAA,CAAApJ,IAAA;gBACAA,IAAA,GAAAoJ,QAAA,CAAApJ,IAAA,EAEA;gBACAmL,MAAA,CAAAjI,gBAAA,CAAAG,WAAA,GAAArD,IAAA,CAAA4K,IAAA;gBACAO,MAAA,CAAAjI,gBAAA,CAAAI,gBAAA,GAAAtD,IAAA,CAAA+K,KAAA,GACA,CAAA/K,IAAA,CAAA+K,KAAA,QAAAS,OAAA,MACA;gBACA;gBACAL,MAAA,CAAAjI,gBAAA,CAAAuI,mBAAA,GAAAzL,IAAA,CAAA6K,KAAA;gBACAM,MAAA,CAAAjI,gBAAA,CAAAwI,qBAAA,GAAA1L,IAAA,CAAA8K,KAAA;gBACAK,MAAA,CAAAjI,gBAAA,CAAAyI,iBAAA,GAAA3L,IAAA,CAAAgL,IAAA;gBACAG,MAAA,CAAAjI,gBAAA,CAAA0I,kBAAA,GAAA5L,IAAA,CAAAiL,KAAA;;gBAEAzC,OAAA,CAAAC,GAAA,kBAAA0C,MAAA,CAAAjI,gBAAA;cACA;gBACAsF,OAAA,CAAA0B,IAAA,sBAAAd,QAAA;gBACA+B,MAAA,CAAAU,yBAAA;cACA;cAAAP,SAAA,CAAA7B,CAAA;cAAA;YAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAAA6B,GAAA,GAAAC,SAAA,CAAA3B,CAAA;cAEAnB,OAAA,CAAArB,KAAA,kBAAAkE,GAAA;cACAF,MAAA,CAAAU,yBAAA;YAAA;cAAA,OAAAP,SAAA,CAAAlB,CAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAEA;IAEA;IACAS,yBAAA,WAAAA,0BAAA;MACArD,OAAA,CAAAC,GAAA;MACA,KAAA4B,MAAA,CAAAC,UAAA;IACA;IAEA;IACAzC,wBAAA,WAAAA,yBAAA;MAAA,IAAAiE,MAAA;MAAA,WAAA/C,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA6C,SAAA;QAAA,IAAA3C,QAAA,EAAA4C,wBAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAlM,IAAA,EAAAmM,UAAA,EAAAC,GAAA;QAAA,WAAAnD,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA+C,SAAA;UAAA,kBAAAA,SAAA,CAAA7C,CAAA,GAAA6C,SAAA,CAAA5C,CAAA;YAAA;cAAA4C,SAAA,CAAA7C,CAAA;cAEAhB,OAAA,CAAAC,GAAA;cAAA4D,SAAA,CAAA5C,CAAA;cAAA,OACA,IAAA6C,mCAAA;gBACA1D,QAAA,EAAAkD,MAAA,CAAA5L;cACA;YAAA;cAFAkJ,QAAA,GAAAiD,SAAA,CAAA1C,CAAA;cAIA;cACAqC,wBAAA,IACA;gBAAA/J,KAAA;gBAAArC,IAAA;cAAA,GACA;gBAAAqC,KAAA;gBAAArC,IAAA;cAAA,GACA;gBAAAqC,KAAA;gBAAArC,IAAA;cAAA,GACA;gBAAAqC,KAAA;gBAAArC,IAAA;cAAA,GACA;gBAAAqC,KAAA;gBAAArC,IAAA;cAAA,EACA,EAEA;cACAqM,UAAA,IACA,WACA,WACA,WACA,WACA,UACA;cACAC,UAAA,IACA,WACA,WACA,WACA,WACA,UACA,EAEA;cACAJ,MAAA,CAAA5I,gBAAA,CAAAK,gBAAA;gBACAvB,SAAA,EAAAiK,UAAA;gBACAjM,IAAA,EAAAgM,wBAAA;gBACA9J,MAAA;kBACAC,MAAA,GACA;oBACAC,MAAA;oBACAC,MAAA;oBACAC,KAAA;sBACAC,IAAA;sBACAC,QAAA;sBACAC,SAAA;sBACAC,QAAA;sBACAC,KAAA;sBACAC,UAAA;oBACA;oBACAC,SAAA;sBACAN,IAAA;sBACAO,MAAA;sBACAC,OAAA;sBACAC,SAAA;wBACAL,KAAA;wBACAM,KAAA;sBACA;oBACA;kBACA;gBAEA;cACA;;cAEA;cACA,IACAmG,QAAA,IACAA,QAAA,CAAAQ,IAAA,YACAR,QAAA,CAAApJ,IAAA,IACAuM,KAAA,CAAAC,OAAA,CAAApD,QAAA,CAAApJ,IAAA,GACA;gBACAA,IAAA,GAAAoJ,QAAA,CAAApJ,IAAA,EACA;gBACAmM,UAAA,GAAAnM,IAAA,CACAyM,IAAA,WAAArC,CAAA,EAAAsC,CAAA;kBAAA,QAAAA,CAAA,CAAAzK,KAAA,UAAAmI,CAAA,CAAAnI,KAAA;gBAAA,GACA0K,KAAA,QAEA;gBACAb,MAAA,CAAA5I,gBAAA,CAAAM,gBAAA;kBACAxB,SAAA,EAAAkK,UAAA;kBACAlM,IAAA,EAAAmM,UAAA;kBACAjK,MAAA;oBACAC,MAAA,GACA;sBACAC,MAAA;sBACAC,MAAA;sBACAC,KAAA;wBACAC,IAAA;wBACAC,QAAA;wBACAC,SAAA;wBACAC,QAAA;wBACAC,KAAA;wBACAC,UAAA;sBACA;sBACAC,SAAA;wBACAN,IAAA;wBACAO,MAAA;wBACAC,OAAA;wBACAC,SAAA;0BACAL,KAAA;0BACAM,KAAA;wBACA;sBACA;oBACA;kBAEA;gBACA;gBAEAuF,OAAA,CAAAC,GAAA;kBACAmE,MAAA;kBACAC,MAAA,EAAAf,MAAA,CAAA5I,gBAAA,CAAAM;gBACA;cACA;gBACAgF,OAAA,CAAA0B,IAAA,sBAAAd,QAAA;gBACA0C,MAAA,CAAAgB,6BAAA;cACA;cAAAT,SAAA,CAAA5C,CAAA;cAAA;YAAA;cAAA4C,SAAA,CAAA7C,CAAA;cAAA4C,GAAA,GAAAC,SAAA,CAAA1C,CAAA;cAEAnB,OAAA,CAAArB,KAAA,kBAAAiF,GAAA;cACAN,MAAA,CAAAgB,6BAAA;YAAA;cAAA,OAAAT,SAAA,CAAAjC,CAAA;UAAA;QAAA,GAAA2B,QAAA;MAAA;IAEA;IAEA;IACAe,6BAAA,WAAAA,8BAAA;MACAtE,OAAA,CAAAC,GAAA;MACA;MACA,IAAAsE,oBAAA,IACA;QAAA9K,KAAA;QAAArC,IAAA;MAAA,GACA;QAAAqC,KAAA;QAAArC,IAAA;MAAA,GACA;QAAAqC,KAAA;QAAArC,IAAA;MAAA,GACA;QAAAqC,KAAA;QAAArC,IAAA;MAAA,GACA;QAAAqC,KAAA;QAAArC,IAAA;MAAA,EACA;MAEA,IAAAsM,UAAA,IACA,WACA,WACA,WACA,WACA,UACA;MAEA,KAAAhJ,gBAAA,CAAAM,gBAAA;QACAxB,SAAA,EAAAkK,UAAA;QACAlM,IAAA,EAAA+M,oBAAA;QACA7K,MAAA;UACAC,MAAA,GACA;YACAC,MAAA;YACAC,MAAA;YACAC,KAAA;cACAC,IAAA;cACAC,QAAA;cACAC,SAAA;cACAC,QAAA;cACAC,KAAA;cACAC,UAAA;YACA;YACAC,SAAA;cACAN,IAAA;cACAO,MAAA;cACAC,OAAA;cACAC,SAAA;gBACAL,KAAA;gBACAM,KAAA;cACA;YACA;UACA;QAEA;MACA;MACA,KAAAoH,MAAA,CAAAC,UAAA;IACA;IAEA;IACAxC,8BAAA,WAAAA,+BAAA;MAAA,IAAAkF,MAAA;MAAA,WAAAjE,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+D,SAAA;QAAA,IAAA7D,QAAA,EAAApJ,IAAA,EAAA6F,SAAA,EAAAqH,gBAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,eAAA,EAAAvL,SAAA,EAAAwL,GAAA;QAAA,WAAAvE,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAmE,SAAA;UAAA,kBAAAA,SAAA,CAAAjE,CAAA,GAAAiE,SAAA,CAAAhE,CAAA;YAAA;cAAAgE,SAAA,CAAAjE,CAAA;cAEAhB,OAAA,CAAAC,GAAA;cAAAgF,SAAA,CAAAhE,CAAA;cAAA,OACA,IAAAiE,yCAAA;YAAA;cAAAtE,QAAA,GAAAqE,SAAA,CAAA9D,CAAA;cAEA,IACAP,QAAA,IACAA,QAAA,CAAAQ,IAAA,YACAR,QAAA,CAAApJ,IAAA,IACAuM,KAAA,CAAAC,OAAA,CAAApD,QAAA,CAAApJ,IAAA,GACA;gBACAA,IAAA,GAAAoJ,QAAA,CAAApJ,IAAA,EAEA;gBACA6F,SAAA,GAAA7F,IAAA,CAAA2N,GAAA,WAAArF,IAAA;kBACA;kBACA,IAAAsF,WAAA,GAAAtF,IAAA,CAAAuF,OAAA;kBACA,OAAAD,WAAA,CAAA9K,MAAA,QACA8K,WAAA,CAAAE,SAAA,kBACAF,WAAA;gBACA,IAEA;gBACAV,gBAAA,GAAAlN,IAAA,CAAA2N,GAAA,CACA,UAAArF,IAAA;kBAAA,OAAAA,IAAA,CAAAuF,OAAA;gBAAA,CACA,GAEA;gBACAV,UAAA,GAAAnN,IAAA,CAAA2N,GAAA,WAAArF,IAAA,EAAAyF,KAAA;kBAAA;oBACA9L,KAAA,EAAA+L,UAAA,CAAA1F,IAAA,CAAA2F,kBAAA;oBACAC,QAAA,EAAAhB,gBAAA,CAAAa,KAAA;kBACA;gBAAA,IACA;gBACAX,UAAA,GAAApN,IAAA,CAAA2N,GAAA,WAAArF,IAAA,EAAAyF,KAAA;kBAAA;oBACA9L,KAAA,EAAA+L,UAAA,CAAA1F,IAAA,CAAA6F,iBAAA;oBACAD,QAAA,EAAAhB,gBAAA,CAAAa,KAAA;kBACA;gBAAA,IAEA;gBACAV,QAAA,GAAAe,IAAA,CAAAzJ,GAAA,CAAA0J,KAAA,CAAAD,IAAA,MAAAE,mBAAA,CAAAtF,OAAA,EACAmE,UAAA,CAAAQ,GAAA,WAAArF,IAAA;kBAAA,OAAAA,IAAA,CAAArG,KAAA;gBAAA,IAAAsM,MAAA,KAAAD,mBAAA,CAAAtF,OAAA,EACAoE,UAAA,CAAAO,GAAA,WAAArF,IAAA;kBAAA,OAAAA,IAAA,CAAArG,KAAA;gBAAA,IACA;gBACAqL,QAAA,GAAAc,IAAA,CAAAI,IAAA,CAAAnB,QAAA;gBAEA;gBACAE,eAAA,GAAAvN,IAAA,CAAAyO,MAAA,WAAAC,GAAA,EAAApG,IAAA;kBAAA,OAAAoG,GAAA,GAAAV,UAAA,CAAA1F,IAAA,CAAA6F,iBAAA;gBAAA,OAEA;gBACAnM,SAAA;gBAEAgL,MAAA,CAAAxH,wBAAA;kBACAxD,SAAA,EAAAA,SAAA;kBACAhC,IAAA,EAAAA,IAAA,CAAA2N,GAAA,WAAArF,IAAA,EAAAyF,KAAA;oBAAA;sBACA9L,KAAA,EAAA+L,UAAA,CAAA1F,IAAA,CAAA6F,iBAAA;sBACAvO,IAAA,EAAA0I,IAAA,CAAAuF,OAAA;oBACA;kBAAA;kBACA3L,MAAA;oBACAC,MAAA,GACA;sBACAC,MAAA;sBACAC,MAAA;sBACAC,KAAA;wBACAC,IAAA;sBACA;sBACAM,SAAA;wBACAN,IAAA;sBACA;oBACA;kBAEA;gBACA;;gBAEA;gBACAyK,MAAA,CAAAvH,qBAAA;kBACAxD,KAAA,EAAAmM,IAAA,CAAAO,KAAA,CAAApB,eAAA,EAAAqB,QAAA;kBACAlJ,IAAA;kBACApD,KAAA;gBACA;;gBAEA;gBACA0K,MAAA,CAAArH,wBAAA,GAAA3F,IAAA,CAAA2N,GAAA,WAAArF,IAAA,EAAAyF,KAAA;kBAAA;oBACAnO,IAAA,EAAA0I,IAAA,CAAAuF,OAAA;oBACA5L,KAAA,EAAAmM,IAAA,CAAAO,KAAA,CAAAX,UAAA,CAAA1F,IAAA,CAAA6F,iBAAA,QAAAS,QAAA;oBACAjM,KAAA,EAAAX,SAAA,CAAA+L,KAAA,GAAA/L,SAAA,CAAAc,MAAA;kBACA;gBAAA;gBAEA0F,OAAA,CAAAC,GAAA,CACA,mBACAuE,MAAA,CAAA6B,qBACA;cACA;gBACArG,OAAA,CAAA0B,IAAA,wBAAAd,QAAA;gBACA4D,MAAA,CAAA8B,mCAAA;cACA;cAAArB,SAAA,CAAAhE,CAAA;cAAA;YAAA;cAAAgE,SAAA,CAAAjE,CAAA;cAAAgE,GAAA,GAAAC,SAAA,CAAA9D,CAAA;cAEAnB,OAAA,CAAArB,KAAA,oBAAAqG,GAAA;cACAR,MAAA,CAAA8B,mCAAA;YAAA;cAAA,OAAArB,SAAA,CAAArD,CAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IAEA;IAEA;IACA6B,mCAAA,WAAAA,oCAAA;MACAtG,OAAA,CAAAC,GAAA;MACA,KAAA4B,MAAA,CAAAC,UAAA;IACA;IAEA;IACAvC,0BAAA,WAAAA,2BAAA;MAAA,IAAAgH,MAAA;MAAA,WAAAhG,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA8F,SAAA;QAAA,IAAA5F,QAAA,EAAApJ,IAAA,EAAAiP,WAAA,EAAA5B,QAAA,EAAA6B,YAAA,EAAAC,WAAA,EAAAC,GAAA;QAAA,WAAAnG,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA+F,SAAA;UAAA,kBAAAA,SAAA,CAAA7F,CAAA,GAAA6F,SAAA,CAAA5F,CAAA;YAAA;cAAA4F,SAAA,CAAA7F,CAAA;cAEAhB,OAAA,CAAAC,GAAA;cAAA4G,SAAA,CAAA5F,CAAA;cAAA,OACA,IAAA6F,qCAAA;YAAA;cAAAlG,QAAA,GAAAiG,SAAA,CAAA1F,CAAA;cAEA,IACAP,QAAA,IACAA,QAAA,CAAAQ,IAAA,YACAR,QAAA,CAAApJ,IAAA,IACAuM,KAAA,CAAAC,OAAA,CAAApD,QAAA,CAAApJ,IAAA,GACA;gBACAA,IAAA,GAAAoJ,QAAA,CAAApJ,IAAA,EAEA;gBACAiP,WAAA,GAAAjP,IAAA,CAAA2M,KAAA,QAEA;gBACAU,QAAA,GAAAe,IAAA,CAAAzJ,GAAA,CAAA0J,KAAA,CAAAD,IAAA,MAAAE,mBAAA,CAAAtF,OAAA,EAAAhJ,IAAA,CAAA2N,GAAA,WAAArF,IAAA;kBAAA,OAAAA,IAAA,CAAArG,KAAA;gBAAA,MAEA;gBACAiN,YAAA,GAAAD,WAAA,CAAAtB,GAAA,WAAArF,IAAA;kBACA;kBACA,OAAAA,IAAA,CAAA1I,IAAA,IAAA0I,IAAA,CAAA1I,IAAA,CAAAkD,MAAA,QACAwF,IAAA,CAAA1I,IAAA,CAAAkO,SAAA,kBACAxF,IAAA,CAAA1I,IAAA;gBACA,IAEA;gBACAuP,WAAA,GAAAF,WAAA,CAAAtB,GAAA,WAAArF,IAAA,EAAAyF,KAAA;kBAAA;oBACAnO,IAAA,EAAAsP,YAAA,CAAAnB,KAAA;oBACA9L,KAAA,EAAAqG,IAAA,CAAArG,KAAA;oBACAiM,QAAA,EAAA5F,IAAA,CAAA1I,IAAA;oBACA2P,SAAA,EAAAjH,IAAA,CAAAiH,SAAA;kBACA;gBAAA,IAEA;gBACAR,MAAA,CAAA5K,qBAAA,CAAAa,KAAA,CAAAhF,IAAA,GAAAkP,YAAA,CAAAM,OAAA;gBACAT,MAAA,CAAA5K,qBAAA,CAAAhC,MAAA,IAAAnC,IAAA,GAAAmP,WAAA,CAAAK,OAAA;gBACAT,MAAA,CAAA5K,qBAAA,CAAAM,KAAA,CAAAE,GAAA,GAAA0I,QAAA;;gBAEA;gBACA0B,MAAA,CAAA9K,qBAAA,GAAAgL,WAAA,CAAAtB,GAAA,WAAArF,IAAA;kBAAA;oBACA1I,IAAA,EAAA0I,IAAA,CAAA1I,IAAA;oBACAqC,KAAA,EAAAqG,IAAA,CAAArG,KAAA;oBACAwN,YAAA,EAAAnH,IAAA,CAAA1I,IAAA;oBACA2P,SAAA,EAAAjH,IAAA,CAAAiH,SAAA;kBACA;gBAAA;;gBAEA;gBACAR,MAAA,CAAAW,SAAA;kBACAX,MAAA,CAAAY,yBAAA;gBACA;gBAEAnH,OAAA,CAAAC,GAAA,kBAAAsG,MAAA,CAAA5K,qBAAA;cACA;gBACAqE,OAAA,CAAA0B,IAAA,sBAAAd,QAAA;gBACA2F,MAAA,CAAAa,+BAAA;cACA;cAAAP,SAAA,CAAA5F,CAAA;cAAA;YAAA;cAAA4F,SAAA,CAAA7F,CAAA;cAAA4F,GAAA,GAAAC,SAAA,CAAA1F,CAAA;cAEAnB,OAAA,CAAArB,KAAA,kBAAAiI,GAAA;cACAL,MAAA,CAAAa,+BAAA;YAAA;cAAA,OAAAP,SAAA,CAAAjF,CAAA;UAAA;QAAA,GAAA4E,QAAA;MAAA;IAEA;IAEA;IACAY,+BAAA,WAAAA,gCAAA;MAAA,IAAAC,MAAA;MACArH,OAAA,CAAAC,GAAA;MACA,KAAA4B,MAAA,CAAAC,UAAA;MACA;MACA,KAAAoF,SAAA;QACAG,MAAA,CAAAF,yBAAA;MACA;IACA;IAEA;IACAA,yBAAA,WAAAA,0BAAA;MAAA,IAAAG,MAAA;MACA,SAAAC,KAAA,CAAA5L,qBAAA;QACA;QACA,SAAAiD,6BAAA;UACA,KAAAA,6BAAA,CAAAc,OAAA;QACA;;QAEA;QACA,KAAAd,6BAAA,GAAA/H,OAAA,CAAA2Q,IAAA,CACA,KAAAD,KAAA,CAAA5L,qBACA;;QAEA;QACA,IAAAjC,MAAA;UACAkC,IAAA,OAAAD,qBAAA,CAAAC,IAAA;UACAK,KAAA,OAAAN,qBAAA,CAAAM,KAAA;UACAO,KAAA,OAAAb,qBAAA,CAAAa,KAAA;UACA7C,MAAA,OAAAgC,qBAAA,CAAAhC,MAAA;UACA0E,OAAA;YACAtE,IAAA;YACA0N,OAAA;YACAC,eAAA;YACAC,WAAA;YACAC,WAAA;YACAC,SAAA;cACA1N,KAAA;cACAD,QAAA;YACA;YACA4N,YAAA,EACA;YACA9N,QAAA,WAAAA,SAAA+N,KAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAC,IAAA,EAAAC,IAAA;cACA;cACA,IAAAC,YAAA,GAAAD,IAAA,CAAAE,WAAA;cACA,IAAAC,aAAA,GAAAH,IAAA,CAAAE,WAAA;;cAEA;cACA,IAAAE,UAAA,GAAAL,IAAA,CAAAzN,KAAA;cACA,IAAA+N,WAAA,GAAAN,IAAA,CAAAO,MAAA;;cAEA;cACA,IAAAC,UAAA,GAAAH,UAAA,GAAAR,KAAA;cACA,IAAAY,SAAA,GAAAZ,KAAA;cAEA,IAAAxJ,CAAA,EAAAC,CAAA;;cAEA;cACA,IAAAkK,UAAA,IAAAN,YAAA,IAAAM,UAAA,GAAAC,SAAA;gBACA;gBACApK,CAAA,GAAAwJ,KAAA;gBACA;gBACA,IAAAxJ,CAAA,GAAA6J,YAAA,GAAAG,UAAA;kBACAhK,CAAA,GAAAgK,UAAA,GAAAH,YAAA;gBACA;cACA,WAAAO,SAAA,IAAAP,YAAA;gBACA;gBACA7J,CAAA,GAAAwJ,KAAA,MAAAK,YAAA;gBACA;gBACA,IAAA7J,CAAA;kBACAA,CAAA;gBACA;cACA;gBACA;gBACAA,CAAA,GAAAqH,IAAA,CAAAzJ,GAAA,CACA,GACAyJ,IAAA,CAAAgD,GAAA,CAAAb,KAAA,UAAAQ,UAAA,GAAAH,YAAA,KACA;cACA;;cAEA;cACA5J,CAAA,GAAAuJ,KAAA,MAAAO,aAAA;cACA,IAAA9J,CAAA;gBACAA,CAAA;cACA,WAAAA,CAAA,GAAA8J,aAAA,GAAAE,WAAA;gBACAhK,CAAA,GAAAgK,WAAA,GAAAF,aAAA;cACA;cAEA,QAAA/J,CAAA,EAAAC,CAAA;YACA;YACAvE,SAAA,WAAAA,UAAA+N,MAAA;cACA,IAAAA,MAAA,CAAAxQ,IAAA,QAAAqR,QAAA,CAAArI,OAAA,EAAAwH,MAAA,CAAAxQ,IAAA;gBACA,IAAAsR,YAAA,GAAAd,MAAA,CAAAxQ,IAAA;kBAAAkO,QAAA,GAAAoD,YAAA,CAAApD,QAAA;kBAAAjM,KAAA,GAAAqP,YAAA,CAAArP,KAAA;kBAAAsN,SAAA,GAAA+B,YAAA,CAAA/B,SAAA;gBAEA,IAAAgC,IAAA,yEAAAhD,MAAA,CAAAL,QAAA;gBACAqD,IAAA,iHAAAhD,MAAA,CAAAtM,KAAA;gBAEA,IAAAsN,SAAA,IAAAA,SAAA,CAAAzM,MAAA;kBACAyO,IAAA,IACA;kBACAA,IAAA,IACA;kBACAhC,SAAA,CAAAiC,OAAA,WAAAlJ,IAAA;oBACAiJ,IAAA,sJAAAhD,MAAA,CAEAjG,IAAA,CAAA1I,IAAA,yGAAA2O,MAAA,CAGAjG,IAAA,CAAArG,KAAA,4CAEA;kBACA;kBACAsP,IAAA;gBACA;kBACAA,IAAA,IACA;gBACA;gBAEA,OAAAA,IAAA;cACA;;cAEA;cACA,wBAAAhD,MAAA,CAAAiC,MAAA,CAAA5Q,IAAA,yBAAA2O,MAAA,CAAAiC,MAAA,CAAAvO,KAAA;YACA;UACA;QACA;QAEA,KAAAmF,6BAAA,CAAAqK,SAAA,CAAAvP,MAAA;;QAEA;QACAwP,MAAA,CAAAC,gBAAA;UACA,IAAA7B,MAAA,CAAA1I,6BAAA;YACA0I,MAAA,CAAA1I,6BAAA,CAAAwK,MAAA;UACA;QACA;MACA;IACA;IAEA;IACA5J,4BAAA,WAAAA,6BAAA;MAAA,IAAA6J,MAAA;MAAA,WAAA9I,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA4I,SAAA;QAAA,IAAA1I,QAAA,EAAApJ,IAAA,EAAA+R,cAAA,EAAAC,WAAA,EAAA3E,QAAA,EAAAC,QAAA,EAAA2E,GAAA;QAAA,WAAAhJ,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAA4I,SAAA;UAAA,kBAAAA,SAAA,CAAA1I,CAAA,GAAA0I,SAAA,CAAAzI,CAAA;YAAA;cAAAyI,SAAA,CAAA1I,CAAA;cAEAhB,OAAA,CAAAC,GAAA;cAAAyJ,SAAA,CAAAzI,CAAA;cAAA,OACA,IAAA0I,uCAAA;YAAA;cAAA/I,QAAA,GAAA8I,SAAA,CAAAvI,CAAA;cAEA,IACAP,QAAA,IACAA,QAAA,CAAAQ,IAAA,YACAR,QAAA,CAAApJ,IAAA,IACAuM,KAAA,CAAAC,OAAA,CAAApD,QAAA,CAAApJ,IAAA,GACA;gBACAA,IAAA,GAAAoJ,QAAA,CAAApJ,IAAA,EAEA;gBACA+R,cAAA,GAAA/R,IAAA,CAAA2N,GAAA,WAAArF,IAAA;kBAAA,OAAAA,IAAA,CAAA1I,IAAA;gBAAA,IAEA;gBACAoS,WAAA,GAAAhS,IAAA,CAAA2N,GAAA,WAAArF,IAAA;kBAAA,OAAAA,IAAA,CAAArG,KAAA;gBAAA,IACA;gBACAoL,QAAA,GAAAe,IAAA,CAAAzJ,GAAA,CAAA0J,KAAA,CAAAD,IAAA,MAAAE,mBAAA,CAAAtF,OAAA,EAAAgJ,WAAA;gBACA1E,QAAA,GAAAD,QAAA,OAAAA,QAAA;gBAEA;gBACAwE,MAAA,CAAAxM,mBAAA;kBACArD,SAAA,GACA,WACA,WACA,WACA,WACA,WACA,UACA;kBACAoC,IAAA;oBACAC,GAAA;oBACAC,IAAA;oBACAC,KAAA;oBACAC,MAAA;kBACA;kBACA4N,MAAA,EAAAC,SAAA;kBAAA;kBACA5N,KAAA;oBACAC,IAAA;oBACA1E,IAAA,EAAA+R,cAAA;oBACAnN,SAAA;sBACAK,QAAA;sBACAK,MAAA;sBACA5C,QAAA;oBACA;kBACA;kBACAsC,KAAA;oBACAN,IAAA;oBACAC,GAAA,EAAA2I,QAAA,OAAAA,QAAA;oBACA8D,GAAA;oBACAnM,QAAA,EAAAqI,QAAA,OAAAc,IAAA,CAAAI,IAAA,CAAAlB,QAAA;oBAAA;oBACA1I,SAAA;sBACAlC,QAAA;oBACA;kBACA;kBACAP,MAAA,GACA;oBACAvC,IAAA;oBACA8E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAvC,KAAA;oBAAA;oBACA3C,IAAA,MAAAuM,KAAA,CAAAvM,IAAA,CAAA8C,MAAA,EAAAwP,IAAA;kBACA,GACA;oBACA1S,IAAA;oBACA8E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAvC,KAAA;oBAAA;oBACA3C,IAAA,MAAAuM,KAAA,CAAAvM,IAAA,CAAA8C,MAAA,EAAAwP,IAAA;kBACA,GACA;oBACA1S,IAAA;oBACA8E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAvC,KAAA;oBAAA;oBACA3C,IAAA,MAAAuM,KAAA,CAAAvM,IAAA,CAAA8C,MAAA,EAAAwP,IAAA;kBACA,GACA;oBACA1S,IAAA;oBACA8E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAvC,KAAA;oBAAA;oBACA3C,IAAA,EAAAgS,WAAA;kBACA,GACA;oBACApS,IAAA;oBACA8E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAvC,KAAA;oBAAA;oBACA3C,IAAA,MAAAuM,KAAA,CAAAvM,IAAA,CAAA8C,MAAA,EAAAwP,IAAA;kBACA,GACA;oBACA1S,IAAA;oBACA8E,IAAA;oBACAa,KAAA;oBACAL,SAAA;sBAAAvC,KAAA;oBAAA;oBACA3C,IAAA,MAAAuM,KAAA,CAAAvM,IAAA,CAAA8C,MAAA,EAAAwP,IAAA;kBACA;gBAEA;gBAEA9J,OAAA,CAAAC,GAAA,kBAAAoJ,MAAA,CAAAxM,mBAAA;cACA;gBACAmD,OAAA,CAAA0B,IAAA,sBAAAd,QAAA;gBACAyI,MAAA,CAAAU,iCAAA;cACA;cAAAL,SAAA,CAAAzI,CAAA;cAAA;YAAA;cAAAyI,SAAA,CAAA1I,CAAA;cAAAyI,GAAA,GAAAC,SAAA,CAAAvI,CAAA;cAEAnB,OAAA,CAAArB,KAAA,kBAAA8K,GAAA;cACAJ,MAAA,CAAAU,iCAAA;YAAA;cAAA,OAAAL,SAAA,CAAA9H,CAAA;UAAA;QAAA,GAAA0H,QAAA;MAAA;IAEA;IAEA;IACAS,iCAAA,WAAAA,kCAAA;MACA/J,OAAA,CAAAC,GAAA;MACA,KAAA4B,MAAA,CAAAC,UAAA;IACA;IAEA;IACAkI,oBAAA,WAAAA,qBAAAC,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAA3J,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAyJ,SAAA;QAAA,IAAAvJ,QAAA,EAAAwJ,GAAA;QAAA,WAAA3J,aAAA,CAAAD,OAAA,IAAAM,CAAA,WAAAuJ,SAAA;UAAA,kBAAAA,SAAA,CAAArJ,CAAA,GAAAqJ,SAAA,CAAApJ,CAAA;YAAA;cAAA,KAEAiJ,MAAA,CAAArL,qBAAA,CAAAoL,aAAA;gBAAAI,SAAA,CAAApJ,CAAA;gBAAA;cAAA;cAAA,OAAAoJ,SAAA,CAAAzI,CAAA;YAAA;cAAAyI,SAAA,CAAArJ,CAAA;cAKAhB,OAAA,CAAAC,GAAA,gBAAAgK,aAAA;cAAAI,SAAA,CAAApJ,CAAA;cAAA,OACA,IAAAqJ,6CAAA,EAAAL,aAAA;YAAA;cAAArJ,QAAA,GAAAyJ,SAAA,CAAAlJ,CAAA;cAEA,IACAP,QAAA,IACAA,QAAA,CAAAQ,IAAA,YACAR,QAAA,CAAApJ,IAAA,IACAuM,KAAA,CAAAC,OAAA,CAAApD,QAAA,CAAApJ,IAAA,GACA;gBACA;gBACA0S,MAAA,CAAArL,qBAAA,CAAAoL,aAAA,IAAArJ,QAAA,CAAApJ,IAAA,CAAA2N,GAAA,CACA,UAAArF,IAAA;kBAAA;oBACA1I,IAAA,EAAA0I,IAAA,CAAA1I,IAAA;oBACAqC,KAAA,EAAAqG,IAAA,CAAArG,KAAA;oBACA8Q,cAAA,EAAAzK,IAAA,CAAAyK;kBACA;gBAAA,CACA;gBAEAvK,OAAA,CAAAC,GAAA,CACA,eACAgK,aAAA,EACAC,MAAA,CAAArL,qBAAA,CAAAoL,aAAA,CACA;;gBAEA;gBACA,IACAC,MAAA,CAAApL,gBAAA,CAAAR,OAAA,IACA4L,MAAA,CAAApL,gBAAA,CAAAL,KAAA,KAAAwL,aAAA,EACA;kBACAC,MAAA,CAAApL,gBAAA,CAAAJ,OAAA;kBACAwL,MAAA,CAAApL,gBAAA,CAAAtH,IAAA,GACA0S,MAAA,CAAArL,qBAAA,CAAAoL,aAAA;gBACA;cACA;gBACA;gBACAC,MAAA,CAAArL,qBAAA,CAAAoL,aAAA;gBACAjK,OAAA,CAAA0B,IAAA,sBAAAd,QAAA;gBACA,IACAsJ,MAAA,CAAApL,gBAAA,CAAAR,OAAA,IACA4L,MAAA,CAAApL,gBAAA,CAAAL,KAAA,KAAAwL,aAAA,EACA;kBACAC,MAAA,CAAApL,gBAAA,CAAAJ,OAAA;kBACAwL,MAAA,CAAApL,gBAAA,CAAAH,KAAA;gBACA;cACA;cAAA0L,SAAA,CAAApJ,CAAA;cAAA;YAAA;cAAAoJ,SAAA,CAAArJ,CAAA;cAAAoJ,GAAA,GAAAC,SAAA,CAAAlJ,CAAA;cAEA;cACA+I,MAAA,CAAArL,qBAAA,CAAAoL,aAAA;cACAjK,OAAA,CAAArB,KAAA,gBAAAyL,GAAA;cACA,IACAF,MAAA,CAAApL,gBAAA,CAAAR,OAAA,IACA4L,MAAA,CAAApL,gBAAA,CAAAL,KAAA,KAAAwL,aAAA,EACA;gBACAC,MAAA,CAAApL,gBAAA,CAAAJ,OAAA;gBACAwL,MAAA,CAAApL,gBAAA,CAAAH,KAAA;cACA;YAAA;cAAA,OAAA0L,SAAA,CAAAzI,CAAA;UAAA;QAAA,GAAAuI,QAAA;MAAA;IAEA;IAEA;IACAK,wBAAA,WAAAA,yBAAAzK,KAAA;MAAA,IAAA0K,qBAAA;MACA;MACA,IAAAvC,IAAA,GAAAnI,KAAA,CAAA2K,aAAA,CAAAC,qBAAA;MACA,IAAApM,CAAA,GAAAwB,KAAA,CAAA6K,OAAA,GAAA1C,IAAA,CAAApM,IAAA;MACA,IAAAyM,UAAA,GAAAL,IAAA,CAAAzN,KAAA;;MAEA;MACA,IAAA8O,cAAA,KAAAkB,qBAAA,QAAA5N,mBAAA,CAAAZ,KAAA,cAAAwO,qBAAA,uBAAAA,qBAAA,CAAAjT,IAAA;MACA,IAAA+R,cAAA,CAAAjP,MAAA;MAEA,IAAAuQ,cAAA,GAAAjF,IAAA,CAAAkF,KAAA,CACAvM,CAAA,GAAAgK,UAAA,GAAAgB,cAAA,CAAAjP,MACA;MACA,IAAAuQ,cAAA,SAAAA,cAAA,GAAAtB,cAAA,CAAAjP,MAAA;QACA,IAAA2P,aAAA,GAAAV,cAAA,CAAAsB,cAAA;;QAEA;QACA,KAAA/L,gBAAA,CAAAR,OAAA;QACA,KAAAQ,gBAAA,CAAAP,CAAA,GAAAwB,KAAA,CAAA6K,OAAA;QACA,KAAA9L,gBAAA,CAAAN,CAAA,GAAAuB,KAAA,CAAAgL,OAAA;QACA,KAAAjM,gBAAA,CAAAL,KAAA,GAAAwL,aAAA;;QAEA;QACA,IAAAe,UAAA,QAAAnM,qBAAA,CAAAoL,aAAA;QACA,IAAAe,UAAA;UACA,KAAAlM,gBAAA,CAAAJ,OAAA;UACA,KAAAI,gBAAA,CAAAH,KAAA;UACA,KAAAG,gBAAA,CAAAtH,IAAA,GAAAwT,UAAA;QACA;UACA,KAAAlM,gBAAA,CAAAJ,OAAA;UACA,KAAAI,gBAAA,CAAAH,KAAA;UACA,KAAAG,gBAAA,CAAAtH,IAAA;;UAEA;UACA,KAAAwS,oBAAA,CAAAC,aAAA;QACA;MACA;IACA;IAEA;IACAgB,yBAAA,WAAAA,0BAAA;MACA,KAAAnM,gBAAA,CAAAR,OAAA;MACA,KAAAQ,gBAAA,CAAAtH,IAAA;MACA,KAAAsH,gBAAA,CAAAH,KAAA;MACA,KAAAG,gBAAA,CAAAJ,OAAA;IACA;IAEA;IACAwM,mBAAA,WAAAA,oBAAA9F,WAAA;MAAA,IAAA+F,SAAA,GAAAC,SAAA,CAAA9Q,MAAA,QAAA8Q,SAAA,QAAAvB,SAAA,GAAAuB,SAAA;MACA,KAAAhG,WAAA;MACA,IAAAA,WAAA,CAAA9K,MAAA,IAAA6Q,SAAA,SAAA/F,WAAA;MACA,OAAAA,WAAA,CAAAE,SAAA,IAAA6F,SAAA;IACA;EACA;AACA", "ignoreList": []}]}