<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="统计编号" prop="statNo">
        <el-input
          v-model="queryParams.statNo"
          placeholder="请输入统计编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计周期" prop="statPeriod">
        <el-input
          v-model="queryParams.statPeriod"
          placeholder="请输入统计周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="施工单位" prop="constructionUnit">
        <el-input
          v-model="queryParams.constructionUnit"
          placeholder="请输入施工单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全管理人员数量" prop="safetyManagerCount">
        <el-input
          v-model="queryParams.safetyManagerCount"
          placeholder="请输入安全管理人员数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="专职安全员数量" prop="safetySupervisorCount">
        <el-input
          v-model="queryParams.safetySupervisorCount"
          placeholder="请输入专职安全员数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="特种作业人员总数" prop="specialOperationStaffCount">
        <el-input
          v-model="queryParams.specialOperationStaffCount"
          placeholder="请输入特种作业人员总数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="持证上岗特种作业人员数量"
        prop="qualifiedSpecialStaffCount"
      >
        <el-input
          v-model="queryParams.qualifiedSpecialStaffCount"
          placeholder="请输入持证上岗特种作业人员数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="特种作业人员持证率"
        prop="specialStaffQualificationRate"
      >
        <el-input
          v-model="queryParams.specialStaffQualificationRate"
          placeholder="请输入特种作业人员持证率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安管人员本月培训总时长" prop="safetyTrainingHours">
        <el-input
          v-model="queryParams.safetyTrainingHours"
          placeholder="请输入安管人员本月培训总时长"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="塔吊数量" prop="towerCraneCount">
        <el-input
          v-model="queryParams.towerCraneCount"
          placeholder="请输入塔吊数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合格塔吊数量" prop="qualifiedTowerCraneCount">
        <el-input
          v-model="queryParams.qualifiedTowerCraneCount"
          placeholder="请输入合格塔吊数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="挖掘机数量" prop="excavatorCount">
        <el-input
          v-model="queryParams.excavatorCount"
          placeholder="请输入挖掘机数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合格挖掘机数量" prop="qualifiedExcavatorCount">
        <el-input
          v-model="queryParams.qualifiedExcavatorCount"
          placeholder="请输入合格挖掘机数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="施工升降机数量" prop="elevatorCount">
        <el-input
          v-model="queryParams.elevatorCount"
          placeholder="请输入施工升降机数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合格施工升降机数量" prop="qualifiedElevatorCount">
        <el-input
          v-model="queryParams.qualifiedElevatorCount"
          placeholder="请输入合格施工升降机数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="其他大型设备数量" prop="otherEquipmentCount">
        <el-input
          v-model="queryParams.otherEquipmentCount"
          placeholder="请输入其他大型设备数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="合格其他大型设备数量"
        prop="qualifiedOtherEquipmentCount"
      >
        <el-input
          v-model="queryParams.qualifiedOtherEquipmentCount"
          placeholder="请输入合格其他大型设备数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="大型设备总数" prop="totalEquipmentCount">
        <el-input
          v-model="queryParams.totalEquipmentCount"
          placeholder="请输入大型设备总数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="合格大型设备总数"
        prop="totalQualifiedEquipmentCount"
      >
        <el-input
          v-model="queryParams.totalQualifiedEquipmentCount"
          placeholder="请输入合格大型设备总数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备合格使用率" prop="equipmentQualificationRate">
        <el-input
          v-model="queryParams.equipmentQualificationRate"
          placeholder="请输入设备合格使用率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="本月设备检查总次数" prop="equipmentInspectionTimes">
        <el-input
          v-model="queryParams.equipmentInspectionTimes"
          placeholder="请输入本月设备检查总次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计编制人" prop="compiler">
        <el-input
          v-model="queryParams.compiler"
          placeholder="请输入统计编制人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计审核人" prop="reviewer">
        <el-input
          v-model="queryParams.reviewer"
          placeholder="请输入统计审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计审批人" prop="approver">
        <el-input
          v-model="queryParams.approver"
          placeholder="请输入统计审批人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制时间" prop="compileTime">
        <el-date-picker
          clearable
          v-model="queryParams.compileTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择编制时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审批时间" prop="approveTime">
        <el-date-picker
          clearable
          v-model="queryParams.approveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审批时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:equipment:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:equipment:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:equipment:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:equipment:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="equipmentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="统计编号" align="center" prop="statNo" />
      <el-table-column label="统计周期" align="center" prop="statPeriod" />
      <!-- 月度/季度 -->
      <el-table-column label="统计类型" align="center" prop="statType" />
      <el-table-column label="项目编码" align="center" prop="projectCode" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="项目地址" align="center" prop="projectAddress" />
      <!-- 在建/停工/待建 -->
      <el-table-column label="项目状态" align="center" prop="projectStatus" />
      <el-table-column
        label="施工单位"
        align="center"
        prop="constructionUnit"
      />
      <el-table-column
        label="安全管理人员数量"
        align="center"
        prop="safetyManagerCount"
      />
      <el-table-column
        label="专职安全员数量"
        align="center"
        prop="safetySupervisorCount"
      />
      <el-table-column
        label="特种作业人员总数"
        align="center"
        prop="specialOperationStaffCount"
      />
      <el-table-column
        label="持证上岗特种作业人员数量"
        align="center"
        prop="qualifiedSpecialStaffCount"
      />
      <!-- <el-table-column
        label="特种作业人员持证率"
        align="center"
        prop="specialStaffQualificationRate"
      />
      <el-table-column
        label="安管人员本月培训总时长"
        align="center"
        prop="safetyTrainingHours"
      />
      <el-table-column label="塔吊数量" align="center" prop="towerCraneCount" />
      <el-table-column
        label="合格塔吊数量"
        align="center"
        prop="qualifiedTowerCraneCount"
      />
      <el-table-column
        label="挖掘机数量"
        align="center"
        prop="excavatorCount"
      />
      <el-table-column
        label="合格挖掘机数量"
        align="center"
        prop="qualifiedExcavatorCount"
      />
      <el-table-column
        label="施工升降机数量"
        align="center"
        prop="elevatorCount"
      />
      <el-table-column
        label="合格施工升降机数量"
        align="center"
        prop="qualifiedElevatorCount"
      />
      <el-table-column
        label="其他大型设备数量"
        align="center"
        prop="otherEquipmentCount"
      />
      <el-table-column
        label="合格其他大型设备数量"
        align="center"
        prop="qualifiedOtherEquipmentCount"
      />
      <el-table-column
        label="大型设备总数"
        align="center"
        prop="totalEquipmentCount"
      />
      <el-table-column
        label="合格大型设备总数"
        align="center"
        prop="totalQualifiedEquipmentCount"
      />
      <el-table-column
        label="设备合格使用率"
        align="center"
        prop="equipmentQualificationRate"
      />
      <el-table-column
        label="本月设备检查总次数"
        align="center"
        prop="equipmentInspectionTimes"
      />
      <el-table-column label="统计说明" align="center" prop="statDesc" />
      <el-table-column
        label="统计状态：草稿/审核中/已审核/已发布"
        align="center"
        prop="statStatus"
      />
      <el-table-column
        label="统计表文件地址"
        align="center"
        prop="statFileUrl"
      />
      <el-table-column label="附件地址" align="center" prop="attachmentUrl" />
      <el-table-column label="统计编制人" align="center" prop="compiler" />
      <el-table-column label="统计审核人" align="center" prop="reviewer" />
      <el-table-column label="统计审批人" align="center" prop="approver" />
      <el-table-column
        label="编制时间"
        align="center"
        prop="compileTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.compileTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approveTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:equipment:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:equipment:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改在建项目安管人员及施工机械设备使用情况对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="统计编号" prop="statNo">
          <el-input v-model="form.statNo" placeholder="请输入统计编号" />
        </el-form-item>
        <el-form-item label="统计周期" prop="statPeriod">
          <el-input v-model="form.statPeriod" placeholder="请输入统计周期" />
        </el-form-item>
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目地址" prop="projectAddress">
          <el-input
            v-model="form.projectAddress"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="施工单位" prop="constructionUnit">
          <el-input
            v-model="form.constructionUnit"
            placeholder="请输入施工单位"
          />
        </el-form-item>
        <el-form-item label="安全管理人员数量" prop="safetyManagerCount">
          <el-input
            v-model="form.safetyManagerCount"
            placeholder="请输入安全管理人员数量"
          />
        </el-form-item>
        <el-form-item label="专职安全员数量" prop="safetySupervisorCount">
          <el-input
            v-model="form.safetySupervisorCount"
            placeholder="请输入专职安全员数量"
          />
        </el-form-item>
        <el-form-item
          label="特种作业人员总数"
          prop="specialOperationStaffCount"
        >
          <el-input
            v-model="form.specialOperationStaffCount"
            placeholder="请输入特种作业人员总数"
          />
        </el-form-item>
        <el-form-item
          label="持证上岗特种作业人员数量"
          prop="qualifiedSpecialStaffCount"
        >
          <el-input
            v-model="form.qualifiedSpecialStaffCount"
            placeholder="请输入持证上岗特种作业人员数量"
          />
        </el-form-item>
        <el-form-item
          label="特种作业人员持证率"
          prop="specialStaffQualificationRate"
        >
          <el-input
            v-model="form.specialStaffQualificationRate"
            placeholder="请输入特种作业人员持证率"
          />
        </el-form-item>
        <el-form-item label="安管人员本月培训总时长" prop="safetyTrainingHours">
          <el-input
            v-model="form.safetyTrainingHours"
            placeholder="请输入安管人员本月培训总时长"
          />
        </el-form-item>
        <el-form-item label="塔吊数量" prop="towerCraneCount">
          <el-input
            v-model="form.towerCraneCount"
            placeholder="请输入塔吊数量"
          />
        </el-form-item>
        <el-form-item label="合格塔吊数量" prop="qualifiedTowerCraneCount">
          <el-input
            v-model="form.qualifiedTowerCraneCount"
            placeholder="请输入合格塔吊数量"
          />
        </el-form-item>
        <el-form-item label="挖掘机数量" prop="excavatorCount">
          <el-input
            v-model="form.excavatorCount"
            placeholder="请输入挖掘机数量"
          />
        </el-form-item>
        <el-form-item label="合格挖掘机数量" prop="qualifiedExcavatorCount">
          <el-input
            v-model="form.qualifiedExcavatorCount"
            placeholder="请输入合格挖掘机数量"
          />
        </el-form-item>
        <el-form-item label="施工升降机数量" prop="elevatorCount">
          <el-input
            v-model="form.elevatorCount"
            placeholder="请输入施工升降机数量"
          />
        </el-form-item>
        <el-form-item label="合格施工升降机数量" prop="qualifiedElevatorCount">
          <el-input
            v-model="form.qualifiedElevatorCount"
            placeholder="请输入合格施工升降机数量"
          />
        </el-form-item>
        <el-form-item label="其他大型设备数量" prop="otherEquipmentCount">
          <el-input
            v-model="form.otherEquipmentCount"
            placeholder="请输入其他大型设备数量"
          />
        </el-form-item>
        <el-form-item
          label="合格其他大型设备数量"
          prop="qualifiedOtherEquipmentCount"
        >
          <el-input
            v-model="form.qualifiedOtherEquipmentCount"
            placeholder="请输入合格其他大型设备数量"
          />
        </el-form-item>
        <el-form-item label="大型设备总数" prop="totalEquipmentCount">
          <el-input
            v-model="form.totalEquipmentCount"
            placeholder="请输入大型设备总数"
          />
        </el-form-item>
        <el-form-item
          label="合格大型设备总数"
          prop="totalQualifiedEquipmentCount"
        >
          <el-input
            v-model="form.totalQualifiedEquipmentCount"
            placeholder="请输入合格大型设备总数"
          />
        </el-form-item>
        <el-form-item label="设备合格使用率" prop="equipmentQualificationRate">
          <el-input
            v-model="form.equipmentQualificationRate"
            placeholder="请输入设备合格使用率"
          />
        </el-form-item>
        <el-form-item
          label="本月设备检查总次数"
          prop="equipmentInspectionTimes"
        >
          <el-input
            v-model="form.equipmentInspectionTimes"
            placeholder="请输入本月设备检查总次数"
          />
        </el-form-item>
        <el-form-item label="统计说明" prop="statDesc">
          <el-input
            v-model="form.statDesc"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="统计表文件地址" prop="statFileUrl">
          <el-input
            v-model="form.statFileUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="附件地址" prop="attachmentUrl">
          <el-input
            v-model="form.attachmentUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="统计编制人" prop="compiler">
          <el-input v-model="form.compiler" placeholder="请输入统计编制人" />
        </el-form-item>
        <el-form-item label="统计审核人" prop="reviewer">
          <el-input v-model="form.reviewer" placeholder="请输入统计审核人" />
        </el-form-item>
        <el-form-item label="统计审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="请输入统计审批人" />
        </el-form-item>
        <el-form-item label="编制时间" prop="compileTime">
          <el-date-picker
            clearable
            v-model="form.compileTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择编制时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批时间" prop="approveTime">
          <el-date-picker
            clearable
            v-model="form.approveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审批时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEquipment,
  getEquipment,
  delEquipment,
  addEquipment,
  updateEquipment,
} from "@/api/system/constructionMachineryEquipment/index";

export default {
  name: "Equipment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 在建项目安管人员及施工机械设备使用情况表格数据
      equipmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        statNo: null,
        statPeriod: null,
        statType: null,
        projectCode: null,
        projectName: null,
        projectAddress: null,
        projectStatus: null,
        constructionUnit: null,
        safetyManagerCount: null,
        safetySupervisorCount: null,
        specialOperationStaffCount: null,
        qualifiedSpecialStaffCount: null,
        specialStaffQualificationRate: null,
        safetyTrainingHours: null,
        towerCraneCount: null,
        qualifiedTowerCraneCount: null,
        excavatorCount: null,
        qualifiedExcavatorCount: null,
        elevatorCount: null,
        qualifiedElevatorCount: null,
        otherEquipmentCount: null,
        qualifiedOtherEquipmentCount: null,
        totalEquipmentCount: null,
        totalQualifiedEquipmentCount: null,
        equipmentQualificationRate: null,
        equipmentInspectionTimes: null,
        statDesc: null,
        statStatus: null,
        statFileUrl: null,
        attachmentUrl: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        statNo: [
          { required: true, message: "统计编号不能为空", trigger: "blur" },
        ],
        statPeriod: [
          { required: true, message: "统计周期不能为空", trigger: "blur" },
        ],
        statType: [
          {
            required: true,
            message: "统计类型：月度/季度不能为空",
            trigger: "change",
          },
        ],
        projectCode: [
          { required: true, message: "项目编码不能为空", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        constructionUnit: [
          { required: true, message: "施工单位不能为空", trigger: "blur" },
        ],
        compiler: [
          { required: true, message: "统计编制人不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询在建项目安管人员及施工机械设备使用情况列表 */
    getList() {
      this.loading = true;
      listEquipment(this.queryParams).then((response) => {
        this.equipmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        statNo: null,
        statPeriod: null,
        statType: null,
        projectCode: null,
        projectName: null,
        projectAddress: null,
        projectStatus: null,
        constructionUnit: null,
        safetyManagerCount: null,
        safetySupervisorCount: null,
        specialOperationStaffCount: null,
        qualifiedSpecialStaffCount: null,
        specialStaffQualificationRate: null,
        safetyTrainingHours: null,
        towerCraneCount: null,
        qualifiedTowerCraneCount: null,
        excavatorCount: null,
        qualifiedExcavatorCount: null,
        elevatorCount: null,
        qualifiedElevatorCount: null,
        otherEquipmentCount: null,
        qualifiedOtherEquipmentCount: null,
        totalEquipmentCount: null,
        totalQualifiedEquipmentCount: null,
        equipmentQualificationRate: null,
        equipmentInspectionTimes: null,
        statDesc: null,
        statStatus: null,
        statFileUrl: null,
        attachmentUrl: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加在建项目安管人员及施工机械设备使用情况";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEquipment(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改在建项目安管人员及施工机械设备使用情况";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateEquipment(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEquipment(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除在建项目安管人员及施工机械设备使用情况编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delEquipment(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/equipment/export",
        {
          ...this.queryParams,
        },
        `equipment_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
