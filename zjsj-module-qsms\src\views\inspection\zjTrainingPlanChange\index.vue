<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="90px"
    >
      <el-form-item label="培训标题" prop="trainingTitle">
        <el-input
          v-model="queryParams.trainingTitle"
          placeholder="请输入培训标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="变更申请人" prop="changeApplicant">
        <el-input
          v-model="queryParams.changeApplicant"
          placeholder="请输入变更申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="变更摘要" prop="changeAbstract">
        <el-input
          v-model="queryParams.changeAbstract"
          placeholder="请输入变更摘要"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="applicationTime">
        <el-date-picker
          clearable
          v-model="queryParams.applicationTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择申请时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="changeStatus">
        <!--     // 0-待审核 1审核通过 2审核不通过 -->
        <el-select v-model="queryParams.changeStatus" placeholder="请选择">
          <el-option label="待审核" value="0" />
          <el-option label="审核通过" value="1" />
          <el-option label="审核不通过" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjTrainingPlanChange:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjTrainingPlanChange:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjTrainingPlanChange:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjTrainingPlanChange:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjTrainingPlanChangeList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 300px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="培训标题" align="center" prop="trainingTitle" />
      <el-table-column
        label="变更申请人"
        align="center"
        prop="changeApplicant"
      />
      <el-table-column label="变更摘要" align="center" prop="changeAbstract" />
      <el-table-column
        label="申请时间"
        align="center"
        prop="applicationTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- 0-待审核 1审核通过 2审核不通过 -->
      <el-table-column label="状态 " align="center" prop="changeStatus">
        <template slot-scope="scope">
          <span>{{ changeStatusMap[scope.row.changeStatus] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjTrainingPlanChange:edit']"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjTrainingPlanChange:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改培训计划变更对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="培训标题" prop="trainingTitle">
          <el-input v-model="form.trainingTitle" placeholder="请输入培训标题" />
        </el-form-item>
        <el-form-item label="变更申请人" prop="changeApplicant">
          <el-input
            v-model="form.changeApplicant"
            placeholder="请输入变更申请人"
          />
        </el-form-item>
        <el-form-item label="变更摘要" prop="changeAbstract">
          <el-input
            v-model="form.changeAbstract"
            placeholder="请输入变更摘要"
          />
        </el-form-item>
        <el-form-item label="申请时间" prop="applicationTime">
          <el-date-picker
            clearable
            v-model="form.applicationTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择申请时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjTrainingPlanChange,
  getZjTrainingPlanChange,
  delZjTrainingPlanChange,
  addZjTrainingPlanChange,
  updateZjTrainingPlanChange,
} from "@/api/inspection/zjTrainingPlanChange";

export default {
  name: "ZjTrainingPlanChange",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 培训计划变更表格数据
      zjTrainingPlanChangeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        trainingTitle: null,
        changeApplicant: null,
        changeAbstract: null,
        applicationTime: null,
        changeStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 0-待审核 1审核通过 2审核不通过
      changeStatusMap: {
        0: "待审核",
        1: "审核通过",
        2: "审核不通过",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询培训计划变更列表 */
    getList() {
      this.loading = true;
      listZjTrainingPlanChange(this.queryParams).then((response) => {
        this.zjTrainingPlanChangeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        trainingTitle: null,
        changeApplicant: null,
        changeAbstract: null,
        applicationTime: null,
        changeStatus: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加培训计划变更";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjTrainingPlanChange(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改培训计划变更";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjTrainingPlanChange(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.changeStatus = 0;
            addZjTrainingPlanChange(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除培训计划变更编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjTrainingPlanChange(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjTrainingPlanChange/export",
        {
          ...this.queryParams,
        },
        `zjTrainingPlanChange_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
