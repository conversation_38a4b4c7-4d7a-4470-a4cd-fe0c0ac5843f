<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="隐患类别名称" prop="hazardCategoryName">
        <el-input
          v-model="queryParams.hazardCategoryName"
          placeholder="请输入隐患类别名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!-- <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:hazardCategory:add']"
        >
          新增
        </el-button> -->
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
          >展开/折叠</el-button
        >
      </el-col>
    </el-row>

    <!-- 隐患类别表格 -->
    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="hazardCategoryList"
      style="width: 100%"
      row-key="hazardId"
      :default-expand-all="isExpandAll"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      height="calc(100vh - 220px) "
    >
      <el-table-column label="序号" align="center" width="100">
        <template #default="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column label="隐患类别名称" align="center" prop="hazardName" />
      <!-- 编码 -->
      <el-table-column label="编码" align="center" prop="hazardCode" />
      <!-- 备注 -->
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <!-- <template #default="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:hazardCategory:edit']"
          >
            修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:hazardCategory:remove']"
          >
            删除
          </el-button>
        </template> -->
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <!-- <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    /> -->
  </div>
</template>

<script>
import { treeHazard } from "@/api/inspection/hazard";

export default {
  data() {
    return {
      loading: false,
      showSearch: true,
      total: 0,
      isExpandAll: true,
      refreshTable: true,
      hazardCategoryList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hazardName: null,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    toggleExpandAll() {
      //   console.log("1111");
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      //   console.log(this.isExpandAll, "this.isExpandAll");
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    // 获取隐患类别列表
    getList() {
      this.loading = true;
      treeHazard(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.hazardCategoryList = res.rows;
            this.total = res.total;
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 搜索操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置操作
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        hazardCategoryName: null,
      };
      this.handleQuery();
    },
    // 新增操作
    handleAdd() {
      // 实现新增逻辑
      console.log("新增隐患类别");
    },
    // 修改操作
    handleUpdate(row) {
      // 实现修改逻辑
      console.log("修改隐患类别", row);
    },
    // 删除操作
    handleDelete(row) {
      this.$confirm("确定要删除该隐患类别吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delHazardCategory(row.id).then(() => {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          this.getList();
        });
      });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
