<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

export default {
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            chart: null,
            colorList: [
                '#3C80E8',
                '#8EE98F',
                '#A1FFEB',
                '#54C255',
                '#A1CDFF',
                '#FF920D',
                '#FECF77',
                '#F3B2B1',
                '#B38DFF'
            ]
        }
    },
    watch: {
        data: {
            handler(newVal, oldVal) {
                if (this.chart) {
                    // 更新图表
                    console.log(newVal)
                    this.data = newVal
                    this.setOption()
                } else {
                    // 初始化图表
                    this.$nextTick(() => {
                        this.initChart()
                    })
                }
            },
            immediate: true
        }
    },
    mounted() {

    },
    beforeD<PERSON>roy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
        window.removeEventListener('resize', this.chartResize)
    },
    methods: {
        chartResize() {
            this.chart.resize()
        },
        initChart() {
            const that = this
            that.chart = echarts.init(that.$el)
            window.addEventListener('resize', that.chartResize)
            that.chart.on('click', (params) => {
                that.$emit('pieClick', params.data)
            })
            that.setOption()
        },
        setOption() {
            // 构建默认配置
            const defaultOption = {
                tooltip: {},
                series: [
                    {
                        type: 'pie',
                        radius: ['60%', '94%'],
                        color: this.data.colorList,
                        data: this.data.data,
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        label: {
                            normal: {
                                position: 'inner',
                                formatter: '{d}%',
                                textStyle: {
                                    color: '#fff',
                                    fontSize: 12,
                                    textShadowColor: 'rgba(0, 0, 0, 0.5)',
                                    textShadowBlur: 10
                                }
                            }
                        }
                    }
                ]
            }

            // 合并用户传入的配置
            const option = { ...defaultOption, ...this.data.option }

            // 如果传入了series配置，需要合并series
            if (this.data.option && this.data.option.series) {
                option.series = [{
                    ...defaultOption.series[0],
                    ...this.data.option.series[0]
                }]
            }

            this.chart.setOption(option)
        }
    }
}
</script>
