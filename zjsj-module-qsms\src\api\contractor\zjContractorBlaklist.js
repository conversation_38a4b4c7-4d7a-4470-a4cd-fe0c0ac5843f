import request from '@/utils/request'

// 查询承包商人员黑名单列表
export function listZjContractorBlaklist(query) {
  return request({
    url: '/contractor/zjContractorBlaklist/list',
    method: 'get',
    params: query
  })
}

// 查询承包商人员黑名单详细
export function getZjContractorBlaklist(id) {
  return request({
    url: '/contractor/zjContractorBlaklist/' + id,
    method: 'get'
  })
}

// 新增承包商人员黑名单
export function addZjContractorBlaklist(data) {
  return request({
    url: '/contractor/zjContractorBlaklist',
    method: 'post',
    data: data
  })
}

// 修改承包商人员黑名单
export function updateZjContractorBlaklist(data) {
  return request({
    url: '/contractor/zjContractorBlaklist',
    method: 'put',
    data: data
  })
}

// 删除承包商人员黑名单
export function delZjContractorBlaklist(id) {
  return request({
    url: '/contractor/zjContractorBlaklist/' + id,
    method: 'delete'
  })
}

// 获取可拉黑的承包商人员信息
export function getUserInfo(type = 1) {
  return request({
    url: '/contractor/zjContractorBlaklist/getUserInfo',
    method: 'get',
    params: { type }
  })
}