import request from '@/utils/request'

// 查询职工伤亡事故列表
export function listZjEmployeeCasualtyAccidents(query) {
  return request({
    url: '/inspection/zjEmployeeCasualtyAccidents/list',
    method: 'get',
    params: query
  })
}

// 查询职工伤亡事故详细
export function getZjEmployeeCasualtyAccidents(id) {
  return request({
    url: '/inspection/zjEmployeeCasualtyAccidents/' + id,
    method: 'get'
  })
}

// 新增职工伤亡事故
export function addZjEmployeeCasualtyAccidents(data) {
  return request({
    url: '/inspection/zjEmployeeCasualtyAccidents',
    method: 'post',
    data: data
  })
}

// 修改职工伤亡事故
export function updateZjEmployeeCasualtyAccidents(data) {
  return request({
    url: '/inspection/zjEmployeeCasualtyAccidents',
    method: 'put',
    data: data
  })
}

// 删除职工伤亡事故
export function delZjEmployeeCasualtyAccidents(id) {
  return request({
    url: '/inspection/zjEmployeeCasualtyAccidents/' + id,
    method: 'delete'
  })
}
