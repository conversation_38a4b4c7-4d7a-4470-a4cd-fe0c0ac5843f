<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24">
        <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true"
          label-width="68px">
          <el-form-item label="问题类别" prop="questionCategory">
            <el-input v-model="queryParams.questionCategory" placeholder="请输入问题类别" clearable
              @keyup.enter.native="handleQuery" style="width: 180px" />
          </el-form-item>
          <el-form-item label="问题描述" prop="commonProblem">
            <el-input v-model="queryParams.commonProblem" placeholder="请输入问题描述" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <!-- <el-form-item label="整改要求" prop="rectificationRequirements">
            <el-input
              v-model="queryParams.rectificationRequirements"
              placeholder="请输入整改要求"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="问题级别" prop="problemLevel">
            <el-input
              v-model="queryParams.problemLevel"
              placeholder="请输入问题级别"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="整改时限(天)" prop="rectificationDeadline">
            <el-input
              v-model="queryParams.rectificationDeadline"
              placeholder="请输入整改时限(天)"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['inspection:zjQualityProblemInfo:add']" type="primary" plain icon="el-icon-plus"
              size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-dropdown @command="handleCommand">
              <el-button v-hasPermi="['inspection:hazard:edit']" type="success" plain icon="el-icon-edit"
                size="mini">修改状态<i class="el-icon-arrow-down el-icon--right" /></el-button>
              <template #dropdown>
                <el-dropdown-menu style="width: 100px; text-align: center">
                  <!-- 下拉选项，可根据实际需求修改 -->
                  <el-dropdown-item command="enable">启用</el-dropdown-item>
                  <el-dropdown-item command="disable">禁用</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['inspection:zjQualityProblemInfo:remove']" type="danger" plain icon="el-icon-delete"
              size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['inspection:zjQualityProblemInfo:export']" type="warning" plain
              icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
        </el-row>

        <el-table v-loading="loading" :data="zjQualityProblemInfoList" height="calc(100vh - 250px)"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="主键" align="center" prop="qualityId" />
          <el-table-column label="父类" align="center" prop="parentId" /> -->
          <!-- <el-table-column
            label="分部分项工程"
            align="center"
            prop="itemName"
          />
          <el-table-column label="序号" align="center" prop="qualityCode" /> -->
          <el-table-column label="问题类别" align="center" prop="questionCategory" width="300" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div style="text-align: left;">
                {{ row.commonProblem }}
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="问题描述" align="center" prop="commonProblem" width="300" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div style="text-align: left;margin-left: 40px;">
                {{ row.commonProblem }}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="整改要求"
            align="center"
            prop="rectificationRequirements"
          /> -->
          <el-table-column label="整改时限(天)" align="center" prop="rectificationDeadline" show-overflow-tooltip />
          <el-table-column label="问题等级" align="center" prop="problemLevel">
            <template slot-scope="scope">
              <span v-if="scope.row.problemLevel === '严重问题'">
                <el-tag type="danger">{{ scope.row.problemLevel }}</el-tag>
              </span>
              <span v-else>
                <el-tag type="success">{{ scope.row.problemLevel }}</el-tag>
              </span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="qualityStatus">
            <template slot-scope="scope">
              <span v-if="scope.row.qualityStatus === '0'">
                <el-tag type="success">启用</el-tag>
              </span>
              <span v-else-if="scope.row.qualityStatus === '1'">
                <el-tag type="danger">禁用</el-tag>
              </span>
              <span v-else>
                <el-tag type="info">-</el-tag>
              </span>
            </template>
          </el-table-column>

          <!-- <el-table-column label="质量类别" align="center" prop="qualityType" /> -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
            <template slot-scope="scope">
              <el-button v-hasPermi="['inspection:zjQualityProblemInfo:edit']" size="mini" type="text"
                icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button v-hasPermi="['inspection:zjQualityProblemInfo:remove']" size="mini" type="text"
                icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改质量问题库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- <el-form-item label="父类" prop="parentId">
          <el-input v-model="form.parentId" placeholder="请输入父类" />
        </el-form-item>
        <el-form-item label="分部分项工程" prop="itemName">
          <el-input v-model="form.itemName" placeholder="请输入分部分项工程" />
        </el-form-item>
        <el-form-item label="序号" prop="qualityCode">
          <el-input v-model="form.qualityCode" placeholder="请输入序号" />
        </el-form-item> -->
        <el-form-item label="问题类别" prop="questionCategory">
          <!-- 修改模式下只读显示 -->
          <div v-if="form.qualityId != null" style="padding: 8px 0; min-height: 32px; line-height: 16px;">
            {{ form.questionCategory }}
          </div>
          <!-- 新增模式下可选择 -->
          <el-cascader
            v-else
            v-model="form.cascaderValue"
            :options="cascaderOptions"
            :props="dialogCascaderProps"
            placeholder="请选择问题类别"
            clearable
            filterable
            style="width: 100%"
            @change="handleDialogCascaderChange"
          />
        </el-form-item>
        <el-form-item label="问题等级" prop="problemLevel">
          <!-- <el-input v-model="form.problemLevel" placeholder="请输入问题级别" /> -->
          <!-- 严重问题 一般问题 -->
          <el-select v-model="form.problemLevel" placeholder="请选择问题等级" style="width: 100%">
            <el-option label="严重问题" value="严重问题" />
            <el-option label="一般问题" value="一般问题" />
          </el-select>
        </el-form-item>
        <el-form-item label="问题描述" prop="commonProblem">
          <el-input v-model="form.commonProblem" placeholder="请输入常见问题" type="textarea" rows="5" />
        </el-form-item>

        <!-- <el-form-item label="整改要求" prop="rectificationRequirements">
          <el-input
            v-model="form.rectificationRequirements"
            placeholder="请输入整改要求"
            type="textarea"
            rows="5"
          />
        </el-form-item> -->

        <el-form-item label="整改时限(天)" prop="rectificationDeadline">
          <el-input v-model="form.rectificationDeadline" type="number" min="0" style="width: 100%"
            placeholder="请输入整改时限(天)" />
        </el-form-item>
        <el-form-item label="状态" prop="qualityStatus">
          <el-select v-model="form.qualityStatus" placeholder="请选择状态" style="width: 100%">
            <el-option label="启用" value="0" />
            <el-option label="禁用" value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjQualityProblemInfo,
  getZjQualityProblemInfo,
  delZjQualityProblemInfo,
  addZjQualityProblemInfo,
  updateZjQualityProblemInfo,
  listZjQualityProblemCategory,
  listZjQualityProblemCategoryFirst
} from '@/api/inspection/zjQualityProblemInfo'

export default {
  name: 'ZjQualityProblemInfo',
  data() {
    return {
      qualityType: '房建',
      qualityTypeList: [
        { id: 1, label: '房建' },
        { id: 2, label: '市政' },
        { id: 3, label: '公路' },
        { id: 4, label: '铁路' },
        { id: 5, label: '水利' },
        { id: 6, label: '桥梁' },
        { id: 7, label: '隧道' },
        { id: 8, label: '地铁' },
        { id: 9, label: '港口航道' },
        { id: 10, label: '通用' }
      ],
      // 级联选择器数据
      cascaderValue: [],
      cascaderOptions: [],
      cascaderProps: {
        value: 'id',
        label: 'label',
        children: 'children',
        expandTrigger: 'hover',
        lazy: true,
        lazyLoad: this.loadCascaderData
      },
      // 对话框级联选择器配置（与查询用的略有不同）
      dialogCascaderProps: {
        value: 'id',
        label: 'label',
        children: 'children',
        expandTrigger: 'hover',
        lazy: true,
        lazyLoad: this.loadDialogCascaderData
      },
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质量问题库表格数据
      zjQualityProblemInfoList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentId: null,
        itemName: null,
        qualityCode: null,
        commonProblem: null,
        rectificationRequirements: null,
        problemLevel: null,
        status: null,
        rectificationDeadline: null,
        questionCategory: null
      },
      // 表单参数
      form: {
        problemLevel: '一般问题',
        questionCategory: null,
        cascaderValue: [], // 级联选择器的值
        qualityStatus: 0
      },
      // 表单校验
      rules: {
        questionCategory: [
          { required: true, message: '问题类别不能为空', trigger: ['change', 'blur'] }
        ],
        problemLevel: [
          { required: true, message: '问题等级不能为空', trigger: 'change' }
        ],
        commonProblem: [
          { required: true, message: '问题描述不能为空', trigger: 'blur' }
        ],
        rectificationDeadline: [
          { required: true, message: '整改时限(天)不能为空', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '整改时限必须为正整数', trigger: 'blur' }
        ],
        qualityStatus: [
          { required: true, message: '状态不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.initCascaderOptions()
    this.getList() // 页面初始化时默认查询数据
  },
  methods: {
    // 初始化级联选择器选项
    initCascaderOptions() {
      this.cascaderOptions = this.qualityTypeList.map(item => ({
        id: item.label,
        label: item.label,
        children: [],
        leaf: false
      }))
    },
    
    // 级联选择器懒加载数据
    loadCascaderData(node, resolve) {
      const { level, data } = node
      
      if (level === 0) {
        // 第一级：工程类型
        const options = this.qualityTypeList.map(item => ({
          id: item.label,
          label: item.label,
          leaf: false
        }))
        resolve(options)
      } else if (level === 1) {
        // 第二级：根据工程类型加载分部分项工程
        const qualityType = data.id
        const params = {
          parentId: '0',
          qualityType: qualityType
        }
        listZjQualityProblemCategoryFirst(params).then((res) => {
          if (res.code === 200 && res.rows) {
            const children = res.rows.map(item => ({
              id: item.qualityId,
              label: item.itemName,
              parentType: qualityType,
              leaf: false
            }))
            resolve(children)
          } else {
            resolve([])
          }
        }).catch(() => {
          resolve([])
        })
      } else if (level >= 2) {
        // 第三级及以上：加载子分类
        const parentId = data.id
        listZjQualityProblemCategory({ parentId }).then((res) => {
          if (res.code === 200 && res.rows) {
            const children = res.rows.map(item => ({
              id: item.qualityId,
              label: item.itemName,
              parentType: data.parentType,
              leaf: !item.children || item.children.length === 0
            }))
            resolve(children)
          } else {
            resolve([])
          }
        }).catch(() => {
          resolve([])
        })
      }
    },
    
    // 级联选择器选择事件
    handleCascaderChange(value) {
      this.cascaderValue = value
      if (value && value.length > 0) {
        // 设置质量类型（第一级选择）
        this.qualityType = value[0]
        // 设置parentId（最后一级选择）
        this.queryParams.parentId = value[value.length - 1]
      } else {
        this.qualityType = '房建'
        this.queryParams.parentId = null
      }
      this.handleQuery()
    },
    
    // 对话框级联选择器懒加载数据
    loadDialogCascaderData(node, resolve) {
      const { level, data } = node
      
      if (level === 0) {
        // 第一级：工程类型
        const options = this.qualityTypeList.map(item => ({
          id: item.label,
          label: item.label,
          leaf: false
        }))
        resolve(options)
      } else if (level === 1) {
        // 第二级：根据工程类型加载分部分项工程
        const qualityType = data.id
        const params = {
          parentId: '0',
          qualityType: qualityType
        }
        listZjQualityProblemCategoryFirst(params).then((res) => {
          if (res.code === 200 && res.rows) {
            const children = res.rows.map(item => ({
              id: item.qualityId,
              label: item.itemName,
              parentType: qualityType,
              leaf: false
            }))
            resolve(children)
          } else {
            resolve([])
          }
        }).catch(() => {
          resolve([])
        })
      } else if (level >= 2) {
        // 第三级及以上：加载子分类
        const parentId = data.id
        listZjQualityProblemCategory({ parentId }).then((res) => {
          if (res.code === 200 && res.rows) {
            const children = res.rows.map(item => ({
              id: item.qualityId,
              label: item.itemName,
              parentType: data.parentType,
              leaf: !item.children || item.children.length === 0
            }))
            resolve(children)
          } else {
            resolve([])
          }
        }).catch(() => {
          resolve([])
        })
      }
    },
    
    // 对话框级联选择器选择事件
    handleDialogCascaderChange(value) {
      this.form.cascaderValue = value
      if (value && value.length > 0) {
        // 设置质量类型（第一级选择）
        this.form.qualityType = value[0]
        // 设置parentId（最后一级选择）
        this.form.parentId = value[value.length - 1]
        // 设置questionCategory为最后一级的label（需要获取label）
        this.getSelectedCategoryLabel(value).then(() => {
          // 获取分类标签后，手动触发表单验证
          this.$nextTick(() => {
            if (this.$refs['form']) {
              this.$refs['form'].validateField('questionCategory')
            }
          })
        }).catch(() => {
          // 即使获取失败也要触发验证
          this.$nextTick(() => {
            if (this.$refs['form']) {
              this.$refs['form'].validateField('questionCategory')
            }
          })
        })
      } else {
        this.form.qualityType = null
        this.form.parentId = null
        this.form.questionCategory = null
        // 清空时也需要触发验证
        this.$nextTick(() => {
          if (this.$refs['form']) {
            this.$refs['form'].validateField('questionCategory')
          }
        })
      }
    },
    
    // 获取选中分类的标签
    async getSelectedCategoryLabel(value) {
      if (!value || value.length === 0) {
        return Promise.resolve()
      }
      
      try {
        const lastLevelId = value[value.length - 1]
        const lastLevelIndex = value.length - 1
        
        if (lastLevelIndex === 0) {
          // 只选择了工程类型
          this.form.questionCategory = value[0]
          return Promise.resolve()
        } else if (lastLevelIndex === 1) {
          // 选择了第二级
          const params = { parentId: '0', qualityType: value[0] }
          const res = await listZjQualityProblemCategoryFirst(params)
          if (res.code === 200 && res.rows) {
            const item = res.rows.find(row => row.qualityId === lastLevelId)
            this.form.questionCategory = item ? item.itemName : null
          }
          return Promise.resolve()
        } else {
          // 选择了第三级及以上
          const params = { parentId: value[lastLevelIndex - 1] }
          const res = await listZjQualityProblemCategory(params)
          if (res.code === 200 && res.rows) {
            const item = res.rows.find(row => row.qualityId === lastLevelId)
            this.form.questionCategory = item ? item.itemName : null
          }
          return Promise.resolve()
        }
      } catch (error) {
        console.error('获取分类标签失败:', error)
        this.form.questionCategory = null
        return Promise.reject(error)
      }
    },
    
    handleCommand(command) {
      if (this.ids.length <= 0) {
        this.$message({
          message: '请选择质量问题条目',
          type: 'warning'
        })
        return
      }
      // console.log(this.ids, "this.ids");
      const status = command === 'enable' ? 0 : 1
      const promises = this.ids.map((item) => {
        return updateZjQualityProblemInfo({
          qualityId: item.qualityId,
          qualityStatus: status
        })
      })
      Promise.allSettled(promises).then((res) => {
        const successCount = res.filter(
          (item) => item.status === 'fulfilled'
        ).length
        const failedResults = res.filter((item) => item.status === 'rejected')
        if (successCount > 0) {
          this.$message({
            message: `${command === 'enable' ? '启用' : '禁用'}成功`,
            type: 'success'
          })
          this.handleQuery()
        } else {
          const errorMessages = failedResults
            .map((result, index) => {
              const id = this.ids[index].serialNumber
              const errorMsg = `${command === 'enable' ? '启用' : '禁用'}失败`
              return `序号为 ${id} 的质量问题条目：${errorMsg}`
            })
            .join('\n')

          this.$message({
            message: `${errorMessages}`,
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
      })
    },
    /** 查询质量问题库列表 */
    getList() {
      this.loading = true
      listZjQualityProblemInfo(this.queryParams).then((res) => {
        this.zjQualityProblemInfoList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        qualityId: null,
        parentId: null,
        itemName: null,
        qualityCode: null,
        commonProblem: null,
        rectificationRequirements: null,
        problemLevel: '一般问题',
        questionCategory: null,
        cascaderValue: [],
        qualityStatus: 0,
        status: null,
        rectificationDeadline: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        qualityType: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      // parentId 由级联选择器设置，不再使用 selectId
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.cascaderValue = []
      this.qualityType = '房建'
      this.queryParams.parentId = null
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.qualityId);
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 如果有选中的级联选择器值，则将其设置为新增项的父节点
      if (this.cascaderValue && this.cascaderValue.length > 0) {
        this.form.parentId = this.cascaderValue[this.cascaderValue.length - 1]
        this.form.qualityType = this.qualityType
      }
      this.open = true
      this.title = '添加质量问题库'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const qualityId = row.qualityId || this.ids
      getZjQualityProblemInfo(qualityId).then((res) => {
        this.form = res.data
        // 确保问题类别字段从表格行数据中获取
        this.form.questionCategory = row.questionCategory
        this.open = true
        this.title = '修改质量问题库'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.qualityId != null) {
            updateZjQualityProblemInfo(this.form).then((res) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addZjQualityProblemInfo(this.form).then((res) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const qualityIds = row && row.qualityId ? row.qualityId : this.ids
      const message = row && row.qualityId
        ? `是否确认删除质量问题库编号为"${qualityIds}"的数据项？`
        : `是否确认删除选中的${this.ids.length}条数据项？`
      
      this.$modal
        .confirm(message)
        .then(function () {
          return delZjQualityProblemInfo(qualityIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 如果有选中记录，导出选中的；否则导出全部
      if (this.ids.length > 0) {
        // 导出选中记录
        this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条记录？').then(() => {
          this.download(
            'inspection/zjQualityProblemInfo/export',
            {
              ids: this.ids.join(',')
            },
            `zjQualityProblemInfo_selected_${new Date().getTime()}.xlsx`
          )
        })
      } else {
        // 导出全部记录（根据查询条件，但不包含分页参数）
        const exportParams = { ...this.queryParams }
        // 移除分页参数，确保导出全部数据
        delete exportParams.pageNum
        delete exportParams.pageSize

        this.download(
          'inspection/zjQualityProblemInfo/export',
          exportParams,
          `zjQualityProblemInfo_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
}
</script>
<style scoped>
.title {
  font-size: 16px;
  cursor: pointer;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-tree-node.is-current>.el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}

.two-lines-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}
</style>
