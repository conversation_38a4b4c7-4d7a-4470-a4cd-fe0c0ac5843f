<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">检查列表</div>
            <div class="top-right">
              <el-button
                type="primary"
                icon="el-icon-download"
                size="small"
                @click="exportExcel"
                >导出</el-button
              >
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="120px"
          >
            <el-form-item label="检查类型" prop="examid">
              <el-select
                v-model="queryParams.examid"
                placeholder="请选择检查类型"
                clearable
              >
                <el-option
                  v-for="item in checkTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="公司名称" prop="corporatename">
              <el-input
                v-model="queryParams.corporatename"
                placeholder="请输入公司名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="检查日期" prop="checkdata">
              <el-date-picker
                clearable
                v-model="queryParams.checkdata"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择检查日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="检查状态" prop="jcStatus">
              <el-select
                v-model="queryParams.jcStatus"
                placeholder="请选择检查状态"
                clearable
              >
                <el-option
                  v-for="item in checkStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:checkdeinspectrecords:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:checkdeinspectrecords:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:checkdeinspectrecords:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:checkdeinspectrecords:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

          <el-table
            v-loading="loading"
            :data="checkdeinspectrecordsList"
            @selection-change="handleSelectionChange"
            height="calc(100vh - 250px)"
            style="width: 100%"
          >
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column label="检查类型" align="center" prop="examName" />
            <el-table-column
              label="检查编号"
              align="center"
              prop="inspectnumber"
            />
            <el-table-column
              label="公司名称"
              align="center"
              prop="corporatename"
            />
            <el-table-column label="检查组长" align="center" prop="staffids" />
            <el-table-column
              label="检查组员"
              align="center"
              prop="teammemberss"
            />
            <el-table-column
              label="检查日期"
              align="center"
              prop="checkdata"
              width="180"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.checkdata, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="1开发区或2企自查id" align="center" prop="censorid" /> -->
            <el-table-column
              label="检查状态"
              align="center"
              prop="jcStatusName"
            />
            <el-table-column
              label="操作"
              fixed="right"
              width="150"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click="handleCheck(scope.row)"
                  v-hasPermi="['system:check:check']"
                  >查看</el-button
                >
                <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:checkdeinspectrecords:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:checkdeinspectrecords:remove']">删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改项目检查对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="检查id  1:安全生产，2:环保检查，3:在建工地" prop="examid">
          <el-input v-model="form.examid" placeholder="请输入检查id  1:安全生产，2:环保检查，3:在建工地" />
        </el-form-item>
        <el-form-item label="登录人id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入登录人id" />
        </el-form-item>
        <el-form-item label="公司id" prop="projectinspecid">
          <el-input v-model="form.projectinspecid" placeholder="请输入公司id" />
        </el-form-item>
        <el-form-item label="公司名称" prop="corporatename">
          <el-input v-model="form.corporatename" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="检查组长" prop="staffid">
          <el-input v-model="form.staffid" placeholder="请输入检查组长" />
        </el-form-item>
        <el-form-item label="通报日期" prop="notificationtime">
          <el-date-picker clearable v-model="form.notificationtime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择通报日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查日期" prop="checkdata">
          <el-date-picker clearable v-model="form.checkdata" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择检查日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查地点" prop="checkplace">
          <el-input v-model="form.checkplace" placeholder="请输入检查地点" />
        </el-form-item>
        <el-form-item label="检查组员" prop="teammembers">
          <el-input v-model="form.teammembers" placeholder="请输入检查组员" />
        </el-form-item>
        <el-form-item label="检查内容">
          <editor v-model="form.checkcontent" :min-height="192" />
        </el-form-item>
        <el-form-item label="检查备注" prop="checkremarks">
          <el-input v-model="form.checkremarks" placeholder="请输入检查备注" />
        </el-form-item>
        <el-form-item label="签名" prop="corporatenamesign">
          <el-input v-model="form.corporatenamesign" placeholder="请输入签名" />
        </el-form-item>
        <el-form-item label="签名时间" prop="signtimes">
          <el-date-picker clearable v-model="form.signtimes" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择签名时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="1开发区或2企自查id" prop="censorid">
          <el-input v-model="form.censorid" placeholder="请输入1开发区或2企自查id" />
        </el-form-item>
        <el-form-item label="检查编号 JC+时间+3位序号" prop="inspectnumber">
          <el-input v-model="form.inspectnumber" placeholder="请输入检查编号 JC+时间+3位序号" />
        </el-form-item>
        <el-form-item label="序号" prop="xh">
          <el-input v-model="form.xh" placeholder="请输入序号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->

    <!-- 检查详情 -->
    <el-dialog :visible.sync="showDetailForm" width="80%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailForm.inspectnumber }} {{ detailForm.corporatename }}</div>
        <div class="title-btn" style="margin-right: 50px">
          <!-- <el-button size="small" type="primary" icon="el-icon-download">导出检查记录</el-button>
                    <el-button size="small" type="primary" icon="el-icon-download">导出报告书</el-button> -->
        </div>
      </div>
      <div class="detail-content">
        <div
          class="detail-item"
          v-for="(item, index) in detailForm.detailList"
          :key="index"
        >
          <div class="detail-top">
            <span
              :class="
                item.statuName == '已提交'
                  ? 'state blue-bg white'
                  : item.statuName == '已整改'
                  ? 'state green-bg white'
                  : 'state red-bg white'
              "
              >{{ item.statuName }}</span
            >
            <div class="name">{{ item.inspectors }}</div>
            <div class="name">{{ item.pitfalltype }}</div>
            <div class="state red red-bg-10">
              {{
                item.gradepitfall == 1
                  ? "重大"
                  : item.gradepitfall == 2
                  ? "较大"
                  : "一般"
              }}
            </div>
            <div class="edit">
              <!-- <el-button size="small" type="primary" icon="el-icon-edit" @click="showEdit(index)"
                                v-hasPermi="['system:check:edit']">编辑</el-button> -->
            </div>
          </div>
          <div class="detail-center">
            <div class="detail-center-left">
              <div class="detail-center-item">
                <div class="detail-center-item-left">安全隐患：</div>
                <div class="detail-center-item-right">
                  {{ item.safetypitfall }}
                </div>
              </div>
              <div class="detail-center-item">
                <div class="detail-center-item-left">法规条款：</div>
                <div class="detail-center-item-right">
                  {{ item.regulatory }}
                </div>
              </div>
              <div class="detail-center-item">
                <div class="detail-center-item-left">可能后果：</div>
                <div class="detail-center-item-right">{{ item.resultant }}</div>
              </div>
              <div class="detail-center-item" v-if="item.dangerimages">
                <div class="detail-center-item-left">隐患图片：</div>
                <div class="detail-center-item-right">
                  <el-image
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="img"
                    fit="cover"
                    :preview-src-list="item.imgList"
                    v-for="(img, index) in item.imgList"
                    :key="index"
                  >
                  </el-image>
                </div>
              </div>
              <div class="detail-center-item" v-if="item.corporatenamesign">
                <div class="detail-center-item-left">签字图片：</div>
                <div class="detail-center-item-right">
                  <el-image
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="img"
                    fit="cover"
                    :preview-src-list="item.imgListsign"
                    v-for="(img, index) in item.imgListsign"
                    :key="index"
                  >
                  </el-image>
                </div>
              </div>
              <div class="detail-center-item" v-if="item.rectificationphotos">
                <div class="detail-center-item-left">整改图片：</div>
                <div class="detail-center-item-right">
                  <el-image
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="img"
                    fit="cover"
                    :preview-src-list="item.imgListgai"
                    v-for="(img, index) in item.imgListgai"
                    :key="index"
                  >
                  </el-image>
                </div>
              </div>
            </div>
            <!-- <div class="detail-center-right">
                            <el-image style="width: 100px; height: 100px" :src="img" fit="cover"
                                :preview-src-list="item.imgList" v-for="(img, index) in item.imgList">
                            </el-image>
                        </div> -->
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="showEditForm" width="80%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>编辑</div>
      </div>
      <div class="detail-content">
        <div class="detail-edit-content">
          <el-form ref="form" :model="editForm" label-width="80px">
            <el-form-item label="隐患名称">
              <el-input v-model="editForm.safetypitfall"></el-input>
            </el-form-item>
            <el-form-item label="法律条款">
              <el-input
                type="textarea"
                v-model="editForm.regulatory"
                :autosize="{ minRows: 5, maxRows: 7 }"
              ></el-input>
            </el-form-item>
            <el-form-item label="可能后果">
              <el-input
                type="textarea"
                v-model="editForm.resultant"
                :autosize="{ minRows: 3, maxRows: 5 }"
              ></el-input>
            </el-form-item>
            <el-form-item
              style="display: flex; justify-content: center; margin-left: -80px"
            >
              <el-button>取消</el-button>
              <el-button type="primary" @click="onSubmit">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCheckdeinspectrecords,
  getCheckdeinspectrecords,
  delCheckdeinspectrecords,
  addCheckdeinspectrecords,
  updateCheckdeinspectrecords,
  queryByInSpectrecordsid,
} from "@/api/system/checkdeinspectrecords";
import { stringToList } from "@/utils/index";

export default {
  name: "Checkdeinspectrecords",
  data() {
    return {
      // 检查类型
      checkTypeOptions: [
        { label: "安全生产", value: 1 },
        { label: "在建工地", value: 2 },
        { label: "燃气检查", value: 3 },
        { label: "食品安全", value: 4 },
      ],
      // 检查状态
      checkStatusOptions: [
        { label: "无隐患", value: 0 },
        { label: "未整改", value: 1 },
        { label: "已整改", value: 2 },
      ],
      yinhuanStatusOptions: [
        { label: "未整改", value: 1 },
        { label: "已整改", value: 2 },
        { label: "已提交", value: 3 },
        { label: "未通过审核", value: 4 },
        { label: "急需整改", value: 5 },
        { label: "已逾期", value: 6 },
      ],
      // 编辑表单内容
      editForm: {},
      // 详情表单内容
      detailForm: {},
      // 显示详情表单
      showDetailForm: false,
      // 显示编辑表单
      showEditForm: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目检查表格数据
      checkdeinspectrecordsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examid: null,
        userId: null,
        projectinspecid: null,
        corporatename: null,
        staffid: null,
        notificationtime: null,
        checkdata: null,
        checkplace: null,
        teammembers: null,
        checkcontent: null,
        checkremarks: null,
        corporatenamesign: null,
        signtimes: null,
        censorid: null,
        inspectnumber: null,
        xh: null,
        jcStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
    // this.loadImgCanvas();
  },
  methods: {
    // 导出表格
    exportExcel() {
      window.location.href =
        process.env.VUE_APP_BASE_API +
        "/pc/checkdeinspectrecords/exportCheckInfo";
    },

    // 查看检查记录详情
    handleCheck(row) {
      this.detailForm = row;
      queryByInSpectrecordsid({
        inspectrecordsid1: row.inspectrecordsid1,
      }).then((res) => {
        res.data.forEach((item) => {
          item.imgList = stringToList(item.dangerimages);
          item.imgListgai = stringToList(item.rectificationphotos);
          item.imgListsign = stringToList(item.corporatenamesign);
          item.statuName = this.yinhuanStatusOptions.find(
            (status) => status.value == item.rectificationstatus
          ).label;
        });
        this.detailForm.detailList = res.data;
        this.showDetailForm = true;
      });
    },
    // 生成文字图片
    loadImgCanvas() {
      let pictureUrl =
        "https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg";
      let pictureName = "我是图片名字";
      let canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      let img = new Image();
      img.crossOrigin = "";
      img.src = pictureUrl;
      //加载图片
      img.onload = () => {
        canvas.setAttribute("width", img.width);
        canvas.setAttribute("height", img.height);
        //绘制图片
        context.drawImage(img, 0, 0, img.width, img.height);
        //绘制底部矩形
        context.fillStyle = "rgba(0, 0, 0, 0.3)"; //fillstyle设置填充颜色
        context.fillRect(0, img.height - 100, img.width, 100);
        //字体
        context.font = "20px Arial";
        context.fillStyle = "#fff"; //fil1Style设置填充颜色
        context.textAlign = "left";
        context.textBaseline = "middle";
        //字体文字，显示位置 图片上需要n个文字就写n个context.fillText(文字,上下,左右);
        context.fillText("上传日期：2024-07-30", 40, img.height - 80);
        context.fillText("项目地址：上海市奉贤区333弄", 40, img.height - 50);
        context.fillText(
          "主要问题：隐患详情隐患详情隐患详情，隐患详情隐患详情隐患",
          40,
          img.height - 20
        );
        let type = pictureUrl.replace(/.+\./g, "");
        //合成图片
        let newPicUrl = canvas.toDataURL(`image/$(type}`, 1.0);
      };
    },
    // 编辑表单提价
    onSubmit() {
      console.log(this.editForm);
    },
    // 显示编辑表单
    showEdit(index) {
      this.showEditForm = true;
      this.editForm = this.detailForm.detailList[index];
    },
    /** 查询项目检查列表 */
    getList() {
      this.loading = true;
      listCheckdeinspectrecords(this.queryParams).then((res) => {
        res.rows.forEach((item) => {
          item.examName = this.checkTypeOptions.find(
            (exam) => exam.value == item.examid
          ).label;
          item.jcStatusName = this.checkStatusOptions.find(
            (status) => status.value == item.jcStatus
          )
            ? this.checkStatusOptions.find(
                (status) => status.value == item.jcStatus
              ).label
            : "";
        });
        this.checkdeinspectrecordsList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        inspectrecordsid1: null,
        examid: null,
        userId: null,
        projectinspecid: null,
        corporatename: null,
        staffid: null,
        notificationtime: null,
        checkdata: null,
        checkplace: null,
        teammembers: null,
        checkcontent: null,
        checkremarks: null,
        corporatenamesign: null,
        signtimes: null,
        censorid: null,
        inspectnumber: null,
        xh: null,
        jcStatus: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.inspectrecordsid1);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目检查";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const inspectrecordsid1 = row.inspectrecordsid1 || this.ids;
      getCheckdeinspectrecords(inspectrecordsid1).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改项目检查";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.inspectrecordsid1 != null) {
            updateCheckdeinspectrecords(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCheckdeinspectrecords(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const inspectrecordsid1s = row.inspectrecordsid1 || this.ids;
      this.$modal
        .confirm(
          '是否确认删除项目检查编号为"' + inspectrecordsid1s + '"的数据项？'
        )
        .then(function () {
          return delCheckdeinspectrecords(inspectrecordsid1s);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/checkdeinspectrecords/export",
        {
          ...this.queryParams,
        },
        `checkdeinspectrecords_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  // overflow-y: auto;
  height: calc(100vh - 100px);

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      .detail-center {
        display: flex;

        .detail-center-left {
          .detail-center-item {
            display: flex;
            line-height: 24px;
            margin: 12px 0;

            .detail-center-item-left {
              width: 70px;
            }

            .detail-center-item-right {
              flex: 1;
            }
          }
        }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 60%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  height: calc(100vh - 150px);
  overflow-y: auto;
  font-size: 14px;
}
</style>
