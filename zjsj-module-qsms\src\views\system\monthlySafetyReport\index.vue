<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="140px"
    >
      <el-form-item label="报表编号" prop="reportNo">
        <el-input
          v-model="queryParams.reportNo"
          placeholder="请输入报表编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="报表月份" prop="reportMonth">
        <el-date-picker
          clearable
          v-model="queryParams.reportMonth"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择报表月份"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="统计范围" prop="orgScope">
        <el-input
          v-model="queryParams.orgScope"
          placeholder="请输入统计范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="本月平均在岗人数" prop="totalEmployees">
        <el-input
          v-model="queryParams.totalEmployees"
          placeholder="请输入本月平均在岗人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="本月工作总工时" prop="workHours">
        <el-input
          v-model="queryParams.workHours"
          placeholder="请输入本月工作总工时"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="隐患总数" prop="hiddenDangerTotal">
        <el-input
          v-model="queryParams.hiddenDangerTotal"
          placeholder="请输入隐患总数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="已整改隐患数" prop="hiddenDangerRectified">
        <el-input
          v-model="queryParams.hiddenDangerRectified"
          placeholder="请输入已整改隐患数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隐患整改率" prop="rectificationRate">
        <el-input
          v-model="queryParams.rectificationRate"
          placeholder="请输入隐患整改率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="违章次数" prop="violationCount">
        <el-input
          v-model="queryParams.violationCount"
          placeholder="请输入违章次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故总数" prop="accidentCount">
        <el-input
          v-model="queryParams.accidentCount"
          placeholder="请输入事故总数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重大事故数" prop="majorAccidentCount">
        <el-input
          v-model="queryParams.majorAccidentCount"
          placeholder="请输入重大事故数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一般事故数" prop="minorAccidentCount">
        <el-input
          v-model="queryParams.minorAccidentCount"
          placeholder="请输入一般事故数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="连续安全生产天数" prop="zeroAccidentDays">
        <el-input
          v-model="queryParams.zeroAccidentDays"
          placeholder="请输入连续安全生产天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训次数" prop="trainingTimes">
        <el-input
          v-model="queryParams.trainingTimes"
          placeholder="请输入培训次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训总时长" prop="trainingHours">
        <el-input
          v-model="queryParams.trainingHours"
          placeholder="请输入培训总时长"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训总人数" prop="trainedPersons">
        <el-input
          v-model="queryParams.trainedPersons"
          placeholder="请输入培训总人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应急演练次数" prop="drillTimes">
        <el-input
          v-model="queryParams.drillTimes"
          placeholder="请输入应急演练次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="演练参与人数" prop="drillParticipants">
        <el-input
          v-model="queryParams.drillParticipants"
          placeholder="请输入演练参与人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="本月安全投入金额" prop="safetyInvestment">
        <el-input
          v-model="queryParams.safetyInvestment"
          placeholder="请输入本月安全投入金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备检查次数" prop="equipmentCheckTimes">
        <el-input
          v-model="queryParams.equipmentCheckTimes"
          placeholder="请输入设备检查次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备合格占比" prop="qualifiedEquipmentRate">
        <el-input
          v-model="queryParams.qualifiedEquipmentRate"
          placeholder="请输入设备合格占比"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制人" prop="compiler">
        <el-input
          v-model="queryParams.compiler"
          placeholder="请输入编制人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="reviewer">
        <el-input
          v-model="queryParams.reviewer"
          placeholder="请输入审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审批人" prop="approver">
        <el-input
          v-model="queryParams.approver"
          placeholder="请输入审批人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制时间" prop="compileTime">
        <el-date-picker
          clearable
          v-model="queryParams.compileTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择编制时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审批时间" prop="approveTime">
        <el-date-picker
          clearable
          v-model="queryParams.approveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审批时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:report:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:report:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:report:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:report:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="reportList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="报表ID" align="center" prop="id" /> -->
      <el-table-column label="报表编号" align="center" prop="reportNo" />
      <el-table-column
        label="报表月份"
        align="center"
        prop="reportMonth"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reportMonth, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="统计范围" align="center" prop="orgScope" />
      <!-- <el-table-column
        label="报表类型：公司级/区域级/项目级"
        align="center"
        prop="reportType"
      /> -->
      <el-table-column
        label="本月平均在岗人数"
        align="center"
        prop="totalEmployees"
        width="140"
      />
      <el-table-column
        label="本月工作总工时"
        align="center"
        prop="workHours"
        width="140"
      />
      <el-table-column
        label="隐患总数"
        align="center"
        prop="hiddenDangerTotal"
      />
      <el-table-column
        label="已整改隐患数"
        align="center"
        prop="hiddenDangerRectified"
      />
      <el-table-column
        label="隐患整改率"
        align="center"
        prop="rectificationRate"
      />
      <el-table-column label="违章次数" align="center" prop="violationCount" />
      <el-table-column label="事故总数" align="center" prop="accidentCount" />
      <el-table-column
        label="重大事故数"
        align="center"
        prop="majorAccidentCount"
      />
      <el-table-column
        label="一般事故数"
        align="center"
        prop="minorAccidentCount"
      />
      <!-- <el-table-column
        label="连续安全生产天数"
        align="center"
        prop="zeroAccidentDays"
      />
      <el-table-column label="培训次数" align="center" prop="trainingTimes" />
      <el-table-column label="培训总时长" align="center" prop="trainingHours" />
      <el-table-column
        label="培训总人数"
        align="center"
        prop="trainedPersons"
      />
      <el-table-column label="应急演练次数" align="center" prop="drillTimes" />
      <el-table-column
        label="演练参与人数"
        align="center"
        prop="drillParticipants"
      />
      <el-table-column
        label="本月安全投入金额"
        align="center"
        prop="safetyInvestment"
      />
      <el-table-column
        label="设备检查次数"
        align="center"
        prop="equipmentCheckTimes"
      />
      <el-table-column
        label="设备合格占比"
        align="center"
        prop="qualifiedEquipmentRate"
      />
      <el-table-column
        label="报表状态：草稿/待审核/已审核/已发布"
        align="center"
        prop="reportStatus"
      />
      <el-table-column
        label="本月安全工作亮点"
        align="center"
        prop="keyAchievements"
      />
      <el-table-column
        label="存在的主要问题"
        align="center"
        prop="existingProblems"
      />
      <el-table-column
        label="下月工作计划"
        align="center"
        prop="nextMonthPlan"
      />
      <el-table-column label="月度安全工作总结" align="center" prop="summary" />
      <el-table-column
        label="月报文件地址"
        align="center"
        prop="reportFileUrl"
      />
      <el-table-column label="编制人" align="center" prop="compiler" />
      <el-table-column label="审核人" align="center" prop="reviewer" />
      <el-table-column label="审批人" align="center" prop="approver" />
      <el-table-column
        label="编制时间"
        align="center"
        prop="compileTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.compileTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approveTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注信息" align="center" prop="remark" /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:report:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:report:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全月报管理（存储月度安全统计数据及报信息）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="报表编号" prop="reportNo">
          <el-input v-model="form.reportNo" placeholder="请输入报表编号" />
        </el-form-item>
        <el-form-item label="报表月份" prop="reportMonth">
          <el-date-picker
            clearable
            v-model="form.reportMonth"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择报表月份"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="统计范围" prop="orgScope">
          <el-input v-model="form.orgScope" placeholder="请输入统计范围" />
        </el-form-item>
        <el-form-item label="本月平均在岗人数" prop="totalEmployees">
          <el-input
            v-model="form.totalEmployees"
            placeholder="请输入本月平均在岗人数"
          />
        </el-form-item>
        <el-form-item label="本月工作总工时" prop="workHours">
          <el-input
            v-model="form.workHours"
            placeholder="请输入本月工作总工时"
          />
        </el-form-item>
        <el-form-item label="隐患总数" prop="hiddenDangerTotal">
          <el-input
            v-model="form.hiddenDangerTotal"
            placeholder="请输入隐患总数"
          />
        </el-form-item>
        <el-form-item label="已整改隐患数" prop="hiddenDangerRectified">
          <el-input
            v-model="form.hiddenDangerRectified"
            placeholder="请输入已整改隐患数"
          />
        </el-form-item>
        <el-form-item label="隐患整改率" prop="rectificationRate">
          <el-input
            v-model="form.rectificationRate"
            placeholder="请输入隐患整改率"
          />
        </el-form-item>
        <el-form-item label="违章次数" prop="violationCount">
          <el-input
            v-model="form.violationCount"
            placeholder="请输入违章次数"
          />
        </el-form-item>
        <el-form-item label="事故总数" prop="accidentCount">
          <el-input v-model="form.accidentCount" placeholder="请输入事故总数" />
        </el-form-item>
        <el-form-item label="重大事故数" prop="majorAccidentCount">
          <el-input
            v-model="form.majorAccidentCount"
            placeholder="请输入重大事故数"
          />
        </el-form-item>
        <el-form-item label="一般事故数" prop="minorAccidentCount">
          <el-input
            v-model="form.minorAccidentCount"
            placeholder="请输入一般事故数"
          />
        </el-form-item>
        <el-form-item label="连续安全生产天数" prop="zeroAccidentDays">
          <el-input
            v-model="form.zeroAccidentDays"
            placeholder="请输入连续安全生产天数"
          />
        </el-form-item>
        <el-form-item label="培训次数" prop="trainingTimes">
          <el-input v-model="form.trainingTimes" placeholder="请输入培训次数" />
        </el-form-item>
        <el-form-item label="培训总时长" prop="trainingHours">
          <el-input
            v-model="form.trainingHours"
            placeholder="请输入培训总时长"
          />
        </el-form-item>
        <el-form-item label="培训总人数" prop="trainedPersons">
          <el-input
            v-model="form.trainedPersons"
            placeholder="请输入培训总人数"
          />
        </el-form-item>
        <el-form-item label="应急演练次数" prop="drillTimes">
          <el-input
            v-model="form.drillTimes"
            placeholder="请输入应急演练次数"
          />
        </el-form-item>
        <el-form-item label="演练参与人数" prop="drillParticipants">
          <el-input
            v-model="form.drillParticipants"
            placeholder="请输入演练参与人数"
          />
        </el-form-item>
        <el-form-item label="本月安全投入金额" prop="safetyInvestment">
          <el-input
            v-model="form.safetyInvestment"
            placeholder="请输入本月安全投入金额"
          />
        </el-form-item>
        <el-form-item label="设备检查次数" prop="equipmentCheckTimes">
          <el-input
            v-model="form.equipmentCheckTimes"
            placeholder="请输入设备检查次数"
          />
        </el-form-item>
        <el-form-item label="设备合格占比" prop="qualifiedEquipmentRate">
          <el-input
            v-model="form.qualifiedEquipmentRate"
            placeholder="请输入设备合格占比"
          />
        </el-form-item>
        <el-form-item label="本月安全工作亮点" prop="keyAchievements">
          <el-input
            v-model="form.keyAchievements"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="存在的主要问题" prop="existingProblems">
          <el-input
            v-model="form.existingProblems"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="下月工作计划" prop="nextMonthPlan">
          <el-input
            v-model="form.nextMonthPlan"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="月度安全工作总结" prop="summary">
          <el-input
            v-model="form.summary"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="月报文件地址" prop="reportFileUrl">
          <el-input
            v-model="form.reportFileUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="编制人" prop="compiler">
          <el-input v-model="form.compiler" placeholder="请输入编制人" />
        </el-form-item>
        <el-form-item label="审核人" prop="reviewer">
          <el-input v-model="form.reviewer" placeholder="请输入审核人" />
        </el-form-item>
        <el-form-item label="审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="请输入审批人" />
        </el-form-item>
        <el-form-item label="编制时间" prop="compileTime">
          <el-date-picker
            clearable
            v-model="form.compileTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择编制时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批时间" prop="approveTime">
          <el-date-picker
            clearable
            v-model="form.approveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审批时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注信息" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listReport,
  getReport,
  delReport,
  addReport,
  updateReport,
} from "@/api/system/monthlySafetyReport/index";

export default {
  name: "Report",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全月报管理（存储月度安全统计数据及报信息）表格数据
      reportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportNo: null,
        reportMonth: null,
        orgScope: null,
        reportType: null,
        totalEmployees: null,
        workHours: null,
        hiddenDangerTotal: null,
        hiddenDangerRectified: null,
        rectificationRate: null,
        violationCount: null,
        accidentCount: null,
        majorAccidentCount: null,
        minorAccidentCount: null,
        zeroAccidentDays: null,
        trainingTimes: null,
        trainingHours: null,
        trainedPersons: null,
        drillTimes: null,
        drillParticipants: null,
        safetyInvestment: null,
        equipmentCheckTimes: null,
        qualifiedEquipmentRate: null,
        reportStatus: null,
        keyAchievements: null,
        existingProblems: null,
        nextMonthPlan: null,
        summary: null,
        reportFileUrl: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        reportNo: [
          { required: true, message: "报表编号不能为空", trigger: "blur" },
        ],
        reportMonth: [
          { required: true, message: "报表月份不能为空", trigger: "blur" },
        ],
        orgScope: [
          { required: true, message: "统计范围不能为空", trigger: "blur" },
        ],
        compiler: [
          { required: true, message: "编制人不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全月报管理（存储月度安全统计数据及报信息）列表 */
    getList() {
      this.loading = true;
      listReport(this.queryParams).then((response) => {
        this.reportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        reportNo: null,
        reportMonth: null,
        orgScope: null,
        reportType: null,
        totalEmployees: null,
        workHours: null,
        hiddenDangerTotal: null,
        hiddenDangerRectified: null,
        rectificationRate: null,
        violationCount: null,
        accidentCount: null,
        majorAccidentCount: null,
        minorAccidentCount: null,
        zeroAccidentDays: null,
        trainingTimes: null,
        trainingHours: null,
        trainedPersons: null,
        drillTimes: null,
        drillParticipants: null,
        safetyInvestment: null,
        equipmentCheckTimes: null,
        qualifiedEquipmentRate: null,
        reportStatus: null,
        keyAchievements: null,
        existingProblems: null,
        nextMonthPlan: null,
        summary: null,
        reportFileUrl: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全月报管理（存储月度安全统计数据及报信息）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getReport(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全月报管理（存储月度安全统计数据及报信息）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateReport(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReport(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除安全月报管理（存储月度安全统计数据及报信息）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delReport(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/report/export",
        {
          ...this.queryParams,
        },
        `report_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
