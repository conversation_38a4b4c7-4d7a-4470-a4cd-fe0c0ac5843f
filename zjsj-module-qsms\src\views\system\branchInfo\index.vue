<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <!-- <el-form-item label="所属公司" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入所属公司"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="分公司名称" prop="branchName">
        <selectComponyTree
          ref="chargePersonName"
          v-model="queryParams.branchName"
          :people-list="companyList"
          placeholder="请搜索或选择分公司名称"
          @change="handleSearchChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:branch:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:branch:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:branch:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="branchList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="分公司名称" align="center" prop="branchName" />
      <el-table-column label="所属公司" align="center" prop="companyName" />
      <el-table-column label="得分" align="center" prop="score" />
      <!-- <el-table-column label="备注" align="center" prop="remarks" /> -->
      <el-table-column label="附件" align="center" prop="attachment">
        <template slot-scope="scope">
          <div v-if="scope.row.attachment" class="contract-file">
            <div
              v-for="(item, index) in scope.row.attachment.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:branch:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:branch:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改分公司考核对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="140px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-form-item label="分公司名称" prop="branchName">
          <selectComponyTree
            ref="chargePersonName"
            v-model="form.branchName"
            :people-list="companyList"
            placeholder="请搜索或选择分公司名称"
            @change="handleFormChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="所属公司" prop="companyName">
          <el-input v-model="form.companyName" placeholder="" disabled />
        </el-form-item>
        <!-- <el-form-item label="分公司" prop="branchId">
          <el-input v-model="form.branchId" placeholder="请输入分公司" />
        </el-form-item> -->
        <!-- <el-form-item label="分公司名称" prop="branchName">
          <el-input
            v-model="form.branchName"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item> -->
        <el-form-item label="得分" prop="score">
          <el-input
            v-model="form.score"
            placeholder="请输入得分"
            @input="handleScoreInput(form.score)"
            @blur="handleScoreBlur(form.score)"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            placeholder="请输入备注"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <file-upload v-model="form.attachment" :disabled="isCheck" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listBranch,
  getBranch,
  delBranch,
  addBranch,
  updateBranch,
} from "@/api/system/branchInfo/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import selectComponyTree from "@/views/components/selectComponyTree.vue";
import { getFileOrignalName } from "@/utils/common.js";
import { querytree } from "@/api/system/info";
import { getEnterpriseInfo } from "@/api/system/info";
export default {
  name: "Branch",
  components: {
    selectPeopleTree,
    selectComponyTree,
  },
  data() {
    return {
      companyList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      isCheck: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分公司考核表格数据
      branchList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyId: null,
        companyName: null,
        branchId: null,
        branchName: null,
        score: null,
        remarks: null,
        attachment: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        branchName: [
          { required: true, message: "分公司名称不能为空", trigger: "change" },
        ],
        score: [{ required: true, message: "得分不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
    this.getCompanyList();
  },
  methods: {
    handleScoreInput(score) {
      let value = score;
      // 清除除数字和小数点外的所有字符
      let val = value.replace(/[^\d.]/g, "");
      // 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");
      // 确保小数点不在开头
      val = val.replace(/^\./g, "");
      // 只保留两位小数
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      this.form.score = val;
    },
    handleScoreBlur(score) {
      let val = score;
      if (val && val[val.length - 1] === ".") {
        val = val.slice(0, -1);
      }
      this.form.score = val;
    },
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    getFileOrignalName,
    handleSearchChange(selectedItem) {
      if (selectedItem) {
        this.queryParams.branchName = selectedItem.label;
        this.queryParams.branchId = selectedItem.id;
      } else {
        this.queryParams.branchName = null;
        this.queryParams.branchId = null;
      }
      this.$forceUpdate();
    },
    handleFormChange(selectedItem) {
      if (selectedItem) {
        this.form.branchName = selectedItem.label;
        this.form.branchId = selectedItem.id;
        this.form.companyId = selectedItem.parentId;
        this.form.companyName = selectedItem.parentName;
      } else {
        this.form.branchName = null;
        this.form.branchId = null;
        this.form.companyId = null;
        this.form.companyName = null;
      }
      this.$forceUpdate();
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    /** 查询分公司考核列表 */
    getList() {
      this.loading = true;
      listBranch(this.queryParams).then((response) => {
        this.branchList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyId: null,
        companyName: null,
        branchId: null,
        branchName: null,
        score: null,
        remarks: null,
        attachment: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.branchId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加分公司考核";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBranch(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改分公司考核";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getBranch(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "查看分公司考核";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateBranch(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addBranch(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除分公司考核编号为"' + ids + '"的数据项？')
        .then(function () {
          return delBranch(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/branch/export",
        {
          ...this.queryParams,
        },
        `分公司考核.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>