<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
      style="display: flex; justify-content: left; align-items: center"
    >
      <el-form-item label="作业证编号" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入作业证编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjNewsInfoList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 280px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" />
      <el-table-column
        label="申请单位"
        align="center"
        prop="name"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="申请人" align="center" prop="gender">
        <template slot-scope="scope">
          <span v-if="scope.row.gender == 1">男</span>
          <span v-else-if="scope.row.gender == 2">女</span>
          <span v-else></span>
        </template>
      </el-table-column>
      <el-table-column
        label="作业证编号"
        align="center"
        prop="birthDate"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      style="height: 800px; overflow: hidden"
      append-to-body
      class="operateDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        class="formDialog viewMode"
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="
          width: 100%;
          margin: 0 auto;
          padding: 20px;
          overflow: auto;
          height: 600px;
        "
      >
        <div
          style="
            font-size: 24px;
            ling-height: 40px;
            display: flex;
            justify-content: center;
            color: #000;
            font-weight: bold;
          "
        >
          动火安全作业证
        </div>
        <el-collapse
          expand-icon-position="left"
          v-model="activeNames"
          :accordion="false"
          isActive
          style="margin-top: 20px"
        >
          <el-row :gutter="20">
            <el-col :span="8" style="padding: 0px">
              <el-form-item
                label="申请单位"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" style="padding: 0px">
              <el-form-item
                label="申请人"
                prop="gender"
                class="formDialogStyle"
              >
                <el-input v-model="form.name"></el-input>
                <!-- <el-radio-group v-model="form.gender">
                  <el-radio :label="1">男</el-radio>
                  <el-radio :label="2">女</el-radio>
                </el-radio-group> -->
              </el-form-item>
            </el-col>
            <el-col :span="8" style="padding: 0px">
              <el-form-item
                label="作业证编号"
                prop="weight"
                class="formDialogStyle"
              >
                <el-input v-model="form.weight" type="number"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24" style="padding: 0px">
              <el-form-item
                label="动火作业级别"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24" style="padding: 0px">
              <el-form-item
                label="动火方式"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24" style="padding: 0px">
              <el-form-item
                label="动火时间"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12" style="padding: 0px">
              <el-form-item
                label="动火作业负责人"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <el-form-item label="动火人" prop="name" class="formDialogStyle">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10" style="padding: 0px">
              <el-form-item
                label="动火分析时间"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10" style="padding: 0px">
              <el-form-item
                label="分析点名称"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10" style="padding: 0px">
              <el-form-item
                label="分析数据"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10" style="padding: 0px">
              <el-form-item
                label="分析人"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10" style="padding: 0px">
              <el-form-item
                label="涉及的其他特殊作业"
                prop="name"
                class="formDialogStyle formLeftStyle formLongLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="7" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24" style="padding: 0px">
              <el-form-item
                label="危害辨识"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivStyle">序号</div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div class="formDialogStyle formDivStyle">安全措施</div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formDivStyle">确认人</div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                1
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                动火设备内部构件清理干净，蒸汽吹扫或水洗合格，达到用火条件
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                2
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                断开与动火设备相连接的所有管线，加盲板
                <el-form-item label="" prop="name" class="formNoLabelStyle">
                  <el-input v-model="form.name"></el-input> </el-form-item
                >块
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                3
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                动火点周围的下水井、地漏、地沟、电缆沟等已清除易燃物，并已采取覆盖、铺沙、水封等手段进行隔离
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                4
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                作业人员按要求佩戴个体劳保用品（防护手套、防护眼镜、电焊面罩、防砸鞋等）
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                5
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                高处作业已采取防火花飞溅措施
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                6
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                动火点周围易燃物已清除
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                7
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                电焊回路线已接在焊件上，把线未穿过下水井或其他设备搭接
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6" style="padding: 0px">
              <div class="formDialogStyle formLeftStyle formDivContentStyle">
                8
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
              >
                乙炔气瓶（直立放置）、氧气瓶与火源间的距离大于10m，各气瓶已固定
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="height: 100px">
            <el-col :span="6" style="padding: 0px; height: 100%">
              <div
                class="formDialogStyle formLeftStyle formDivContentStyle"
                style="height: 100%"
              >
                9
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px; height: 100px">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
                style="
                  display: flex;
                  flex-wrap: wrap;
                  padding: 0px 8px;
                  height: 100%;
                "
              >
                现场配备消防蒸汽带
                <el-form-item
                  label=""
                  prop="name"
                  class="formFormDivContentStyle formNoLabelStyle"
                >
                  <el-input v-model="form.name"></el-input> </el-form-item
                >根，灭火器
                <el-form-item
                  label=""
                  prop="name"
                  class="formFormDivContentStyle formNoLabelStyle"
                >
                  <el-input v-model="form.name"></el-input> </el-form-item
                >台，铁锹
                <el-form-item
                  label=""
                  prop="name"
                  class="formFormDivContentStyle formNoLabelStyle"
                >
                  <el-input v-model="form.name"></el-input> </el-form-item
                >把，石棉布
                <el-form-item
                  label=""
                  prop="name"
                  class="formFormDivContentStyle formNoLabelStyle"
                >
                  <el-input v-model="form.name"></el-input> </el-form-item
                >块
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px; height: 100px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle formNoLabelMoreContentStyle"
                style="height: 100px; line-height: 100px"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="height: 160px">
            <el-col :span="6" style="padding: 0px; height: 100%">
              <div
                class="formDialogStyle formLeftStyle formDivContentStyle"
                style="height: 100%"
              >
                10
              </div>
            </el-col>
            <el-col :span="12" style="padding: 0px; height: 100%">
              <div
                class="formDialogStyle formDivContentStyle formDivLeftContentStyle"
                style="
                  display: flex;
                  padding: 0px 8px;
                  height: 100%;
                  flex-direction: column;
                "
              >
                <div
                  style="
                    height: 100px;
                    width: 100%;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                  "
                >
                  其他安全措施:
                  <el-form-item
                    label=""
                    prop="name"
                    class="formFormDivContent2Style formNoLabelStyle formTextareaStyle"
                  >
                    <el-input
                      v-model="form.name"
                      type="textarea"
                      :rows="4"
                    ></el-input>
                  </el-form-item>
                </div>
                <div
                  style="
                    height: 50px;
                    display: flex;
                    width: 100%;
                    justify-content: end;
                    align-items: center;
                  "
                >
                  编制人:
                  <el-form-item
                    label=""
                    prop="name"
                    class="formFormDivContent1Style formNoLabelStyle"
                  >
                    <el-input v-model="form.name"></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-col>
            <el-col :span="6" style="padding: 0px; height: 160px">
              <el-form-item
                label=""
                prop="name"
                class="formDialogStyle formNoLabelStyle formNoLabelMoreContent2Style"
                style="height: 160px; line-height: 160px"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8" style="padding: 0px">
              <el-form-item
                label="生产单位负责人"
                prop="name"
                class="formDialogStyle formLeftStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" style="padding: 0px">
              <el-form-item
                label="监火人"
                prop="gender"
                class="formDialogStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" style="padding: 0px">
              <el-form-item
                label="动火初审人"
                prop="weight"
                class="formDialogStyle"
              >
                <el-input v-model="form.weight" type="number"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24" style="padding: 0px">
              <el-form-item
                label="实施安全教育人"
                prop="name"
                class="formDialogStyle formLeftStyle formBottomStyle"
              >
                <el-input v-model="form.name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse>
      </el-form>
      <div
        style="
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 10px;
        "
      >
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="附件信息"
      :visible.sync="attachmentDialogVisible"
      width="40%"
      append-to-body
    >
      <div v-for="(item, index) in attachmentList" :key="index">
        <div class="demo-list-item__name">
          附件{{ index + 1 }}:
          <span @click="handleView(item)" class="attachment-item">
            <i class="el-icon-document"></i>
            {{ item.split("/").pop() }}
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjNewsInfo,
  getZjNewsInfo,
  delZjNewsInfo,
  addZjNewsInfo,
  updateZjNewsInfo,
} from "@/api/inspection/zjNewsInfo";
import {
  healthList,
  getHealthDetail,
  addHealth,
  updateHealth,
  delHealth,
  exportHealth,
} from "@/api/healthRecord/index";

export default {
  name: "ZjNewsInfo",
  data() {
    return {
      activeNames: ["1", "2", "3", "4"],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻管理表格数据
      zjNewsInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 弹出层是否显示附件信息
      attachmentDialogVisible: false,
      // 附件列表
      attachmentList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
      },
      // 表单参数
      form: {
        id: "",
        // 姓名
        name: "",
        // 所属厂区
        // factoryArea: "",
        // 账号
        // account: "",
        // 车间
        // workshop: "",
        // 岗位
        position: "",
        // 性别，1-男，2-女
        gender: "",
        // 出生日期
        birthDate: "",
        // 体重（公斤）
        weight: "",
        // 安全科
        // safetyDepartment: "",
        // 吸烟状态，1-是，2-否，3-已戒
        smokingStatus: "",
        // 饮酒状态，1-是，2-否
        drinkingStatus: "",
        // 是否患有肝病、肾病等慢性疾病，1-是，2-否
        chronicDisease: "",
        // 听力较差吗，1-是，2-否
        hearingProblem: "",
        // 经常有耳鸣现象吗，1-是，2-否
        tinnitus: "",
        // 有时会流鼻血吗，1-是，2-否
        nosebleed: "",
        // 有常年腹泻的现象吗，1-是，2-否
        diarrhea: "",
        // 常感到关节肿痛吗，1-是，2-否
        jointPain: "",
        // 经常失眠吗，1-是，2-否
        insomnia: "",
        // 目前有无长期服药史(连续服药3个月以上)，1-是，2-否
        longTermMedication: "",
        // 对自身健康状况是否了解，1-是，2-否
        healthAwareness: "",
        // 体检次数，1-一年一次，2-半年一次，3-三月一次，4-基本不参加
        physicalExamFrequency: "",
        // 是否有高血压、高血脂、糖尿病，1-是，2-否
        hypertensionDiabetes: "",
        // 是否有胸闷、经常性头晕等情况，1-是，2-否
        chestDiscomfort: "",
        // 是否有贫血、低血压等情况，1-是，2-否
        anemiaEtc: "",
        // 是否患有非传染性疾病，1-是，2-否
        nonInfectiousDisease: "",
        // 是否有肢体抽筋（或酸痛）等情况，1-是，2-否
        bodyCramps: "",
        // 是否有气虚（虚弱无力）等情况，1-是，2-否
        qiDeficiency: "",
        // 是否有过痉挛或癫痫的现象，1-是，2-否
        convulsionEpilepsy: "",
        // 是否受伤造成骨折或骨裂现象，1-是，2-否
        fractureHistory: "",
        // 是否曾患乙型肝炎表面抗原阳性，1-是，2-否
        hepatitisB: "",
        // 是否曾患丙型肝炎抗体阳性，1-是，2-否
        hepatitisC: "",
        // 是否曾患肺结核，1-是，2-否
        tuberculosis: "",
        // 体检报告附件路径
        medicalReport: "",
      },
      // 表单校验
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        // factoryArea: [
        //   { required: true, message: "请选择所属厂区", trigger: "change" },
        // ],
        // account: [{ required: true, message: "请选择账号", trigger: "change" }],
        gender: [{ required: true, message: "请选择性别", trigger: "change" }],
        birthDate: [
          { required: true, message: "请选择出生日期", trigger: "change" },
        ],
        weight: [{ required: true, message: "请输入体重", trigger: "blur" }],
        // safetyDepartment: [
        //   { required: true, message: "请选择安全科", trigger: "change" },
        // ],
        smokingStatus: [
          { required: true, message: "请选择吸烟状态", trigger: "change" },
        ],
        drinkingStatus: [
          { required: true, message: "请选择饮酒状态", trigger: "change" },
        ],
        chronicDisease: [
          { required: true, message: "请选择慢性疾病", trigger: "change" },
        ],
        hearingProblem: [
          { required: true, message: "请选择听力状况", trigger: "change" },
        ],
        tinnitus: [
          { required: true, message: "请选择耳鸣状况", trigger: "change" },
        ],
        nosebleed: [
          { required: true, message: "请选择流鼻血状况", trigger: "change" },
        ],
        diarrhea: [
          { required: true, message: "请选择腹泻状况", trigger: "change" },
        ],
        jointPain: [
          { required: true, message: "请选择关节肿痛状况", trigger: "change" },
        ],
        insomnia: [
          { required: true, message: "请选择失眠状况", trigger: "change" },
        ],
        longTermMedication: [
          { required: true, message: "请选择服药史状况", trigger: "change" },
        ],
        healthAwareness: [
          { required: true, message: "请选择健康状况", trigger: "change" },
        ],
        physicalExamFrequency: [
          { required: true, message: "请选择体检次数", trigger: "change" },
        ],
        hypertensionDiabetes: [
          { required: true, message: "请选择三高状况", trigger: "change" },
        ],
        chestDiscomfort: [
          { required: true, message: "请选择胸闷头晕状况", trigger: "change" },
        ],
        anemiaEtc: [
          {
            required: true,
            message: "请选择贫血、低血压状况",
            trigger: "change",
          },
        ],
        nonInfectiousDisease: [
          {
            required: true,
            message: "请选择非传染性状况",
            trigger: "change",
          },
        ],
        bodyCramps: [
          {
            required: true,
            message: "请选择肢体状况",
            trigger: "change",
          },
        ],
        qiDeficiency: [
          {
            required: true,
            message: "请选择气虚状况",
            trigger: "change",
          },
        ],
        convulsionEpilepsy: [
          {
            required: true,
            message: "请选择痉挛癫痫状况",
            trigger: "change",
          },
        ],
        fractureHistory: [
          {
            required: true,
            message: "请选择骨折状况",
            trigger: "change",
          },
        ],
        hepatitisB: [
          {
            required: true,
            message: "请选择乙肝状况",
            trigger: "change",
          },
        ],
        hepatitisC: [
          {
            required: true,
            message: "请选择丙肝状况",
            trigger: "change",
          },
        ],
        tuberculosis: [
          {
            required: true,
            message: "请选择肺结核状况",
            trigger: "change",
          },
        ],
        medicalReport: [
          {
            required: true,
            message: "请上传附件",
            trigger: "change",
          },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.editorRef && this.$refs.editorRef.quill) {
        console.log("this.$refs.editorRef.quill", this.$refs.editorRef.quill);
        this.$refs.editorRef.quill.on(
          "text-change",
          (delta, oldDelta, source) => {
            console.log("text-change", delta, oldDelta, source);
            if (source === "user") {
              this.checkForPastedImages();
            }
          }
        );
      }
    });
  },
  methods: {
    /** 图片上传失败回调 */
    handleImageUploadError(error) {
      this.$message.error("图片上传失败: " + error.message);
    },

    handleViewAttachment(value) {
      this.attachmentDialogVisible = true;
      console.log(value, "附件");
      this.attachmentList = value.split(",");
    },
    async handleView(value) {
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    /** 查询新闻管理列表 */
    getList() {
      this.loading = true;
      healthList(this.queryParams).then((res) => {
        console.log("获取数组", res);
        this.zjNewsInfoList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: "",
        // 姓名
        name: "",
        // 所属厂区
        // factoryArea: "",
        // 账号
        // account: "",
        // 车间
        // workshop: "",
        // 岗位
        position: "",
        // 性别，1-男，2-女
        gender: "",
        // 出生日期
        birthDate: "",
        // 体重（公斤）
        weight: "",
        // 安全科
        // safetyDepartment: "",
        // 吸烟状态，1-是，2-否，3-已戒
        smokingStatus: "",
        // 饮酒状态，1-是，2-否
        drinkingStatus: "",
        // 是否患有肝病、肾病等慢性疾病，1-是，2-否
        chronicDisease: "",
        // 听力较差吗，1-是，2-否
        hearingProblem: "",
        // 经常有耳鸣现象吗，1-是，2-否
        tinnitus: "",
        // 有时会流鼻血吗，1-是，2-否
        nosebleed: "",
        // 有常年腹泻的现象吗，1-是，2-否
        diarrhea: "",
        // 常感到关节肿痛吗，1-是，2-否
        jointPain: "",
        // 经常失眠吗，1-是，2-否
        insomnia: "",
        // 目前有无长期服药史(连续服药3个月以上)，1-是，2-否
        longTermMedication: "",
        // 对自身健康状况是否了解，1-是，2-否
        healthAwareness: "",
        // 体检次数，1-一年一次，2-半年一次，3-三月一次，4-基本不参加
        physicalExamFrequency: "",
        // 是否有高血压、高血脂、糖尿病，1-是，2-否
        hypertensionDiabetes: "",
        // 是否有胸闷、经常性头晕等情况，1-是，2-否
        chestDiscomfort: "",
        // 是否有贫血、低血压等情况，1-是，2-否
        anemiaEtc: "",
        // 是否患有非传染性疾病，1-是，2-否
        nonInfectiousDisease: "",
        // 是否有肢体抽筋（或酸痛）等情况，1-是，2-否
        bodyCramps: "",
        // 是否有气虚（虚弱无力）等情况，1-是，2-否
        qiDeficiency: "",
        // 是否有过痉挛或癫痫的现象，1-是，2-否
        convulsionEpilepsy: "",
        // 是否受伤造成骨折或骨裂现象，1-是，2-否
        fractureHistory: "",
        // 是否曾患乙型肝炎表面抗原阳性，1-是，2-否
        hepatitisB: "",
        // 是否曾患丙型肝炎抗体阳性，1-是，2-否
        hepatitisC: "",
        // 是否曾患肺结核，1-是，2-否
        tuberculosis: "",
        // 体检报告附件路径
        medicalReport: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增动火安全作业证";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHealthDetail(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改动火安全作业证";
      });
    },
    temporarySave() {
      this.open = false;
    },
    /** 提交按钮 */
    submitForm(type) {
      console.log("提交清单", this.form);
      // this.form.newStatus = type;
      // this.form.publishTime = new Date();
      this.$refs["form"].validate((valid, fields) => {
        console.log("valid", fields);
        if (valid) {
          if (this.form.id != null && this.form.id != "") {
            updateHealth(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHealth(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        } else {
          const obj = Object.keys(fields)[0];
          const firstValueList = fields[obj];
          this.$message.error("" + firstValueList[0].message);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // console.log("批量删除", row);
      const ids = [];
      ids.push(row.id || this.ids);
      this.$modal
        .confirm("是否确认删除?")
        .then(function () {
          return delHealth(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/form/export",
        {
          ...this.queryParams,
        },
        `健康档案_${new Date().getTime()}.xlsx`
      );
    },
    handleImageUploadSuccess() {},
    getInfo() {
      // console.log("getInfo", this.form.smokingStatus);
    },
    cancleSave() {},
  },
};
</script>
<style scoped lang="scss">
::v-deep .demo-list-item__name {
  .attachment-item {
    cursor: pointer;
    color: #409eff;
    &:hover {
      text-decoration-line: underline;
    }
  }
}
::v-deep .formDialog .el-form-item__label {
  color: #303133;
  padding: 4px;
  // height: 60px;
  border-right: 1px solid #606266 !important;
}
.operateDialog ::v-deep .el-dialog__body {
  padding: 10px 20px !important;
}
::v-deep .formDialog .el-form-item__content {
  padding: 4px 0 0 4px;
}
::v-deep .formFileUpload .el-form-item__content {
  border-left: 1px solid;
}
::v-deep .formFileUpload .el-form-item__label {
  border-right: 0px !important;
}

::v-deep .el-select {
  width: 100% !important;
}

::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
.formDivStyle {
  color: #303133;
  font-weight: 550;
  font-size: 14px;
  width: 100%;
  line-height: 42px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.formDivContentStyle {
  color: #303133;
  font-size: 12px;
  width: 100%;
  min-height: 46px;
  // line-height: 42px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.formDivLeftContentStyle {
  justify-content: left;
}
.formDialogBNoRightStyle {
  border-right: 0px !important;
}
.formDialogStyle {
  border-top: 1px solid #8a8787;
  border-right: 1px solid #8a8787;
  padding: 0px 12px;
  // height: 60px;
}
.formTopStyle {
  border-top: 1px solid #8a8787;
  padding: 0px 12px;
}
// .formRightStyle {
//   border-right: 1px solid #8a8787;
// }
.formBottomStyle {
  border-bottom: 1px solid #8a8787;
}
.formLeftStyle {
  border-left: 1px solid #8a8787;
}
::v-deep .formNoLabelStyle .el-form-item__content {
  margin-left: 0px !important;
  padding: 4px;
  line-height: 36px;
}
::v-deep .formNoLabelMoreContentStyle .el-form-item__content {
  line-height: 100px;
}
::v-deep .formNoLabelMoreContent2Style .el-form-item__content {
  line-height: 160px;
}
::v-deep .formFormDivContentStyle .el-form-item__content {
  width: 90px !important;
}
::v-deep .formFormDivContent1Style .el-form-item__content {
  width: 100% !important;
}
::v-deep .formFormDivContent2Style .el-form-item__content {
  width: 226% !important;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0px;
  font-size: 13px;
  color: #303133;
  border-left: 1px solid #8a8787;
  border-right: 1px solid #8a8787 !important;
}
::v-deep .formDialog .el-form-item {
  margin-bottom: 0px !important;
}
::v-deep .formDialog .el-form-item {
  margin-bottom: 0px !important;
  text-align: left;
}
::v-deep .formDialog .formLongText .el-form-item__label {
  margin-bottom: 0px !important;
  text-align: left;
}
::v-deep .formDialog .formLongTitleText .el-form-item__label {
  margin-bottom: 0px !important;
  text-align: left;
  color: #303133;
  font-size: 18px;
  // font-weight: 550;
}
.formTitle {
  display: flex;
  justify-content: left;
  align-items: center;
  height: 40px;
  color: #303133;
  font-size: 18px;
  // font-weight: 550;
}
::v-deep .el-form-item__error {
  display: none;
  color: #ff4949;
  font-size: 10px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 65% !important;
  // top: 27% !important;
  // left: 50% !important;
  left: 1% !important;
}
</style>
<style scoped>
::v-deep .viewMode .el-form-item__label::before {
  content: none !important;
}
::v-deep .formDialog .el-form-item__label {
  color: #303133;
  padding: 4px;
  border-right: 1px solid #606266 !important;
  text-align: center;
}
::v-deep .formLongLabelStyle .el-form-item__label {
  font-size: 12px !important;
}
::v-deep .formTextareaStyle .el-textarea__inner {
  resize: none;
  width: 108%;
  /* min-height: 120px; */
}
</style>