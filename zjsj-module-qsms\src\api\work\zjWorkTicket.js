import request from "@/utils/request";

// 查询作业票列表
export function listZjWorkTicket(query) {
  return request({
    url: "/work/zjWorkTicket/list",
    method: "get",
    params: query,
  });
}

// 查询作业票详细
export function getZjWorkTicket(id) {
  return request({
    url: "/work/zjWorkTicket/" + id,
    method: "get",
  });
}

// 新增作业票
export function addZjWorkTicket(data) {
  return request({
    url: "/work/zjWorkTicket",
    method: "post",
    data: data,
  });
}

// 修改作业票
export function updateZjWorkTicket(data) {
  return request({
    url: "/work/zjWorkTicket",
    method: "put",
    data: data,
  });
}

// 删除作业票
export function delZjWorkTicket(id) {
  return request({
    url: "/work/zjWorkTicket/" + id,
    method: "delete",
  });
}
// 作业区域
export function getWorkAreaList(query) {
  return request({
    url: "/work/zjWorkArea/queryZjWorkAreaTree",
    method: "get",
    params: query,
  });
}
// 数据字典
// /system/dict/data/list?pageNum=1&pageSize=50&dictType=fxbs
export function getDictDataList(query) {
  return request({
    url: "system/dict/data/list",
    method: "get",
    params: query,
  });
}
