import request from '@/utils/request'

// 查询企业项目列表
export function listProjectinspection(query) {
  return request({
    url: '/pc/projectinspection/list',
    method: 'get',
    params: query
  })
}

// 查询企业项目详细
export function getProjectinspection(projectinspectionid) {
  return request({
    url: '/system/projectinspection/' + projectinspectionid,
    method: 'get'
  })
}

// 新增企业项目
export function addProjectinspection(data) {
  return request({
    url: '/pc/projectinspection',
    method: 'post',
    data: data
  })
}

// 修改企业项目
export function updateProjectinspection(data) {
  return request({
    url: '/pc/projectinspection',
    method: 'put',
    data: data
  })
}

// 删除企业项目
export function delProjectinspection(projectinspectionid) {
  return request({
    url: '/pc/projectinspection/' + projectinspectionid,
    method: 'delete'
  })
}


// 单位下建筑列表
export function getUnitBuildList(projectinspectionid) {
  return request({
    url: '/api/selectAjBuildingInfoByQyId/' + projectinspectionid,
    method: 'get'
  })
}

// 建筑下单位列表
export function getBuildUnitList(projectinspectionid) {
  return request({
    url: '/api/selectProjectinspectionidByBId/' + projectinspectionid,
    method: 'get'
  })
}

// 新增数据字典
export function addCustomDict(data) {
  return request({
    url: '/api/addDictData',
    method: 'post',
    data: data
  })
}

// 单位类别1列表
export function getUnitCate1List() {
  return request({
    url: '/api/type/aj_densely_populated_areas',
    method: 'get'
  })
}

// 单位类别3列表
export function getUnitCate3List() {
  return request({
    url: '/api/type/aj_nine_small_places',
    method: 'get'
  })
}