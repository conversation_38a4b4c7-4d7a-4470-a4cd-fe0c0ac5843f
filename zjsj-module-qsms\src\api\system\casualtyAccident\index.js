import request from '@/utils/request'

// 查询伤亡事故统计（按周期、范围汇总事故及伤亡数据）列表
export function listStatistics(query) {
  return request({
    url: '/system/statistics/list',
    method: 'get',
    params: query
  })
}

// 查询伤亡事故统计（按周期、范围汇总事故及伤亡数据）详细
export function getStatistics(id) {
  return request({
    url: '/system/statistics/' + id,
    method: 'get'
  })
}

// 新增伤亡事故统计（按周期、范围汇总事故及伤亡数据）
export function addStatistics(data) {
  return request({
    url: '/system/statistics',
    method: 'post',
    data: data
  })
}

// 修改伤亡事故统计（按周期、范围汇总事故及伤亡数据）
export function updateStatistics(data) {
  return request({
    url: '/system/statistics',
    method: 'put',
    data: data
  })
}

// 删除伤亡事故统计（按周期、范围汇总事故及伤亡数据）
export function delStatistics(id) {
  return request({
    url: '/system/statistics/' + id,
    method: 'delete'
  })
}
