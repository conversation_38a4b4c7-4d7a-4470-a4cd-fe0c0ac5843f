<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">规范分类设置</div>
            <div class="top-right">
              <!-- <el-button type="primary" icon="el-icon-download" size="small">下载检查规范模板</el-button>
                          <el-upload style="margin-left: 20px;" action :auto-upload="false" :show-file-list="false"
                              :on-change="uploadFile" accept=".xlsx, .xls">
                              <el-button type="primary" icon="el-icon-upload2" size="small">上传检查规范</el-button>
                          </el-upload> -->
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="12">
      <el-col :span="12" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <div>
              <span>规范大类：</span>
              <el-select
                v-if="examOption.length > 0"
                v-model="examType"
                placeholder="请选择"
                @change="getCategoryList()"
              >
                <el-option
                  v-for="item in examOption"
                  :key="item.examid"
                  :label="item.examtype"
                  :value="item.examid"
                >
                </el-option>
              </el-select>
            </div>
            <div>
              <!-- <el-button type="primary" size="small">字符串排序</el-button> -->
              <el-button type="primary" size="small" @click="addlei(1)"
                >新建大类</el-button
              >
              <!-- <el-button type="primary" size="small">导出明细</el-button> -->
            </div>
          </div>
          <div class="card-content">
            <div
              :class="
                treeIndex == index ? 'card-click-bg card-item' : 'card-item'
              "
              v-for="(item, index) in treeData"
              :key="index"
              @click="changeCategory(index)"
            >
              <div class="card-item-title">
                {{ item.hazardName }}
                <el-tag
                  v-if="item.isDaji"
                  style="margin-left: 12px; font-weight: normal"
                  type="danger"
                  >大机</el-tag
                >
              </div>
              <div
                class="card-item-tools"
                :style="
                  treeIndex == index
                    ? 'visibility: visible;'
                    : 'visibility: hidden;'
                "
              >
                <el-button type="text" size="small" @click.stop="deleteLei(1)"
                  >删除</el-button
                >
                <el-button type="text" size="small" @click.stop="editName(1)"
                  >修改</el-button
                >
                <el-button type="text" size="small" @click.stop="toUp(1)"
                  >上移</el-button
                >
                <el-button type="text" size="small" @click.stop="toBottom(1)"
                  >下移</el-button
                >
                <!-- <el-button type="text" size="small">导出</el-button> -->
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="card-header">
            <div v-if="treeData.length > 0">
              <span>{{ treeData[treeIndex].hazardName }}</span>
            </div>
            <div>
              <!-- <el-switch v-model="daji">
                          </el-switch><span style="margin: 0 20px 0 6px; color: #333;">大机</span> -->
              <el-button type="primary" size="small" @click="addlei(2)"
                >新建小类</el-button
              >
            </div>
          </div>
          <div class="card-content">
            <div
              :class="
                treeChildrenIndex == index
                  ? 'card-click-bg card-item'
                  : 'card-item'
              "
              v-for="(item, index) in treeChildren"
              :key="index"
              @click="treeChildrenIndex = index"
            >
              <div class="card-item-title">{{ item.hazardName }}</div>
              <div
                class="card-item-title text-underline"
                @click="showGuiFList(index)"
              >
                {{ item.specificationCount }}条规范
              </div>
              <!-- <div class="card-item-title text-underline">24条隐患</div> -->
              <div
                class="card-item-tools"
                :style="
                  treeChildrenIndex == index
                    ? 'visibility: visible;'
                    : 'visibility: hidden;'
                "
              >
                <el-button type="text" size="small" @click.stop="deleteLei(2)"
                  >删除</el-button
                >
                <el-button type="text" size="small" @click.stop="editName(2)"
                  >修改</el-button
                >
                <el-button type="text" size="small" @click.stop="toUp(2)"
                  >上移</el-button
                >
                <el-button type="text" size="small" @click.stop="toBottom(2)"
                  >下移</el-button
                >
                <!-- <el-button type="text" size="small">导出</el-button> -->
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      title="请输入"
      :visible.sync="dialogVisible"
      width="30%"
      style="margin-top: 20vh"
      center
    >
      <el-input v-model="inputValue" placeholder="请输入名称"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 规范列表弹窗 -->
    <el-dialog :visible.sync="showDetailList" width="80%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailListObj.title }}</div>
        <div class="title-btn" style="margin-right: 50px">
          <el-button size="small" type="primary" icon="el-icon-download"
            >导出规则</el-button
          >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-download"
            @click="handleEditDetail()"
            >添加规则</el-button
          >
        </div>
      </div>
      <div class="detail-content">
        <el-table
          :data="detailListObj.tableData"
          stripe
          style="width: 100%"
          :key="Math.random()"
          height="calc(100vh - 250px)"
        >
          <el-table-column prop="hazardLevel" label="等级" width="60">
          </el-table-column>
          <el-table-column
            label="隐患类型"
            align="center"
            prop="hazardType"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.hazardType }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="存在问题"
            align="center"
            prop="existingProblems"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.existingProblems }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="违反对应规范"
            align="center"
            prop="violationRegulations"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.violationRegulations }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="可能产生后果" align="center" prop="possibleConsequences" :show-overflow-tooltip="true">
                      <template slot-scope="scope">
                          <span>{{ scope.row.possibleConsequences }}</span>
                      </template>
                  </el-table-column> -->
          <el-table-column
            label="建议"
            align="center"
            prop="proposal"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.proposal }}</span>
            </template>
          </el-table-column>
          <el-table-column label="附件" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="!!scope.row.fileList"
                size="mini"
                type="text"
                @click="handleSafetyTrainingRecords(scope.row.fileList)"
                >查看</el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="附件状态">
            <template slot-scope="scope">
              <span>{{ scope.row.fileStatus }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            width="150"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                plain
                @click="handleEditDetail(scope.row)"
                v-hasPermi="['system:guifan:detail:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="danger"
                plain
                @click="handleDeleteDetail(scope.row)"
                v-hasPermi="['system:guifan:detail:delete']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="showEditDetail" width="50%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>编辑</div>
      </div>
      <div class="detail-content">
        <div class="detail-edit-content">
          <el-form ref="form" :model="editDetailForm" label-width="100px">
            <el-form-item label="等级">
              <el-select
                v-model="editDetailForm.hazardLevel"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in gradeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="隐患类型">
              <el-input v-model="editDetailForm.hazardType"></el-input>
            </el-form-item>
            <el-form-item label="存在问题">
              <el-input
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 5 }"
                v-model="editDetailForm.existingProblems"
              ></el-input>
            </el-form-item>
            <el-form-item label="违反对应规范">
              <el-input
                type="textarea"
                :autosize="{ minRows: 5, maxRows: 7 }"
                v-model="editDetailForm.violationRegulations"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="可能产生后果">
                          <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" v-model="editDetailForm.possibleConsequences"></el-input>
                      </el-form-item> -->
            <el-form-item label="附件">
              <file-upload v-model="editDetailForm.fileList"></file-upload>
            </el-form-item>
            <el-form-item label="附件状态">
              <el-select
                v-model="editDetailForm.fileStatus"
                style="width: 100%"
                placeholder="请选择"
              >
                <el-option label="有效" value="1"></el-option>
                <el-option label="废止" value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="建议">
              <el-input
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 5 }"
                v-model="editDetailForm.proposal"
              ></el-input>
            </el-form-item>

            <el-form-item
              style="display: flex; justify-content: center; margin-left: -80px"
            >
              <el-button @click="showEditDetail = false">取消</el-button>
              <el-button type="primary" @click="onSubmit">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getExamList,
  getCategoryType,
  getsubclassInfo,
  getSpecificationInfo,
  updateList,
  editCategoryName,
  deleteCategory,
  editOrderNum,
  addGuif,
  editGuif,
  deleteGuif,
} from "@/api/system/guifan";

export default {
  data() {
    return {
      // 类型选项列表
      examOption: [],
      // 类型
      examType: "",
      // 等级选项列表
      gradeOptions: [
        { label: "一般", value: "一般" },
        { label: "较大", value: "较大" },
        { label: "重大", value: "重大" },
      ],
      // 标记选项列表
      biaojiOptions: [
        { label: "1", value: "1" },
        { label: "2", value: "2" },
        { label: "3", value: "3" },
      ],
      // 显示规范编辑弹窗
      showEditDetail: false,
      editDetailForm: {},
      // 规范详情列表
      // tableData: [
      //     {
      //         biaoji: '1',
      //         grade: '1',
      //         title: '标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,',
      //         detail: '详细标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,',
      //         houguo: '后果标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,标题标题安全生产,'
      //     },
      // ],
      // 规范列表弹窗
      showDetailList: false,
      // 规范列表弹窗内容
      detailListObj: {},
      daji: false,
      // 左侧大类数组
      treeData: [],
      // 右侧小类数组
      treeChildren: [],
      treeIndex: 0,
      treeChildrenIndex: 0,
      dialogVisible: false,
      inputValue: "",
      isAddDa: true,
      isAddXiao: false,
      isEditDa: false,
      isEditXiao: false,
      fileList: [],
    };
  },
  created() {
    this.getExamList();
  },
  methods: {
    // 显示规范列表弹窗
    showGuiFList(index) {
      this.detailListObj.title = this.treeChildren[index].hazardName;

      getSpecificationInfo({
        categoryId: this.treeChildren[index].parentId,
        subclassId: this.treeChildren[index].hazardid,
      }).then((res) => {
        this.$forceUpdate();
        this.detailListObj.tableData = res;
        this.showDetailList = true;
      });
    },
    // 获取类型列表
    getExamList() {
      getExamList().then((res) => {
        this.examOption = res;
        this.examType = this.examOption[0].examid;
        this.getCategoryList();
      });
    },

    // 获取大类
    getCategoryList() {
      getCategoryType({
        examid: this.examType,
      }).then(async (res) => {
        this.treeData = res;
        this.getSubCategory();
      });
    },
    // 切换大类
    changeCategory(index) {
      this.treeIndex = index;
      this.getSubCategory();
    },
    // 获取小类
    getSubCategory() {
      if (!this.treeData[this.treeIndex].hazardid) {
        this.treeChildren = [];
        return;
      }
      getsubclassInfo({
        hazardid: this.treeData[this.treeIndex].hazardid,
      }).then((res) => {
        this.treeChildren = res;
      });
    },
    // 显示编辑规范详情
    handleEditDetail(row) {
      // this.showDetailList = false
      if (row) {
        this.editDetailForm = JSON.parse(JSON.stringify(row));
        this.showEditDetail = true;
      } else {
        this.editDetailForm = {};
        this.showEditDetail = true;
      }
    },
    // 删除规范详情
    handleDeleteDetail(row) {
      this.$confirm("是否删除该项", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteGuif(row.id).then((res) => {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        });
      });
    },
    //onSubmit
    onSubmit() {
      console.log(this.editDetailForm);
      if (this.editDetailForm.categoryId && this.editDetailForm.subclassId) {
        // 修改
        editGuif({
          ...this.editDetailForm,
        }).then((res) => {
          this.showEditDetail = false;
          this.editDetailForm = {};
          this.showGuiFList();
        });
      } else {
        // 添加
        addGuif({
          ...this.editDetailForm,
          categoryId: this.treeChildren[this.treeChildrenIndex].parentId,
          subclassId: this.treeChildren[this.treeChildrenIndex].hazardid,
        }).then((res) => {
          this.showEditDetail = false;
          this.editDetailForm = {};
          this.showGuiFList();
        });
      }
    },
    addlei(type) {
      this.dialogVisible = true;
      this.inputValue = "";
      if (type == 1) {
        this.isAddDa = true;
        this.isAddXiao = false;
        this.isEditDa = false;
        this.isEditXiao = false;
      } else {
        this.isAddDa = false;
        this.isAddXiao = true;
        this.isEditDa = false;
        this.isEditXiao = false;
      }
    },
    editName(type) {
      this.dialogVisible = true;
      this.isAddDa = false;
      this.isAddXiao = false;
      this.isEditDa = false;
      this.isEditXiao = false;
      if (type == 1) {
        this.isEditDa = true;
        this.inputValue = this.treeData[this.treeIndex].hazardName;
      } else {
        this.isEditXiao = true;
        this.inputValue = this.treeChildren[this.treeChildrenIndex].hazardName;
      }
    },
    submit() {
      this.dialogVisible = false;
      console.log(this.inputValue);
      if (this.isAddDa) {
        console.log("添加大类");
        // treeIndex: 0,
        // treeChildrenIndex: 0,
        updateList({
          hazardName: this.inputValue,
          parentId: 0,
          examid: this.examType,
          orderNum: this.treeData[this.treeData.length - 1].orderNum + 1,
        }).then((res) => {
          this.$message.success("添加成功");
          this.getCategoryList();
        });
      } else if (this.isAddXiao) {
        console.log("添加小类");
        updateList({
          hazardName: this.inputValue,
          parentId: this.treeData[this.treeIndex].hazardid,
          examid: this.examType,
          orderNum:
            this.treeChildren.length == 0
              ? 1
              : this.treeChildren[this.treeChildren.length - 1].orderNum + 1,
        }).then((res) => {
          this.$message.success("添加成功");
          this.getSubCategory();
        });
      } else if (this.isEditDa) {
        console.log("编辑大类");
        editCategoryName({
          ...this.treeData[this.treeIndex],
          children: null,
          hazardName: this.inputValue,
        }).then((res) => {
          this.$message.success("编辑成功");
          this.getCategoryList();
        });
      } else {
        console.log("编辑小类");
        editCategoryName({
          ...this.treeChildren[this.treeChildrenIndex],
          hazardName: this.inputValue,
        }).then((res) => {
          this.$message.success("编辑成功");
          this.getSubCategory();
        });
      }
    },
    toUp(type) {
      if (type == 1) {
        if (this.treeIndex > 0) {
          let params = {
            hazardid: this.treeData[this.treeIndex].hazardid,
            orderNum: this.treeData[this.treeIndex].orderNum,
            moveHazardid: this.treeData[this.treeIndex - 1].hazardid,
            moveOrderNum: this.treeData[this.treeIndex - 1].orderNum,
          };
          console.log(params);
          editOrderNum(params).then((res) => {
            this.$message.success("上移成功");
            this.getCategoryList();
            this.treeIndex--;
          });
          // 当前元素与上一个元素交换
          // this.treeData.splice(this.treeIndex - 1, 0, this.treeData.splice(this.treeIndex, 1)[0])

          // this.treeIndex--
        }
      } else {
        if (this.treeChildrenIndex > 0) {
          let params = {
            hazardid: this.treeChildren[this.treeChildrenIndex].hazardid,
            orderNum: this.treeChildren[this.treeChildrenIndex].orderNum,
            moveHazardid:
              this.treeChildren[this.treeChildrenIndex - 1].hazardid,
            moveOrderNum:
              this.treeChildren[this.treeChildrenIndex - 1].orderNum,
          };
          console.log(params);
          editOrderNum(params).then((res) => {
            this.$message.success("上移成功");
            this.getSubCategory();
            this.treeChildrenIndex--;
          });
          // 当前元素与上一个元素交换
          // this.treeChildren.splice(this.treeChildrenIndex - 1, 0, this.treeChildren.splice(this.treeChildrenIndex, 1)[0])
          // this.treeChildrenIndex--
        }
      }
    },
    toBottom(type) {
      if (type == 1) {
        if (this.treeIndex < this.treeData.length - 1) {
          let params = {
            hazardid: this.treeData[this.treeIndex].hazardid,
            orderNum: this.treeData[this.treeIndex].orderNum,
            moveHazardid: this.treeData[this.treeIndex + 1].hazardid,
            moveOrderNum: this.treeData[this.treeIndex + 1].orderNum,
          };
          console.log(params);
          editOrderNum(params).then((res) => {
            this.$message.success("下移成功");
            this.getCategoryList();
            this.treeIndex++;
          });
          // 当前元素与下一个元素交换 treeIndex + 1
          // this.treeData.splice(this.treeIndex + 1, 0, this.treeData.splice(this.treeIndex, 1)[0])
          // this.treeIndex++
        }
      } else {
        if (this.treeChildrenIndex < this.treeChildren.length - 1) {
          let params = {
            hazardid: this.treeChildren[this.treeChildrenIndex].hazardid,
            orderNum: this.treeChildren[this.treeChildrenIndex].orderNum,
            moveHazardid:
              this.treeChildren[this.treeChildrenIndex + 1].hazardid,
            moveOrderNum:
              this.treeChildren[this.treeChildrenIndex + 1].orderNum,
          };
          console.log(params);
          editOrderNum(params).then((res) => {
            this.$message.success("下移成功");
            this.getSubCategory();
            this.treeChildrenIndex++;
          });
          // 当前元素与下一个元素交换
          // this.treeChildren.splice(this.treeChildrenIndex + 1, 0, this.treeChildren.splice(this.treeChildrenIndex, 1)[0])
          // this.treeChildrenIndex++
        }
      }
    },
    deleteLei(type) {
      if (type == 1) {
        // 删除大类
        this.$confirm("是否删除该类", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          deleteCategory(this.treeData[this.treeIndex].hazardid).then((res) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            if (this.treeIndex == this.treeData.length - 1) {
              this.treeIndex--;
            }
            this.getCategoryList();
          });
        });
      } else {
        // 删除小类
        this.$confirm("是否删除该类", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          deleteCategory(
            this.treeChildren[this.treeChildrenIndex].hazardid
          ).then((res) => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            if (this.treeChildrenIndex == this.treeChildren.length - 1) {
              this.treeChildrenIndex--;
            }
            this.getSubCategory();
          });
        });
      }
    },
    uploadFile(file) {
      console.log(file);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 100px);
}

.el-row {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.box-card {
  height: calc(100vh - 162px);
  font-size: 14px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-content {
  height: calc(100vh - 250px);
  // background-color: #000;
  overflow-y: auto;
}

.card-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  color: #333;
}

.card-item-title {
  min-width: 25%;
  font-size: 12px;
  font-weight: bold;
  line-height: 44px;
  cursor: pointer;
}

.text-underline:hover {
  text-decoration: underline;
}

.card-item-tools {
  visibility: hidden;
}

.card-click-bg {
  background-color: rgba($color: #3c80e8, $alpha: 0.1);
}

.detail-content {
  width: 100%;
  height: 70vh;
  overflow-y: auto;
  color: #333;
}
.detail-edit-content {
  width: 80%;
  margin: 0 auto;
}
</style>
