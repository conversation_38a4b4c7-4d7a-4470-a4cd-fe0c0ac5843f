import request from '@/utils/request'

// 查询带班计划（记录各时段带班安排、人员及工作内容）列表
export function listPlan(query) {
  return request({
    url: '/system/leadPlan/list',
    method: 'get',
    params: query
  })
}

// 查询带班计划（记录各时段带班安排、人员及工作内容）详细
export function getPlan(id) {
  return request({
    url: '/system/leadPlan/' + id,
    method: 'get'
  })
}

// 新增带班计划（记录各时段带班安排、人员及工作内容）
export function addPlan(data) {
  return request({
    url: '/system/leadPlan',
    method: 'post',
    data: data
  })
}

// 修改带班计划（记录各时段带班安排、人员及工作内容）
export function updatePlan(data) {
  return request({
    url: '/system/leadPlan',
    method: 'put',
    data: data
  })
}

// 删除带班计划（记录各时段带班安排、人员及工作内容）
export function delPlan(id) {
  return request({
    url: '/system/leadPlan/' + id,
    method: 'delete'
  })
}
