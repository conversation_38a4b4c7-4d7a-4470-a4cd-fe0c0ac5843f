{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjEmployeeCasualtyAccidents\\index.vue?vue&type=style&index=0&id=8b9b6350&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjEmployeeCasualtyAccidents\\index.vue", "mtime": 1757497628586}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757382153709}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757382157092}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757382154814}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757382152798}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDnoa7kv53liIbpobXlm7rlrprlnKjlupXpg6jvvIzlj4LogIPlhbbku5bpobXpnaLnmoTlrp7njrAgKi8NCi5hcHAtY29udGFpbmVyIHsNCiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gODRweCk7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi8qIOaQnOe0ouihqOWNleWMuuWfnyAqLw0KLmFwcC1jb250YWluZXIgLmVsLWZvcm0gew0KICBmbGV4LXNocmluazogMDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLyog5bel5YW35qCP5Yy65Z+fICovDQouYXBwLWNvbnRhaW5lciAuZWwtcm93Lm1iOCB7DQogIGZsZXgtc2hyaW5rOiAwOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQoNCi8qIOihqOagvOWMuuWfnyAtIOWNoOaNruWJqeS9meepuumXtCAqLw0KLmFwcC1jb250YWluZXIgLmVsLXRhYmxlIHsNCiAgZmxleDogMTsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLyog5YiG6aG15qC35byPIC0g5Zu65a6a5Zyo5bqV6YOoICovDQoucGFnaW5hdGlvbi13cmFwcGVyIHsNCiAgZmxleC1zaHJpbms6IDA7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogMTZweCAwOw0KICBtYXJnaW4tdG9wOiBhdXRvOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCiAgYm94LXNoYWRvdzogMCAtMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDYpOw0KfQ0KDQovKiDliIbpobXnu4Tku7blk43lupTlvI/moLflvI8gKi8NCi5wYWdpbmF0aW9uLXdyYXBwZXIgOjp2LWRlZXAgLmVsLXBhZ2luYXRpb24gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBnYXA6IDhweDsNCn0NCg0KLnBhZ2luYXRpb24td3JhcHBlciA6OnYtZGVlcCAuZWwtcGFnaW5hdGlvbiAuZWwtcGFnaW5hdGlvbl9fdG90YWwgew0KICBtYXJnaW4tcmlnaHQ6IDE2cHg7DQogIG9yZGVyOiAxOw0KfQ0KDQoucGFnaW5hdGlvbi13cmFwcGVyIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIC5idG4tcHJldiB7DQogIG9yZGVyOiAyOw0KfQ0KDQoucGFnaW5hdGlvbi13cmFwcGVyIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIC5lbC1wYWdlciB7DQogIG9yZGVyOiAzOw0KfQ0KDQoucGFnaW5hdGlvbi13cmFwcGVyIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIC5idG4tbmV4dCB7DQogIG9yZGVyOiA0Ow0KfQ0KDQovKiDkuK3nrYnlsY/luZXpgILphY0gKOW5s+advykgKi8NCkBtZWRpYSAobWF4LXdpZHRoOiAxMDI0cHgpIGFuZCAobWluLXdpZHRoOiA3NjlweCkgew0KICAucGFnaW5hdGlvbi13cmFwcGVyIHsNCiAgICBwYWRkaW5nOiAxMHB4IDAgMTVweDsNCiAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2ViZWVmNTsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIGJveC1zaGFkb3c6IDAgLTJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA4KTsNCiAgfQ0KDQogIC5wYWdpbmF0aW9uLXdyYXBwZXIgOjp2LWRlZXAgLmVsLXBhZ2luYXRpb24gLmVsLXBhZ2luYXRpb25fX3RvdGFsIHsNCiAgICBmb250LXNpemU6IDEzcHg7DQogIH0NCg0KICAucGFnaW5hdGlvbi13cmFwcGVyIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIC5idG4tcHJldiwNCiAgLnBhZ2luYXRpb24td3JhcHBlciA6OnYtZGVlcCAuZWwtcGFnaW5hdGlvbiAuYnRuLW5leHQgew0KICAgIHBhZGRpbmc6IDAgMTBweDsNCiAgICBtaW4td2lkdGg6IDM2cHg7DQogIH0NCg0KICAucGFnaW5hdGlvbi13cmFwcGVyIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIC5lbC1wYWdlciBsaSB7DQogICAgbWluLXdpZHRoOiAzNnB4Ow0KICAgIGhlaWdodDogMzZweDsNCiAgICBsaW5lLWhlaWdodDogMzZweDsNCiAgICBmb250LXNpemU6IDEzcHg7DQogIH0NCn0NCg0KLyog5bCP5bGP5bmV6YCC6YWNICovDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLmFwcC1jb250YWluZXIgew0KICAgIGhlaWdodDogY2FsYygxMDB2aCAtIDg0cHgpOw0KICAgIHBhZGRpbmc6IDEwcHg7DQogIH0NCg0KICAucGFnaW5hdGlvbi13cmFwcGVyIHsNCiAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgIHBvc2l0aW9uOiBmaXhlZDsNCiAgICBib3R0b206IDA7DQogICAgbGVmdDogMDsNCiAgICByaWdodDogMDsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgYm9yZGVyLXRvcDogMnB4IHNvbGlkICNlYmVlZjU7DQogICAgYm94LXNoYWRvdzogMCAtNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICAgIHotaW5kZXg6IDEwMDA7DQogIH0NCg0KICAucGFnaW5hdGlvbi13cmFwcGVyIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGdhcDogNHB4Ow0KICB9DQoNCiAgLnBhZ2luYXRpb24td3JhcHBlciA6OnYtZGVlcCAuZWwtcGFnaW5hdGlvbiAuZWwtcGFnaW5hdGlvbl9fdG90YWwgew0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgfQ0KDQogIC5wYWdpbmF0aW9uLXdyYXBwZXIgOjp2LWRlZXAgLmVsLXBhZ2luYXRpb24gLmJ0bi1wcmV2LA0KICAucGFnaW5hdGlvbi13cmFwcGVyIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uIC5idG4tbmV4dCB7DQogICAgcGFkZGluZzogMCA4cHg7DQogICAgbWluLXdpZHRoOiAzMnB4Ow0KICB9DQoNCiAgLnBhZ2luYXRpb24td3JhcHBlciA6OnYtZGVlcCAuZWwtcGFnaW5hdGlvbiAuZWwtcGFnZXIgbGkgew0KICAgIG1pbi13aWR0aDogMzJweDsNCiAgICBoZWlnaHQ6IDMycHg7DQogICAgbGluZS1oZWlnaHQ6IDMycHg7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICB9DQoNCiAgLyog5Li65bCP5bGP5bmV6aKE55WZ5bqV6YOo5YiG6aG156m66Ze0ICovDQogIC5lbC10YWJsZSB7DQogICAgbWFyZ2luLWJvdHRvbTogNjBweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4ZA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/zjEmployeeCasualtyAccidents", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"公司id\" prop=\"companyId\">\r\n        <el-input\r\n          v-model=\"queryParams.companyId\"\r\n          placeholder=\"请输入公司id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          v-model=\"queryParams.companyName\"\r\n          placeholder=\"请输入公司名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目id\" prop=\"projectId\">\r\n        <el-input\r\n          v-model=\"queryParams.projectId\"\r\n          placeholder=\"请输入项目id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"事故日期\" prop=\"accidentDate\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.accidentDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择事故日期\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"事故件数\" prop=\"accidentsNum\">\r\n        <el-input\r\n          v-model=\"queryParams.accidentsNum\"\r\n          placeholder=\"请输入事故件数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"死亡人数\" prop=\"casualtiesTotalNum\">\r\n        <el-input\r\n          v-model=\"queryParams.casualtiesTotalNum\"\r\n          placeholder=\"请输入死亡人数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"重伤人数\" prop=\"seriousInjuryTotalNum\">\r\n        <el-input\r\n          v-model=\"queryParams.seriousInjuryTotalNum\"\r\n          placeholder=\"请输入重伤人数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"轻伤人数\" prop=\"minorInjuryTotalNum\">\r\n        <el-input\r\n          v-model=\"queryParams.minorInjuryTotalNum\"\r\n          placeholder=\"请输入轻伤人数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"zjEmployeeCasualtyAccidentsList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"公司\" align=\"center\" prop=\"companyName\" />\r\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" />\r\n      <el-table-column label=\"事故日期\" align=\"center\" prop=\"accidentDate\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.accidentDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"事故件数\" align=\"center\" prop=\"accidentsNum\" />\r\n      <el-table-column label=\"伤亡人数\" align=\"center\">\r\n        <el-table-column label=\"死亡\" align=\"center\" prop=\"casualtiesTotalNum\" />\r\n        <el-table-column label=\"重伤\" align=\"center\" prop=\"seriousInjuryTotalNum\" />\r\n        <el-table-column label=\"轻伤\" align=\"center\" prop=\"minorInjuryTotalNum\" />\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <div class=\"pagination-wrapper\">\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改职工伤亡事故对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"公司id\" prop=\"companyId\">\r\n          <el-input v-model=\"form.companyId\" placeholder=\"请输入公司id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n          <el-input v-model=\"form.companyName\" placeholder=\"请输入公司名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n          <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目id\" prop=\"projectId\">\r\n          <el-input v-model=\"form.projectId\" placeholder=\"请输入项目id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"事故日期\" prop=\"accidentDate\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.accidentDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择事故日期\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"事故件数\" prop=\"accidentsNum\">\r\n          <el-input v-model=\"form.accidentsNum\" placeholder=\"请输入事故件数\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"死亡人数\" prop=\"casualtiesTotalNum\">\r\n          <el-input v-model=\"form.casualtiesTotalNum\" placeholder=\"请输入死亡人数\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"重伤人数\" prop=\"seriousInjuryTotalNum\">\r\n          <el-input v-model=\"form.seriousInjuryTotalNum\" placeholder=\"请输入重伤人数\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"轻伤人数\" prop=\"minorInjuryTotalNum\">\r\n          <el-input v-model=\"form.minorInjuryTotalNum\" placeholder=\"请输入轻伤人数\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listZjEmployeeCasualtyAccidents, getZjEmployeeCasualtyAccidents, delZjEmployeeCasualtyAccidents, addZjEmployeeCasualtyAccidents, updateZjEmployeeCasualtyAccidents } from \"@/api/inspection/zjEmployeeCasualtyAccidents\";\r\n\r\nexport default {\r\n  name: \"ZjEmployeeCasualtyAccidents\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 职工伤亡事故表格数据\r\n      zjEmployeeCasualtyAccidentsList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        companyId: null,\r\n        companyName: null,\r\n        projectName: null,\r\n        projectId: null,\r\n        accidentDate: null,\r\n        accidentsNum: null,\r\n        casualtiesTotalNum: null,\r\n        seriousInjuryTotalNum: null,\r\n        minorInjuryTotalNum: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询职工伤亡事故列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listZjEmployeeCasualtyAccidents(this.queryParams).then(response => {\r\n        this.zjEmployeeCasualtyAccidentsList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        companyId: null,\r\n        companyName: null,\r\n        projectName: null,\r\n        projectId: null,\r\n        accidentDate: null,\r\n        accidentsNum: null,\r\n        casualtiesTotalNum: null,\r\n        seriousInjuryTotalNum: null,\r\n        minorInjuryTotalNum: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加职工伤亡事故\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getZjEmployeeCasualtyAccidents(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改职工伤亡事故\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateZjEmployeeCasualtyAccidents(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addZjEmployeeCasualtyAccidents(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids;\r\n      if (row && row.id) {\r\n        // 单行删除\r\n        ids = [row.id];\r\n      } else {\r\n        // 多行删除\r\n        ids = this.ids;\r\n      }\r\n\r\n      if (!ids || (Array.isArray(ids) && ids.length === 0)) {\r\n        this.$modal.msgError(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n\r\n      this.$modal\r\n        .confirm('是否确认删除' + ids.length + '条职工伤亡事故数据项？')\r\n        .then(function() {\r\n          return delZjEmployeeCasualtyAccidents(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 根据选中状态决定导出内容和参数\r\n      const hasSelection = this.ids.length > 0;\r\n      const confirmMessage = hasSelection\r\n        ? `是否确认导出选中的${this.ids.length}条职工伤亡事故数据项?`\r\n        : \"是否确认导出所有职工伤亡事故数据项?\";\r\n\r\n      // 准备导出参数\r\n      const exportParams = hasSelection\r\n        ? { ids: this.ids.join(\",\") } // 选中了行，只传ids参数\r\n        : { ...this.queryParams }; // 没选中，传查询参数\r\n\r\n      // 如果导出全部，移除分页参数\r\n      if (!hasSelection) {\r\n        delete exportParams.pageNum;\r\n        delete exportParams.pageSize;\r\n      }\r\n\r\n      this.$modal\r\n        .confirm(confirmMessage)\r\n        .then(() => {\r\n          this.download(\r\n            'inspection/zjEmployeeCasualtyAccidents/export',\r\n            exportParams,\r\n            `zjEmployeeCasualtyAccidents_${hasSelection ? 'selected_' : ''}${new Date().getTime()}.xlsx`\r\n          );\r\n        })\r\n        .catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 确保分页固定在底部，参考其他页面的实现 */\r\n.app-container {\r\n  height: calc(100vh - 84px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px;\r\n}\r\n\r\n/* 搜索表单区域 */\r\n.app-container .el-form {\r\n  flex-shrink: 0;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n/* 工具栏区域 */\r\n.app-container .el-row.mb8 {\r\n  flex-shrink: 0;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 表格区域 - 占据剩余空间 */\r\n.app-container .el-table {\r\n  flex: 1;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 分页样式 - 固定在底部 */\r\n.pagination-wrapper {\r\n  flex-shrink: 0;\r\n  text-align: center;\r\n  padding: 16px 0;\r\n  margin-top: auto;\r\n  background-color: #fff;\r\n  border-top: 1px solid #ebeef5;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* 分页组件响应式样式 */\r\n.pagination-wrapper ::v-deep .el-pagination {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .el-pagination__total {\r\n  margin-right: 16px;\r\n  order: 1;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .btn-prev {\r\n  order: 2;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .el-pager {\r\n  order: 3;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .btn-next {\r\n  order: 4;\r\n}\r\n\r\n/* 中等屏幕适配 (平板) */\r\n@media (max-width: 1024px) and (min-width: 769px) {\r\n  .pagination-wrapper {\r\n    padding: 10px 0 15px;\r\n    border-top: 1px solid #ebeef5;\r\n    background: #fff;\r\n    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {\r\n    font-size: 13px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-prev,\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-next {\r\n    padding: 0 10px;\r\n    min-width: 36px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pager li {\r\n    min-width: 36px;\r\n    height: 36px;\r\n    line-height: 36px;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n/* 小屏幕适配 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    height: calc(100vh - 84px);\r\n    padding: 10px;\r\n  }\r\n\r\n  .pagination-wrapper {\r\n    margin-bottom: 0;\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    background: #fff;\r\n    border-top: 2px solid #ebeef5;\r\n    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);\r\n    z-index: 1000;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination {\r\n    flex-direction: row;\r\n    justify-content: center;\r\n    gap: 4px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {\r\n    font-size: 12px;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-prev,\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-next {\r\n    padding: 0 8px;\r\n    min-width: 32px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pager li {\r\n    min-width: 32px;\r\n    height: 32px;\r\n    line-height: 32px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  /* 为小屏幕预留底部分页空间 */\r\n  .el-table {\r\n    margin-bottom: 60px;\r\n  }\r\n}\r\n</style>\r\n"]}]}