<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="名称关键字" prop="checkItemName">
        <el-input
          v-model="queryParams.checkItemName"
          placeholder="请输入名称关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="启用" value="启用" />
          <el-option label="停用" value="停用" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-setting"
          size="mini"
          :disabled="multiple"
          @click="handleBatchManage"
          >批量管理</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="standardList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 250px)"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        prop="checkItemName"
        label="检查内容名称"
        :show-overflow-tooltip="true"
        width="140"
      />
      <el-table-column
        prop="checkContent"
        label="检查内容"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="checkMethod"
        label="检查方式"
        align="center"
        width="100"
      />
      <el-table-column
        prop="rectificationRequirement"
        label="整改要求"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="standardType"
        label="标准类型"
        align="center"
        width="100"
      />
      <el-table-column prop="status" label="状态" align="center" width="80">
        <template slot-scope="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改检查标准对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="检查项名称" prop="checkItemName">
              <el-input
                v-model="form.checkItemName"
                placeholder="请输入检查项名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查内容" prop="checkContent">
              <el-input
                v-model="form.checkContent"
                type="textarea"
                placeholder="请输入检查内容"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查方式" prop="checkMethod">
              <el-select
                v-model="form.checkMethod"
                placeholder="请选择检查方式"
                style="width: 100%"
              >
                <el-option
                  v-for="item in checkMethodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标准类型" prop="standardType">
              <el-select
                v-model="form.standardType"
                placeholder="请选择标准类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in standardTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="整改要求" prop="rectificationRequirement">
              <el-input
                v-model="form.rectificationRequirement"
                type="textarea"
                placeholder="请输入整改要求"
                :rows="3"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="适用范围" prop="applicableScope">
              <el-select
                v-model="form.applicableScope"
                multiple
                placeholder="请选择适用范围（可多选）"
                style="width: 100%"
              >
                <el-option
                  v-for="item in applicableScopeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="启用">启用</el-radio>
                <el-radio label="停用">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="附件上传">
              <el-upload
                ref="upload"
                :action="uploadAction"
                :headers="uploadHeaders"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                multiple
                :limit="5"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              >
                <el-button size="small" type="primary" icon="el-icon-upload"
                  >上传检查标准附件</el-button
                >
                <div slot="tip" class="el-upload__tip">
                  支持上传PDF/图片/Word文档，单文件不超过10MB，最多5个文件
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看检查标准详情对话框 -->
    <el-dialog
      title="检查标准详情"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="检查项名称">{{
          viewForm.checkItemName
        }}</el-descriptions-item>
        <el-descriptions-item label="标准类型">{{
          viewForm.standardType
        }}</el-descriptions-item>
        <el-descriptions-item label="检查方式">{{
          viewForm.checkMethod
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="statusOptions" :value="viewForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="检查内容" :span="2">{{
          viewForm.checkContent
        }}</el-descriptions-item>
        <el-descriptions-item label="整改要求" :span="2">{{
          viewForm.rectificationRequirement
        }}</el-descriptions-item>
        <el-descriptions-item label="适用范围" :span="2">
          <el-tag
            v-for="scope in viewForm.applicableScope"
            :key="scope"
            style="margin-right: 5px"
            >{{ scope }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{
          parseTime(viewForm.createTime)
        }}</el-descriptions-item>
      </el-descriptions>

      <div
        v-if="viewForm.attachments && viewForm.attachments.length > 0"
        style="margin-top: 20px"
      >
        <h4>相关附件：</h4>
        <el-tag
          v-for="file in viewForm.attachments"
          :key="file.name"
          style="margin-right: 10px; margin-bottom: 5px; cursor: pointer"
          @click="downloadFile(file)"
        >
          <i class="el-icon-paperclip" /> {{ file.name }}
        </el-tag>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "CheckStandard",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查标准库表格数据
      standardList: [
        {
          id: 1,
          checkItemName: "电梯年检资料",
          checkContent: "详细描述电梯设备的要害",
          checkMethod: "测试",
          rectificationRequirement: "不合规项需在何时整改不合规项需如何整改",
          standardType: "综合",
          status: "启用",
          createTime: "2025-05-12 12:00:00",
          applicableScope: ["建筑施工", "设备管理"],
          attachments: [
            { name: "电梯检查标准.pdf", url: "/files/elevator-standard.pdf" },
          ],
        },
        {
          id: 2,
          checkItemName: "电梯年检资料",
          checkContent: "详细描述电梯设备的要害",
          checkMethod: "测试",
          rectificationRequirement: "不合规项需在何时整改不合规项需如何整改",
          standardType: "综合",
          status: "启用",
          createTime: "2025-05-12 12:00:00",
          applicableScope: ["脚手架工程"],
          attachments: [],
        },
        {
          id: 3,
          checkItemName: "电梯年检资料",
          checkContent: "详细描述电梯设备的要害",
          checkMethod: "测试",
          rectificationRequirement: "不合规项需在何时整改不合规项需如何整改",
          standardType: "综合",
          status: "启用",
          createTime: "2025-05-12 12:00:00",
          applicableScope: ["消防安全"],
          attachments: [],
        },
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹窗
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkItemName: undefined,
        status: undefined,
        dateRange: [],
      },
      // 表单参数
      form: {},
      // 查看表单数据
      viewForm: {},
      // 检查方式选项
      checkMethodOptions: [
        { label: "观察", value: "观察" },
        { label: "检测", value: "检测" },
        { label: "测试", value: "测试" },
        { label: "询问", value: "询问" },
        { label: "查阅", value: "查阅" },
      ],
      // 标准类型选项
      standardTypeOptions: [
        { label: "安全", value: "安全" },
        { label: "消防", value: "消防" },
        { label: "环保", value: "环保" },
        { label: "质量", value: "质量" },
        { label: "综合", value: "综合" },
      ],
      // 适用范围选项
      applicableScopeOptions: [
        { label: "建筑施工", value: "建筑施工" },
        { label: "设备管理", value: "设备管理" },
        { label: "脚手架工程", value: "脚手架工程" },
        { label: "消防安全", value: "消防安全" },
        { label: "电气工程", value: "电气工程" },
        { label: "机械设备", value: "机械设备" },
        { label: "环境保护", value: "环境保护" },
        { label: "质量管控", value: "质量管控" },
      ],
      // 状态选项
      statusOptions: [
        { label: "启用", value: "启用" },
        { label: "停用", value: "停用" },
      ],
      // 文件列表
      fileList: [],
      // 上传地址
      uploadAction: process.env.VUE_APP_BASE_API + "/system/upload",
      // 上传请求头
      uploadHeaders: {
        Authorization: "Bearer " + this.$store.getters.token,
      },
      // 表单校验
      rules: {
        checkItemName: [
          { required: true, message: "检查项名称不能为空", trigger: "blur" },
        ],
        checkContent: [
          { required: true, message: "检查内容不能为空", trigger: "blur" },
        ],
        checkMethod: [
          { required: true, message: "检查方式不能为空", trigger: "change" },
        ],
        standardType: [
          { required: true, message: "标准类型不能为空", trigger: "change" },
        ],
        rectificationRequirement: [
          { required: true, message: "整改要求不能为空", trigger: "blur" },
        ],
        applicableScope: [
          { required: true, message: "适用范围不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 时间格式化
    parseTime,
    /** 查询检查标准库列表 */
    getList() {
      this.loading = true;
      // 模拟数据加载
      setTimeout(() => {
        this.total = this.standardList.length;
        this.loading = false;
      }, 500);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        checkItemName: undefined,
        checkContent: undefined,
        checkMethod: undefined,
        rectificationRequirement: undefined,
        standardType: undefined,
        applicableScope: [],
        status: "启用",
        attachments: [],
      };
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查标准";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const selectedRow =
        row || this.standardList.find((item) => this.ids.includes(item.id));
      if (selectedRow) {
        this.form = { ...selectedRow };
        this.fileList = selectedRow.attachments || [];
      }
      this.open = true;
      this.title = "修改检查标准";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = { ...row };
      this.viewOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.attachments = this.fileList;
          if (this.form.id != null) {
            // 修改的提交
            this.$modal.msgSuccess("修改成功");
          } else {
            // 新增的提交
            this.$modal.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      const names = row
        ? [row.checkItemName]
        : this.standardList
            .filter((item) => ids.includes(item.id))
            .map((item) => item.checkItemName);
      this.$modal
        .confirm('是否确认删除检查标准"' + names.join("、") + '"?')
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.getList();
        })
        .catch(() => {});
    },
    /** 批量管理按钮操作 */
    handleBatchManage() {
      this.$modal.msgInfo("批量管理功能待实现");
    },
    /** 导入按钮操作 */
    handleImport() {
      this.$modal.msgInfo("导入功能待实现");
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.msgInfo("导出功能待实现");
    },
    /** 文件上传前检查 */
    beforeUpload(file) {
      const isValidType = [
        "application/pdf",
        "image/jpeg",
        "image/png",
        "image/jpg",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ].includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isValidType) {
        this.$modal.msgError("只能上传PDF、图片或Word文档!");
        return false;
      }
      if (!isLt10M) {
        this.$modal.msgError("上传文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    /** 文件上传成功 */
    handleUploadSuccess(response, file, fileList) {
      this.fileList = fileList;
      this.$modal.msgSuccess("文件上传成功");
    },
    /** 删除文件 */
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    /** 预览文件 */
    handlePreview(file) {
      window.open(file.url);
    },
    /** 下载文件 */
    downloadFile(file) {
      window.open(file.url);
    },
  },
};
</script>

<style scoped>
.el-upload__tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin: 0 10px;
  min-width: 80px;
}
</style>
