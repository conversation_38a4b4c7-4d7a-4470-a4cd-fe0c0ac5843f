import request from '@/utils/request'

// 查询事件上报列表
export function listZjEventReporting(query) {
  return request({
    url: '/inspection/zjEventReporting/list',
    method: 'get',
    params: query
  })
}

// 查询事件上报详细
export function getZjEventReporting(id) {
  return request({
    url: '/inspection/zjEventReporting/' + id,
    method: 'get'
  })
}

// 新增事件上报
export function addZjEventReporting(data) {
  return request({
    url: '/inspection/zjEventReporting',
    method: 'post',
    data: data
  })
}

// 修改事件上报
export function updateZjEventReporting(data) {
  return request({
    url: '/inspection/zjEventReporting',
    method: 'put',
    data: data
  })
}

// 删除事件上报
export function delZjEventReporting(id) {
  return request({
    url: '/inspection/zjEventReporting/' + id,
    method: 'delete'
  })
}
