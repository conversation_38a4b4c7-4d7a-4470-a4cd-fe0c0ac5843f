<template>
  <div class="app-container">
    <el-row>
      <el-col :span="4">
        <!-- <div class="title mb-2">组织架构</div>
        <el-tree
          :data="data"
          :props="defaultProps"
          @node-click="handleNodeClick"
        ></el-tree> -->
        <org-tree :type="'1'" @nodeClick="handleOrgTreeNodeClick" />
      </el-col>
      <el-col :span="19" style="margin-left: 2px">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="80px"
        >
          <el-form-item label="员工姓名" prop="employeeName">
            <el-input
              v-model="queryParams.employeeName"
              placeholder="请输入员工姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="身份证" prop="employeeSfz">
        <el-input
          v-model="queryParams.employeeSfz"
          placeholder="请输入身份证"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <!-- <el-form-item label="员工职责" prop="employeeZz">
        <el-input
          v-model="queryParams.employeeZz"
          placeholder="请输入员工职责"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <!-- <el-form-item label="安全履历" prop="anquanlvli">
        <el-input
          v-model="queryParams.anquanlvli"
          placeholder="请输入安全履历"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <!-- <el-form-item label="持有证书" prop="zzqk">
        <el-input
          v-model="queryParams.zzqk"
          placeholder="请输入持有证书情况"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjEmployeeInfo:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjEmployeeInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjEmployeeInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjEmployeeInfo:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjEmployeeInfoList"
          @selection-change="handleSelectionChange"
          height="calc(100vh - 240px)"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" type="index" width="50" />
          <!-- <el-table-column label="主键" align="center" prop="id" /> -->
          <el-table-column label="姓名" align="center" prop="employeeName" />
          <el-table-column
            label="工号"
            align="center"
            prop="employeeWorkNumber"
          />
          <el-table-column
            label="所属公司/部门"
            align="center"
            prop="companyName"
            width="150"
          >
            <template slot-scope="scope">
              {{ scope.row.companyName }}
            </template>
          </el-table-column>
          <el-table-column label="岗位" align="center" prop="employeePost" />
          <el-table-column
            label="联系方式"
            align="center"
            prop="employeePhone"
          />
          <!-- <el-table-column label="入职时间" align="center" prop="entryTime" /> -->
          <el-table-column label="安全职责" align="center" prop="employeeZz">
            <template slot-scope="scope">
              <el-button
                v-if="!!scope.row.employeeZz"
                size="mini"
                type="text"
                @click="handleAttach(scope.row.employeeZz)"
                >查看</el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="安全培训记录"
            align="center"
            prop="safetyTraining"
          >
            <template slot-scope="scope">
              <el-button
                v-if="!!scope.row.safetyTraining"
                size="mini"
                type="text"
                @click="handleAttach(scope.row.safetyTraining)"
                >查看</el-button
              >
              <span v-else>-</span>

              <!-- {{ baseUrl + scope.row.safetyTraining }} -->
            </template>
          </el-table-column>
          <el-table-column
            label="健康体检信息"
            align="center"
            prop="physicalExamination"
          >
            <template slot-scope="scope">
              <el-button
                v-if="!!scope.row.physicalExamination"
                size="mini"
                type="text"
                @click="handleAttach(scope.row.physicalExamination)"
                >查看</el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="持有证书" align="center" prop="zzqk">
            <template slot-scope="scope">
              <el-button
                v-if="!!scope.row.zzqk"
                size="mini"
                type="text"
                @click="handleAttach(scope.row.zzqk)"
                >查看</el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="安全履历" align="center" prop="anquanlvli">
            <template slot-scope="scope">
              <el-button
                v-if="!!scope.row.anquanlvli"
                size="mini"
                type="text"
                @click="handleAttach(scope.row.anquanlvli)"
                >查看</el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column> -->

          <el-table-column
            label="操作"
            align="center"
            width="140"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjEmployeeInfo:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjEmployeeInfo:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改员工情况对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="姓名" prop="employeeName">
          <el-input v-model="form.employeeName" placeholder="请输入员工姓名" />
        </el-form-item>
        <!-- <el-form-item label="身份证" prop="employeeSfz">
          <el-input v-model="form.employeeSfz" placeholder="请输入身份证" />
        </el-form-item>
        <el-form-item label="员工职责" prop="employeeZz">
          <el-input v-model="form.employeeZz" placeholder="请输入员工职责" />
        </el-form-item> -->
        <!-- <el-form-item label="性别" prop="employeeSex">
          <el-radio-group v-model="form.employeeSex">
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="工号" prop="employeeWorkNumber">
          <el-input
            v-model="form.employeeWorkNumber"
            placeholder="请输入工号"
          />
        </el-form-item>
        <el-form-item label="所属公司/部门" prop="companyName">
          <!-- <el-input v-model="form.id" placeholder="请输入部门" /> -->
          <selectPeopleTree
            v-model="form.companyName"
            :peopleList="companyList"
            placeholder="请搜索或选择责任公司"
            @change="handleChange"
            ref="chargePersonName"
          ></selectPeopleTree>
        </el-form-item>
        <el-form-item label="岗位" prop="employeePost">
          <el-input v-model="form.employeePost" placeholder="请输入岗位" />
        </el-form-item>
        <el-form-item label="联系方式" prop="employeePhone">
          <el-input v-model="form.employeePhone" placeholder="请输入联系方式" />
        </el-form-item>
        <!-- <el-form-item label="入职时间" prop="entryTime">
          <el-date-picker
            v-model="form.entryTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择入职时间"
            style="width: 100%"
          />
        </el-form-item> -->

        <el-form-item label="安全职责" prop="employeeZz">
          <file-upload v-model="form.employeeZz" :fileType="allowedFileTypes" />
        </el-form-item>

        <el-form-item label="安全培训记录" prop="safetyTraining">
          <file-upload
            v-model="form.safetyTraining"
            :fileType="allowedFileTypes"
          />
        </el-form-item>
        <el-form-item label="健康体检信息" prop="physicalExamination">
          <file-upload
            v-model="form.physicalExamination"
            :fileType="allowedFileTypes"
          />
        </el-form-item>
        <el-form-item label="持有证书" prop="zzqk">
          <file-upload v-model="form.zzqk" :fileType="allowedFileTypes" />
        </el-form-item>
        <!-- <el-form-item label="安全履历" prop="anquanlvli">
          <file-upload v-model="form.anquanlvli" :fileType="allowedFileTypes" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog
      v-model="attachmentDialogVisible"
      :attachmentList="attachmentList"
    ></AttachmentDialog>
  </div>
</template>

<script>
import {
  listZjEmployeeInfo,
  getZjEmployeeInfo,
  delZjEmployeeInfo,
  addZjEmployeeInfo,
  updateZjEmployeeInfo,
} from "@/api/inspection/zjEmployeeInfo";
import { listInfo } from "@/api/system/info";
import { getEnterpriseInfo } from "@/api/system/info";

import OrgTree from "@/views/components/orgTree.vue";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";

export default {
  name: "ZjEmployeeInfo",
  components: {
    OrgTree,
    AttachmentDialog,
    selectPeopleTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 员工情况表格数据
      zjEmployeeInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        employeeName: null,
        employeeSex: null,
        employeeSfz: null,
        employeeZz: null,
        anquanlvli: null,
        zzqk: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        employeeName: [
          { required: true, message: "员工姓名不能为空", trigger: "blur" },
        ],
        employeePhone: [
          {
            trigger: "blur",
            pattern: /^1[3456789]\d{9}$/,
            message: "请输入正确的手机号",
          },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_URL,
      deptList: [],
      deptParams: {
        enterpriseName: undefined,
        status: undefined,
      },
      currentDeptId: null,
      attachmentDialogVisible: false,
      attachmentList: [],
      companyList: [],
      // 允许上传的文件类型（排除txt）
      allowedFileTypes: [
        "doc",
        "docx",
        "xls",
        "ppt",
        "pdf",
        "mp4",
        "avi",
        "mov",
        "mkv",
        "flv",
        "wmv",
        "png",
        "jpg",
        "jpeg",
      ],
    };
  },
  created() {
    this.getList();
    this.getDeptList();
    this.getCompanyList();
  },
  methods: {
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    handleChange(selectedItem) {
      if (selectedItem) {
        // this.form.subjectId = selectedItem.id;
        this.form.companyName = selectedItem.label;
      } else {
        // this.form.subjectId = null;
        this.form.companyName = null;
      }
    },
    // 查看附件
    handleAttach(attachmentUrl) {
      this.attachmentDialogVisible = true;
      this.attachmentList = attachmentUrl.split(",");
    },
    handleOrgTreeNodeClick(nodeData) {
      // 在这里处理接收到的子组件数据
      console.log("接收到子组件数据:", nodeData);
      // 可以根据nodeData更新查询参数或其他状态
      this.queryParams.deptId = nodeData.id; // 假设nodeData中有id字段
      this.currentDeptId = nodeData.id;
      this.handleQuery(); // 触发查询
    },
    /** 查询员工情况列表 */
    getList() {
      this.loading = true;
      listZjEmployeeInfo(this.queryParams).then((res) => {
        this.zjEmployeeInfoList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 不扁平化
    renderDeptOptions(children, level = 1) {
      let result = [];
      children.forEach((child) => {
        result.push({
          ...child,
          level,
          enterpriseName: " ".repeat(level * 2) + child.enterpriseName,
        });
        if (child.children && child.children.length) {
          result = result.concat(
            this.renderDeptOptions(child.children, level + 1)
          );
        }
      });
      return result;
    },
    // 添加扁平化部门的方法
    // flattenDept(children) {
    //   let result = [];
    //   children.forEach((child) => {
    //     result.push(child);
    //     if (child.children && child.children.length) {
    //       result = result.concat(this.flattenDept(child.children));
    //     }
    //   });
    //   return result;
    // },
    // getDeptName(deptId) {
    //   const dept = this.flattenDept(this.deptList).find(
    //     (item) => item.id === deptId
    //   );
    //   return dept ? dept.enterpriseName : "";
    // },
    /** 查询部门列表 */
    getDeptList() {
      listInfo(this.deptParams).then((res) => {
        this.deptList = this.handleTree(res.data, "id");
        // this.deptList = res.rows;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        employeeName: null,
        employeeSex: null,
        employeeSfz: null,
        employeeZz: null,
        anquanlvli: null,
        zzqk: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 保持当前选中的部门ID，不重置左侧菜单选择
      if (this.currentDeptId) {
        this.queryParams.deptId = this.currentDeptId;
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      if (this.currentDeptId) {
        this.form.deptId = this.currentDeptId;
      }
      this.title = "添加员工情况";
    },
    /** 查看 */
    // async handleSafetyTrainingRecords(value) {
    //   // if (value) {
    //   //   window.open(this.baseUrl + value);
    //   // } else {
    //   //   this.$message.warning("该记录没有附件");
    //   // }
    //   if (!value) {
    //     this.$message.warning("该记录没有附件");
    //     return;
    //   }
    //   try {
    //     //获取文件完整URL
    //     const fileUrl = this.baseUrl + value;
    //     // 如果是TXT文件，使用fetch获取并处理编码
    //     if (fileUrl.toLowerCase().endsWith(".txt")) {
    //       const res = await fetch(fileUrl);
    //       const buffer = await res.arrayBuffer();

    //       // 尝试用常见中文编码解码
    //       const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
    //       const text = decoder.decode(buffer);

    //       // 在弹窗中显示文本内容
    //       this.$alert(text, "文件内容", {
    //         customClass: "txt-preview-dialog",
    //         showConfirmButton: false,
    //         closeOnClickModal: true,
    //       });
    //     } else {
    //       // 其他文件类型直接打开
    //       window.open(fileUrl);
    //     }
    //   } catch (error) {
    //     this.$message.error("文件打开失败: " + error.message);
    //   }
    // },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjEmployeeInfo(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改员工情况";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjEmployeeInfo(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEmployeeInfo(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除员工情况编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjEmployeeInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjEmployeeInfo/export",
        {
          ...this.queryParams,
        },
        `zjEmployeeInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
