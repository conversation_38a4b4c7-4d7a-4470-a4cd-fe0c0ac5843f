<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button
          type="text"
          icon="el-icon-arrow-left"
          @click="goBack"
          class="back-btn"
        >
          返回列表
        </el-button>
        <div class="page-title">承包商详情</div>
      </div>
      <div class="header-right">
        <!-- 保留右侧空间以保持布局平衡 -->
      </div>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content" v-loading="loading">
      <!-- 基本信息表单 -->
      <div class="info-section">
        <div class="section-title">基本信息</div>
        <el-form class="detail-form" label-width="120px">
          <el-row :gutter="20">
            <!-- 第一行：承包商名称 | 管理人 -->
            <el-col :span="12">
              <el-form-item label="承包商名称">
                <el-input
                  v-model="contractorInfo.contractorName"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理人">
                <el-input
                  v-model="contractorInfo.administratorName"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第二行：统一社会信用代码 | 服务起止时间 -->
            <el-col :span="12">
              <el-form-item label="统一社会信用代码">
                <el-input
                  v-model="contractorInfo.creditCode"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务起止时间">
                <el-date-picker
                  :value="getServiceTimeRange()"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  disabled
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第三行：承包商负责人 | 承包商类别 -->
            <el-col :span="12">
              <el-form-item label="承包商负责人">
                <el-input
                  v-model="contractorInfo.contractorManager"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="承包商类别">
                <el-select
                  v-model="contractorInfo.contractorCategory"
                  disabled
                  placeholder="-"
                  style="width: 100%"
                >
                  <el-option label="准承包商" :value="1" />
                  <el-option label="合格承包商" :value="2" />
                  <el-option label="不合格承包商" :value="3" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第四行：承包商类型 | 公司简介 -->
            <el-col :span="12">
              <el-form-item label="承包商类型">
                <el-select
                  v-model="contractorInfo.contractorType"
                  disabled
                  placeholder="-"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in dict.type.sys_contractor_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司简介">
                <el-input
                  v-model="contractorInfo.companyProfile"
                  type="textarea"
                  :rows="4"
                  placeholder="-"
                  style="width: 100%"
                  disabled
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第五行：负责人联系电话 | 单位性质 -->
            <el-col :span="12">
              <el-form-item label="负责人联系电话">
                <el-input
                  v-model="contractorInfo.chargeContactNumber"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位性质">
                <el-input
                  v-model="contractorInfo.unitNature"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第六行：法定代表人 | 公司地址 -->
            <el-col :span="12">
              <el-form-item label="法定代表人">
                <el-input
                  v-model="contractorInfo.legalRepresentative"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司地址">
                <el-input
                  v-model="contractorInfo.companyAddress"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- 第七行：法定代表人联系电话 | 公司邮箱 -->
            <el-col :span="12">
              <el-form-item label="法定代表人联系电话">
                <el-input
                  v-model="contractorInfo.legalRepresentativePhone"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司邮箱">
                <el-input
                  v-model="contractorInfo.companyEmail"
                  disabled
                  placeholder="-"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- Tab切换内容 -->
      <div class="tab-section">
        <el-tabs v-model="activeTabName" @tab-click="handleTabClick">
          <el-tab-pane label="资质信息" name="qualification">
            <qualification-tab :contractor-id="contractorInfo.id" />
          </el-tab-pane>
          <el-tab-pane label="业绩信息" name="performance">
            <performance-tab :contractor-id="contractorInfo.id" />
          </el-tab-pane>
          <el-tab-pane label="作业票" name="workTicket">
            <work-ticket-tab :contractor-id="contractorInfo.id" />
          </el-tab-pane>
          <el-tab-pane label="施工信息" name="construction">
            <construction-tab :contractor-id="contractorInfo.id" />
          </el-tab-pane>
          <el-tab-pane label="评价记录" name="evaluation">
            <evaluation-tab :contractor-id="contractorInfo.id" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <!-- 第一行：承包商名称 | 管理人 -->
          <el-col :span="12">
            <el-form-item label="承包商名称" prop="contractorName">
              <el-input
                v-model="form.contractorName"
                placeholder="承包商名称不能为空"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理人" prop="administratorId">
              <el-select
                v-model="form.administratorId"
                placeholder="管理人不能为空"
                style="width: 100%"
                @change="handleManagerChange"
              >
                <el-option
                  v-for="(item, index) in managerOptions"
                  :key="`form_manager_${item.value}_${index}`"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第二行：统一社会信用代码 | 服务起止时间 -->
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="creditCode">
              <el-input
                v-model="form.creditCode"
                placeholder="统一社会信用代码不能为空"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务起止时间" prop="serviceTimeRange">
              <el-date-picker
                v-model="form.serviceTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="handleServiceTimeChange"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第三行：承包商负责人 | 承包商类别 -->
          <el-col :span="12">
            <el-form-item label="承包商负责人" prop="contractorManager">
              <el-input
                v-model="form.contractorManager"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="承包商类别" prop="contractorCategory">
              <el-select
                v-model="form.contractorCategory"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.contractor_category"
                  :key="`form_contractor_category_${dict.value}_${index}`"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第四行：承包商类型 | 公司简介（经营范围） -->
          <el-col :span="12">
            <el-form-item label="承包商类型" prop="contractorType">
              <el-select
                v-model="form.contractorType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.sys_contractor_type"
                  :key="`form_contractor_type_${dict.value}_${index}`"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司简介" prop="companyProfile">
              <el-input
                v-model="form.companyProfile"
                type="textarea"
                :rows="4"
                placeholder="请输入内容"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第五行：负责人联系电话 | 单位性质 -->
          <el-col :span="12">
            <el-form-item label="负责人联系电话" prop="chargeContactNumber">
              <el-input
                v-model="form.chargeContactNumber"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位性质" prop="unitNature">
              <el-input v-model="form.unitNature" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第六行：法定代表人 | 公司地址 -->
          <el-col :span="12">
            <el-form-item label="法定代表人" prop="legalRepresentative">
              <el-input
                v-model="form.legalRepresentative"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司地址" prop="companyAddress">
              <el-input
                v-model="form.companyAddress"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第七行：法定代表人联系电话 | 公司邮箱 -->
          <el-col :span="12">
            <el-form-item
              label="法定代表人联系电话"
              prop="legalRepresentativePhone"
            >
              <el-input
                v-model="form.legalRepresentativePhone"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司邮箱" prop="companyEmail">
              <el-input v-model="form.companyEmail" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getZjContractorInfo,
  updateZjContractorInfo,
} from "@/api/contractor/zjContractorInfo";
import { getUserInfo } from "@/api/contractor/zjContractorBlaklist";
import QualificationTab from "./components/QualificationTab.vue";
import PerformanceTab from "./components/PerformanceTab.vue";
import WorkTicketTab from "./components/WorkTicketTab.vue";
import ConstructionTab from "./components/ConstructionTab.vue";
import EvaluationTab from "./components/EvaluationTab.vue";

export default {
  name: "ZjContractorInfoDetail",
  dicts: ["contractor_category", "sys_contractor_type"],
  components: {
    QualificationTab,
    PerformanceTab,
    WorkTicketTab,
    ConstructionTab,
    EvaluationTab,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 承包商信息
      contractorInfo: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        contractorName: [
          { required: true, message: "承包商名称不能为空", trigger: "blur" },
        ],
        administratorId: [
          { required: true, message: "管理人不能为空", trigger: "change" },
        ],
        creditCode: [
          {
            required: true,
            message: "统一社会信用代码不能为空",
            trigger: "blur",
          },
        ],
        serviceTimeRange: [
          {
            required: true,
            message: "服务起止时间不能为空",
            trigger: "change",
          },
        ],
        contractorManager: [
          { required: true, message: "承包商负责人不能为空", trigger: "blur" },
        ],
      },
      // 管理人选项
      managerOptions: [],
      // 当前活跃的tab
      activeTabName: "qualification",
    };
  },
  created() {
    const id = this.$route.params.id;
    if (id) {
      this.getContractorDetail(id);
      this.loadManagerOptions();
    } else {
      this.$modal.msgError("缺少承包商ID参数");
      this.goBack();
    }
  },
  methods: {
    /** 获取承包商详情 */
    getContractorDetail(id) {
      this.loading = true;
      getZjContractorInfo(id)
        .then((response) => {
          this.contractorInfo = response.data;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.$modal.msgError("获取承包商详情失败");
        });
    },

    /** 返回列表页 */
    goBack() {
      // 优先使用浏览器历史返回，避免写死路径
      if (window.history.length > 1) {
        this.$router.back();
        return;
      }

      // 兜底1：如果路由meta提供了activeMenu，则跳转该菜单
      const activeMenu = this.$route && this.$route.meta && this.$route.meta.activeMenu;
      if (activeMenu) {
        this.$router.replace(activeMenu);
        return;
      }

      // 兜底2：根据当前路径动态计算上一级路径
      const pathParts = (this.$route && this.$route.path ? this.$route.path : "/").split("/").filter(Boolean);
      if (pathParts.length > 1) {
        const last = pathParts[pathParts.length - 1];
        const prev = pathParts[pathParts.length - 2] || "";
        // 若路径以 /.../detail/:id 或 /.../edit/:id 形式存在，则回退两级；
        // 否则默认去掉最后一级
        const isIdLike = /^\d+$/.test(last) || /^[0-9a-fA-F-]{8,}$/.test(last);
        const isActionSegment = ["detail", "edit", "view"].includes(prev);
        const parentParts = isIdLike && isActionSegment ? pathParts.slice(0, -2) : pathParts.slice(0, -1);
        const parentPath = "/" + parentParts.join("/");
        this.$router.replace(parentPath || "/");
        return;
      }

      // 最终兜底：回首页
      this.$router.replace("/");
    },

    /** 编辑按钮操作 */
    handleEdit() {
      this.reset();
      this.form = { ...this.contractorInfo };
      // 设置服务时间范围
      if (this.form.serviceStartTime && this.form.serviceEndTime) {
        this.form.serviceTimeRange = [
          this.form.serviceStartTime,
          this.form.serviceEndTime,
        ];
      } else {
        this.form.serviceTimeRange = null;
      }
      this.open = true;
      this.title = "修改承包商信息";
    },

    /** 加载管理人选项 */
    loadManagerOptions() {
      getUserInfo(1)
        .then((response) => {
          const data = response.data || response.rows || response || [];
          const uniqueManagers = new Map();
          data.forEach((manager, index) => {
            const value = manager.userId || manager.id || `temp_${index}`;
            const label =
              manager.nickName ||
              manager.name ||
              manager.userName ||
              `未命名_${index}`;
            const nickName =
              manager.nickName ||
              manager.name ||
              manager.userName ||
              `未命名_${index}`;
            if (!uniqueManagers.has(value)) {
              uniqueManagers.set(value, {
                value,
                label,
                nickName,
              });
            }
          });
          this.managerOptions = Array.from(uniqueManagers.values());
        })
        .catch((error) => {
          console.error("获取管理人选项失败:", error);
          this.$modal.msgError("获取管理人选项失败");
        });
    },

    /** 处理服务时间范围变化 */
    handleServiceTimeChange(value) {
      if (value && value.length === 2) {
        this.form.serviceStartTime = value[0];
        this.form.serviceEndTime = value[1];
      } else {
        this.form.serviceStartTime = null;
        this.form.serviceEndTime = null;
      }
    },

    /** 处理管理人变化 */
    handleManagerChange(value) {
      if (value) {
        const selectedManager = this.managerOptions.find(
          (manager) => manager.value == value
        );
        if (selectedManager) {
          this.form.administratorName = selectedManager.nickName;
        }
      } else {
        this.form.administratorName = null;
      }
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        contractorName: null,
        administratorId: null,
        administratorName: null,
        creditCode: null,
        serviceStartTime: null,
        serviceEndTime: null,
        serviceTimeRange: null,
        contractorManager: null,
        contractorCategory: null,
        contractorType: null,
        companyProfile: null,
        chargeContactNumber: null,
        unitNature: null,
        legalRepresentative: null,
        companyAddress: null,
        legalRepresentativePhone: null,
        companyEmail: null,
        blacklistStatus: null,
        accessStatus: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        blacklistReason: null,
      };
      this.resetForm("form");
    },

    /** 获取服务时间范围 */
    getServiceTimeRange() {
      if (
        this.contractorInfo.serviceStartTime &&
        this.contractorInfo.serviceEndTime
      ) {
        return [
          this.contractorInfo.serviceStartTime,
          this.contractorInfo.serviceEndTime,
        ];
      }
      return null;
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          updateZjContractorInfo(this.form).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            // 重新获取详情数据
            this.getContractorDetail(this.form.id);
          });
        }
      });
    },

    /** 处理tab点击 */
    handleTabClick(tab) {
      this.activeTabName = tab.name;
    },
  },
};
</script>

<style scoped>
.app-container {
  width: 100%;
  background-color: rgb(247, 248, 250);
  min-height: calc(100vh - 60px);
  padding-bottom: 20px;
}

.page-header {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-btn {
  color: #1890ff;
  padding: 0;
  margin-right: 15px;
  font-size: 14px;
}

.back-btn:hover {
  color: #40a9ff;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.detail-content {
  min-height: 500px;
  overflow: visible;
}

.detail-form {
  padding: 0;
}

.detail-form .el-form-item {
  margin-bottom: 22px;
}

.detail-form .el-input.is-disabled .el-input__inner,
.detail-form .el-textarea.is-disabled .el-textarea__inner,
.detail-form .el-select.is-disabled .el-input__inner {
  background-color: #f8f9fa !important;
  border-color: #e8e8e8 !important;
  color: #333 !important;
  cursor: default;
}

.detail-form .el-date-editor.is-disabled {
  background-color: #f8f9fa !important;
}

.detail-form .el-date-editor.is-disabled .el-input__inner {
  background-color: #f8f9fa !important;
  border-color: #e8e8e8 !important;
  color: #333 !important;
}

.info-section {
  background-color: #fff;
  padding: 24px;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #1890ff;
  position: relative;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 50px;
  height: 2px;
  background-color: #1890ff;
}

.tab-section {
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  overflow: visible;
  padding: 20px 0 20px 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: start;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
}

.info-item label {
  font-weight: 500;
  color: #666;
  min-width: 120px;
  margin-right: 12px;
  line-height: 22px;
  flex-shrink: 0;
}

.info-item span {
  color: #333;
  line-height: 22px;
  word-break: break-all;
}

.textarea-content {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  color: #333;
  line-height: 1.6;
  min-height: 60px;
  width: 100%;
  white-space: pre-wrap;
  word-break: break-word;
}

.el-tag {
  font-weight: 500;
}

/* 页面容器滚动由 AppMain 组件控制 */

/* Tab内容样式 */
.el-tabs__content {
  overflow: visible;
}

.el-tab-pane {
  overflow: visible;
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-right {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
