<template>
  <div class="app-container home bg">
    <!-- 顶部统计卡片 -->
    <div class="top-stats">
      <!-- 超危大工程 -->
      <div class="stat-card stat-card-1">
        <div class="stat-header">
          <h3>超危大工程</h3>
        </div>
        <div class="stat-content-dual">
          <div class="stat-item">
            <div class="stat-title">超危工程数</div>
            <div class="stat-number-row">
              <span class="stat-number">{{ statsData.superDangerousProjects }}</span>
              <span class="stat-unit">个</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-title">危大工程数</div>
            <div class="stat-number-row">
              <span class="stat-number">{{ statsData.dangerousProjects }}</span>
              <span class="stat-unit">个</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全生产 -->
      <div class="stat-card stat-card-2">
        <div class="stat-header">
          <h3>安全生产</h3>
        </div>
        <div class="stat-content-dual">
          <div class="stat-item">
            <div class="stat-title">国内安全生产投入</div>
            <div class="stat-number-row">
              <span class="stat-number">{{ statsData.domesticSafetyInvestment }}</span>
              <span class="stat-unit">万</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-title">国际安全生产投入</div>
            <div class="stat-number-row">
              <span class="stat-number">{{ statsData.internationalSafetyInvestment }}</span>
              <span class="stat-unit">万</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 在建项目 -->
      <div class="stat-card stat-card-3">
        <div class="stat-header">
          <h3>在建项目</h3>
        </div>
        <div class="stat-content-dual">
          <div class="stat-item">
            <div class="stat-title">国内在建项目数</div>
            <div class="stat-number-row">
              <span class="stat-number">{{ statsData.domesticProjects }}</span>
              <span class="stat-unit">个</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-title">国际在建项目数</div>
            <div class="stat-number-row">
              <span class="stat-number">{{ statsData.internationalProjects }}</span>
              <span class="stat-unit">个</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第二行统计卡片 -->
    <div class="second-stats">
      <div class="stat-card-small stat-card-small-1">
        <div class="stat-icon">
          <i class="el-icon-office-building"></i>
        </div>
        <div class="stat-content-small">
          <div class="stat-title-small">企业分支</div>
          <div class="stat-number-row-small">
            <span class="stat-number-small">{{ statsData.companyBranches }}</span>
            <span class="stat-unit-small">个</span>
          </div>
        </div>
      </div>

      <div class="stat-card-small stat-card-small-2">
        <div class="stat-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="stat-content-small">
          <div class="stat-title-small">伤亡事故人数</div>
          <div class="stat-number-row-small">
            <span class="stat-number-small">{{ statsData.casualties }}</span>
            <span class="stat-unit-small">人</span>
          </div>
        </div>
      </div>

      <div class="stat-card-small stat-card-small-3">
        <div class="stat-icon">
          <i class="el-icon-warning-outline"></i>
        </div>
        <div class="stat-content-small">
          <div class="stat-title-small">重大危险源项目</div>
          <div class="stat-number-row-small">
            <span class="stat-number-small">{{ statsData.majorHazards }}</span>
            <span class="stat-unit-small">项</span>
          </div>
        </div>
      </div>

      <div class="stat-card-small stat-card-small-4">
        <div class="stat-icon">
          <i class="el-icon-cpu"></i>
        </div>
        <div class="stat-content-small">
          <div class="stat-title-small">大型设备数量</div>
          <div class="stat-number-row-small">
            <span class="stat-number-small">{{ statsData.largeEquipment }}</span>
            <span class="stat-unit-small">台</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 第三行：超危大工程和安全生产投入 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <div class="chart-card">
          <div class="chart-header">
            <h4>超危大工程</h4>
          </div>
          <div class="chart-content">
            <div class="dangerous-chart-container">
              <div
                ref="dangerousProjectChart"
                class="chart-container"
                style="height: 280px"
              />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-card">
          <div class="chart-header">
            <h4>安全生产投入</h4>
          </div>
          <div class="chart-content">
            <div class="safety-investment-container">
              <!-- 左侧环形图 -->
              <div class="pie-chart-section">
                <div class="chart-wrapper">
                  <pieChart 
                    height="280px" 
                    :data="safetyInvestmentPieChart" 
                    :showCenterText="true"
                    :centerText="safetyInvestmentTotal"
                  />
                </div>
              </div>
              <!-- 右侧项目列表 -->
              <div class="project-list-section">
                <div class="project-list">
                  <div 
                    v-for="(item, index) in safetyInvestmentProjects" 
                    :key="index"
                    class="project-item"
                  >
                    <div class="project-dot" :style="{backgroundColor: item.color}"></div>
                    <div class="project-info">
                      <div class="project-name">{{ item.name }}</div>
                      <div class="project-amount">{{ item.value }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 底部：质量管理和安全管理 -->
    <el-row :gutter="20" class="management-row">
      <el-col :span="12">
        <div class="management-card">
          <div class="management-header">
            <div class="header-content">
              <h3>质量管理</h3>
              <div class="time-tabs">
                <!-- timeType=1  月  2：年 -->
                <span
                  class="tab-item"
                  :class="{ active: qualityTimeType === 2 }"
                  @click="changeQualityTimeType(2)"
                >
                  本年
                </span>
                <span class="tab-divider">/</span>
                <span
                  class="tab-item"
                  :class="{ active: qualityTimeType === 1 }"
                  @click="changeQualityTimeType(1)"
                >
                  本月
                </span>
              </div>
            </div>
          </div>
          <div style="background: #f5f7f9; padding: 10px 0px 1px 20px">
            <!-- 检查下发部分 -->
            <div class="section-header">
              <span class="section-title">检查下发</span>
            </div>
            <div class="quality-stats">
              <div class="stat-group">
                <div class="stat-item">
                  <div class="stat-header">
                    <div class="stat-icon">
                      <img
                        src="@/assets/images/home/<USER>"
                        alt="检查计划数"
                        class="check-icon-img"
                      />
                    </div>
                    <div class="stat-label">检查计划数</div>
                  </div>
                  <div class="stat-number">
                    {{ qualityManagement.inspectionPlans }}
                  </div>
                  <div class="stat-detail">
                    <span
                      >日常巡检
                      <span class="number">{{
                        qualityManagement.dailyInspections
                      }}</span>
                      次</span
                    >
                    <span
                      >专项巡检
                      <span class="number">{{
                        qualityManagement.specialInspections
                      }}</span>
                      次</span
                    >
                  </div>
                </div>

                <div class="stat-item">
                  <div class="stat-header">
                    <div class="stat-icon">
                      <img
                        src="@/assets/images/home/<USER>"
                        alt="发现问题数"
                        class="check-icon-img"
                      />
                    </div>
                    <div class="stat-label">发现问题数</div>
                  </div>
                  <div class="stat-number">
                    {{ qualityManagement.foundProblems }}
                  </div>
                  <div class="stat-detail">
                    <span
                      >已整改
                      <span class="number">{{
                        qualityManagement.fixedProblems
                      }}</span>
                      个</span
                    >
                    <span
                      >待整改
                      <span class="number">{{
                        qualityManagement.pendingProblems
                      }}</span>
                      个</span
                    >
                  </div>
                </div>

                <div class="stat-item">
                  <div class="stat-header">
                    <div class="stat-icon">
                      <img
                        src="@/assets/images/home/<USER>"
                        alt="按时整改率"
                        class="check-icon-img"
                      />
                    </div>
                    <div class="stat-label">按时整改率</div>
                  </div>
                  <div class="stat-number">
                    {{ qualityManagement.onTimeRate }}%
                  </div>
                  <div class="stat-detail">
                    <span
                      >按时整改
                      <span class="number">{{
                        qualityManagement.onTimeFixed
                      }}</span>
                      个</span
                    >
                    <span
                      >未按时整改
                      <span class="number">{{
                        qualityManagement.overdueFixed
                      }}</span>
                      个</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            style="
              background: #f5f7f9;
              padding: 10px 0px 1px 20px;
              margin-top: 20px;
            "
          >
            <!-- 自主上报部分 -->
            <div class="section-header">
              <span class="section-title">自主上报</span>
            </div>
            <div class="quality-stats">
              <div class="stat-group">
                <div class="stat-item">
                  <div class="stat-header">
                    <div class="stat-icon">
                      <img
                        src="@/assets/images/home/<USER>"
                        alt="自主上报问题数"
                        class="check-icon-img"
                      />
                    </div>
                    <div class="stat-label">自主上报问题数</div>
                  </div>
                  <div class="stat-number">
                    {{ qualityManagement.selfReportProblems }}
                  </div>
                  <div class="stat-detail">
                    <span
                      >已整改
                      <span class="number">{{
                        qualityManagement.selfReportFixed
                      }}</span>
                      个</span
                    >
                    <span
                      >待整改
                      <span class="number">{{
                        qualityManagement.selfReportPending
                      }}</span>
                      个</span
                    >
                  </div>
                </div>

                <div class="stat-item">
                  <div class="stat-header">
                    <div class="stat-icon">
                      <img
                        src="@/assets/images/home/<USER>"
                        alt="按时整改率"
                        class="check-icon-img"
                      />
                    </div>
                    <div class="stat-label">按时整改率</div>
                  </div>
                  <div class="stat-number">
                    {{ qualityManagement.selfReportRate }}%
                  </div>
                  <div class="stat-detail">
                    <span
                      >按时整改
                      <span class="number">{{
                        qualityManagement.selfReportOnTime
                      }}</span>
                      个</span
                    >
                    <span
                      >未按时整改
                      <span class="number">{{
                        qualityManagement.selfReportOverdue
                      }}</span>
                      个</span
                    >
                  </div>
                </div>
                <!-- 空占位符，用于对齐 -->
                <div class="stat-item stat-placeholder" />
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="management-card">
          <div class="management-header">
            <div class="header-content">
              <h3>安全管理</h3>
              <div class="time-tabs">
                <span
                  class="tab-item"
                  :class="{ active: safetyTimeType === 2 }"
                  @click="changeSafetyTimeType(2)"
                >
                  本年
                </span>
                <span class="tab-divider">/</span>
                <span
                  class="tab-item"
                  :class="{ active: safetyTimeType === 1 }"
                  @click="changeSafetyTimeType(1)"
                >
                  本月
                </span>
              </div>
            </div>
          </div>

          <!-- 顶部统计卡片 -->
          <div class="safety-top-stats">
            <div class="safety-stat-card-new">
              <div class="safety-stat-icon">
                <i class="el-icon-warning-outline"></i>
              </div>
              <div class="safety-stat-content-new">
                <div class="safety-stat-label">安全隐患数</div>
                <div class="safety-stat-number-new">
                  <span class="number">{{ safetyManagement.hazardCount }}</span>
                  <span class="unit">个</span>
                </div>
              </div>
            </div>
            
            <div class="safety-stat-card-new">
              <div class="safety-stat-icon rate-icon">
                <i class="el-icon-data-analysis"></i>
              </div>
              <div class="safety-stat-content-new">
                <div class="safety-stat-label">按时整改率</div>
                <div class="safety-stat-number-new">
                  <span class="number">{{ safetyManagement.reportOnTimeRate }}</span>
                  <span class="unit">%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 饼状图区域 -->
          <div class="safety-chart-area">
            <pieChart
              :data="safetyManagement.hazardTypeChart2"
              height="180px"
              :showCenterText="true"
              :centerText="{
                value: safetyManagement.hazardCount.toString(),
                unit: '安全隐患总数',
                label: ''
              }"
            />
          </div>

          <!-- 底部统计数据 -->
          <div class="safety-bottom-stats">
            <div class="stats-row">
              <div class="stat-item">
                <div class="stat-dot" style="background: #2656F5;"></div>
                <span class="stat-label">待整改</span>
                <span class="stat-value">4</span>
                <span class="stat-unit">个</span>
              </div>
              <div class="stat-item">
                <div class="stat-dot" style="background: #FF920D;"></div>
                <span class="stat-label">已整改</span>
                <span class="stat-value">4</span>
                <span class="stat-unit">个</span>
              </div>
            </div>
            <div class="stats-row">
              <div class="stat-item">
                <div class="stat-dot" style="background: #54C255;"></div>
                <span class="stat-label">已合格</span>
                <span class="stat-value">50</span>
                <span class="stat-unit">个</span>
              </div>
              <div class="stat-item">
                <div class="stat-dot" style="background: #8EE98F;"></div>
                <span class="stat-label">整改率</span>
                <span class="stat-value">50</span>
                <span class="stat-unit">%</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 底部大型设备图表 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="24">
        <div class="chart-card">
          <div class="chart-header">
            <h4>大型设备</h4>
          </div>
          <div class="chart-content">
            <div
              class="equipment-chart-container"
              @mousemove="handleEquipmentMouseMove"
              @mouseleave="handleEquipmentMouseLeave"
            >
              <barChart
                height="300px"
                :data="largeEquipmentChart"
                :show-tooltip="false"
              />

              <!-- 大型设备悬浮框 -->
              <div
                v-show="equipmentTooltip.visible"
                class="project-tooltip"
                :style="{
                  left: equipmentTooltip.x + 'px',
                  top: equipmentTooltip.y + 'px',
                }"
              >
                <div class="tooltip-header">{{ equipmentTooltip.title }}</div>
                <div v-if="equipmentTooltip.loading" class="tooltip-loading">
                  加载中...
                </div>
                <div v-else-if="equipmentTooltip.error" class="tooltip-error">
                  {{ equipmentTooltip.error }}
                </div>
                <div
                  v-else-if="equipmentTooltip.data.length > 0"
                  class="tooltip-content"
                >
                  <div
                    v-for="item in equipmentTooltip.data"
                    :key="item.name"
                    class="tooltip-item"
                  >
                    <span class="tooltip-name">{{ item.name }}</span>
                    <span class="tooltip-value">{{ item.value }}</span>
                  </div>
                </div>
                <div v-else class="tooltip-empty">暂无详细数据</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";
import barChart from "./components/barChart.vue";
import pieChart from "./components/pieChart.vue";
import {
  getManagementOverview,
  getQualityStatistics,
  getSafetyStatistics,
  getDangerTypeStatistics,
  getSafetyProductionStatistics,
  getDangerousProStatistics,
  getLargeEquipmentStatistics,
  getLargeEquipmentByNameStatistics,
} from "@/api/statistics";
export default {
  name: "Index",
  components: {
    barChart,
    pieChart,
  },
  data() {
    return {
      // 时间类型选择 (1-月, 2-年)
      qualityTimeType: 2, // 默认为年
      safetyTimeType: 2, // 默认为年
      // 顶部统计数据
      statsData: {
        // 原有数据
        domesticProjects: 1126,
        internationalProjects: 1126,
        safetyInvestment: 1500,
        largeEquipment: 1126,
        dangerousProjects: 1126,
        // 新增数据
        superDangerousProjects: 1126,
        domesticSafetyInvestment: 1126,
        internationalSafetyInvestment: 1126,
        companyBranches: 1126,
        casualties: 0,
        majorHazards: 1126,
      },
      // 质量管理数据
      qualityManagement: {
        // 检查下发
        inspectionPlans: 122,
        dailyInspections: 89,
        specialInspections: 33,
        foundProblems: 31,
        fixedProblems: 29,
        pendingProblems: 2,
        onTimeRate: 96,
        onTimeFixed: 28,
        overdueFixed: 1,
        // 自主上报
        selfReportProblems: 103,
        selfReportFixed: 100,
        selfReportPending: 3,
        selfReportRate: 86.45,
        selfReportOnTime: 31,
        selfReportOverdue: 7,
      },
      // 安全管理数据
      safetyManagement: {
        // 检查下发
        inspectionCount: 58,
        hazardFound: 20,
        onTimeRate: 89,
        // 自主上报
        hazardCount: 103,
        reportOnTimeRate: 86.45,
        // 隐患类别统计图表
        hazardTypeChart1: {
          colorList: [
            "#2656F5",
            "#8EE98F",
            "#FF920D",
            "#54C255",
            "#A1CDFF",
            "#E54545",
            "#FECF77",
            "#FF7730",
            "#B38DFF",
            "#A1FFEB",
          ],
          data: [
            { value: 35, name: "基础设施" },
            { value: 28, name: "设备维护" },
            { value: 25, name: "消防安全" },
            { value: 22, name: "电气安全" },
            { value: 18, name: "高空作业" },
            { value: 15, name: "机械操作" },
            { value: 12, name: "化学品管理" },
            { value: 10, name: "个人防护" },
            { value: 8, name: "环境卫生" },
            { value: 5, name: "其他" },
          ],
          option: {
            series: [
              {
                center: ["50%", "52%"],
                radius: ["45%", "75%"],
                label: {
                  show: true,
                  position: "outside",
                  formatter: "{b}\n{c}",
                  fontSize: 10,
                  color: "#666",
                  lineHeight: 14,
                },
                labelLine: {
                  show: true,
                  length: 8,
                  length2: 15,
                  lineStyle: {
                    color: "#666",
                    width: 1,
                  },
                },
              },
            ],
          },
        },
        hazardTypeChart2: {
          colorList: [
            "#2656F5",
            "#8EE98F",
            "#FF920D",
            "#54C255",
            "#A1CDFF",
          ],
          data: [
            { value: 15, name: "基础设施" },
            { value: 12, name: "设备维护" },
            { value: 11, name: "消防安全" },
            { value: 10, name: "电气安全" },
            { value: 10, name: "高空作业" },
          ],
          option: {
            series: [
              {
                center: ["50%", "52%"],
                radius: ["45%", "75%"],
                label: {
                  show: true,
                  position: "outside",
                  formatter: "{b}\n{c}",
                  fontSize: 10,
                  color: "#666",
                  lineHeight: 14,
                },
                labelLine: {
                  show: true,
                  length: 8,
                  length2: 15,
                  lineStyle: {
                    color: "#666",
                    width: 1,
                  },
                },
              },
            ],
          },
        },
      },
      // 自主上报数据
      selfReport: {
        reportCount: 103,
        completed: 100,
        pending: 3,
        onTimeRate: 86.45,
        onTimeCompleted: 31,
        overdueCompleted: 7,
      },
      // 统计分析数据
      statisticsAnalysis: {
        overallChart: {
          colorList: [
            "#2656F5",
            "#8EE98F",
            "#A1FFEB",
            "#54C255",
            "#A1CDFF",
            "#FF920D",
          ],
          data: [
            { value: 25, name: "安全基础管理" },
            { value: 20, name: "消防安全" },
            { value: 18, name: "电气安全" },
            { value: 15, name: "特种设备" },
            { value: 12, name: "危化品" },
            { value: 10, name: "其他" },
          ],
        },
      },
      // 危大工程项目列表数据
      dangerousProjectsList: [
        { name: "苏电产业科创园(NO.2010G32)07-13地块项目", progress: 100 },
        { name: "未来出行产业园项目（直流分公司）", progress: 80 },
        { name: "华为网络石代表处项目", progress: 70 },
        { name: "年产3001万件汽车底盘等部件生产线项目", progress: 30 },
        { name: "泪源城土壤生产及新客体验中心二期建设项目", progress: 30 },
      ],
      // 危大工程图表配置
      dangerousProjectChart: {
        colorList: ["#5990FD", "#5990FD", "#5990FD", "#5990FD", "#5990FD"],
        grid: {
          top: 30,
          left: "35%",
          right: "10%",
          bottom: "5%",
        },
        xAxis: {
          type: "value",
          max: 10,
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          type: "category",
          data: [
            "苏电产业科创园(NO.2010G32) 07-13地块项目",
            "未来出行产业园项目（直流分公司）",
            "华为网络石代表处项目",
            "年产3001万件汽车底盘等部件生产线项目",
            "泪源城土壤生产及新客体验中心二期建设项目",
          ],
          axisLabel: {
            fontSize: 12,
            color: "#333",
            interval: 0,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: [
          {
            name: "危大工程数量",
            type: "bar",
            data: [10, 8, 7, 3, 3],
            itemStyle: {
              color: "#5990FD",
              borderRadius: [0, 4, 4, 0],
            },
            barWidth: "60%",
            label: {
              show: true,
              position: "right",
              color: "#333",
              fontSize: 12,
              formatter: "{c}",
            },
          },
        ],
      },
      largeEquipmentChart: {
        colorList: [
          "#FF920D",
          "#FECF77",
          "#FF7730",
          "#54C255",
          "#2656F5",
          "#2C2C2C",
        ],
        grid: {
          top: 30,
          left: "8%",
          right: "8%",
          bottom: "25%",
        },
        xAxis: {
          type: "category",
          data: [
            "设备分类1",
            "设备分类2",
            "设备分类3",
            "设备分类4",
            "设备分类5",
            "设备分类6",
          ],
          axisLabel: {
            interval: 0,
            rotate: 0,
            fontSize: 12,
          },
        },
        yAxis: {
          type: "value",
          max: 210,
          axisLabel: {
            fontSize: 12,
          },
        },
        series: [
          {
            name: "未安装",
            type: "bar",
            stack: "total",
            itemStyle: { color: "#FF920D" },
            data: [35, 0, 20, 0, 0, 35],
          },
          {
            name: "安装中",
            type: "bar",
            stack: "total",
            itemStyle: { color: "#FECF77" },
            data: [0, 0, 0, 0, 0, 5],
          },
          {
            name: "验收中",
            type: "bar",
            stack: "total",
            itemStyle: { color: "#FF7730" },
            data: [0, 0, 10, 0, 0, 0],
          },
          {
            name: "运行中",
            type: "bar",
            stack: "total",
            itemStyle: { color: "#54C255" },
            data: [175, 120, 150, 30, 180, 150],
          },
          {
            name: "维修中",
            type: "bar",
            stack: "total",
            itemStyle: { color: "#2656F5" },
            data: [0, 0, 0, 0, 0, 0],
          },
          {
            name: "已报废",
            type: "bar",
            stack: "total",
            itemStyle: { color: "#2C2C2C" },
            data: [0, 0, 0, 0, 0, 0],
          },
        ],
      },
      // 安全生产投入环形图数据
      safetyInvestmentPieChart: {
        colorList: [
          "#2656F5",
          "#FF920D", 
          "#54C255",
          "#E54545",
          "#8EE98F",
          "#A1CDFF"
        ],
        data: [
          { value: 2000, name: "海外工程一公司" },
          { value: 2000, name: "海外工程二公司" },
          { value: 2000, name: "海外工程三公司" },
          { value: 2000, name: "中江国际集团" },
          { value: 2000, name: "第五建设分公司" }
        ],
        option: {
          series: [
            {
              center: ["50%", "50%"],
              radius: ["55%", "75%"],
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          ]
        }
      },
      // 安全生产投入总金额
      safetyInvestmentTotal: {
        value: "6000",
        unit: "总投入(万元)",
        label: "安全生产投入"
      },
      // 安全生产投入项目列表
      safetyInvestmentProjects: [
        { name: "海外工程一公司", value: "2000", color: "#2656F5" },
        { name: "海外工程二公司", value: "2000", color: "#FF920D" },
        { name: "海外工程三公司", value: "2000", color: "#54C255" },
        { name: "中江国际集团", value: "2000", color: "#E54545" },
        { name: "第五建设分公司", value: "2000", color: "#8EE98F" }
      ],
      // 保留这些旧数据结构以防止报错，后续可以逐步清理
      lineData: {
        grid: {
          top: 10,
          left: "6%",
          right: "6%",
          bottom: "12%",
        },
        xAxisData: [],
        seriesData: [],
      },
      mainData: {},
      yearCount: {},
      dangerList: [],
      echartData: { colorList: [], data: [] },
      cateBarData: { colorList: [], series: [] },
      yearBarData: { series: [] },
      chart2Lengend: [],
      echartType1: 1,
      echartType2: 1,
      echartTypeList1: [],
      echartTypeList2: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 悬浮框相关数据
      tooltip: {
        visible: false,
        x: 0,
        y: 0,
        title: "",
        loading: false,
        error: "",
        data: [],
      },
      // 危大工程图表实例
      dangerousProjectChartInstance: null,
      // 设备详细信息缓存
      equipmentDetailsCache: {},
      // 设备悬浮框相关数据
      equipmentTooltip: {
        visible: false,
        x: 0,
        y: 0,
        title: "",
        loading: false,
        error: "",
        data: [],
      },
      requestQueue: new Set(), // 正在请求的项目名称队列
    };
  },
  created() {
    this.loadManagementOverview();
    this.loadQualityStatistics();
    this.loadSafetyStatistics();
    this.loadDangerTypeStatistics();
    this.loadSafetyProductionStatistics();
    this.loadDangerousProStatistics();
    this.loadLargeEquipmentStatistics();
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.dangerousProjectChartInstance) {
      this.dangerousProjectChartInstance.dispose();
      this.dangerousProjectChartInstance = null;
    }

    // 清理请求队列
    this.requestQueue.clear();
  },
  methods: {
    // 切换质量管理时间类型
    changeQualityTimeType(timeType) {
      if (this.qualityTimeType !== timeType) {
        this.qualityTimeType = timeType;
        this.loadQualityStatistics();
      }
    },

    // 切换安全管理时间类型
    changeSafetyTimeType(timeType) {
      if (this.safetyTimeType !== timeType) {
        this.safetyTimeType = timeType;
        this.loadSafetyStatistics();
        this.loadDangerTypeStatistics();
      }
    },

    // 获取安全管理总览数据
    async loadManagementOverview() {
      try {
        console.log("开始加载安全管理总览数据...");
        const response = await getManagementOverview();

        if (response && response.code === 200 && response.data) {
          const data = response.data;

          // 映射接口数据到现有的数据结构
          this.statsData.dangerousProjects = data.wdgcs || 0; // 危大工程数
          this.statsData.safetyInvestment = data.aqtzzje || 0; // 安全投资总金额
          this.statsData.domesticProjects = data.gnzjxms || 0; // 国内在建项目
          this.statsData.internationalProjects = data.gjzjxms || 0; // 国际在建项目
          this.statsData.largeEquipment = data.dxsbs || 0; // 大型设备数

          console.log("安全管理总览数据加载成功:", this.statsData);
        } else {
          console.warn("接口返回数据格式异常:", response);
          this.handleDataLoadError();
        }
      } catch (error) {
        console.error("获取安全管理总览数据失败:", error);
        this.handleDataLoadError();
      }
    },

    // 处理数据加载错误
    handleDataLoadError() {
      console.log("使用默认数据");
      // 保持原有的默认数据，确保页面正常显示
      this.$modal.msgWarning("数据加载失败，显示默认数据");
    },

    // 获取质量管理统计数据
    async loadQualityStatistics() {
      try {
        console.log(
          "开始加载质量管理统计数据...",
          "timeType:",
          this.qualityTimeType
        );
        const response = await getQualityStatistics({
          timeType: this.qualityTimeType,
        });

        if (response && response.code === 200 && response.data) {
          const data = response.data;

          // 映射接口数据到现有的质量管理数据结构（自主上报部分）
          this.qualityManagement.selfReportProblems = data.jczs || 0; // 自主上报问题数
          this.qualityManagement.selfReportFixed = data.ywcsl || 0; // 已整改数量
          this.qualityManagement.selfReportPending = data.kwcsl || 0; // 待整改数量
          this.qualityManagement.selfReportRate = data.aszgl
            ? data.aszgl * 100
            : 0; // 按时整改率（转换为百分比）
          this.qualityManagement.selfReportOnTime = data.aszg || 0; // 按时整改
          this.qualityManagement.selfReportOverdue = data.waszg || 0; // 未按时整改

          console.log("质量管理统计数据加载成功:", this.qualityManagement);
        } else {
          console.warn("质量管理统计接口返回数据格式异常:", response);
          this.handleQualityDataLoadError();
        }
      } catch (error) {
        console.error("获取质量管理统计数据失败:", error);
        this.handleQualityDataLoadError();
      }
    },

    // 处理质量管理数据加载错误
    handleQualityDataLoadError() {
      console.log("使用质量管理默认数据");
      this.$modal.msgWarning("质量管理数据加载失败，显示默认数据");
    },

    // 获取安全管理统计数据
    async loadSafetyStatistics() {
      try {
        console.log(
          "开始加载安全管理统计数据...",
          "timeType:",
          this.safetyTimeType
        );
        const response = await getSafetyStatistics({
          timeType: this.safetyTimeType,
        });

        if (response && response.code === 200 && response.data) {
          const data = response.data;

          // 映射接口数据到现有的安全管理数据结构（自主上报部分）
          this.safetyManagement.hazardCount = data.jczs || 0; // 隐患数（问题数）
          this.safetyManagement.reportOnTimeRate = data.aszgl
            ? (data.aszgl * 100).toFixed(1)
            : 0; // 按时整改率（转换为百分比）
          // 为了保持数据一致性，也可以存储更多详细数据备用
          this.safetyManagement.safetyFixedProblems = data.ywcsl || 0; // 已整改数量
          this.safetyManagement.safetyPendingProblems = data.kwcsl || 0; // 待整改数量
          this.safetyManagement.safetyOnTimeFixed = data.aszg || 0; // 按时整改
          this.safetyManagement.safetyOverdueFixed = data.waszg || 0; // 未按时整改

          console.log("安全管理统计数据加载成功:", this.safetyManagement);
        } else {
          console.warn("安全管理统计接口返回数据格式异常:", response);
          this.handleSafetyDataLoadError();
        }
      } catch (error) {
        console.error("获取安全管理统计数据失败:", error);
        this.handleSafetyDataLoadError();
      }
    },

    // 处理安全管理数据加载错误
    handleSafetyDataLoadError() {
      console.log("使用安全管理默认数据");
      this.$modal.msgWarning("安全管理数据加载失败，显示默认数据");
    },

    // 获取隐患类别统计数据
    async loadDangerTypeStatistics() {
      try {
        console.log("开始加载隐患类别统计数据...");
        const response = await getDangerTypeStatistics({
          timeType: this.safetyTimeType,
        });

        // 检查下发的图表使用默认数据，总和等于检查次数58
        const defaultDataForInspection = [
          { value: 15, name: "基础设施" },
          { value: 12, name: "设备维护" },
          { value: 11, name: "消防安全" },
          { value: 10, name: "电气安全" },
          { value: 10, name: "高空作业" },
        ];

        // 定义颜色数组
        const colorList1 = [
          "#2656F5",
          "#8EE98F",
          "#FF920D",
          "#54C255",
          "#A1CDFF",
        ];
        const colorList2 = [
          "#FF920D",
          "#E54545",
          "#54C255",
          "#2656F5",
          "#8EE98F",
        ];

        // 更新检查下发的隐患类别统计图表（使用默认数据）
        this.safetyManagement.hazardTypeChart1 = {
          colorList: colorList1,
          data: defaultDataForInspection,
          option: {
            series: [
              {
                center: ["50%", "52%"],
                radius: ["45%", "75%"],
                label: {
                  show: true,
                  position: "outside",
                  formatter: "{b}\n{c}",
                  fontSize: 10,
                  color: "#666",
                  lineHeight: 14,
                },
                labelLine: {
                  show: true,
                  length: 8,
                  length2: 15,
                  lineStyle: {
                    color: "#666",
                    width: 1,
                  },
                },
              },
            ],
          },
        };

        // 自主上报的图表使用接口数据
        if (
          response &&
          response.code === 200 &&
          response.data &&
          Array.isArray(response.data)
        ) {
          const data = response.data;
          // 对数据按value值降序排序，只取前5个
          const sortedData = data
            .sort((a, b) => (b.value || 0) - (a.value || 0))
            .slice(0, 5);

          // 更新自主上报的隐患类别统计图表（使用接口数据）
          this.safetyManagement.hazardTypeChart2 = {
            colorList: colorList2,
            data: sortedData,
            option: {
              series: [
                {
                  center: ["50%", "52%"],
                  radius: ["45%", "75%"],
                  label: {
                    show: true,
                    position: "outside",
                    formatter: "{b}\n{c}",
                    fontSize: 10,
                    color: "#666",
                    lineHeight: 14,
                  },
                  labelLine: {
                    show: true,
                    length: 8,
                    length2: 15,
                    lineStyle: {
                      color: "#666",
                      width: 1,
                    },
                  },
                },
              ],
            },
          };

          console.log("隐患类别统计数据加载成功:", {
            chart1: "使用默认数据",
            chart2: this.safetyManagement.hazardTypeChart2,
          });
        } else {
          console.warn("隐患类别统计接口返回数据格式异常:", response);
          this.handleDangerTypeDataLoadError();
        }
      } catch (error) {
        console.error("获取隐患类别统计数据失败:", error);
        this.handleDangerTypeDataLoadError();
      }
    },

    // 处理隐患类别统计数据加载错误
    handleDangerTypeDataLoadError() {
      console.log("自主上报图表使用默认数据");
      // 如果接口失败，自主上报图表也使用默认数据
      const defaultDataForReport = [
        { value: 8, name: "基础设施" },
        { value: 7, name: "设备维护" },
        { value: 6, name: "消防安全" },
        { value: 5, name: "电气安全" },
        { value: 4, name: "高空作业" },
      ];

      const colorList2 = [
        "#FF920D",
        "#E54545",
        "#54C255",
        "#2656F5",
        "#8EE98F",
      ];

      this.safetyManagement.hazardTypeChart2 = {
        colorList: colorList2,
        data: defaultDataForReport,
        option: {
          series: [
            {
              center: ["50%", "52%"],
              radius: ["45%", "75%"],
              label: {
                show: true,
                position: "outside",
                formatter: "{b}\n{c}",
                fontSize: 10,
                color: "#666",
                lineHeight: 14,
              },
              labelLine: {
                show: true,
                length: 8,
                length2: 15,
                lineStyle: {
                  color: "#666",
                  width: 1,
                },
              },
            },
          ],
        },
      };
      this.$modal.msgWarning("隐患类别统计数据加载失败，显示默认数据");
    },

    // 获取安全生产投入统计数据
    async loadSafetyProductionStatistics() {
      try {
        console.log("开始加载安全生产投入统计数据...");
        const response = await getSafetyProductionStatistics();

        if (
          response &&
          response.code === 200 &&
          response.data &&
          Array.isArray(response.data)
        ) {
          const data = response.data;

          // 提取公司名称作为X轴标签（截取显示）
          const xAxisData = data.map((item) => {
            // 截取公司名称用于X轴显示，保持图表美观
            const companyName = item.company || "未知公司";
            return companyName.length > 10
              ? companyName.substring(0, 10) + "..."
              : companyName;
          });

          // 保存完整的公司名称用于tooltip显示
          const fullCompanyNames = data.map(
            (item) => item.company || "未知公司"
          );

          // 提取年度预算金额数据，并保存完整公司名称
          const budgetData = data.map((item, index) => ({
            value: parseFloat(item.annualBudgetAmount || 0),
            fullName: fullCompanyNames[index],
          }));
          // 提取实际投入金额数据，并保存完整公司名称
          const actualData = data.map((item, index) => ({
            value: parseFloat(item.actualInputAmount || 0),
            fullName: fullCompanyNames[index],
          }));

          // 动态计算Y轴最大值
          const maxValue = Math.max(
            ...budgetData.map((item) => item.value),
            ...actualData.map((item) => item.value)
          );
          const yAxisMax = Math.ceil((maxValue * 1.2) / 1000) * 1000; // 向上取整到千位

          // 计算总投入
          const totalInvestment = data.reduce((sum, item) => sum + parseFloat(item.actualInputAmount || 0), 0);

          // 更新环形图数据
          const colorList = ["#2656F5", "#FF920D", "#54C255", "#E54545", "#8EE98F", "#A1CDFF"];
          
          this.safetyInvestmentPieChart = {
            colorList: colorList,
            data: data.map((item, index) => ({
              value: parseFloat(item.actualInputAmount || 0),
              name: item.company || "未知公司"
            })),
            option: {
              series: [
                {
                  center: ["50%", "50%"],
                  radius: ["55%", "75%"],
                  label: {
                    show: false
                  },
                  labelLine: {
                    show: false
                  }
                }
              ]
            }
          };

          // 更新中心文字
          this.safetyInvestmentTotal = {
            value: Math.round(totalInvestment).toString(),
            unit: "总投入(万元)",
            label: "安全生产投入"
          };

          // 更新项目列表
          this.safetyInvestmentProjects = data.map((item, index) => ({
            name: item.company || "未知公司",
            value: Math.round(parseFloat(item.actualInputAmount || 0)).toString(),
            color: colorList[index % colorList.length]
          }));

          console.log(
            "安全生产投入统计数据加载成功:",
            this.safetyInvestmentChart
          );
        } else {
          console.warn("安全生产投入统计接口返回数据格式异常:", response);
          this.handleSafetyProductionDataLoadError();
        }
      } catch (error) {
        console.error("获取安全生产投入统计数据失败:", error);
        this.handleSafetyProductionDataLoadError();
      }
    },

    // 处理安全生产投入统计数据加载错误
    handleSafetyProductionDataLoadError() {
      console.log("使用安全生产投入统计默认数据");
      this.$modal.msgWarning("安全生产投入统计数据加载失败，显示默认数据");
    },

    // 获取危大工程统计数据
    async loadDangerousProStatistics() {
      try {
        console.log("开始加载危大工程统计数据...");
        const response = await getDangerousProStatistics();

        if (
          response &&
          response.code === 200 &&
          response.data &&
          Array.isArray(response.data)
        ) {
          const data = response.data;

          // 获取前5个项目数据
          const topProjects = data.slice(0, 5);

          // 找出最大的value值，用于计算X轴最大值
          const maxValue = Math.max(...data.map((item) => item.value));

          // 提取项目名称和数值，保留完整数据用于tooltip
          const projectNames = topProjects.map((item) => {
            // 截取项目名称，避免过长
            return item.name && item.name.length > 10
              ? item.name.substring(0, 10) + "..."
              : item.name || "未知项目";
          });

          // 构建包含详细信息的数据
          const projectData = topProjects.map((item, index) => ({
            name: projectNames[index],
            value: item.value || 0,
            fullName: item.name || "未知项目",
            detalList: item.detalList || [],
          }));

          // 更新图表配置
          this.dangerousProjectChart.yAxis.data = projectNames.reverse();
          this.dangerousProjectChart.series[0].data = projectData.reverse();
          this.dangerousProjectChart.xAxis.max = maxValue;

          // 保存原始数据供其他用途使用
          this.dangerousProjectsList = topProjects.map((item) => ({
            name: item.name || "未知项目",
            value: item.value || 0,
            originalName: item.name,
            detalList: item.detalList || [],
          }));

          // 初始化图表
          this.$nextTick(() => {
            this.initDangerousProjectChart();
          });

          console.log("危大工程统计数据加载成功:", this.dangerousProjectChart);
        } else {
          console.warn("危大工程统计接口返回数据格式异常:", response);
          this.handleDangerousProDataLoadError();
        }
      } catch (error) {
        console.error("获取危大工程统计数据失败:", error);
        this.handleDangerousProDataLoadError();
      }
    },

    // 处理危大工程统计数据加载错误
    handleDangerousProDataLoadError() {
      console.log("使用危大工程统计默认数据");
      this.$modal.msgWarning("危大工程统计数据加载失败，显示默认数据");
      // 使用默认数据初始化图表
      this.$nextTick(() => {
        this.initDangerousProjectChart();
      });
    },

    // 初始化危大工程图表
    initDangerousProjectChart() {
      if (this.$refs.dangerousProjectChart) {
        // 销毁现有实例
        if (this.dangerousProjectChartInstance) {
          this.dangerousProjectChartInstance.dispose();
        }

        // 创建新的图表实例
        this.dangerousProjectChartInstance = echarts.init(
          this.$refs.dangerousProjectChart
        );

        // 设置图表选项
        const option = {
          grid: this.dangerousProjectChart.grid,
          xAxis: this.dangerousProjectChart.xAxis,
          yAxis: this.dangerousProjectChart.yAxis,
          series: this.dangerousProjectChart.series,
          tooltip: {
            show: true,
            trigger: "item",
            backgroundColor: "#fff",
            borderColor: "#ccc",
            borderWidth: 1,
            textStyle: {
              color: "#333",
              fontSize: 12,
            },
            extraCssText:
              "box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 400px; white-space: normal;",
            position: function (point, params, dom, rect, size) {
              // 获取 tooltip 的实际尺寸
              const tooltipWidth = size.contentSize[0];
              const tooltipHeight = size.contentSize[1];

              // 图表容器尺寸
              const chartWidth = rect.width;
              const chartHeight = rect.height;

              // 计算右边和左边的可用空间
              const rightSpace = chartWidth - point[0] - 10;
              const leftSpace = point[0] - 10;

              let x, y;

              // 优先选择空间更大的一边，但确保不会被遮挡
              if (rightSpace >= tooltipWidth || rightSpace > leftSpace) {
                // 显示在右边
                x = point[0] + 10;
                // 如果右边真的放不下，尝试调整到图表右边界内
                if (x + tooltipWidth > chartWidth) {
                  x = chartWidth - tooltipWidth - 5;
                }
              } else if (leftSpace >= tooltipWidth) {
                // 显示在左边，但确保有足够空间
                x = point[0] - tooltipWidth - 10;
                // 确保不会超出左边界
                if (x < 0) {
                  x = 5;
                }
              } else {
                // 如果两边都放不下，选择右边并强制显示在图表内
                x = Math.max(
                  5,
                  Math.min(point[0] + 10, chartWidth - tooltipWidth - 5)
                );
              }

              // 垂直居中，但确保不超出边界
              y = point[1] - tooltipHeight / 2;
              if (y < 10) {
                y = 10;
              } else if (y + tooltipHeight > chartHeight - 10) {
                y = chartHeight - tooltipHeight - 10;
              }

              return [x, y];
            },
            formatter: (params) => {
              if (params.data && typeof params.data === "object") {
                const { fullName, value, detalList } = params.data;

                let html = `<div style="font-weight: bold; margin-bottom: 8px; color: #333;">${fullName}</div>`;
                html += `<div style="margin-bottom: 8px;">总数量: <span style="color: #1890ff; font-weight: bold;">${value}</span></div>`;

                if (detalList && detalList.length > 0) {
                  html +=
                    '<div style="border-top: 1px solid #eee; padding-top: 8px;">';
                  html +=
                    '<div style="font-weight: bold; margin-bottom: 6px; color: #666;">详细信息:</div>';
                  detalList.forEach((item) => {
                    html += `<div style="margin-bottom: 4px; padding-left: 8px; border-left: 2px solid #1890ff;">
                      <div style="font-weight: 500;">${
                        item.name || "未知类型"
                      }</div>
                      <div style="color: #666; font-size: 11px;">数量: ${
                        item.value || 0
                      }</div>
                    </div>`;
                  });
                  html += "</div>";
                } else {
                  html +=
                    '<div style="color: #999; font-style: italic;">暂无详细信息</div>';
                }

                return html;
              }

              // 兜底显示
              return `项目: ${params.name}<br/>数量: ${params.value}`;
            },
          },
        };

        this.dangerousProjectChartInstance.setOption(option);

        // 自适应大小
        window.addEventListener("resize", () => {
          if (this.dangerousProjectChartInstance) {
            this.dangerousProjectChartInstance.resize();
          }
        });
      }
    },

    // 获取大型设备统计数据
    async loadLargeEquipmentStatistics() {
      try {
        console.log("开始加载大型设备统计数据...");
        const response = await getLargeEquipmentStatistics();

        if (
          response &&
          response.code === 200 &&
          response.data &&
          Array.isArray(response.data)
        ) {
          const data = response.data;

          // 提取设备名称作为X轴类别
          const equipmentNames = data.map((item) => item.name || "未知设备");

          // 提取设备数量，全部设为"运行中"状态
          const runningData = data.map((item) => item.value || 0);
          // 动态计算Y轴最大值，直接使用接口数据的最大值
          const maxValue = Math.max(...runningData);
          const yAxisMax = maxValue > 0 ? maxValue : 10; // Y轴上限就是接口里最大的数据

          // 更新大型设备图表配置
          this.largeEquipmentChart = {
            colorList: [
              "#FF920D",
              "#FECF77",
              "#FF7730",
              "#54C255",
              "#2656F5",
              "#2C2C2C",
            ],
            grid: {
              top: 30,
              left: "8%",
              right: "8%",
              bottom: "25%",
            },
            legend: undefined, // 显式移除图例
            xAxis: {
              type: "category",
              data: equipmentNames,
              axisLabel: {
                interval: 0,
                rotate: 0,
                fontSize: 12,
              },
            },
            yAxis: {
              type: "value",
              max: yAxisMax > 0 ? yAxisMax : 10,
              min: 0,
              interval: yAxisMax > 0 ? Math.ceil(yAxisMax / 5) : 2, // 将Y轴等分为5个区间，确保间隔为整数
              axisLabel: {
                fontSize: 12,
              },
            },
            series: [
              {
                name: "未安装",
                type: "bar",
                stack: "total",
                itemStyle: { color: "#FF920D" },
                data: new Array(data.length).fill(0), // 全部设为0
              },
              {
                name: "安装中",
                type: "bar",
                stack: "total",
                itemStyle: { color: "#FECF77" },
                data: new Array(data.length).fill(0), // 全部设为0
              },
              {
                name: "验收中",
                type: "bar",
                stack: "total",
                itemStyle: { color: "#FF7730" },
                data: new Array(data.length).fill(0), // 全部设为0
              },
              {
                name: "运行中",
                type: "bar",
                stack: "total",
                itemStyle: { color: "#54C255" },
                data: runningData, // 使用真实数据
              },
              {
                name: "维修中",
                type: "bar",
                stack: "total",
                itemStyle: { color: "#2656F5" },
                data: new Array(data.length).fill(0), // 全部设为0
              },
              {
                name: "已报废",
                type: "bar",
                stack: "total",
                itemStyle: { color: "#2C2C2C" },
                data: new Array(data.length).fill(0), // 全部设为0
              },
            ],
          };

          console.log("大型设备统计数据加载成功:", this.largeEquipmentChart);
        } else {
          console.warn("大型设备统计接口返回数据格式异常:", response);
          this.handleLargeEquipmentDataLoadError();
        }
      } catch (error) {
        console.error("获取大型设备统计数据失败:", error);
        this.handleLargeEquipmentDataLoadError();
      }
    },

    // 处理大型设备统计数据加载错误
    handleLargeEquipmentDataLoadError() {
      console.log("使用大型设备统计默认数据");
      this.$modal.msgWarning("大型设备统计数据加载失败，显示默认数据");
    },

    // 异步加载设备详细信息
    async loadEquipmentDetails(equipmentName) {
      // 如果已经有缓存，直接返回
      if (this.equipmentDetailsCache[equipmentName]) {
        return;
      }

      try {
        console.log("开始加载设备详细信息:", equipmentName);
        const response = await getLargeEquipmentByNameStatistics(equipmentName);

        if (
          response &&
          response.code === 200 &&
          response.data &&
          Array.isArray(response.data)
        ) {
          // 缓存详细数据
          this.equipmentDetailsCache[equipmentName] = response.data.map(
            (item) => ({
              name: item.name,
              value: item.value,
              equipment_name: item.equipment_name,
            })
          );

          console.log(
            "设备详细信息加载成功:",
            equipmentName,
            this.equipmentDetailsCache[equipmentName]
          );

          // 更新当前悬浮框的数据
          if (
            this.equipmentTooltip.visible &&
            this.equipmentTooltip.title === equipmentName
          ) {
            this.equipmentTooltip.loading = false;
            this.equipmentTooltip.data =
              this.equipmentDetailsCache[equipmentName];
          }
        } else {
          // 设置空数据避免重复请求
          this.equipmentDetailsCache[equipmentName] = [];
          console.warn("设备详细信息接口返回数据格式异常:", response);
          if (
            this.equipmentTooltip.visible &&
            this.equipmentTooltip.title === equipmentName
          ) {
            this.equipmentTooltip.loading = false;
            this.equipmentTooltip.error = "数据格式异常";
          }
        }
      } catch (error) {
        // 设置空数据避免重复请求
        this.equipmentDetailsCache[equipmentName] = [];
        console.error("获取设备详细信息失败:", error);
        if (
          this.equipmentTooltip.visible &&
          this.equipmentTooltip.title === equipmentName
        ) {
          this.equipmentTooltip.loading = false;
          this.equipmentTooltip.error = "加载失败";
        }
      }
    },

    // 处理设备图表鼠标移动
    handleEquipmentMouseMove(event) {
      // 获取鼠标位置
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const chartWidth = rect.width;

      // 根据鼠标位置推断设备（简化实现）
      const equipmentNames = this.largeEquipmentChart.xAxis?.data || [];
      if (equipmentNames.length === 0) return;

      const equipmentIndex = Math.floor(
        (x / chartWidth) * equipmentNames.length
      );
      if (equipmentIndex >= 0 && equipmentIndex < equipmentNames.length) {
        const equipmentName = equipmentNames[equipmentIndex];

        // 显示悬浮框
        this.equipmentTooltip.visible = true;
        this.equipmentTooltip.x = event.clientX + 10;
        this.equipmentTooltip.y = event.clientY - 10;
        this.equipmentTooltip.title = equipmentName;

        // 检查缓存数据
        const cachedData = this.equipmentDetailsCache[equipmentName];
        if (cachedData) {
          this.equipmentTooltip.loading = false;
          this.equipmentTooltip.error = "";
          this.equipmentTooltip.data = cachedData;
        } else {
          this.equipmentTooltip.loading = true;
          this.equipmentTooltip.error = "";
          this.equipmentTooltip.data = [];

          // 加载详细数据
          this.loadEquipmentDetails(equipmentName);
        }
      }
    },

    // 处理设备图表鼠标离开
    handleEquipmentMouseLeave() {
      this.equipmentTooltip.visible = false;
      this.equipmentTooltip.data = [];
      this.equipmentTooltip.error = "";
      this.equipmentTooltip.loading = false;
    },

    // 截取公司名称显示
    truncateCompanyName(companyName, maxLength = 20) {
      if (!companyName) return "";
      if (companyName.length <= maxLength) return companyName;
      return companyName.substring(0, maxLength) + "...";
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  padding: 15px; // 默认固定内边距，避免大屏幕下过大
  background: #f5f7fa;
  font-size: 14px; // 默认固定基础字体大小
  /* 响应式基础字体大小 */

  @media (max-width: 1199px) {
    padding: 1%;
    font-size: clamp(12px, 1.5vw, 16px);
  }
}

.el-row {
  margin-bottom: 20px; // 默认固定间距

  @media (max-width: 1199px) {
    margin-bottom: 1%;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 顶部统计卡片样式（第一行 - 3个大卡片）
.top-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;

  @media (max-width: 1199px) {
    gap: 1.5%;
    margin-bottom: 1%;
  }

  .stat-card {
    flex: 1;
    background: white;
    border-radius: 7px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-height: 120px;
    min-width: 30%;

    @media (max-width: 1199px) {
      padding: 3% 2%;
    }

    .stat-header {
      margin-bottom: 16px;
      
      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
        
        @media (max-width: 1199px) {
          font-size: clamp(1rem, 1.8vw, 1.125rem);
        }
      }
    }

    .stat-content-dual {
      display: flex;
      gap: 20px;
      
      @media (max-width: 1199px) {
        gap: 1.5%;
      }
      
      .stat-item {
        flex: 1;
        
        .stat-title {
          font-size: 12px;
          color: #666;
          margin-bottom: 8px;
          line-height: 1.2;
          
          @media (max-width: 1199px) {
            font-size: clamp(0.625rem, 1vw, 0.75rem);
          }
        }

        .stat-number-row {
          display: flex;
          align-items: baseline;
          gap: 4px;

          .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #1479fc;
            line-height: 1;

            @media (max-width: 1199px) {
              font-size: clamp(1rem, 2.2vw, 1.75rem);
            }
          }

          .stat-unit {
            font-size: 14px;
            color: #666;
            font-weight: normal;

            @media (max-width: 1199px) {
              font-size: clamp(0.75rem, 1.1vw, 0.875rem);
            }
          }
        }
      }
    }

    &.stat-card-1 {
      background: linear-gradient(120deg, #F74A34 0%, #FF7C5E 100%);
      border-radius: 7px;
      color: white;
      
      .stat-header h3 {
        color: white;
      }
      
      .stat-content-dual .stat-item {
        .stat-title {
          color: rgba(255, 255, 255, 0.9);
        }
        .stat-number {
          color: white;
        }
        .stat-unit {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    &.stat-card-2 {
      background: linear-gradient(137deg, #1688E6 0%, #46ABFF 100%);
      border-radius: 7px;
      color: white;
      
      .stat-header h3 {
        color: white;
      }
      
      .stat-content-dual .stat-item {
        .stat-title {
          color: rgba(255, 255, 255, 0.9);
        }
        .stat-number {
          color: white;
        }
        .stat-unit {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    &.stat-card-3 {
      background: linear-gradient(137deg, #F5873E 0%, #F5A645 100%);
      border-radius: 7px;
      color: white;
      
      .stat-header h3 {
        color: white;
      }
      
      .stat-content-dual .stat-item {
        .stat-title {
          color: rgba(255, 255, 255, 0.9);
        }
        .stat-number {
          color: white;
        }
        .stat-unit {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

// 第二行统计卡片样式（4个小卡片）
.second-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;

  @media (max-width: 1199px) {
    gap: 1.5%;
    margin-bottom: 1%;
  }

  .stat-card-small {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 7px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    flex: 1;
    min-width: 22%;
    min-height: 110px;

    @media (max-width: 1199px) {
      padding: 2% 1.5%;
    }

    .stat-icon {
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;

      @media (max-width: 1199px) {
        width: 35px;
        height: 35px;
        margin-right: 8px;
      }

      i {
        font-size: 20px;
        color: #666;

        @media (max-width: 1199px) {
          font-size: 18px;
        }
      }
    }

    .stat-content-small {
      flex: 1;

      .stat-title-small {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
        line-height: 1.2;

        @media (max-width: 1199px) {
          font-size: clamp(0.625rem, 1vw, 0.75rem);
        }
      }

      .stat-number-row-small {
        display: flex;
        align-items: baseline;
        gap: 4px;

        .stat-number-small {
          font-size: 20px;
          font-weight: bold;
          color: #333;
          line-height: 1;

          @media (max-width: 1199px) {
            font-size: clamp(1rem, 1.6vw, 1.25rem);
          }
        }

        .stat-unit-small {
          font-size: 12px;
          color: #666;
          font-weight: normal;

          @media (max-width: 1199px) {
            font-size: clamp(0.625rem, 1vw, 0.75rem);
          }
        }
      }
    }

    &.stat-card-small-1 {
      background: linear-gradient(137deg, #156BF6 0%, #4681FF 100%);
      border-radius: 7px;
      color: white;

      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
        
        i {
          color: white;
        }
      }

      .stat-content-small {
        .stat-title-small {
          color: rgba(255, 255, 255, 0.9);
        }

        .stat-number-small {
          color: white;
        }

        .stat-unit-small {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    &.stat-card-small-2 {
      background: linear-gradient(137deg, #FC9920 0%, #F5AC45 100%);
      border-radius: 7px;
      color: white;

      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
        
        i {
          color: white;
        }
      }

      .stat-content-small {
        .stat-title-small {
          color: rgba(255, 255, 255, 0.9);
        }

        .stat-number-small {
          color: white;
        }

        .stat-unit-small {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    &.stat-card-small-3 {
      background: linear-gradient(137deg, #9D59FF 0%, #CA79F5 100%);
      border-radius: 7px;
      color: white;

      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
        
        i {
          color: white;
        }
      }

      .stat-content-small {
        .stat-title-small {
          color: rgba(255, 255, 255, 0.9);
        }

        .stat-number-small {
          color: white;
        }

        .stat-unit-small {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    &.stat-card-small-4 {
      background: linear-gradient(147deg, #18C68C 0%, #2BD181 100%);
      border-radius: 7px;
      color: white;

      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
        
        i {
          color: white;
        }
      }

      .stat-content-small {
        .stat-title-small {
          color: rgba(255, 255, 255, 0.9);
        }

        .stat-number-small {
          color: white;
        }

        .stat-unit-small {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

// 管理卡片样式
.management-row {
  margin-bottom: 20px; // 默认固定间距

  @media (max-width: 1199px) {
    margin-bottom: 1%;
  }

  .management-card {
    background: white;
    border-radius: 0.75rem;
    padding: 15px; // 减少内边距，默认固定内边距
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 370px;
    overflow: hidden; // 防止内容超出
    box-sizing: border-box; // 确保内边距计算在内

    @media (max-width: 1199px) {
      padding: 1%;
    }

    .management-header {
      margin-bottom: 10px; // 减少间距，默认固定间距

      @media (max-width: 1199px) {
        margin-bottom: 1%;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      h3 {
        margin: 0;
        font-size: 16px; // 默认固定字体大小
        font-weight: 400;
        color: #333;

        @media (max-width: 1199px) {
          font-size: clamp(0.875rem, 1.6vw, 1rem);
        }
      }

      .time-tabs {
        display: flex;
        align-items: center;
        gap: 4px;

        .tab-item {
          font-size: 12px;
          color: #666;
          cursor: pointer;
          padding: 2px 6px;
          border-radius: 4px;
          transition: all 0.3s ease;
          user-select: none;

          &:hover {
            color: #2656f5;
            background: rgba(38, 86, 245, 0.1);
          }

          &.active {
            color: #2656f5;
            background: rgba(38, 86, 245, 0.1);
            font-weight: 500;
          }

          @media (max-width: 1199px) {
            font-size: clamp(0.625rem, 1vw, 0.75rem);
            padding: 1px 4px;
          }
        }

        .tab-divider {
          font-size: 12px;
          color: #ccc;
          margin: 0 2px;

          @media (max-width: 1199px) {
            font-size: clamp(0.625rem, 1vw, 0.75rem);
          }
        }
      }
    }

    // 质量管理样式
    .section-header {
      margin-bottom: 1%;

      .section-title {
        font-size: clamp(0.625rem, 1vw, 0.75rem);
        color: #666;
        padding: 0.25rem 0.5rem;
        background: #f5f7fa;
        border-radius: 0.25rem;
      }
    }

    .quality-stats {
      margin-bottom: 1%;

      .stat-group {
        display: flex;
        gap: 1.5%;
        align-items: flex-start;

        .stat-item {
          flex: 1;
          display: flex;
          flex-direction: column;

          &.stat-placeholder {
            visibility: hidden;
          }

          .stat-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            min-height: 28px;

            .stat-icon {
              width: 1.25rem;
              height: 1.25rem;
              border-radius: 0.25rem;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: clamp(0.625rem, 1vw, 0.75rem);

              &.blue {
                background: #2656f5;
              }

              &.orange {
                background: #ff920d;
              }

              &.green {
                background: #54c255;
              }
            }

            .stat-label {
              font-size: clamp(0.75rem, 1.2vw, 0.875rem);
              color: #666;
              font-weight: 500;
            }
          }

          .stat-number {
            font-size: clamp(0.75rem, 1.3vw, 1.25rem);
            font-weight: bold;
            color: #2656f5;
            line-height: 1;
            margin-bottom: 0.5rem;
            min-height: 24px;
            text-align: left;
          }

          .stat-detail {
            display: flex;
            gap: 1.5%;
            align-items: flex-start;

            span {
              font-size: clamp(0.5rem, 0.7vw, 0.625rem);
              color: #999;
              line-height: 1.4;

              .number {
                color: #333;
                font-weight: 500;
              }
            }
          }
        }
      }
    }

    // 安全管理专用样式
    // 新的安全管理样式
    .safety-top-stats {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;

      @media (max-width: 1199px) {
        gap: 1.5%;
        margin-bottom: 1.5%;
      }

      .safety-stat-card-new {
        flex: 1;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        display: flex;
        align-items: center;
        gap: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        @media (max-width: 1199px) {
          padding: 1.2%;
          gap: 1%;
        }

        .safety-stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          &.rate-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }

          @media (max-width: 1199px) {
            width: clamp(36px, 4vw, 48px);
            height: clamp(36px, 4vw, 48px);
          }

          i {
            font-size: 24px;
            color: white;

            @media (max-width: 1199px) {
              font-size: clamp(18px, 2vw, 24px);
            }
          }
        }

        .safety-stat-content-new {
          flex: 1;

          .safety-stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 6px;
            line-height: 1.2;

            @media (max-width: 1199px) {
              font-size: clamp(0.75rem, 1.2vw, 0.875rem);
            }
          }

          .safety-stat-number-new {
            display: flex;
            align-items: baseline;
            gap: 4px;

            .number {
              font-size: 28px;
              font-weight: bold;
              color: #1479fc;
              line-height: 1;

              @media (max-width: 1199px) {
                font-size: clamp(1.25rem, 2.5vw, 1.75rem);
              }
            }

            .unit {
              font-size: 16px;
              color: #666;
              font-weight: normal;

              @media (max-width: 1199px) {
                font-size: clamp(0.875rem, 1.4vw, 1rem);
              }
            }
          }
        }
      }
    }

    .safety-chart-area {
      margin-bottom: 20px;
      padding: 0 20px;

      @media (max-width: 1199px) {
        margin-bottom: 1.5%;
        padding: 0 2%;
      }
    }

    .safety-bottom-stats {
      .stats-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        @media (max-width: 1199px) {
          margin-bottom: 1%;
        }

        .stat-item {
          display: flex;
          align-items: center;
          gap: 8px;
          flex: 1;
          justify-content: center;

          @media (max-width: 1199px) {
            gap: 0.8%;
          }

          .stat-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            flex-shrink: 0;

            @media (max-width: 1199px) {
              width: clamp(8px, 1vw, 10px);
              height: clamp(8px, 1vw, 10px);
            }
          }

          .stat-label {
            font-size: 12px;
            color: #666;
            min-width: 40px;

            @media (max-width: 1199px) {
              font-size: clamp(0.625rem, 1vw, 0.75rem);
              min-width: clamp(30px, 3vw, 40px);
            }
          }

          .stat-value {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            min-width: 20px;
            text-align: right;

            @media (max-width: 1199px) {
              font-size: clamp(0.75rem, 1.2vw, 0.875rem);
            }
          }

          .stat-unit {
            font-size: 12px;
            color: #666;

            @media (max-width: 1199px) {
              font-size: clamp(0.625rem, 1vw, 0.75rem);
            }
          }
        }
      }
    }

    .safety-sections-container {
      display: flex;
      gap: 15px; // 减少间距，默认固定间距
      width: 100%;
      overflow: hidden; // 防止内容超出

      @media (max-width: 1199px) {
        gap: 1.5%;
      }
    }

    .safety-section-independent {
      flex: 1;
      background: #f5f7f9;
      padding: 10px 15px 12px 15px; // 减少内边距，默认固定内边距
      border-radius: 0.5rem;
      min-width: 0; // 允许flex项目缩小到内容大小以下
      box-sizing: border-box; // 确保内边距计算在内

      @media (max-width: 1199px) {
        padding: 0.8% 1.5% 1% 1.5%;
      }

      .section-header {
        margin-bottom: 12px; // 默认固定间距

        @media (max-width: 1199px) {
          margin-bottom: 1%;
        }

        .section-title {
          font-size: 12px; // 默认固定字体大小
          color: #666;
          padding: 0.25rem 0.5rem;
          background: rgba(245, 247, 250, 0.8);
          border-radius: 0.25rem;

          @media (max-width: 1199px) {
            font-size: clamp(0.625rem, 1vw, 0.75rem);
          }
        }
      }
    }

    .safety-stats-row {
      display: flex;
      gap: 8px; // 进一步减少间距，默认固定间距
      margin-top: 10px; // 减少上边距，默认固定间距

      @media (max-width: 1199px) {
        gap: 1.5%;
        margin-top: 1%;
      }

      .safety-stat-card {
        flex: 1;
        background: white;
        border-radius: 0.5rem;
        padding: 10px 12px; // 大幅减少内边距，默认固定内边距
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
        min-width: 0; // 允许flex项目缩小
        box-sizing: border-box; // 确保内边距计算在内

        @media (max-width: 1199px) {
          padding: 1.2% 1.6%;
        }

        .safety-stat-title {
          font-size: 10px; // 默认固定字体大小
          color: #666666;
          margin-bottom: 0.5rem;
          line-height: 1.2;

          @media (max-width: 1199px) {
            font-size: clamp(0.5rem, 0.8vw, 0.625rem);
          }
        }

        .safety-stat-content-row {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          gap: 15px; // 默认固定间距

          @media (max-width: 1199px) {
            gap: 1.2%;
          }
        }

        .safety-stat-main {
          display: flex;
          align-items: baseline;
          gap: 0.25rem;
          margin-bottom: 0;

          .safety-stat-number {
            font-size: 22px; // 调整默认固定字体大小，适合1300px+屏幕
            font-weight: bold;
            color: #1479fc;
            line-height: 1;

            @media (max-width: 1199px) {
              font-size: clamp(0.75rem, 1.5vw, 1.5rem);
              /* 减小响应式字体：12px-24px */
            }
          }

          .safety-stat-unit {
            font-size: 10px; // 默认固定字体大小
            color: #666666;
            font-weight: normal;
            line-height: 1;

            @media (max-width: 1199px) {
              font-size: clamp(0.5rem, 0.8vw, 0.625rem);
            }
          }
        }

        .safety-stat-subtitle {
          font-size: 9px; // 默认固定字体大小
          color: #999999;
          line-height: 1.2;
          white-space: nowrap;

          @media (max-width: 1199px) {
            font-size: clamp(0.4375rem, 0.7vw, 0.5625rem);
          }

          .safety-highlight-number {
            color: #ff920d;
            font-weight: 500;
          }
        }
      }
    }

    // 内嵌图表样式
    .chart-section-in-section {
      .chart-title {
        font-size: 12px; // 默认固定字体大小
        color: #666;
        padding: 0.25rem 0.5rem;
        background: rgba(245, 247, 250, 0.8);
        border-radius: 0.25rem;
        margin-bottom: 0.5rem;
        text-align: left;
        display: inline-block;

        @media (max-width: 1199px) {
          font-size: clamp(0.625rem, 1vw, 0.75rem);
        }
      }
    }

    .chart-section {
      .chart-row {
        display: flex;
        gap: 1.5%;

        .chart-item {
          flex: 1;

          .chart-title {
            font-size: clamp(0.625rem, 1vw, 0.75rem);
            color: #666;
            margin-bottom: 0.5rem;
            text-align: center;
          }
        }
      }
    }

    .pie-chart-section {
      .chart-title {
        font-size: clamp(0.75rem, 1.2vw, 0.875rem);
        color: #666;
        margin-bottom: 1rem;
        text-align: center;
      }

      &.full-height {
        height: calc(100% - 5rem);

        .chart-container {
          height: 100%;
        }
      }
    }
  }
}

// 底部图表卡片样式
.charts-row {
  .chart-card {
    background: white;
    border-radius: 0.75rem;
    padding: 2.5%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 340px;

    .chart-header {
      margin-bottom: 1.5%;

      h4 {
        margin: 0;
        font-size: clamp(0.875rem, 1.4vw, 1rem);
        font-weight: 600;
        color: #333;
      }
    }

    .chart-content {
      height: calc(100% - 4rem);
    }
  }
}

// 图表容器样式
.chart-container {
  width: 100%;
  height: 100%;
}

// 设备图表容器样式
.equipment-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

// 危大工程图表容器样式
.dangerous-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

// 安全生产投入容器样式
.safety-investment-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 24px;
  align-items: center;

  @media (max-width: 1199px) {
    gap: 2%;
  }

  .pie-chart-section {
    width: 280px;
    height: 280px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    
    @media (max-width: 1199px) {
      width: 35%;
      height: auto;
      aspect-ratio: 1;
    }
    
    .chart-wrapper {
      width: 100%;
      height: 100%;
      position: relative;
    }
  }

  .project-list-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 24px;

    @media (max-width: 1199px) {
      padding-left: 2%;
    }

    .project-list {
      width: 100%;
      max-width: 300px;
      
      .project-item {
        display: flex;
        align-items: center;
        margin-bottom: 18px;
        padding: 10px 0;
        transition: background-color 0.2s ease;

        @media (max-width: 1199px) {
          margin-bottom: 1.8%;
          padding: 1% 0;
        }

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          background-color: rgba(20, 121, 252, 0.05);
          border-radius: 4px;
          padding-left: 8px;
          padding-right: 8px;
        }

        .project-dot {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          margin-right: 15px;
          flex-shrink: 0;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          @media (max-width: 1199px) {
            width: 1.4vw;
            height: 1.4vw;
            margin-right: 1.5%;
          }
        }

        .project-info {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          min-width: 0;

          .project-name {
            font-size: 15px;
            color: #333;
            font-weight: 500;
            flex: 1;
            margin-right: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.4;

            @media (max-width: 1199px) {
              font-size: clamp(0.8rem, 1.5vw, 0.9375rem);
            }
          }

          .project-amount {
            font-size: 16px;
            color: #1479fc;
            font-weight: 700;
            white-space: nowrap;
            line-height: 1;

            @media (max-width: 1199px) {
              font-size: clamp(0.875rem, 1.6vw, 1rem);
            }

            &::after {
              content: '万';
              font-size: 13px;
              color: #666;
              margin-left: 3px;
              font-weight: 400;

              @media (max-width: 1199px) {
                font-size: clamp(0.7rem, 1.3vw, 0.8125rem);
              }
            }
          }
        }
      }
    }
  }
}

// 悬浮框样式
.project-tooltip {
  position: fixed;
  z-index: 1000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 0;
  min-width: 12.5rem;
  max-width: 50rem;
  width: auto;
  font-size: clamp(0.625rem, 1vw, 0.75rem);

  .tooltip-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 600;
    color: #374151;
    border-radius: 0.5rem 0.5rem 0 0;
    font-size: clamp(0.75rem, 1.1vw, 0.8125rem);
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.4;
  }

  .tooltip-loading,
  .tooltip-error,
  .tooltip-empty {
    padding: 1rem;
    text-align: center;
    color: #6b7280;
  }

  .tooltip-error {
    color: #ef4444;
  }

  .tooltip-content {
    padding: 0.5rem 0;
    max-height: 18.75rem;
    overflow-y: auto;

    .tooltip-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 1rem;
      border-bottom: 1px solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f8f9fa;
      }

      .tooltip-name {
        flex: 1;
        color: #374151;
        line-height: 1.4;
        margin-right: 0.75rem;
        font-size: clamp(0.625rem, 1vw, 0.75rem);
        text-align: left;
        word-wrap: break-word;
      }

      .tooltip-value {
        color: #2563eb;
        font-weight: 600;
        font-size: clamp(0.625rem, 1vw, 0.75rem);
        min-width: 1.875rem;
        text-align: right;
      }
    }
  }
}

// 响应式图标样式
.stat-icon-img {
  width: 40px; // 默认固定大小
  height: 40px;
  object-fit: contain;
  flex-shrink: 0;

  @media (max-width: 1199px) {
    width: clamp(24px, 3vw, 48px);
    height: clamp(24px, 3vw, 48px);
  }
}

// 小卡片图标样式（现在使用字体图标，此样式已不需要）
// .stat-icon-img-small {
//   width: 24px;
//   height: 24px;
//   object-fit: contain;
//   flex-shrink: 0;

//   @media (max-width: 1199px) {
//     width: clamp(20px, 2.5vw, 28px);
//     height: clamp(20px, 2.5vw, 28px);
//   }
// }

.check-icon-img {
  width: 14px; // 默认固定大小
  height: 14px;

  @media (max-width: 1199px) {
    width: clamp(12px, 1.5vw, 16px);
    height: clamp(12px, 1.5vw, 16px);
  }
}

// 响应式媒体查询 - 针对1200px-1920px范围优化，让1200px也有1920px的效果
@media (min-width: 1200px) and (max-width: 1920px) {
  .home {
    font-size: 14px;
    /* 固定字体大小 */
  }

  .top-stats {
    .stat-card {
      .stat-icon {
        width: 50px;
        height: 50px;
        min-width: 50px;
        min-height: 50px;
        max-width: 50px;
        max-height: 50px;
      }

      .stat-content {
        .stat-title {
          font-size: 12px;
        }

        .stat-number-row {
          .stat-number {
            font-size: 24px;
          }

          .stat-unit {
            font-size: 12px;
          }
        }
      }
    }
  }

  .stat-icon-img {
    width: 40px !important;
    height: 40px !important;
  }

  .management-card {
    .management-header h3 {
      font-size: 16px;
    }

    .section-title {
      font-size: 12px;
    }

    .quality-stats .stat-group .stat-item {
      .stat-header {
        .stat-icon {
          width: 18px;
          height: 18px;
        }

        .stat-label {
          font-size: 13px;
        }
      }

      .stat-number {
        font-size: 20px;
      }

      .stat-detail span {
        font-size: 10px;
      }
    }

    // 添加安全管理数字字体大小设置
    .safety-stat-card {
      .safety-stat-main {
        .safety-stat-number {
          font-size: 22px;
          /* 1300-1920px下适中的字体大小 */
        }
      }
    }
  }

  .check-icon-img {
    width: 14px;
    height: 14px;
  }

  .chart-card {
    .chart-header h4 {
      font-size: 16px;
    }
  }

  .project-tooltip {
    font-size: 12px;

    .tooltip-header {
      font-size: 13px;
    }

    .tooltip-content .tooltip-item {
      .tooltip-name {
        font-size: 12px;
      }

      .tooltip-value {
        font-size: 12px;
      }
    }
  }

  // 固定间距和布局
  .top-stats {
    gap: 20px;
    /* 固定间距 */

    .stat-card {
      padding: 20px 15px;
      /* 固定内边距 */
      min-height: 120px;
    }
  }

  .management-row {
    margin-bottom: 20px;

    .management-card {
      padding: 15px;
      /* 与默认样式保持一致 */
      height: 370px;
      /* 确保高度一致 */

      .management-header {
        margin-bottom: 10px;
        /* 与默认样式保持一致 */
      }

      .section-header {
        margin-bottom: 12px;
      }

      .quality-stats {
        margin-bottom: 15px;

        .stat-group {
          gap: 20px;
          /* 固定间距 */
        }
      }

      .safety-sections-container {
        gap: 15px;
        /* 与默认样式保持一致 */
        /* 固定间距 */
      }

      .safety-section-independent {
        padding: 10px 15px 12px 15px;
        /* 与默认样式保持一致 */
        /* 固定内边距 */

        .safety-stats-row {
          gap: 8px;
          /* 与默认样式保持一致 */
          /* 固定间距 */
          margin-top: 10px;
          /* 与默认样式保持一致 */

          .safety-stat-card {
            padding: 10px 12px;
            /* 与默认样式保持一致 */
            /* 固定内边距 */
          }
        }
      }
    }
  }

  .charts-row {
    .chart-card {
      padding: 20px;
      height: 340px;
      /* 确保高度一致 */

      .chart-header {
        margin-bottom: 15px;
      }

      .chart-content {
        height: calc(100% - 50px);
        /* 固定计算高度 */
      }
    }
  }

  // 确保栅格系统间距一致
  .el-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 统一进度条样式
  .project-progress {
    .progress-bar {
      height: 12px;
      /* 固定高度 */
    }
  }
}

@media (max-width: 768px) {
  .home {
    padding: 1%;
    font-size: clamp(10px, 2vw, 14px);
  }

  .top-stats {
    flex-direction: column;
    gap: 1%;

    .stat-card {
      min-height: 140px;
      min-width: 100%;

      .stat-header h3 {
        font-size: clamp(1rem, 3vw, 1.25rem) !important;
        margin-bottom: 12px;
      }

      .stat-content-dual {
        flex-direction: column;
        gap: 16px;

        .stat-item {
          .stat-title {
            font-size: clamp(0.75rem, 2.5vw, 1rem) !important;
          }

          .stat-number-row .stat-number {
            font-size: clamp(1.25rem, 4vw, 2rem) !important;
          }

          .stat-number-row .stat-unit {
            font-size: clamp(0.875rem, 2.5vw, 1.125rem) !important;
          }
        }
      }
    }
  }

  .second-stats {
    flex-direction: column;
    gap: 1%;

    .stat-card-small {
      min-width: 100%;
      min-height: 70px;

      .stat-content-small {
        .stat-title-small {
          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;
        }

        .stat-number-row-small .stat-number-small {
          font-size: clamp(1rem, 3.5vw, 1.5rem) !important;
        }

        .stat-number-row-small .stat-unit-small {
          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;
        }
      }
    }
  }

  .stat-icon-img {
    width: 40px !important;
    height: 40px !important;
  }

  .quality-stats .stat-group .stat-item .stat-number {
    font-size: clamp(0.75rem, 2vw, 1rem) !important;
  }

  .safety-stat-main .safety-stat-number {
    font-size: clamp(0.75rem, 2vw, 1.125rem) !important;
    /* 减小字体：12px-18px */
  }

  .management-row {
    .el-col {
      margin-bottom: 1%;
    }
  }

  .safety-sections-container {
    flex-direction: column;
    gap: 1%;
  }

  .management-card {
    height: 350px;
  }

  .chart-card {
    height: 300px;
  }

  .project-tooltip {
    max-width: 95vw;
    min-width: 85vw;
    width: auto;
  }
}

@media (max-width: 480px) {
  .home {
    font-size: clamp(8px, 3vw, 12px);
  }

  .top-stats {
    .stat-card {
      min-height: 120px;
      padding: 3%;

      .stat-header h3 {
        font-size: clamp(0.875rem, 4vw, 1.125rem) !important;
        margin-bottom: 10px;
      }

      .stat-content-dual {
        flex-direction: column;
        gap: 12px;

        .stat-item {
          .stat-title {
            font-size: clamp(0.625rem, 3vw, 0.875rem) !important;
          }

          .stat-number-row .stat-number {
            font-size: clamp(1rem, 5vw, 1.75rem) !important;
          }

          .stat-number-row .stat-unit {
            font-size: clamp(0.75rem, 3vw, 1rem) !important;
          }
        }
      }
    }
  }

  .second-stats {
    .stat-card-small {
      min-height: 60px;
      padding: 2.5%;

      .stat-content-small {
        .stat-title-small {
          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;
        }

        .stat-number-row-small .stat-number-small {
          font-size: clamp(0.875rem, 4vw, 1.25rem) !important;
        }

        .stat-number-row-small .stat-unit-small {
          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;
        }
      }
    }
  }

  .stat-icon-img {
    width: 32px !important;
    height: 32px !important;
  }

  .quality-stats .stat-group .stat-item .stat-number {
    font-size: clamp(0.75rem, 3.5vw, 1rem) !important;
  }

  .safety-stat-main .safety-stat-number {
    font-size: clamp(0.625rem, 3vw, 1rem) !important;
    /* 进一步减小：10px-16px */
  }

  // 新的安全管理移动端样式
  .safety-top-stats {
    flex-direction: column;
    gap: 1%;

    .safety-stat-card-new {
      padding: 3%;

      .safety-stat-icon {
        width: 40px !important;
        height: 40px !important;

        i {
          font-size: 20px !important;
        }
      }

      .safety-stat-content-new {
        .safety-stat-label {
          font-size: clamp(0.75rem, 3vw, 1rem) !important;
        }

        .safety-stat-number-new {
          .number {
            font-size: clamp(1rem, 4vw, 1.5rem) !important;
          }

          .unit {
            font-size: clamp(0.75rem, 3vw, 1rem) !important;
          }
        }
      }
    }
  }

  .safety-bottom-stats {
    .stats-row {
      .stat-item {
        gap: 1%;

        .stat-label {
          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;
          min-width: 25px !important;
        }

        .stat-value {
          font-size: clamp(0.75rem, 3.5vw, 1rem) !important;
        }

        .stat-unit {
          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;
        }
      }
    }
  }

  .management-card {
    height: 320px;
  }

  .chart-card {
    height: 280px;
  }

  .safety-stats-row {
    flex-direction: column;
    gap: 1%;
  }

  .quality-stats .stat-group {
    flex-direction: column;
    gap: 1%;
  }
}

// 全局样式覆盖
::v-deep .el-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

::v-deep .el-card__header {
  border-bottom: 1px solid #f0f0f0;
  padding: 1rem 1.25rem;
}

::v-deep .el-card__body {
  padding: 1.25rem;
}
</style>
