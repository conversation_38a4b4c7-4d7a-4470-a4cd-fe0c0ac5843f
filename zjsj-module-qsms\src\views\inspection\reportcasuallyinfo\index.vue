<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="隐患问题" prop="dangerProblem">
        <el-input
          v-model="queryParams.dangerProblem"
          placeholder="请输入隐患问题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="语音记录" prop="voiceRecording">
        <el-input
          v-model="queryParams.voiceRecording"
          placeholder="请输入语音记录"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上报人姓名" prop="reporterName">
        <el-input
          v-model="queryParams.reporterName"
          placeholder="请输入上报人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上报人电话" prop="reporterPhone">
        <el-input
          v-model="queryParams.reporterPhone"
          placeholder="请输入上报人电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上报时间" prop="reporterDate">
        <el-date-picker
          clearable
          v-model="queryParams.reporterDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择上报时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="隐患单位" prop="dangerUnit">
        <el-input
          v-model="queryParams.dangerUnit"
          placeholder="请输入隐患单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隐患部门" prop="dangerDepartment">
        <el-input
          v-model="queryParams.dangerDepartment"
          placeholder="请输入隐患部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:reportcasuallyinfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:reportcasuallyinfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:reportcasuallyinfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:reportcasuallyinfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="reportcasuallyinfoList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 300px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <!-- <el-table-column label="隐患照片" align="center" prop="dangerPhotos" /> -->
      <el-table-column label="隐患类别" align="center" prop="dangerType" />
      <el-table-column label="隐患问题" align="center" prop="dangerProblem" />
      <el-table-column label="问题描述" align="center" prop="problemRemark" />
      <el-table-column label="语音记录" align="center" prop="voiceRecording" />
      <el-table-column label="隐患位置" align="center" prop="dangerAddress" />
      <el-table-column label="上报人姓名" align="center" prop="reporterName" />
      <el-table-column label="上报人电话" align="center" prop="reporterPhone" />
      <el-table-column
        label="上报时间"
        align="center"
        prop="reporterDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reporterDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="隐患单位" align="center" prop="dangerUnit" />
      <el-table-column
        label="隐患部门"
        align="center"
        prop="dangerDepartment"
      />
      <!-- ：0-未审核 1-审核通过 2-审核不通过 -->
      <el-table-column label="隐患状态" align="center" prop="dangerStatus" />
      <el-table-column label="审核意见" align="center" prop="reviewComments" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:reportcasuallyinfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:reportcasuallyinfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改随手报对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="隐患照片" prop="dangerPhotos">
          <!-- <el-input
            v-model="form.dangerPhotos"
            type="textarea"
            placeholder="请输入内容"
          /> -->
          <image-upload v-model="form.dangerPhotos" />
        </el-form-item>
        <el-form-item label="隐患问题" prop="dangerProblem">
          <el-input v-model="form.dangerProblem" placeholder="请输入隐患问题" />
        </el-form-item>
        <el-form-item label="问题描述" prop="problemRemark">
          <el-input
            v-model="form.problemRemark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="语音记录" prop="voiceRecording">
          <el-input
            v-model="form.voiceRecording"
            placeholder="请输入语音记录"
          />
        </el-form-item>
        <el-form-item label="隐患位置" prop="dangerAddress">
          <el-input
            v-model="form.dangerAddress"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="上报人姓名" prop="reporterName">
          <el-input
            v-model="form.reporterName"
            placeholder="请输入上报人姓名"
          />
        </el-form-item>
        <el-form-item label="上报人电话" prop="reporterPhone">
          <el-input
            v-model="form.reporterPhone"
            placeholder="请输入上报人电话"
          />
        </el-form-item>
        <el-form-item label="上报时间" prop="reporterDate">
          <el-date-picker
            clearable
            v-model="form.reporterDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择上报时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="隐患单位" prop="dangerUnit">
          <el-input v-model="form.dangerUnit" placeholder="请输入隐患单位" />
        </el-form-item>
        <el-form-item label="隐患部门" prop="dangerDepartment">
          <el-input
            v-model="form.dangerDepartment"
            placeholder="请输入隐患部门"
          />
        </el-form-item>
        <el-form-item label="审核意见" prop="reviewComments">
          <el-input
            v-model="form.reviewComments"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listReportcasuallyinfo,
  getReportcasuallyinfo,
  delReportcasuallyinfo,
  addReportcasuallyinfo,
  updateReportcasuallyinfo,
} from "@/api/inspection/reportcasuallyinfo";

export default {
  name: "Reportcasuallyinfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 随手报表格数据
      reportcasuallyinfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dangerPhotos: null,
        dangerType: null,
        dangerProblem: null,
        problemRemark: null,
        voiceRecording: null,
        dangerAddress: null,
        reporterName: null,
        reporterPhone: null,
        reporterDate: null,
        dangerUnit: null,
        dangerDepartment: null,
        dangerStatus: null,
        reviewComments: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询随手报列表 */
    getList() {
      this.loading = true;
      listReportcasuallyinfo(this.queryParams).then((response) => {
        this.reportcasuallyinfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        dangerPhotos: null,
        dangerType: null,
        dangerProblem: null,
        problemRemark: null,
        voiceRecording: null,
        dangerAddress: null,
        reporterName: null,
        reporterPhone: null,
        reporterDate: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        dangerUnit: null,
        dangerDepartment: null,
        dangerStatus: null,
        reviewComments: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加随手报";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getReportcasuallyinfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改随手报";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateReportcasuallyinfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReportcasuallyinfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除随手报编号为"' + ids + '"的数据项？')
        .then(function () {
          return delReportcasuallyinfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/reportcasuallyinfo/export",
        {
          ...this.queryParams,
        },
        `reportcasuallyinfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
