<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="计划名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查类型" prop="checkType">
        <el-select
          v-model="queryParams.checkType"
          placeholder="请选择检查类型"
          clearable
        >
          <el-option
            v-for="item in checkTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="planList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 250px)"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        prop="planName"
        label="计划名称"
        :show-overflow-tooltip="true"
        width="180"
      />
      <el-table-column
        prop="checkType"
        label="检查类型"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <dict-tag :options="checkTypeOptions" :value="scope.row.checkType" />
        </template>
      </el-table-column>
      <el-table-column
        prop="checkTime"
        label="检查时间"
        align="center"
        width="220"
      />
      <el-table-column
        prop="frequency"
        label="频率"
        align="center"
        width="100"
      />
      <el-table-column
        prop="participants"
        label="参与人"
        :show-overflow-tooltip="true"
        width="120"
      />
      <el-table-column prop="status" label="状态" align="center" width="100">
        <template slot-scope="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-if="scope.row.status === '进行中'"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleExecute(scope.row)"
            >执行</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改检查计划对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="计划名称" prop="planName">
              <el-input
                v-model="form.planName"
                placeholder="请输入计划名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查类型" prop="checkType">
              <el-select
                v-model="form.checkType"
                placeholder="请选择检查类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in checkTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查时间" prop="checkTime">
              <el-date-picker
                v-model="form.checkTime"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="频率" prop="frequency">
              <el-select
                v-model="form.frequency"
                placeholder="请选择频率"
                style="width: 100%"
              >
                <el-option label="每日一次" value="每日一次" />
                <el-option label="每周一次" value="每周一次" />
                <el-option label="每月一次" value="每月一次" />
                <el-option label="每季度一次" value="每季度一次" />
                <el-option label="每半年一次" value="每半年一次" />
                <el-option label="每年一次" value="每年一次" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查范围" prop="checkScope">
              <el-input
                v-model="form.checkScope"
                type="textarea"
                placeholder="请输入检查范围"
                :rows="3"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查内容" prop="checkContent">
              <el-input
                v-model="form.checkContent"
                type="textarea"
                placeholder="请输入检查内容，如：安全用电、设备管理等"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="执行人员" prop="participants">
              <el-select
                v-model="form.participants"
                multiple
                placeholder="支持执行人员多选"
                style="width: 100%"
              >
                <el-option label="张三" value="张三" />
                <el-option label="李四" value="李四" />
                <el-option label="王五" value="王五" />
                <el-option label="赵六" value="赵六" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提醒设置" prop="reminderSettings">
              <el-select
                v-model="form.reminderSettings"
                multiple
                placeholder="请选择提醒方式"
                style="width: 100%"
              >
                <el-option
                  label="规划前2天自动提醒"
                  value="规划前2天自动提醒"
                />
                <el-option label="短信提醒" value="短信提醒" />
                <el-option label="邮件提醒" value="邮件提醒" />
                <el-option label="微信提醒" value="微信提醒" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="附件上传">
              <file-upload
                v-model="form.attachments"
                :limit="5"
                :file-type="['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'png']"
                :file-size="10"
              />
              <div class="el-upload__tip">
                可上传相关检查文件或图片，单文件不超过10MB，最多5个文件
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="保存后提交文档" prop="submitAfterSave">
              <el-radio-group v-model="form.submitAfterSave">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="取消选择其他源" prop="cancelOtherSources">
              <el-button
                type="primary"
                size="mini"
                @click="handleCancelOtherSources"
                >取消选择其他源文档</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看检查计划详情对话框 -->
    <el-dialog
      title="检查计划详情"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="计划名称">{{
          viewForm.planName
        }}</el-descriptions-item>
        <el-descriptions-item label="检查类型">
          <dict-tag :options="checkTypeOptions" :value="viewForm.checkType" />
        </el-descriptions-item>
        <el-descriptions-item label="检查时间">{{
          viewForm.checkTime
        }}</el-descriptions-item>
        <el-descriptions-item label="频率">{{
          viewForm.frequency
        }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="statusOptions" :value="viewForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="参与人">{{
          viewForm.participants
        }}</el-descriptions-item>
        <el-descriptions-item label="检查范围" :span="2">{{
          viewForm.checkScope
        }}</el-descriptions-item>
        <el-descriptions-item label="检查内容" :span="2">{{
          viewForm.checkContent
        }}</el-descriptions-item>
        <el-descriptions-item label="提醒设置" :span="2">
          <el-tag
            v-for="reminder in viewForm.reminderSettings"
            :key="reminder"
            style="margin-right: 5px"
            >{{ reminder }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{
          parseTime(viewForm.createTime)
        }}</el-descriptions-item>
      </el-descriptions>

      <div
        v-if="viewForm.attachments && viewForm.attachments.length > 0"
        style="margin-top: 20px"
      >
        <h4>相关附件：</h4>
        <el-tag
          v-for="file in viewForm.attachments"
          :key="file.name"
          style="margin-right: 10px; margin-bottom: 5px; cursor: pointer"
          @click="downloadFile(file)"
        >
          <i class="el-icon-paperclip" /> {{ file.name }}
        </el-tag>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "CheckPlanManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查计划表格数据
      planList: [
        {
          id: 1,
          planName: "节前专项检查计划",
          checkType: "专项检查",
          checkTime: "2025-05-12 至 2025-06-12",
          frequency: "每日一次",
          participants: "张三、李四等",
          status: "进行中",
          checkScope: "全部项目部",
          checkContent: "安全用电、设备管理等",
          reminderSettings: ["规划前2天自动提醒", "短信提醒"],
          attachments: [],
          submitAfterSave: true,
          createTime: "2025-05-12 12:00:00",
        },
        {
          id: 2,
          planName: "节前专项检查计划",
          checkType: "专项检查",
          checkTime: "2025-05-12 至 2025-06-12",
          frequency: "每日一次",
          participants: "张三、李四等",
          status: "进行中",
          checkScope: "新建项目工地",
          checkContent: "安全用电、设备管理等",
          reminderSettings: ["邮件提醒"],
          attachments: [],
          submitAfterSave: false,
          createTime: "2025-05-12 12:00:00",
        },
        {
          id: 3,
          planName: "节前专项检查计划",
          checkType: "专项检查",
          checkTime: "2025-05-12 至 2025-06-12",
          frequency: "每日一次",
          participants: "张三、李四等",
          status: "进行中",
          checkScope: "办公区域",
          checkContent: "安全用电、设备管理等",
          reminderSettings: ["微信提醒"],
          attachments: [],
          submitAfterSave: true,
          createTime: "2025-05-12 12:00:00",
        },
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹窗
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planName: undefined,
        checkType: undefined,
        status: undefined,
        dateRange: [],
      },
      // 表单参数
      form: {},
      // 查看表单数据
      viewForm: {},
      // 检查类型选项
      checkTypeOptions: [
        { label: "综合检查", value: "综合检查" },
        { label: "专项检查", value: "专项检查" },
        { label: "节假日检查", value: "节假日检查" },
        { label: "季度检查", value: "季度检查" },
      ],
      // 状态选项
      statusOptions: [
        { label: "待执行", value: "待执行" },
        { label: "进行中", value: "进行中" },
        { label: "已完成", value: "已完成" },
        { label: "已暂停", value: "已暂停" },
      ],
      // 表单校验
      rules: {
        planName: [
          { required: true, message: "计划名称不能为空", trigger: "blur" },
        ],
        checkType: [
          { required: true, message: "检查类型不能为空", trigger: "change" },
        ],
        checkTime: [
          { required: true, message: "检查时间不能为空", trigger: "change" },
        ],
        frequency: [
          { required: true, message: "频率不能为空", trigger: "change" },
        ],
        checkScope: [
          { required: true, message: "检查范围不能为空", trigger: "blur" },
        ],
        checkContent: [
          { required: true, message: "检查内容不能为空", trigger: "blur" },
        ],
        participants: [
          { required: true, message: "执行人员不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 时间格式化
    parseTime,
    /** 查询检查计划列表 */
    getList() {
      this.loading = true;
      // 模拟数据加载
      setTimeout(() => {
        this.total = this.planList.length;
        this.loading = false;
      }, 500);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        planName: undefined,
        checkType: undefined,
        checkTime: undefined,
        frequency: undefined,
        checkScope: undefined,
        checkContent: undefined,
        participants: [],
        reminderSettings: [],
        attachments: [],
        submitAfterSave: true,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const selectedRow =
        row || this.planList.find((item) => this.ids.includes(item.id));
      if (selectedRow) {
        this.form = { ...selectedRow };
      }
      this.open = true;
      this.title = "修改检查计划";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = { ...row };
      this.viewOpen = true;
    },
    /** 执行按钮操作 */
    handleExecute(row) {
      this.$modal.confirm("是否确认执行该检查计划?").then(() => {
        this.$modal.msgSuccess("计划已开始执行");
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            this.$modal.msgSuccess("修改成功");
          } else {
            this.$modal.msgSuccess("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      const names = row
        ? [row.planName]
        : this.planList
            .filter((item) => ids.includes(item.id))
            .map((item) => item.planName);
      this.$modal
        .confirm('是否确认删除检查计划"' + names.join("、") + '"?')
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.getList();
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.msgInfo("导出功能待实现");
    },
    /** 取消选择其他源 */
    handleCancelOtherSources() {
      this.$modal.msgInfo("取消选择其他源文档功能待实现");
    },
    /** 下载文件 */
    downloadFile(file) {
      this.$modal.msgInfo("下载文件：" + file.name);
    },
  },
};
</script>

<style scoped>
.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}
</style>
