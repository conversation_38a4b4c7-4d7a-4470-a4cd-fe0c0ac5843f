<template>
  <div class="tree">
    <div class="title mb-2">组织架构</div>
    <div class="tree-scroll-container">
      <el-tree
        :data="data"
        :props="defaultProps"
        highlight-current
        @node-click="handleNodeClick"
        v-loading="loading"
      >
        <template #default="{ node, data }">
          <el-tooltip effect="dark" :content="data.label" placement="top">
            <span
              :ref="(el) => setLabelRef(el, node)"
              class="el-tree-node__label"
            >
              {{ node.label }}
            </span>
          </el-tooltip>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: "OrgTree",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      labelRefs: new Map(),
      defaultProps: {
        children: "children",
        label: "label",
        id: "id",
        isLeaf: "isLeaf",
      },
      loading: false,
      queryParams: {},
    };
  },
  created() {},
  methods: {
    handleNodeClick(nodeData, node) {
      // console.log("nodeData", nodeData);
      this.$emit("nodeClick", nodeData);
    },

    setLabelRef(el, node) {
      if (el) {
        this.labelRefs.set(node.id || node.label, el);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.tree {
  height: 80vh;
  overflow: auto;
}
.title {
  font-size: 16px;
}

.tree-scroll-container {
  overflow-x: auto;
  overflow-y: hidden;
  min-width: 100%;
}
::v-deep .el-tree-node__label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 隐藏滚动条 - Chrome, Safari 和 Opera */
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
}
/* 隐藏滚动条 - Chrome, Safari 和 Opera */
::v-deep .el-tree-node__label::-webkit-scrollbar {
  display: none;
}

::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}
</style>
