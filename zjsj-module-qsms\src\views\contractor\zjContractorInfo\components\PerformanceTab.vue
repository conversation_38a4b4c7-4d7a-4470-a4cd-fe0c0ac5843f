<template>
  <div class="performance-tab">
    <!-- 操作按钮区域 -->
    <div class="toolbar" style="margin-bottom: 20px;">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
    </div>
    
    <el-table v-loading="loading" :data="performanceList" border style="width: 100%">
      <el-table-column prop="certificateName" label="证书名称" align="left" width="200" />
      <el-table-column label="业绩周期" align="center" width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.cycleBeginTime && scope.row.cycleEndTime">
            {{ parseTime(scope.row.cycleBeginTime, '{y}-{m}-{d}') }} ~ {{ parseTime(scope.row.cycleEndTime, '{y}-{m}-{d}') }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="performanceOverview" label="业绩概述" align="left" />
      <el-table-column label="资质文件" align="center" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.attachmentUrl">
            <el-button type="text" size="small" @click="viewAttachments(scope.row.attachmentUrl)">查看文件</el-button>
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增业绩证书弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="证书名称" prop="certificateName">
          <el-input v-model="form.certificateName" placeholder="请输入证书名称" />
        </el-form-item>
        
        <el-form-item label="业绩周期" prop="performancePeriod">
          <el-date-picker
            v-model="form.performancePeriod"
            type="daterange"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="业绩概述" prop="performanceOverview">
          <el-input v-model="form.performanceOverview" placeholder="请输入业绩概述" />
        </el-form-item>

        <el-form-item label="附件" prop="attachmentUrl">
          <image-upload v-model="form.attachmentUrl"/>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listZjContractorCertificate, addZjContractorCertificate } from "@/api/contractor/zjContractorCertificate";

export default {
  name: "PerformanceTab",
  props: {
    contractorId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      performanceList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorId: null
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        certificateName: [
          { required: true, message: "证书名称不能为空", trigger: "blur" }
        ],
        performancePeriod: [
          { required: true, message: "业绩周期不能为空", trigger: "change" }
        ],
        performanceOverview: [
          { required: true, message: "业绩概述不能为空", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    contractorId: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.contractorId = newVal;
          this.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询业绩信息列表 */
    getList() {
      if (!this.contractorId) return;
      this.loading = true;
      listZjContractorCertificate(this.queryParams).then(response => {
        this.performanceList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.performanceList = [];
        this.total = 0;
      });
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增业绩证书";
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        contractorId: this.contractorId,
        certificateName: null,
        performancePeriod: null,
        cycleBeginTime: null,
        cycleEndTime: null,
        performanceOverview: null,
        attachmentUrl: null
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理日期范围
          if (this.form.performancePeriod && this.form.performancePeriod.length === 2) {
            this.form.cycleBeginTime = this.form.performancePeriod[0];
            this.form.cycleEndTime = this.form.performancePeriod[1];
          }
          
          addZjContractorCertificate(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError(error.msg || "新增失败");
          });
        }
      });
    },

    /** 查看附件 */
    viewAttachments(attachmentUrl) {
      if (!attachmentUrl) {
        this.$modal.msgInfo("暂无附件");
        return;
      }
      
      // 直接打开附件URL
      window.open(attachmentUrl, '_blank');
    },


  }
};
</script>

<style scoped>
.performance-tab {
  padding: 20px;
}
</style>
