<template>
    <div class="audio-player">
        <audio 
            ref="audioPlayer" 
            :src="src"
            @timeupdate="onTimeUpdate"
            @loadedmetadata="onLoadedMetadata"
            @ended="onEnded"
            @error="onError"
        ></audio>
        
        <div class="player-controls" v-if="!error">
            <!-- 播放/暂停按钮 -->
            <el-button 
                :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'" 
                circle
                size="mini"
                @click="togglePlay"
            ></el-button>

            <!-- 进度条 -->
            <!-- <div class="progress-bar">
                <el-slider
                    v-model="currentTime"
                    :max="duration"
                    :format-tooltip="formatTime"
                    @change="onProgressChange"
                ></el-slider>
                <div class="time-display">
                    <span>{{ formatTime(currentTime) }}</span>
                    <span>/</span>
                    <span>{{ formatTime(duration) }}</span>
                </div>
            </div> -->

            <!-- 音量控制 -->
            <div class="volume-control">
                <el-button 
                    :icon="volume === 0 ? 'el-icon-turn-off-microphone' : 'el-icon-microphone'" 
                    circle
                    size="mini"
                    @click="toggleMute"
                ></el-button>
                <el-slider
                    v-model="volume"
                    :max="100"
                    :min="0"
                    class="volume-slider"
                ></el-slider>
            </div>
        </div>
        
        <!-- 错误提示 -->
        <div v-else class="error-message">
            <i class="el-icon-warning-outline"></i>
            <span>音频加载失败</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'AudioPlayer',
    props: {
        src: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            isPlaying: false,
            currentTime: 0,
            duration: 0,
            volume: 100,
            previousVolume: 100,
            error: false
        }
    },
    methods: {
        // 播放/暂停切换
        async togglePlay() {
            try {
                if (this.isPlaying) {
                    await this.$refs.audioPlayer.pause()
                } else {
                    await this.$refs.audioPlayer.play()
                }
                this.isPlaying = !this.isPlaying
                this.error = false
            } catch (err) {
                console.error('Audio playback error:', err)
                this.error = true
                this.$message.error('音频播放失败')
            }
        },

        // 静音切换
        toggleMute() {
            if (this.volume > 0) {
                this.previousVolume = this.volume
                this.volume = 0
            } else {
                this.volume = this.previousVolume
            }
            this.$refs.audioPlayer.volume = this.volume / 100
        },

        // 时间格式化
        formatTime(seconds) {
            const mins = Math.floor(seconds / 60)
            const secs = Math.floor(seconds % 60)
            return `${mins}:${secs.toString().padStart(2, '0')}`
        },

        // 进度条更新
        onTimeUpdate() {
            this.currentTime = this.$refs.audioPlayer.currentTime
        },

        // 音频加载完成
        onLoadedMetadata() {
            this.duration = this.$refs.audioPlayer.duration
        },

        // 音频播放结束
        onEnded() {
            this.isPlaying = false
            this.currentTime = 0
        },

        // 进度条改变
        onProgressChange(value) {
            this.$refs.audioPlayer.currentTime = value
        },

        // 错误处理
        onError(e) {
            console.error('Audio error:', e)
            this.error = true
            this.isPlaying = false
            this.$message.error('音频加载失败')
        }
    },
    watch: {
        // 监听音量变化
        volume(newValue) {
            this.$refs.audioPlayer.volume = newValue / 100
        },

        // 监听源文件变化
        src: {
            immediate: true,
            handler(newSrc) {
                if (!newSrc) {
                    this.error = true
                    return
                }
                this.error = false
                this.isPlaying = false
                this.currentTime = 0
                this.$nextTick(() => {
                    if (this.$refs.audioPlayer) {
                        this.$refs.audioPlayer.load()
                    }
                })
            }
        }
    },
    beforeDestroy() {
        // 组件销毁前停止播放
        if (this.$refs.audioPlayer) {
            this.$refs.audioPlayer.pause()
        }
    }
}
</script>

<style lang="scss" scoped>
.audio-player {
    width: 200px;
    padding: 12px;
    border-radius: 4px;
    background: #f5f7fa;

    .player-controls {
        display: flex;
        align-items: center;
        gap: 12px;

        .progress-bar {
            flex: 1;
            margin: 0 12px;

            .time-display {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #909399;
                margin-top: 4px;
            }
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 120px;

            .volume-slider {
                flex: 1;
            }
        }
    }

    :deep(.el-slider__runway) {
        margin: 8px 0;
    }

    :deep(.el-slider__bar) {
        background-color: #409EFF;
    }

    :deep(.el-slider__button) {
        border-color: #409EFF;
    }

    .error-message {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #F56C6C;
        font-size: 14px;
        padding: 8px;
        
        i {
            margin-right: 8px;
            font-size: 16px;
        }
    }
}
</style> 