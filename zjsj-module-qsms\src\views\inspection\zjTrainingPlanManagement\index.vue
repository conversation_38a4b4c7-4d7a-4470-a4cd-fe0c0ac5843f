<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="培训标题" prop="trainingTitle">
        <el-input
          v-model="queryParams.trainingTitle"
          placeholder="请输入培训标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训时间" prop="trainingTime">
        <el-date-picker
          clearable
          v-model="queryParams.trainingTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择培训时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="培训地点" prop="trainingAddress">
        <el-input
          v-model="queryParams.trainingAddress"
          placeholder="请输入培训地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布人" prop="publisher">
        <el-input
          v-model="queryParams.publisher"
          placeholder="请输入发布人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参训人数" prop="trainees">
        <el-input
          v-model="queryParams.trainees"
          placeholder="请输入参训人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="培训形式" prop="trainingFormat">
        <el-input
          v-model="queryParams.trainingFormat"
          placeholder="请输入培训形式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjTrainingPlanManagement:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjTrainingPlanManagement:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjTrainingPlanManagement:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjTrainingPlanManagement:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjTrainingPlanManagementList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 300px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="培训标题" align="center" prop="trainingTitle" />
      <el-table-column
        label="培训时间"
        align="center"
        prop="trainingTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.trainingTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训地点" align="center" prop="trainingAddress" />
      <el-table-column label="培训类型" align="center" prop="trainingType">
        <template slot-scope="scope">
          <span>{{ trainingTypeMap[scope.row.trainingType] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发布人" align="center" prop="publisher" />
      <el-table-column label="参训人数" align="center" prop="trainees" />
      <el-table-column label="状态" align="center" prop="trainingStatus">
        <template slot-scope="scope">
          <span>{{ trainingStatusMap[scope.row.trainingStatus] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="培训形式" align="center" prop="trainingFormat" />
      <el-table-column label="附件" align="center" prop="attachmentUrl">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.attachmentUrl"
            size="mini"
            type="text"
            @click="handleAttach(scope.row.attachmentUrl)"
            >查看</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjTrainingPlanManagement:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjTrainingPlanManagement:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改培训计划管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="年度计划" prop="trainingTitle">
          <el-select
            v-model="form.trainingTitle"
            placeholder="请选择年度计划"
            style="width: 100%"
          >
            <el-option label="2023年" value="2023" />
            <el-option label="2024年" value="2024" />
          </el-select>
        </el-form-item>
        <el-form-item label="培训时间" prop="trainingTime">
          <el-date-picker
            clearable
            v-model="form.trainingTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择培训时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="培训地点" prop="trainingAddress">
          <el-input
            v-model="form.trainingAddress"
            placeholder="请输入培训地点"
          />
        </el-form-item>
        <!-- 培训类型 -->
        <!-- <el-form-item label="培训类型">
          <el-select
            v-model="form.trainingType"
            placeholder="请选择培训类型"
            style="width: 100%"
          >
            <el-option label="安全教育培训" value="1" />
            <el-option label="技能培训" value="2" />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="发布人" prop="publisher">
          <el-input v-model="form.publisher" placeholder="请输入发布人" />
        </el-form-item> -->
        <el-form-item label="参训人数" prop="trainees">
          <el-input
            v-model="form.trainees"
            type="number"
            placeholder="请输入参训人员"
          />
        </el-form-item>
        <!-- 当前状态 -->
        <!-- <el-form-item label="当前状态">
          <el-select v-model="form.trainingStatus" placeholder="请选择当前状态">
            <el-option label="已发布" value="1" />
            <el-option label="未发布" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="培训形式" prop="trainingFormat">
          <el-input
            v-model="form.trainingFormat"
            placeholder="请输入培训形式"
          />
        </el-form-item> -->
        <el-form-item label="附件" prop="attachmentUrl">
          <file-upload v-model="form.attachmentUrl"></file-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog
      v-model="attachmentDialogVisible"
      :attachment-list="attachmentList"
    ></AttachmentDialog>
  </div>
</template>

<script>
import {
  listZjTrainingPlanManagement,
  getZjTrainingPlanManagement,
  delZjTrainingPlanManagement,
  addZjTrainingPlanManagement,
  updateZjTrainingPlanManagement,
} from "@/api/inspection/zjTrainingPlanManagement";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";

export default {
  name: "ZjTrainingPlanManagement",
  components: {
    AttachmentDialog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 培训计划管理表格数据
      zjTrainingPlanManagementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        trainingTitle: null,
        trainingTime: null,
        trainingAddress: null,
        trainingType: null,
        publisher: null,
        trainees: null,
        trainingStatus: null,
        trainingFormat: null,
        attachmentUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      attachmentDialogVisible: false,
      attachmentList: [],
      trainingTypeMap: {
        1: "安全教育培训",
        2: "技能培训",
      },
      trainingStatusMap: {
        1: "已发布",
        2: "未发布",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查看附件
    handleAttach(url) {
      this.attachmentList = url.split(",");
      this.attachmentDialogVisible = true;
    },
    /** 查询培训计划管理列表 */
    getList() {
      this.loading = true;
      listZjTrainingPlanManagement(this.queryParams).then((response) => {
        this.zjTrainingPlanManagementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        trainingTitle: null,
        trainingTime: null,
        trainingAddress: null,
        trainingType: null,
        publisher: null,
        trainees: null,
        trainingStatus: null,
        trainingFormat: null,
        attachmentUrl: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加培训计划管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjTrainingPlanManagement(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改培训计划管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjTrainingPlanManagement(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjTrainingPlanManagement(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除培训计划管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjTrainingPlanManagement(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjTrainingPlanManagement/export",
        {
          ...this.queryParams,
        },
        `zjTrainingPlanManagement_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
