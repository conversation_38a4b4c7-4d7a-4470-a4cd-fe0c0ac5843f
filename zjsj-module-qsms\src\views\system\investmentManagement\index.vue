<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="责任部门" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入责任部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年度预算金额" prop="budgetAmount">
        <el-input
          v-model="queryParams.budgetAmount"
          placeholder="请输入年度预算金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实际投入金额" prop="actualAmount">
        <el-input
          v-model="queryParams.actualAmount"
          placeholder="请输入实际投入金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="剩余预算金额" prop="remainingBudget">
        <el-input
          v-model="queryParams.remainingBudget"
          placeholder="请输入剩余预算金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实际投入时间" prop="investmentTime">
        <el-date-picker
          clearable
          v-model="queryParams.investmentTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择实际投入时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:management:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:management:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:management:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:management:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="managementList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="预算年度" align="center" prop="budgetYear" />
      <el-table-column label="责任部门" align="center" prop="deptName" />
      <el-table-column label="投入类型" align="center" prop="investmentType" />
      <el-table-column
        label="年度预算金额"
        align="center"
        prop="budgetAmount"
      />
      <el-table-column
        label="实际投入金额"
        align="center"
        prop="actualAmount"
      />
      <el-table-column
        label="剩余预算金额"
        align="center"
        prop="remainingBudget"
      />
      <el-table-column label="投入说明" align="center" prop="investmentDesc" />
      <el-table-column
        label="实际投入时间"
        align="center"
        prop="investmentTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.investmentTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="凭证附件地址"
        align="center"
        prop="attachmentUrl"
      />
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:management:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:management:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全投入管理（预算及实际投入记录）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="责任部门" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入责任部门" />
        </el-form-item>
        <el-form-item label="年度预算金额" prop="budgetAmount">
          <el-input
            v-model="form.budgetAmount"
            placeholder="请输入年度预算金额"
          />
        </el-form-item>
        <el-form-item label="实际投入金额" prop="actualAmount">
          <el-input
            v-model="form.actualAmount"
            placeholder="请输入实际投入金额"
          />
        </el-form-item>
        <el-form-item label="剩余预算金额" prop="remainingBudget">
          <el-input
            v-model="form.remainingBudget"
            placeholder="请输入剩余预算金额"
          />
        </el-form-item>
        <el-form-item label="投入说明" prop="investmentDesc">
          <el-input
            v-model="form.investmentDesc"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="实际投入时间" prop="investmentTime">
          <el-date-picker
            clearable
            v-model="form.investmentTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择实际投入时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="凭证附件地址" prop="attachmentUrl">
          <el-input
            v-model="form.attachmentUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listManagement,
  getManagement,
  delManagement,
  addManagement,
  updateManagement,
} from "@/api/system/investmentManagement/index";

export default {
  name: "Management",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全投入管理（预算及实际投入记录）表格数据
      managementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        budgetYear: null,
        deptName: null,
        investmentType: null,
        budgetAmount: null,
        actualAmount: null,
        remainingBudget: null,
        investmentDesc: null,
        investmentTime: null,
        attachmentUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        budgetYear: [
          { required: true, message: "预算年度不能为空", trigger: "blur" },
        ],
        deptName: [
          { required: true, message: "责任部门不能为空", trigger: "blur" },
        ],
        investmentType: [
          {
            required: true,
            message: "投入类型：设备购置/培训/维护/检测/其他不能为空",
            trigger: "change",
          },
        ],
        budgetAmount: [
          { required: true, message: "年度预算金额不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全投入管理（预算及实际投入记录）列表 */
    getList() {
      this.loading = true;
      listManagement(this.queryParams).then((response) => {
        this.managementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        budgetYear: null,
        deptName: null,
        investmentType: null,
        budgetAmount: null,
        actualAmount: null,
        remainingBudget: null,
        investmentDesc: null,
        investmentTime: null,
        attachmentUrl: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全投入管理（预算及实际投入记录）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getManagement(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全投入管理（预算及实际投入记录）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateManagement(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addManagement(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除安全投入管理（预算及实际投入记录）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delManagement(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/management/export",
        {
          ...this.queryParams,
        },
        `management_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
