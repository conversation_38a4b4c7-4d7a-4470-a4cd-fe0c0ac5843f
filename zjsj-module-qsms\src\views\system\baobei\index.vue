<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">八项报备</div>
            <div class="top-right">
              <el-button
                type="primary"
                icon="el-icon-download"
                size="small"
                @click="exportExcel"
                >导出</el-button
              >
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="120px"
          >
            <el-form-item label="公司" prop="project">
              <el-input
                v-model="queryParams.project"
                placeholder="请输入公司"
                clearable
              />
            </el-form-item>
            <el-form-item label="报备时间" prop="date">
              <el-input
                v-model="queryParams.Reportingtime"
                placeholder="请输入报备时间"
                clearable
              />
            </el-form-item>
            <el-form-item label="作业类型" prop="types">
              <el-select
                v-model="queryParams.types"
                placeholder="请选择作业类型"
                clearable
              >
                <el-option
                  v-for="item in typeMap"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
              <!-- <el-button size="mini" @click="baobeiForm = true">八项报备</el-button> -->
            </el-form-item>
          </el-form>

          <!-- <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                            v-hasPermi="['system:reports:add']">新增</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                            v-hasPermi="['system:reports:edit']">修改</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
                            v-hasPermi="['system:reports:remove']">删除</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                            v-hasPermi="['system:reports:export']">导出</el-button>
                        </el-col>
                        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
                    </el-row> -->

          <el-table
            v-loading="loading"
            :data="reportsList"
            @selection-change="handleSelectionChange"
            height="calc(100vh - 250px)"
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="报备时间"
              align="center"
              prop="Reportingtime"
            />
            <el-table-column label="项目" align="center" prop="project" />
            <el-table-column label="作业内容" align="center" prop="content" />
            <el-table-column label="作业类型" align="center" prop="typeName" />
            <!-- <el-table-column label="报备时间" align="center" prop="reportingtime" width="180">
                <template slot-scope="scope">
                <span>{{ parseTime(scope.row.reportingtime, '{y}-{m}-{d}') }}</span>
                </template>
</el-table-column> -->
            <el-table-column
              label="操作"
              align="center"
              fixed="right"
              width="150"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click="handleCheck(scope.row, false)"
                  v-hasPermi="['system:baobei:check']"
                  >查看</el-button
                >
                <!-- <el-button size="mini" type="primary" plain @click="handleCheck(scope.row, true)"
                                    v-hasPermi="['system:baobei:edit']">编辑</el-button> -->
                <el-button
                  size="mini"
                  type="danger"
                  plain
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:baobei:delete']"
                  >删除</el-button
                >
                <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                                    v-hasPermi="['system:reports:edit']">修改</el-button>
                                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                                    v-hasPermi="['system:reports:remove']">删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      :title="isEditbaobeiFormData ? '编辑' : '查看'"
      :visible.sync="baobeiForm"
      width="80%"
    >
      <div class="baobeiContent">
        <el-row :gutter="12">
          <el-col :span="12">
            <div style="height: 75vh; overflow: auto">
              <table style="width: 100%">
                <tr>
                  <td class="td-title">作业单位</td>
                  <td colspan="4">{{ baobeiFormData.project }}</td>
                  <td class="td-title">作业时间</td>
                  <td colspan="4">{{ baobeiFormData.date }}</td>
                </tr>
                <tr>
                  <td class="td-title">作业地点</td>
                  <td colspan="9">{{ baobeiFormData.location }}</td>
                </tr>
                <tr>
                  <td class="td-title">作业内容</td>
                  <td colspan="9">
                    <el-input
                      type="textarea"
                      :rows="2"
                      placeholder="请输入内容"
                      v-model="baobeiFormData.content"
                      resize="none"
                      :disabled="!isEditbaobeiFormData"
                    >
                    </el-input>
                  </td>
                </tr>
                <tr>
                  <td class="td-title">作业类型</td>
                  <td colspan="9">
                    <el-checkbox-group
                      v-model="baobeiFormData.types"
                      :disabled="!isEditbaobeiFormData"
                    >
                      <el-raw>
                        <el-col :span="6">
                          <el-checkbox label="1">检维修作业</el-checkbox>
                          <!-- <el-radio v-model="baobeiFormData.types" label="1">检维修作业</el-radio> -->
                        </el-col>
                        <el-col :span="6">
                          <el-checkbox label="2">高处作业</el-checkbox>
                        </el-col>
                        <el-col :span="6">
                          <el-checkbox label="3">临时起重作业</el-checkbox>
                        </el-col>
                        <el-col :span="6">
                          <el-checkbox label="4">拆除作业</el-checkbox>
                        </el-col>
                      </el-raw>
                      <el-raw>
                        <el-col :span="6">
                          <el-checkbox label="5">受限空间作业</el-checkbox>
                        </el-col>
                        <el-col :span="6">
                          <el-checkbox label="6">动土作业</el-checkbox>
                        </el-col>
                        <el-col :span="6">
                          <el-checkbox label="7">临时用电作业</el-checkbox>
                        </el-col>
                        <el-col :span="6">
                          <el-checkbox label="8">临时动火作业</el-checkbox>
                        </el-col>
                      </el-raw>
                    </el-checkbox-group>
                  </td>
                </tr>
                <tr>
                  <td class="td-title">作业人员</td>
                  <td colspan="4">
                    <el-input
                      v-model="baobeiFormData.operatives"
                      placeholder="请输入内容"
                      :disabled="!isEditbaobeiFormData"
                    ></el-input>
                  </td>
                  <td class="td-title">作业监护人</td>
                  <td colspan="4">
                    <el-input
                      v-model="baobeiFormData.supervisor"
                      placeholder="请输入内容"
                      :disabled="!isEditbaobeiFormData"
                    ></el-input>
                  </td>
                </tr>
                <tr>
                  <td class="td-title">具体时间</td>
                  <td colspan="9">
                    <el-input
                      v-model="baobeiFormData.specific"
                      placeholder="请输入内容"
                      :disabled="!isEditbaobeiFormData"
                    ></el-input>
                  </td>
                </tr>
                <tr>
                  <td class="td-title">主要使用器具</td>
                  <td colspan="9">
                    <el-input
                      type="textarea"
                      :rows="2"
                      placeholder="请输入内容"
                      v-model="baobeiFormData.utensil"
                      resize="none"
                      :disabled="!isEditbaobeiFormData"
                    >
                    </el-input>
                  </td>
                </tr>
                <tr>
                  <td class="td-title">安全措施</td>
                  <td colspan="9">
                    <el-input
                      type="textarea"
                      :rows="2"
                      placeholder="请输入内容"
                      v-model="baobeiFormData.measures"
                      resize="none"
                      :disabled="!isEditbaobeiFormData"
                    >
                    </el-input>
                  </td>
                </tr>
                <tr>
                  <td class="td-title">报备时间</td>
                  <td colspan="4">{{ baobeiFormData.reportingtime }}</td>
                  <td class="td-title">报备人</td>
                  <td colspan="4">{{ baobeiFormData.reporter }}</td>
                </tr>
              </table>
              <div class="tip">注：仅该项目人员才可修改保存</div>
              <div class="file-content">
                <el-row v-if="isEditbaobeiFormData">
                  <el-col :span="12">
                    <el-input
                      v-model="fileParams.name"
                      prefix-icon="el-icon-search"
                      placeholder="请输入关键字"
                    ></el-input>
                  </el-col>
                  <el-col :span="12" style="display: flex">
                    <el-button
                      style="margin-left: 20px"
                      type="primary"
                      icon="el-icon-search"
                      >搜索</el-button
                    >
                    <!-- <el-button type="primary" icon="el-icon-search"
                                            @click="shaiFormShow = true">高级</el-button> -->
                    <el-upload
                      style="margin-left: 10px"
                      action
                      :auto-upload="false"
                      :show-file-list="false"
                      :on-change="uploadFile"
                      accept=".xlsx, .xls"
                    >
                      <el-button type="primary" icon="el-icon-upload2"
                        >上传文件</el-button
                      >
                    </el-upload>
                  </el-col>
                </el-row>
                <el-row style="margin-top: 10px">
                  <el-table
                    :data="baobeiFormData.reportFileInfos"
                    style="width: 100%"
                  >
                    <el-table-column prop="fileName" label="文件名">
                    </el-table-column>
                    <el-table-column prop="fileSize" label="文件大小">
                    </el-table-column>
                    <el-table-column prop="createTime" label="上传日期">
                    </el-table-column>
                    <el-table-column prop="fileExtname" label="类型">
                    </el-table-column>
                    <!-- <el-table-column prop="isTop" label="置顶">
                                        </el-table-column> -->
                    <el-table-column label="操作" fixed="right" width="150">
                      <template slot-scope="scope">
                        <el-button type="text" @click="readFile(scope.row)"
                          >查看</el-button
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </el-row>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div style="height: 75vh; overflow: auto">
              <table style="width: 100%">
                <template v-if="ifHaveType(1)">
                  <tr>
                    <td class="td-title">检修部位</td>
                    <td colspan="6">
                      {{ baobeiFormData.ajTaskDetail.jwxMaintenanceLocation }}
                    </td>
                    <td class="td-title">检修负责人</td>
                    <td colspan="2">
                      {{ baobeiFormData.ajTaskDetail.responsiblePerson }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">检修内容</td>
                    <td colspan="9">
                      {{ baobeiFormData.ajTaskDetail.jwxMaintenanceContent }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">检修人员</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.jwxMaintenancePersonnel }}
                    </td>
                    <td class="td-title">检修监护人</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.jwxMaintenanceGuardian }}
                    </td>
                  </tr>
                </template>

                <template v-if="ifHaveType(2)">
                  <tr>
                    <td class="td-title">作业高度</td>
                    <td colspan="9">
                      {{ baobeiFormData.ajTaskDetail.gcAssignmentHeight }}
                    </td>
                  </tr>
                </template>

                <template v-if="ifHaveType(3)">
                  <tr>
                    <td class="td-title">起重质量</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.qzHoistingMachinery }}
                    </td>
                    <td class="td-title">起重机械</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.ccDemolishedName }}
                    </td>
                  </tr>
                </template>

                <template v-if="ifHaveType(4)">
                  <tr>
                    <td class="td-title">拆除物名称</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.ccDemolishedName }}
                    </td>
                    <td class="td-title">拆除物面积、高度</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.ccDemolishedArea }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">负责人</td>
                    <td colspan="9">
                      {{ baobeiFormData.ajTaskDetail.director }}
                    </td>
                  </tr>
                </template>

                <template v-if="ifHaveType(5)">
                  <tr>
                    <td class="td-title">受限空间名称</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.sxkjConfinedSpace }}
                    </td>
                    <td class="td-title">检测人</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.sxkjInspector }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">受限空间内介质</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.sxkjConfinedMedium }}
                    </td>
                    <td class="td-title">氧含量、有毒气体 检测结果</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.sxkjDetectionResult }}
                    </td>
                  </tr>
                </template>

                <template v-if="ifHaveType(6)">
                  <tr>
                    <td class="td-title">开挖深度</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.dtExcavationDepth }}
                    </td>
                    <td class="td-title">面积大小</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.dtAreaSize }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">作业工具</td>
                    <td colspan="9">
                      {{ baobeiFormData.ajTaskDetail.dtHomeworkTools }}
                    </td>
                  </tr>
                </template>

                <template v-if="ifHaveType(7)">
                  <tr>
                    <td class="td-title">设备工具</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.lsydEquipmentTools }}
                    </td>
                    <td class="td-title">供电部位</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.lsydPowerSupplyPosition }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">现场电工</td>
                    <td colspan="9">
                      {{ baobeiFormData.ajTaskDetail.lsydElectrician }}
                    </td>
                  </tr>
                </template>

                <template v-if="ifHaveType(8)">
                  <tr>
                    <td class="td-title">动火地点</td>
                    <td colspan="9">
                      {{ baobeiFormData.ajTaskDetail.lsdhHotworkPlace }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">动火人</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.lsdhIgnitor }}
                    </td>
                    <td class="td-title">证件号</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.lsdhIgnitorNumber }}
                    </td>
                  </tr>
                  <tr>
                    <td class="td-title">可燃气体检测结果</td>
                    <td colspan="4">
                      {{ baobeiFormData.ajTaskDetail.lsdhDetectionResult }}
                    </td>
                  </tr>
                </template>

                <tr>
                  <th class="td-title" colspan="1">序号</th>
                  <th colspan="6">主要安全措施</th>
                  <th class="td-title" colspan="2" style="width: 100px">
                    确认结果
                  </th>
                  <th class="td-title" colspan="1">确认人</th>
                </tr>
                <tr
                  v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm"
                  :key="menuIndex"
                >
                  <td class="td-title" colspan="1">{{ menuIndex + 1 }}</td>
                  <td colspan="6">{{ menu.measuresing }}</td>
                  <td class="td-title" colspan="2">
                    <el-select
                      v-model="menu.confirmType"
                      placeholder="请选择"
                      :disabled="!isEditbaobeiFormData"
                    >
                      <el-option
                        v-for="rangeItem in range"
                        :key="rangeItem.value"
                        :label="rangeItem.label"
                        :value="rangeItem.value"
                      >
                      </el-option>
                    </el-select>
                  </td>
                  <td class="td-title" colspan="1">
                    <el-input
                      v-model="menu.confirmedBy"
                      placeholder="请输入内容"
                      :disabled="!isEditbaobeiFormData"
                    ></el-input>
                  </td>
                </tr>
              </table>
              <!-- <table v-if="ifHaveType(2)" style="width: 100%;">
                                <tr>
                                    <td class="td-title">作业高度</td>
                                    <td colspan='9'>{{ baobeiFormData.ajTaskDetail.gcAssignmentHeight }}</td>
                                </tr>
                                <tr>
                                    <th class="td-title" colspan='1'>序号</th>
                                    <th colspan='6'>主要安全措施</th>
                                    <th class="td-title" colspan='2' style="width: 100px;">确认结果</th>
                                    <th class="td-title" colspan='1'>确认人</th>
                                </tr>
                                <tr v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm" :key="menuIndex">
                                    <td class="td-title" colspan='1'>{{ menuIndex + 1 }}</td>
                                    <td colspan='6'>{{ menu.measuresing }}</td>
                                    <td class="td-title" colspan='2'>
                                        <el-select v-model="menu.confirmType" placeholder="请选择">
                                            <el-option v-for="rangeItem in range" :key="rangeItem.value"
                                                :label="rangeItem.label" :value="rangeItem.value">
                                            </el-option>
                                        </el-select>
                                    </td>
                                    <td class="td-title" colspan='1'>{{ menu.confirmedBy }}</td>
                                </tr>

                            </table>
                            <table v-if="ifHaveType(3)" style="width: 100%;">
                                <tr>
                                    <td class="td-title">起重质量</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.qzHoistingMachinery }}</td>
                                    <td class="td-title">起重机械</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.ccDemolishedName }}</td>
                                </tr>
                                <tr>
                                    <th class="td-title" colspan='1'>序号</th>
                                    <th colspan='6'>主要安全措施</th>
                                    <th class="td-title" colspan='2' style="width: 100px;">确认结果</th>
                                    <th class="td-title" colspan='1'>确认人</th>
                                </tr>
                                <tr v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm" :key="menuIndex">
                                    <td class="td-title" colspan='1'>{{ menuIndex + 1 }}</td>
                                    <td colspan='6'>{{ menu.measuresing }}</td>
                                    <td class="td-title" colspan='2'>
                                        <el-select v-model="menu.confirmType" placeholder="请选择">
                                            <el-option v-for="rangeItem in range" :key="rangeItem.value"
                                                :label="rangeItem.label" :value="rangeItem.value">
                                            </el-option>
                                        </el-select>
                                    </td>
                                    <td class="td-title" colspan='1'>{{ menu.confirmedBy }}</td>
                                </tr>

                            </table>
                            <table v-if="ifHaveType(4)" style="width: 100%;">
                                <tr>
                                    <td class="td-title">拆除物名称</td>
                                    <td colspan="4">{{ baobeiFormData.ajTaskDetail.ccDemolishedName }}</td>
                                    <td class="td-title">拆除物面积、高度</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.ccDemolishedArea }}</td>
                                </tr>
                                <tr>
                                    <td class="td-title">负责人</td>
                                    <td colspan="9">{{ baobeiFormData.ajTaskDetail.director }}</td>
                                </tr>
                                <tr>
                                    <th class="td-title" colspan='1'>序号</th>
                                    <th colspan='6'>主要安全措施</th>
                                    <th class="td-title" colspan='2' style="width: 100px;">确认结果</th>
                                    <th class="td-title" colspan='1'>确认人</th>
                                </tr>
                                <tr v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm" :key="menuIndex">
                                    <td class="td-title" colspan='1'>{{ menuIndex + 1 }}</td>
                                    <td colspan='6'>{{ menu.measuresing }}</td>
                                    <td class="td-title" colspan='2'>
                                        <el-select v-model="menu.confirmType" placeholder="请选择">
                                            <el-option v-for="rangeItem in range" :key="rangeItem.value"
                                                :label="rangeItem.label" :value="rangeItem.value">
                                            </el-option>
                                        </el-select>
                                    </td>
                                    <td class="td-title" colspan='1'>{{ menu.confirmedBy }}</td>
                                </tr>

                            </table>
                            <table v-if="ifHaveType(5)" style="width: 100%;">
                                <tr>
                                    <td class="td-title">受限空间名称</td>
                                    <td colspan="4">{{ baobeiFormData.ajTaskDetail.sxkjConfinedSpace }}</td>
                                    <td class="td-title"> 检测人</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.sxkjInspector }}</td>
                                </tr>
                                <tr>
                                    <td class="td-title">受限空间内介质</td>
                                    <td colspan="4">{{ baobeiFormData.ajTaskDetail.sxkjConfinedMedium }}</td>
                                    <td class="td-title">氧含量、有毒气体 检测结果</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.sxkjDetectionResult }}</td>
                                </tr>
                                <tr>
                                    <th class="td-title" colspan='1'>序号</th>
                                    <th colspan='6'>主要安全措施</th>
                                    <th class="td-title" colspan='2' style="width: 100px;">确认结果</th>
                                    <th class="td-title" colspan='1'>确认人</th>
                                </tr>
                                <tr v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm" :key="menuIndex">
                                    <td class="td-title" colspan='1'>{{ menuIndex + 1 }}</td>
                                    <td colspan='6'>{{ menu.measuresing }}</td>
                                    <td class="td-title" colspan='2'>
                                        <el-select v-model="menu.confirmType" placeholder="请选择">
                                            <el-option v-for="rangeItem in range" :key="rangeItem.value"
                                                :label="rangeItem.label" :value="rangeItem.value">
                                            </el-option>
                                        </el-select>
                                    </td>
                                    <td class="td-title" colspan='1'>{{ menu.confirmedBy }}</td>
                                </tr>

                            </table>
                            <table v-if="ifHaveType(6)" style="width: 100%;">
                                <tr>
                                    <td class="td-title">开挖深度</td>
                                    <td colspan="4">{{ baobeiFormData.ajTaskDetail.dtExcavationDepth }}</td>
                                    <td class="td-title"> 面积大小</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.dtAreaSize }}</td>
                                </tr>
                                <tr>
                                    <td class="td-title">作业工具</td>
                                    <td colspan="9">{{ baobeiFormData.ajTaskDetail.dtHomeworkTools }}</td>
                                </tr>
                                <tr>
                                    <th class="td-title" colspan='1'>序号</th>
                                    <th colspan='6'>主要安全措施</th>
                                    <th class="td-title" colspan='2' style="width: 100px;">确认结果</th>
                                    <th class="td-title" colspan='1'>确认人</th>
                                </tr>
                                <tr v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm" :key="menuIndex">
                                    <td class="td-title" colspan='1'>{{ menuIndex + 1 }}</td>
                                    <td colspan='6'>{{ menu.measuresing }}</td>
                                    <td class="td-title" colspan='2'>
                                        <el-select v-model="menu.confirmType" placeholder="请选择">
                                            <el-option v-for="rangeItem in range" :key="rangeItem.value"
                                                :label="rangeItem.label" :value="rangeItem.value">
                                            </el-option>
                                        </el-select>
                                    </td>
                                    <td class="td-title" colspan='1'>{{ menu.confirmedBy }}</td>
                                </tr>

                            </table>
                            <table v-if="ifHaveType(7)" style="width: 100%;">
                                <tr>
                                    <td class="td-title">设备工具</td>
                                    <td colspan="4">{{ baobeiFormData.ajTaskDetail.lsydEquipmentTools }}</td>
                                    <td class="td-title"> 供电部位</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.lsydPowerSupplyPosition }}</td>
                                </tr>
                                <tr>
                                    <td class="td-title">现场电工</td>
                                    <td colspan='9'>{{ baobeiFormData.ajTaskDetail.lsydElectrician }}</td>
                                </tr>
                                <tr>
                                    <th class="td-title" colspan='1'>序号</th>
                                    <th colspan='6'>主要安全措施</th>
                                    <th class="td-title" colspan='2' style="width: 100px;">确认结果</th>
                                    <th class="td-title" colspan='1'>确认人</th>
                                </tr>
                                <tr v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm" :key="menuIndex">
                                    <td class="td-title" colspan='1'>{{ menuIndex + 1 }}</td>
                                    <td colspan='6'>{{ menu.measuresing }}</td>
                                    <td class="td-title" colspan='2'>
                                        <el-select v-model="menu.confirmType" placeholder="请选择">
                                            <el-option v-for="rangeItem in range" :key="rangeItem.value"
                                                :label="rangeItem.label" :value="rangeItem.value">
                                            </el-option>
                                        </el-select>
                                    </td>
                                    <td class="td-title" colspan='1'>{{ menu.confirmedBy }}</td>
                                </tr>
                            </table>
                            <table v-if="ifHaveType(8)" style="width: 100%;">
                                <tr>
                                    <td class="td-title">动火地点 </td>
                                    <td colspan="9">{{ baobeiFormData.ajTaskDetail.lsdhHotworkPlace }}</td>
                                </tr>
                                <tr>
                                    <td class="td-title">动火人</td>
                                    <td colspan="4">{{ baobeiFormData.ajTaskDetail.lsdhIgnitor }}</td>
                                    <td class="td-title"> 证件号</td>
                                    <td colspan='4'>{{ baobeiFormData.ajTaskDetail.lsdhIgnitorNumber }}</td>
                                </tr>
                                <tr>
                                    <td class="td-title">可燃气体检测结果</td>
                                    <td colspan="4">{{ baobeiFormData.ajTaskDetail.lsdhDetectionResult }}</td>
                                </tr>
                                <tr>
                                    <th class="td-title" colspan='1'>序号</th>
                                    <th colspan='6'>主要安全措施</th>
                                    <th class="td-title" colspan='2' style="width: 100px;">确认结果</th>
                                    <th class="td-title" colspan='1'>确认人</th>
                                </tr>
                                <tr v-for="(menu, menuIndex) in baobeiFormData.ajMeasureConfirm" :key="menuIndex">
                                    <td class="td-title" colspan='1'>{{ menuIndex + 1 }}</td>
                                    <td colspan='6'>{{ menu.measuresing }}</td>
                                    <td class="td-title" colspan='2'>
                                        <el-select v-model="menu.confirmType" placeholder="请选择">
                                            <el-option v-for="rangeItem in range" :key="rangeItem.value"
                                                :label="rangeItem.label" :value="rangeItem.value">
                                            </el-option>
                                        </el-select>
                                    </td>
                                    <td class="td-title" colspan='1'>{{ menu.confirmedBy }}</td>
                                </tr>

                            </table> -->
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <el-dialog title="高级筛选" :visible.sync="shaiFormShow" width="30%">
      <div>
        <el-row
          :gutter="12"
          type="flex"
          align="middle"
          style="margin-bottom: 12px"
        >
          <el-col style="text-align: right" :span="5">文件名</el-col>
          <el-col :span="16">
            <el-input
              placeholder="请输入文件名称"
              suffix-icon="el-icon-date"
              v-model="fileParams.fileName"
            >
            </el-input>
          </el-col>
        </el-row>
        <el-row
          :gutter="12"
          type="flex"
          align="middle"
          style="margin-bottom: 12px"
        >
          <el-col style="text-align: right" :span="5">用户</el-col>
          <el-col :span="16">
            <el-input
              placeholder="请输入用户名称"
              suffix-icon="el-icon-date"
              v-model="fileParams.name"
            >
            </el-input>
          </el-col>
        </el-row>
        <el-row
          :gutter="12"
          type="flex"
          align="middle"
          style="margin-bottom: 12px"
        >
          <el-col style="text-align: right" :span="5">上传日期</el-col>
          <el-col :span="16">
            <el-date-picker
              v-model="fileParams.uploadTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-col>
        </el-row>
        <el-row :gutter="12" type="flex" align="middle">
          <el-col style="text-align: right" :span="5">置顶</el-col>
          <el-col :span="16">
            <el-switch v-model="fileParams.top"> </el-switch>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="shaiFormShow = false">取 消</el-button>
        <el-button type="primary" @click="shaiFormShow = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  listReports,
  getReports,
  delReports,
  addReports,
  updateReports,
  uploadFile,
} from "@/api/system/reports";
import { getDicts } from "@/api/system/dict/data";
import configService from "@/api/env";
import { formatDate } from "@/utils";

export default {
  name: "Reports",
  data() {
    return {
      uploadFileList: [],
      imgUrl: configService.apiUrl,
      // 是否编辑
      isEditbaobeiFormData: false,
      // 安全措施结果列表
      range: [
        {
          value: "1",
          label: "符合",
        },
        {
          value: "2",
          label: "不符合",
        },
      ],
      // 安全措施列表
      menuMap: {
        2: [
          {
            measuresing:
              "患高血压、心脏病、贫血病、癫痫病、精神病以及其他不适于高处作业的人员，不得从事高处作业。",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "作业人员着装符合要求，戴好安全帽、衣着要灵便，禁止穿硬底和带钉易滑的鞋",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "作业人员是否按要求佩戴安全带",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "使用吊篮高处作业人员是否佩戴安全带和生命线",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "现场搭设的脚手架、防护围栏符合安全规程	",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "禁止上下交叉作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "六级风以上和雷电、暴雨、大雾等恶劣气候条件下，禁止进行露天高处作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "在邻近地区设有排放有毒、有害气体及粉尘超出允许浓度的烟囱及设备的场所，严禁进行高处作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "无挂扣安全带的现场，设置生命线，生命线拉设垂度小于长度的5%",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "禁止踩在彩钢板、瓦棱板等不承重的物体上进行作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "使用移动脚手架，是否安装护栏，紧锁滑轮",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "作业上方有电源线的是否切断电源",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
        8: [
          {
            measuresing: "有爆炸、火灾的危险动火作业，制定安全措施",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "动火作业现场作业区域周围10m内，是否清理所有可燃物",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "高处动火在作业区域下方周围15m内要是否清理所有可燃物",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "对有可燃物的设施、设备进行动火作业，设施、设备内的可燃物是否清理干净",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "现场灭火机是否已配置",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "乙炔气瓶（禁止卧放）、氧气瓶与火源间的距离不得少于10米",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
        7: [
          {
            measuresing: "安装临时线路人员持有电工作业操作证",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "在防爆场所使用的临时电源、电气元件和线路达到相应的防爆等级要求",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "临时用电实行三相五线配电系统",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "临时用电线路架空高度在装置内不低于2.5米，道路不低于5米",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "临时用电线路架空进线不得采用裸线，不得在树上和脚手架上架设",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "暗管埋设及地下电缆线路设有“走向标志”和安全标志，电缆埋深大于0.7米",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "现场临时用电配电盘、箱应有防雨措施",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "临时用电设施安有漏电保护器，移动工具、手持工具应有一机一闸一保护",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "用电设备、线路容量、负荷符合要求",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "行灯电压不得超过36V，在特别潮湿的场所或塔、釜、槽、罐等金属设备作业装设的临时照明行灯不应超过12V",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
        3: [
          {
            measuresing: "起重机械是否经过检验",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "吊机司机、起重工是否持证作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "起重机械、吊具、索具、钢丝绳是否符合要求",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "吊装现场是否已设置安全警戒区",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "人员是否已撤离警戒区",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "起重臂吊钩或吊物下面是否有人",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "吊装区域有电线的是否切断电源",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "有输电线路是否得到电力部门同意",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "起重物是否捆绑、紧固",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "禁止单点起重物件",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
        6: [
          {
            measuresing: "电力电缆已确认，保护措施已落实",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "电信电缆已确定，保护措施已落实",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "地下供排水管线、工艺管线已确认，保护措施已落实",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "已按施工方案图划线施工",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "作业现场围栏、警戒线、警告牌、夜间警示灯已按要求设置",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "间有充足照明：A.普通灯；B.防焊灯",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "间有充足照明：A.普通灯；B.防焊灯",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "作业人员已佩戴防护器具",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "机械开挖时，人员不得在机械臂下方同时作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "1m以下的基坑要放坡处理和固壁支撑",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing:
              "动土时可能危及周围有电线杆、构筑物、堆叠物等设施的，已进行加固，切断电源",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
        4: [
          {
            measuresing: "拆除方案是否编制",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "是否搭建了拆除作业平台",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "拆除人员严禁站在被拆除的物体上作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "拆除作业严禁立体交叉作业",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "机械拆除时是否设立警戒区域，禁止人员进入",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "拆除周围有电源线的，是否已切断电源",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "拆除可能危及毗邻建筑、设备、设施的是否有保护措施",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "作业人员是否佩戴劳动保护用品",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
        1: [
          {
            measuresing: "是否制定了检修安全措施",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "检修机械设备是否已停止运行",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "机械设备电源是否切断、挂牌",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "检修周围的危险物是否采取措施",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "检维修的对象有坍塌、坠落等风险是否加固",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "作业人员是否穿戴劳防用品",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "涉及其他特殊作业是否开具作业许可证",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
        5: [
          {
            measuresing:
              "受限空间作业人员是否佩戴符合作业场所的防毒面具和个人监护设备",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "进入有毒有害受限空间作业人员是否经过培训持证上岗",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "受限空间有相连物料管道是否隔离、脱开、加盲板",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "有氮气等惰性气体管道是否脱开",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "受限空间的盖子、人孔是否全部打开",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "受限空间内的物料、气体是否清理干净",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "受限空间是否已进行通风措施",
            confirmType: 1,
            confirmBy: "",
          },
          {
            measuresing: "",
            confirmType: 1,
            confirmBy: "",
          },
        ],
      },
      // 八项报备类型
      typeMap: [],
      // 高级筛选表单
      shaiFormShow: false,
      // 文件检索参数
      fileParams: {
        name: "",
        fileName: "",
        uploadTime: [],
        top: false,
      },
      // 文件表格数据
      fileTableData: [],
      // 显示报备表单
      baobeiForm: false,
      // 报备表单内容
      baobeiFormData: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      reportsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectinspecid: null,
        project: null,
        date: null,
        content: null,
        types: null,
        location: null,
        contacts: null,
        phone: null,
        utensil: null,
        measures: null,
        Reportingtime: null,
        reporter: null,
        keywords: null,
        operatives: null,
        supervisor: null,
        maintenance: null,
        specific: null,
        states: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    getDicts("an_shenbao_type").then((res) => {
      res.data.forEach((item) => {
        this.typeMap.push({
          value: item.dictSort,
          label: item.dictLabel,
        });
      });
      this.getList();
    });
  },
  methods: {
    // 导出表格
    exportExcel() {
      window.location.href =
        process.env.VUE_APP_BASE_API +
        "/pc/bx/reports/exportReportInfo?project=" +
        (this.queryParams.project ? this.queryParams.project : "") +
        "&Reportingtime=" +
        (this.queryParams.Reportingtime ? this.queryParams.Reportingtime : "") +
        "&types=" +
        (this.queryParams.types ? this.queryParams.types : "") +
        "&ids=" +
        (this.ids ? this.ids : "");
    },
    //readFile
    readFile(row) {
      const filePath = row.fileUrl;
      const tempLink = document.createElement("a");
      tempLink.style.display = "none";
      tempLink.href = filePath;
      tempLink.setAttribute("download", row.fileName);
      tempLink.setAttribute("target", "_blank");
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
    },
    // 查看详情
    handleCheck(row, isEdit) {
      const reportsid = row.reportsid || this.ids;
      this.isEditbaobeiFormData = isEdit;
      getReports(reportsid).then((res) => {
        res.data.ajMeasureConfirm.forEach((item, index) => {
          if (item.otherContent || item.measuresXh == 99) {
            item.measuresing = item.otherContent;
          } else {
            item.measuresing =
              this.menuMap[item.reportType][item.measuresXh - 1].measuresing;
          }
        });
        this.baobeiFormData = res.data;
        this.baobeiFormData.types = res.data.types.split(",");
        this.baobeiForm = true;
      });
    },
    // 上传文件
    uploadFile(file) {
      let obj = {
        fileName: file.name,
        fileExtname: file.name.split(".").pop(),
        fileSize: (file.size / 1024).toFixed(2),
        createTime: formatDate(new Date()),
      };
      uploadFile({
        file: file.raw,
      }).then((res) => {
        obj.fileUrl = this.imgUrl + res.msg;
        this.uploadFileList.push(obj);
        this.baobeiFormData.reportFileInfos.push(obj);
      });
    },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;
      listReports(this.queryParams)
        .then((response) => {
          response.rows.forEach((item) => {
            item.types = item.types.split(",");
            item.typeName = this.setType(item.types);
            // item.typeName = this.typeMap.find(type => type.value == item.types).label
          });
          this.reportsList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
        });
    },
    ifHaveType(type) {
      if (
        this.baobeiFormData.types &&
        this.baobeiFormData.types.findIndex((item) => item == type) != -1
      ) {
        return true;
      } else {
        return false;
      }
    },
    setType(list) {
      let str = "";
      list.forEach((item, index) => {
        if (index != list.length - 1) {
          str =
            str + this.typeMap.find((type) => type.value == item).label + "、";
        } else {
          str = str + this.typeMap.find((type) => type.value == item).label;
        }
      });
      return str;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        reportsid: null,
        projectinspecid: null,
        project: null,
        date: null,
        content: null,
        types: null,
        location: null,
        contacts: null,
        phone: null,
        utensil: null,
        measures: null,
        reportingtime: null,
        reporter: null,
        keywords: null,
        operatives: null,
        supervisor: null,
        maintenance: null,
        specific: null,
        states: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.reportsid);
      console.log(this.ids);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加【请填写功能名称】";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const reportsid = row.reportsid || this.ids;
      getReports(reportsid).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改【请填写功能名称】";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.reportsid != null) {
            updateReports(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReports(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reportsids = row.reportsid || this.ids;
      this.$modal
        .confirm("是否确认删除")
        .then(function () {
          return delReports(reportsids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/reports/export",
        {
          ...this.queryParams,
        },
        `reports_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 100px);
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.baobeiContent {
  width: 100%;
  height: 76vh;

  table {
    width: 95%;
    border-collapse: collapse;
  }

  th,
  td {
    border: 1px solid #ebebeb;
    text-align: center;
    padding: 10px 5px;
    line-height: 30px;
    color: #333333;
  }

  .td-title {
    width: 80px;
  }

  .tip {
    width: 100%;
    text-align: center;
    line-height: 40px;
    font-size: 12px;
    color: #999;
  }

  .el-table .el-table__header-wrapper th {
    background: rgba(#3c80e8, 0.1);
  }

  .el-dialog__body {
    padding: 14px;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  height: calc(100vh - 150px);
  overflow-y: auto;
  font-size: 14px;
}
</style>
