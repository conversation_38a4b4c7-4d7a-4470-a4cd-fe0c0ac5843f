{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue", "mtime": 1757499054501}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gImVjaGFydHMiOw0KaW1wb3J0IGJhckNoYXJ0IGZyb20gIi4vY29tcG9uZW50cy9iYXJDaGFydC52dWUiOw0KaW1wb3J0IHBpZUNoYXJ0IGZyb20gIi4vY29tcG9uZW50cy9waWVDaGFydC52dWUiOw0KaW1wb3J0IHsNCiAgZ2V0TWFuYWdlbWVudE92ZXJ2aWV3LA0KICBnZXRRdWFsaXR5U3RhdGlzdGljcywNCiAgZ2V0U2FmZXR5U3RhdGlzdGljcywNCiAgZ2V0RGFuZ2VyVHlwZVN0YXRpc3RpY3MsDQogIGdldFNhZmV0eVByb2R1Y3Rpb25TdGF0aXN0aWNzLA0KICBnZXREYW5nZXJvdXNQcm9TdGF0aXN0aWNzLA0KICBnZXRMYXJnZUVxdWlwbWVudFN0YXRpc3RpY3MsDQogIGdldExhcmdlRXF1aXBtZW50QnlOYW1lU3RhdGlzdGljcywNCn0gZnJvbSAiQC9hcGkvc3RhdGlzdGljcyI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJJbmRleCIsDQogIGNvbXBvbmVudHM6IHsNCiAgICBiYXJDaGFydCwNCiAgICBwaWVDaGFydCwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5pe26Ze057G75Z6L6YCJ5oupICgxLeaciCwgMi3lubQpDQogICAgICBxdWFsaXR5VGltZVR5cGU6IDIsIC8vIOm7mOiupOS4uuW5tA0KICAgICAgc2FmZXR5VGltZVR5cGU6IDIsIC8vIOm7mOiupOS4uuW5tA0KICAgICAgLy8g6aG26YOo57uf6K6h5pWw5o2uDQogICAgICBzdGF0c0RhdGE6IHsNCiAgICAgICAgLy8g5Y6f5pyJ5pWw5o2uDQogICAgICAgIGRvbWVzdGljUHJvamVjdHM6IDExMjYsDQogICAgICAgIGludGVybmF0aW9uYWxQcm9qZWN0czogMTEyNiwNCiAgICAgICAgc2FmZXR5SW52ZXN0bWVudDogMTUwMCwNCiAgICAgICAgbGFyZ2VFcXVpcG1lbnQ6IDExMjYsDQogICAgICAgIGRhbmdlcm91c1Byb2plY3RzOiAxMTI2LA0KICAgICAgICAvLyDmlrDlop7mlbDmja4NCiAgICAgICAgc3VwZXJEYW5nZXJvdXNQcm9qZWN0czogMTEyNiwNCiAgICAgICAgZG9tZXN0aWNTYWZldHlJbnZlc3RtZW50OiAxMTI2LA0KICAgICAgICBpbnRlcm5hdGlvbmFsU2FmZXR5SW52ZXN0bWVudDogMTEyNiwNCiAgICAgICAgY29tcGFueUJyYW5jaGVzOiAxMTI2LA0KICAgICAgICBjYXN1YWx0aWVzOiAwLA0KICAgICAgICBtYWpvckhhemFyZHM6IDExMjYsDQogICAgICB9LA0KICAgICAgLy8g6LSo6YeP566h55CG5pWw5o2uDQogICAgICBxdWFsaXR5TWFuYWdlbWVudDogew0KICAgICAgICAvLyDmo4Dmn6XkuIvlj5ENCiAgICAgICAgaW5zcGVjdGlvblBsYW5zOiAxMjIsDQogICAgICAgIGRhaWx5SW5zcGVjdGlvbnM6IDg5LA0KICAgICAgICBzcGVjaWFsSW5zcGVjdGlvbnM6IDMzLA0KICAgICAgICBmb3VuZFByb2JsZW1zOiAzMSwNCiAgICAgICAgZml4ZWRQcm9ibGVtczogMjksDQogICAgICAgIHBlbmRpbmdQcm9ibGVtczogMiwNCiAgICAgICAgb25UaW1lUmF0ZTogOTYsDQogICAgICAgIG9uVGltZUZpeGVkOiAyOCwNCiAgICAgICAgb3ZlcmR1ZUZpeGVkOiAxLA0KICAgICAgICAvLyDoh6rkuLvkuIrmiqUNCiAgICAgICAgc2VsZlJlcG9ydFByb2JsZW1zOiAxMDMsDQogICAgICAgIHNlbGZSZXBvcnRGaXhlZDogMTAwLA0KICAgICAgICBzZWxmUmVwb3J0UGVuZGluZzogMywNCiAgICAgICAgc2VsZlJlcG9ydFJhdGU6IDg2LjQ1LA0KICAgICAgICBzZWxmUmVwb3J0T25UaW1lOiAzMSwNCiAgICAgICAgc2VsZlJlcG9ydE92ZXJkdWU6IDcsDQogICAgICAgIC8vIOi0qOmHj+exu+WIq+e7n+iuoeWbvuihqA0KICAgICAgICBxdWFsaXR5VHlwZUNoYXJ0OiB7DQogICAgICAgICAgY29sb3JMaXN0OiBbJyMyNjU2RjUnLCAnI0ZGOTIwRCcsICcjNTRDMjU1JywgJyNFNTQ1NDUnLCAnIzhFRTk4RiddLA0KICAgICAgICAgIGRhdGE6IFsNCiAgICAgICAgICAgIHsgdmFsdWU6IDMwLCBuYW1lOiAn5pa95bel6LSo6YePJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMjUsIG5hbWU6ICfmnZDmlpnotKjph48nIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAyMCwgbmFtZTogJ+W3peiJuui0qOmHjycgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDE1LCBuYW1lOiAn6K6+5aSH6LSo6YePJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMTAsIG5hbWU6ICflhbbku5botKjph48nIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgIG9wdGlvbjogew0KICAgICAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICBjZW50ZXI6IFsiNTAlIiwgIjUyJSJdLA0KICAgICAgICAgICAgICAgIHJhZGl1czogWyI0NSUiLCAiNzUlIl0sDQogICAgICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgICAgICBwb3NpdGlvbjogIm91dHNpZGUiLA0KICAgICAgICAgICAgICAgICAgZm9ybWF0dGVyOiAie2J9XG57Y30iLA0KICAgICAgICAgICAgICAgICAgZm9udFNpemU6IDEwLA0KICAgICAgICAgICAgICAgICAgY29sb3I6ICIjNjY2IiwNCiAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDE0LA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgbGFiZWxMaW5lOiB7DQogICAgICAgICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgICAgICAgbGVuZ3RoOiA4LA0KICAgICAgICAgICAgICAgICAgbGVuZ3RoMjogMTUsDQogICAgICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICIjNjY2IiwNCiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEsDQogICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICBdLA0KICAgICAgICAgIH0sDQogICAgICAgIH0sDQogICAgICB9LA0KICAgICAgLy8g5a6J5YWo566h55CG5pWw5o2uDQogICAgICBzYWZldHlNYW5hZ2VtZW50OiB7DQogICAgICAgIC8vIOajgOafpeS4i+WPkQ0KICAgICAgICBpbnNwZWN0aW9uQ291bnQ6IDU4LA0KICAgICAgICBoYXphcmRGb3VuZDogMjAsDQogICAgICAgIG9uVGltZVJhdGU6IDg5LA0KICAgICAgICAvLyDoh6rkuLvkuIrmiqUNCiAgICAgICAgaGF6YXJkQ291bnQ6IDEwMywNCiAgICAgICAgcmVwb3J0T25UaW1lUmF0ZTogODYuNDUsDQogICAgICAgIC8vIOmakOaCo+exu+WIq+e7n+iuoeWbvuihqA0KICAgICAgICBoYXphcmRUeXBlQ2hhcnQxOiB7DQogICAgICAgICAgY29sb3JMaXN0OiBbDQogICAgICAgICAgICAiIzI2NTZGNSIsDQogICAgICAgICAgICAiIzhFRTk4RiIsDQogICAgICAgICAgICAiI0ZGOTIwRCIsDQogICAgICAgICAgICAiIzU0QzI1NSIsDQogICAgICAgICAgICAiI0ExQ0RGRiIsDQogICAgICAgICAgICAiI0U1NDU0NSIsDQogICAgICAgICAgICAiI0ZFQ0Y3NyIsDQogICAgICAgICAgICAiI0ZGNzczMCIsDQogICAgICAgICAgICAiI0IzOERGRiIsDQogICAgICAgICAgICAiI0ExRkZFQiIsDQogICAgICAgICAgXSwNCiAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAzNSwgbmFtZTogIuWfuuehgOiuvuaWvSIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDI4LCBuYW1lOiAi6K6+5aSH57u05oqkIiB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMjUsIG5hbWU6ICLmtojpmLLlronlhagiIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAyMiwgbmFtZTogIueUteawlOWuieWFqCIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDE4LCBuYW1lOiAi6auY56m65L2c5LiaIiB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMTUsIG5hbWU6ICLmnLrmorDmk43kvZwiIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAxMiwgbmFtZTogIuWMluWtpuWTgeeuoeeQhiIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDEwLCBuYW1lOiAi5Liq5Lq66Ziy5oqkIiB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogOCwgbmFtZTogIueOr+Wig+WNq+eUnyIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDUsIG5hbWU6ICLlhbbku5YiIH0sDQogICAgICAgICAgXSwNCiAgICAgICAgICBvcHRpb246IHsNCiAgICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgY2VudGVyOiBbIjUwJSIsICI1MiUiXSwNCiAgICAgICAgICAgICAgICByYWRpdXM6IFsiNDUlIiwgIjc1JSJdLA0KICAgICAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICJvdXRzaWRlIiwNCiAgICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogIntifVxue2N9IiwNCiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMCwNCiAgICAgICAgICAgICAgICAgIGNvbG9yOiAiIzY2NiIsDQogICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAxNCwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIGxlbmd0aDogOCwNCiAgICAgICAgICAgICAgICAgIGxlbmd0aDI6IDE1LA0KICAgICAgICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgICAgICAgIGNvbG9yOiAiIzY2NiIsDQogICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxLA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgXSwNCiAgICAgICAgICB9LA0KICAgICAgICB9LA0KICAgICAgICBoYXphcmRUeXBlQ2hhcnQyOiB7DQogICAgICAgICAgY29sb3JMaXN0OiBbDQogICAgICAgICAgICAiIzI2NTZGNSIsDQogICAgICAgICAgICAiIzhFRTk4RiIsDQogICAgICAgICAgICAiI0ZGOTIwRCIsDQogICAgICAgICAgICAiIzU0QzI1NSIsDQogICAgICAgICAgICAiI0ExQ0RGRiIsDQogICAgICAgICAgXSwNCiAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAxNSwgbmFtZTogIuWfuuehgOiuvuaWvSIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDEyLCBuYW1lOiAi6K6+5aSH57u05oqkIiB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMTEsIG5hbWU6ICLmtojpmLLlronlhagiIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAxMCwgbmFtZTogIueUteawlOWuieWFqCIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDEwLCBuYW1lOiAi6auY56m65L2c5LiaIiB9LA0KICAgICAgICAgIF0sDQogICAgICAgICAgb3B0aW9uOiB7DQogICAgICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIGNlbnRlcjogWyI1MCUiLCAiNTIlIl0sDQogICAgICAgICAgICAgICAgcmFkaXVzOiBbIjQ1JSIsICI3NSUiXSwNCiAgICAgICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAib3V0c2lkZSIsDQogICAgICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICJ7Yn1cbntjfSIsDQogICAgICAgICAgICAgICAgICBmb250U2l6ZTogMTAsDQogICAgICAgICAgICAgICAgICBjb2xvcjogIiM2NjYiLA0KICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogMTQsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBsYWJlbExpbmU6IHsNCiAgICAgICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgICAgICBsZW5ndGg6IDgsDQogICAgICAgICAgICAgICAgICBsZW5ndGgyOiAxNSwNCiAgICAgICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogIiM2NjYiLA0KICAgICAgICAgICAgICAgICAgICB3aWR0aDogMSwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIF0sDQogICAgICAgICAgfSwNCiAgICAgICAgfSwNCiAgICAgIH0sDQogICAgICAvLyDoh6rkuLvkuIrmiqXmlbDmja4NCiAgICAgIHNlbGZSZXBvcnQ6IHsNCiAgICAgICAgcmVwb3J0Q291bnQ6IDEwMywNCiAgICAgICAgY29tcGxldGVkOiAxMDAsDQogICAgICAgIHBlbmRpbmc6IDMsDQogICAgICAgIG9uVGltZVJhdGU6IDg2LjQ1LA0KICAgICAgICBvblRpbWVDb21wbGV0ZWQ6IDMxLA0KICAgICAgICBvdmVyZHVlQ29tcGxldGVkOiA3LA0KICAgICAgfSwNCiAgICAgIC8vIOe7n+iuoeWIhuaekOaVsOaNrg0KICAgICAgc3RhdGlzdGljc0FuYWx5c2lzOiB7DQogICAgICAgIG92ZXJhbGxDaGFydDogew0KICAgICAgICAgIGNvbG9yTGlzdDogWw0KICAgICAgICAgICAgIiMyNjU2RjUiLA0KICAgICAgICAgICAgIiM4RUU5OEYiLA0KICAgICAgICAgICAgIiNBMUZGRUIiLA0KICAgICAgICAgICAgIiM1NEMyNTUiLA0KICAgICAgICAgICAgIiNBMUNERkYiLA0KICAgICAgICAgICAgIiNGRjkyMEQiLA0KICAgICAgICAgIF0sDQogICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgeyB2YWx1ZTogMjUsIG5hbWU6ICLlronlhajln7rnoYDnrqHnkIYiIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAyMCwgbmFtZTogIua2iOmYsuWuieWFqCIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDE4LCBuYW1lOiAi55S15rCU5a6J5YWoIiB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogMTUsIG5hbWU6ICLnibnnp43orr7lpIciIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAxMiwgbmFtZTogIuWNseWMluWTgSIgfSwNCiAgICAgICAgICAgIHsgdmFsdWU6IDEwLCBuYW1lOiAi5YW25LuWIiB9LA0KICAgICAgICAgIF0sDQogICAgICAgIH0sDQogICAgICB9LA0KICAgICAgLy8g5Y2x5aSn5bel56iL6aG555uu5YiX6KGo5pWw5o2uDQogICAgICBkYW5nZXJvdXNQcm9qZWN0c0xpc3Q6IFsNCiAgICAgICAgeyBuYW1lOiAi6IuP55S15Lqn5Lia56eR5Yib5ZutKE5PLjIwMTBHMzIpMDctMTPlnLDlnZfpobnnm64iLCBwcm9ncmVzczogMTAwIH0sDQogICAgICAgIHsgbmFtZTogIuacquadpeWHuuihjOS6p+S4muWbremhueebru+8iOebtOa1geWIhuWFrOWPuO+8iSIsIHByb2dyZXNzOiA4MCB9LA0KICAgICAgICB7IG5hbWU6ICLljY7kuLrnvZHnu5znn7Pku6PooajlpITpobnnm64iLCBwcm9ncmVzczogNzAgfSwNCiAgICAgICAgeyBuYW1lOiAi5bm05LqnMzAwMeS4h+S7tuaxvei9puW6leebmOetiemDqOS7tueUn+S6p+e6v+mhueebriIsIHByb2dyZXNzOiAzMCB9LA0KICAgICAgICB7IG5hbWU6ICLms6rmupDln47lnJ/lo6TnlJ/kuqflj4rmlrDlrqLkvZPpqozkuK3lv4PkuozmnJ/lu7rorr7pobnnm64iLCBwcm9ncmVzczogMzAgfSwNCiAgICAgIF0sDQogICAgICAvLyDljbHlpKflt6XnqIvlm77ooajphY3nva4NCiAgICAgIGRhbmdlcm91c1Byb2plY3RDaGFydDogew0KICAgICAgICBjb2xvckxpc3Q6IFsiIzU5OTBGRCIsICIjNTk5MEZEIiwgIiM1OTkwRkQiLCAiIzU5OTBGRCIsICIjNTk5MEZEIl0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICB0b3A6IDMwLA0KICAgICAgICAgIGxlZnQ6ICIzNSUiLA0KICAgICAgICAgIHJpZ2h0OiAiMTAlIiwNCiAgICAgICAgICBib3R0b206ICI1JSIsDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogInZhbHVlIiwNCiAgICAgICAgICBtYXg6IDEwLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzVGljazogew0KICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlLA0KICAgICAgICAgIH0sDQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogImNhdGVnb3J5IiwNCiAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICAi6IuP55S15Lqn5Lia56eR5Yib5ZutKE5PLjIwMTBHMzIpIDA3LTEz5Zyw5Z2X6aG555uuIiwNCiAgICAgICAgICAgICLmnKrmnaXlh7rooYzkuqfkuJrlm63pobnnm67vvIjnm7TmtYHliIblhazlj7jvvIkiLA0KICAgICAgICAgICAgIuWNjuS4uue9kee7nOefs+S7o+ihqOWkhOmhueebriIsDQogICAgICAgICAgICAi5bm05LqnMzAwMeS4h+S7tuaxvei9puW6leebmOetiemDqOS7tueUn+S6p+e6v+mhueebriIsDQogICAgICAgICAgICAi5rOq5rqQ5Z+O5Zyf5aOk55Sf5Lqn5Y+K5paw5a6i5L2T6aqM5Lit5b+D5LqM5pyf5bu66K6+6aG555uuIiwNCiAgICAgICAgICBdLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgZm9udFNpemU6IDEyLA0KICAgICAgICAgICAgY29sb3I6ICIjMzMzIiwNCiAgICAgICAgICAgIGludGVydmFsOiAwLA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc1RpY2s6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlLA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIHNob3c6IGZhbHNlLA0KICAgICAgICAgIH0sDQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLljbHlpKflt6XnqIvmlbDph48iLA0KICAgICAgICAgICAgdHlwZTogImJhciIsDQogICAgICAgICAgICBkYXRhOiBbMTAsIDgsIDcsIDMsIDNdLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAiIzU5OTBGRCIsDQogICAgICAgICAgICAgIGJvcmRlclJhZGl1czogWzAsIDQsIDQsIDBdLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGJhcldpZHRoOiAiNjAlIiwNCiAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgIHBvc2l0aW9uOiAicmlnaHQiLA0KICAgICAgICAgICAgICBjb2xvcjogIiMzMzMiLA0KICAgICAgICAgICAgICBmb250U2l6ZTogMTIsDQogICAgICAgICAgICAgIGZvcm1hdHRlcjogIntjfSIsDQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgbGFyZ2VFcXVpcG1lbnRDaGFydDogew0KICAgICAgICBjb2xvckxpc3Q6IFsNCiAgICAgICAgICAiI0ZGOTIwRCIsDQogICAgICAgICAgIiNGRUNGNzciLA0KICAgICAgICAgICIjRkY3NzMwIiwNCiAgICAgICAgICAiIzU0QzI1NSIsDQogICAgICAgICAgIiMyNjU2RjUiLA0KICAgICAgICAgICIjMkMyQzJDIiwNCiAgICAgICAgXSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIHRvcDogMzAsDQogICAgICAgICAgbGVmdDogIjglIiwNCiAgICAgICAgICByaWdodDogIjglIiwNCiAgICAgICAgICBib3R0b206ICIyNSUiLA0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICJjYXRlZ29yeSIsDQogICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgIuiuvuWkh+WIhuexuzEiLA0KICAgICAgICAgICAgIuiuvuWkh+WIhuexuzIiLA0KICAgICAgICAgICAgIuiuvuWkh+WIhuexuzMiLA0KICAgICAgICAgICAgIuiuvuWkh+WIhuexuzQiLA0KICAgICAgICAgICAgIuiuvuWkh+WIhuexuzUiLA0KICAgICAgICAgICAgIuiuvuWkh+WIhuexuzYiLA0KICAgICAgICAgIF0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBpbnRlcnZhbDogMCwNCiAgICAgICAgICAgIHJvdGF0ZTogMCwNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMiwNCiAgICAgICAgICB9LA0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICJ2YWx1ZSIsDQogICAgICAgICAgbWF4OiAyMTAsDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBmb250U2l6ZTogMTIsDQogICAgICAgICAgfSwNCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogIuacquWuieijhSIsDQogICAgICAgICAgICB0eXBlOiAiYmFyIiwNCiAgICAgICAgICAgIHN0YWNrOiAidG90YWwiLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAiI0ZGOTIwRCIgfSwNCiAgICAgICAgICAgIGRhdGE6IFszNSwgMCwgMjAsIDAsIDAsIDM1XSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLlronoo4XkuK0iLA0KICAgICAgICAgICAgdHlwZTogImJhciIsDQogICAgICAgICAgICBzdGFjazogInRvdGFsIiwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogIiNGRUNGNzciIH0sDQogICAgICAgICAgICBkYXRhOiBbMCwgMCwgMCwgMCwgMCwgNV0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAi6aqM5pS25LitIiwNCiAgICAgICAgICAgIHR5cGU6ICJiYXIiLA0KICAgICAgICAgICAgc3RhY2s6ICJ0b3RhbCIsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICIjRkY3NzMwIiB9LA0KICAgICAgICAgICAgZGF0YTogWzAsIDAsIDEwLCAwLCAwLCAwXSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLov5DooYzkuK0iLA0KICAgICAgICAgICAgdHlwZTogImJhciIsDQogICAgICAgICAgICBzdGFjazogInRvdGFsIiwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogIiM1NEMyNTUiIH0sDQogICAgICAgICAgICBkYXRhOiBbMTc1LCAxMjAsIDE1MCwgMzAsIDE4MCwgMTUwXSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICLnu7Tkv67kuK0iLA0KICAgICAgICAgICAgdHlwZTogImJhciIsDQogICAgICAgICAgICBzdGFjazogInRvdGFsIiwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogIiMyNjU2RjUiIH0sDQogICAgICAgICAgICBkYXRhOiBbMCwgMCwgMCwgMCwgMCwgMF0sDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAi5bey5oql5bqfIiwNCiAgICAgICAgICAgIHR5cGU6ICJiYXIiLA0KICAgICAgICAgICAgc3RhY2s6ICJ0b3RhbCIsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICIjMkMyQzJDIiB9LA0KICAgICAgICAgICAgZGF0YTogWzAsIDAsIDAsIDAsIDAsIDBdLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgLy8g5a6J5YWo55Sf5Lqn5oqV5YWl546v5b2i5Zu+5pWw5o2uDQogICAgICBzYWZldHlJbnZlc3RtZW50UGllQ2hhcnQ6IHsNCiAgICAgICAgY29sb3JMaXN0OiBbDQogICAgICAgICAgIiMyNjU2RjUiLA0KICAgICAgICAgICIjRkY5MjBEIiwgDQogICAgICAgICAgIiM1NEMyNTUiLA0KICAgICAgICAgICIjRTU0NTQ1IiwNCiAgICAgICAgICAiIzhFRTk4RiIsDQogICAgICAgICAgIiNBMUNERkYiDQogICAgICAgIF0sDQogICAgICAgIGRhdGE6IFsNCiAgICAgICAgICB7IHZhbHVlOiAyMDAwLCBuYW1lOiAi5rW35aSW5bel56iL5LiA5YWs5Y+4IiB9LA0KICAgICAgICAgIHsgdmFsdWU6IDIwMDAsIG5hbWU6ICLmtbflpJblt6XnqIvkuozlhazlj7giIH0sDQogICAgICAgICAgeyB2YWx1ZTogMjAwMCwgbmFtZTogIua1t+WkluW3peeoi+S4ieWFrOWPuCIgfSwNCiAgICAgICAgICB7IHZhbHVlOiAyMDAwLCBuYW1lOiAi5Lit5rGf5Zu96ZmF6ZuG5ZuiIiB9LA0KICAgICAgICAgIHsgdmFsdWU6IDIwMDAsIG5hbWU6ICLnrKzkupTlu7rorr7liIblhazlj7giIH0NCiAgICAgICAgXSwNCiAgICAgICAgb3B0aW9uOiB7DQogICAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIGNlbnRlcjogWyI1MCUiLCAiNTAlIl0sDQogICAgICAgICAgICAgIHJhZGl1czogWyI1NSUiLCAiNzUlIl0sDQogICAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgbGFiZWxMaW5lOiB7DQogICAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIC8vIOWuieWFqOeUn+S6p+aKleWFpeaAu+mHkeminQ0KICAgICAgc2FmZXR5SW52ZXN0bWVudFRvdGFsOiB7DQogICAgICAgIHZhbHVlOiAiNjAwMCIsDQogICAgICAgIHVuaXQ6ICLmgLvmipXlhaUo5LiH5YWDKSIsDQogICAgICAgIGxhYmVsOiAi5a6J5YWo55Sf5Lqn5oqV5YWlIg0KICAgICAgfSwNCiAgICAgIC8vIOWuieWFqOeUn+S6p+aKleWFpemhueebruWIl+ihqA0KICAgICAgc2FmZXR5SW52ZXN0bWVudFByb2plY3RzOiBbDQogICAgICAgIHsgbmFtZTogIua1t+WkluW3peeoi+S4gOWFrOWPuCIsIHZhbHVlOiAiMjAwMCIsIGNvbG9yOiAiIzI2NTZGNSIgfSwNCiAgICAgICAgeyBuYW1lOiAi5rW35aSW5bel56iL5LqM5YWs5Y+4IiwgdmFsdWU6ICIyMDAwIiwgY29sb3I6ICIjRkY5MjBEIiB9LA0KICAgICAgICB7IG5hbWU6ICLmtbflpJblt6XnqIvkuInlhazlj7giLCB2YWx1ZTogIjIwMDAiLCBjb2xvcjogIiM1NEMyNTUiIH0sDQogICAgICAgIHsgbmFtZTogIuS4reaxn+WbvemZhembhuWboiIsIHZhbHVlOiAiMjAwMCIsIGNvbG9yOiAiI0U1NDU0NSIgfSwNCiAgICAgICAgeyBuYW1lOiAi56ys5LqU5bu66K6+5YiG5YWs5Y+4IiwgdmFsdWU6ICIyMDAwIiwgY29sb3I6ICIjOEVFOThGIiB9DQogICAgICBdLA0KICAgICAgLy8g5L+d55WZ6L+Z5Lqb5pen5pWw5o2u57uT5p6E5Lul6Ziy5q2i5oql6ZSZ77yM5ZCO57ut5Y+v5Lul6YCQ5q2l5riF55CGDQogICAgICBsaW5lRGF0YTogew0KICAgICAgICBncmlkOiB7DQogICAgICAgICAgdG9wOiAxMCwNCiAgICAgICAgICBsZWZ0OiAiNiUiLA0KICAgICAgICAgIHJpZ2h0OiAiNiUiLA0KICAgICAgICAgIGJvdHRvbTogIjEyJSIsDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzRGF0YTogW10sDQogICAgICAgIHNlcmllc0RhdGE6IFtdLA0KICAgICAgfSwNCiAgICAgIG1haW5EYXRhOiB7fSwNCiAgICAgIHllYXJDb3VudDoge30sDQogICAgICBkYW5nZXJMaXN0OiBbXSwNCiAgICAgIGVjaGFydERhdGE6IHsgY29sb3JMaXN0OiBbXSwgZGF0YTogW10gfSwNCiAgICAgIGNhdGVCYXJEYXRhOiB7IGNvbG9yTGlzdDogW10sIHNlcmllczogW10gfSwNCiAgICAgIHllYXJCYXJEYXRhOiB7IHNlcmllczogW10gfSwNCiAgICAgIGNoYXJ0MkxlbmdlbmQ6IFtdLA0KICAgICAgZWNoYXJ0VHlwZTE6IDEsDQogICAgICBlY2hhcnRUeXBlMjogMSwNCiAgICAgIGVjaGFydFR5cGVMaXN0MTogW10sDQogICAgICBlY2hhcnRUeXBlTGlzdDI6IFtdLA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSwNCiAgICAgIC8vIOaCrOa1ruahhuebuOWFs+aVsOaNrg0KICAgICAgdG9vbHRpcDogew0KICAgICAgICB2aXNpYmxlOiBmYWxzZSwNCiAgICAgICAgeDogMCwNCiAgICAgICAgeTogMCwNCiAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgZXJyb3I6ICIiLA0KICAgICAgICBkYXRhOiBbXSwNCiAgICAgIH0sDQogICAgICAvLyDljbHlpKflt6XnqIvlm77ooajlrp7kvosNCiAgICAgIGRhbmdlcm91c1Byb2plY3RDaGFydEluc3RhbmNlOiBudWxsLA0KICAgICAgLy8g6K6+5aSH6K+m57uG5L+h5oGv57yT5a2YDQogICAgICBlcXVpcG1lbnREZXRhaWxzQ2FjaGU6IHt9LA0KICAgICAgLy8g6K6+5aSH5oKs5rWu5qGG55u45YWz5pWw5o2uDQogICAgICBlcXVpcG1lbnRUb29sdGlwOiB7DQogICAgICAgIHZpc2libGU6IGZhbHNlLA0KICAgICAgICB4OiAwLA0KICAgICAgICB5OiAwLA0KICAgICAgICB0aXRsZTogIiIsDQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBlcnJvcjogIiIsDQogICAgICAgIGRhdGE6IFtdLA0KICAgICAgfSwNCiAgICAgIHJlcXVlc3RRdWV1ZTogbmV3IFNldCgpLCAvLyDmraPlnKjor7fmsYLnmoTpobnnm67lkI3np7DpmJ/liJcNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMubG9hZE1hbmFnZW1lbnRPdmVydmlldygpOw0KICAgIHRoaXMubG9hZFF1YWxpdHlTdGF0aXN0aWNzKCk7DQogICAgdGhpcy5sb2FkU2FmZXR5U3RhdGlzdGljcygpOw0KICAgIHRoaXMubG9hZERhbmdlclR5cGVTdGF0aXN0aWNzKCk7DQogICAgdGhpcy5sb2FkU2FmZXR5UHJvZHVjdGlvblN0YXRpc3RpY3MoKTsNCiAgICB0aGlzLmxvYWREYW5nZXJvdXNQcm9TdGF0aXN0aWNzKCk7DQogICAgdGhpcy5sb2FkTGFyZ2VFcXVpcG1lbnRTdGF0aXN0aWNzKCk7DQogIH0sDQogIGJlZm9yZURlc3Ryb3koKSB7DQogICAgLy8g6ZSA5q+B5Zu+6KGo5a6e5L6LDQogICAgaWYgKHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0SW5zdGFuY2UpIHsNCiAgICAgIHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0SW5zdGFuY2UuZGlzcG9zZSgpOw0KICAgICAgdGhpcy5kYW5nZXJvdXNQcm9qZWN0Q2hhcnRJbnN0YW5jZSA9IG51bGw7DQogICAgfQ0KDQogICAgLy8g5riF55CG6K+35rGC6Zif5YiXDQogICAgdGhpcy5yZXF1ZXN0UXVldWUuY2xlYXIoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOWkhOeQhuWuieWFqOeUn+S6p+aKleWFpemhueebrmhvdmVy5LqL5Lu2DQogICAgaGFuZGxlSXRlbUhvdmVyKGl0ZW0sIGV2ZW50KSB7DQogICAgICAvLyDlj6/ku6XlnKjov5nph4zmt7vliqBob3ZlcuaViOaenOeahOmAu+i+kQ0KICAgICAgLy8g5L6L5aaC5pi+56S66K+m57uG5L+h5oGv55qEdG9vbHRpcOetiQ0KICAgICAgY29uc29sZS5sb2coJ0hvdmVyaW5nIG92ZXI6JywgaXRlbS5uYW1lLCBpdGVtLnZhbHVlKTsNCiAgICB9LA0KDQogICAgLy8g6ZqQ6JePdG9vbHRpcA0KICAgIGhpZGVUb29sdGlwKCkgew0KICAgICAgLy8g6ZqQ6JePdG9vbHRpcOeahOmAu+i+kQ0KICAgIH0sDQoNCiAgICAvLyDliIfmjaLotKjph4/nrqHnkIbml7bpl7TnsbvlnosNCiAgICBjaGFuZ2VRdWFsaXR5VGltZVR5cGUodGltZVR5cGUpIHsNCiAgICAgIGlmICh0aGlzLnF1YWxpdHlUaW1lVHlwZSAhPT0gdGltZVR5cGUpIHsNCiAgICAgICAgdGhpcy5xdWFsaXR5VGltZVR5cGUgPSB0aW1lVHlwZTsNCiAgICAgICAgdGhpcy5sb2FkUXVhbGl0eVN0YXRpc3RpY3MoKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YiH5o2i5a6J5YWo566h55CG5pe26Ze057G75Z6LDQogICAgY2hhbmdlU2FmZXR5VGltZVR5cGUodGltZVR5cGUpIHsNCiAgICAgIGlmICh0aGlzLnNhZmV0eVRpbWVUeXBlICE9PSB0aW1lVHlwZSkgew0KICAgICAgICB0aGlzLnNhZmV0eVRpbWVUeXBlID0gdGltZVR5cGU7DQogICAgICAgIHRoaXMubG9hZFNhZmV0eVN0YXRpc3RpY3MoKTsNCiAgICAgICAgdGhpcy5sb2FkRGFuZ2VyVHlwZVN0YXRpc3RpY3MoKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W5a6J5YWo566h55CG5oC76KeI5pWw5o2uDQogICAgYXN5bmMgbG9hZE1hbmFnZW1lbnRPdmVydmlldygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCLlvIDlp4vliqDovb3lronlhajnrqHnkIbmgLvop4jmlbDmja4uLi4iKTsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRNYW5hZ2VtZW50T3ZlcnZpZXcoKTsNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YTsNCg0KICAgICAgICAgIC8vIOaYoOWwhOaOpeWPo+aVsOaNruWIsOeOsOacieeahOaVsOaNrue7k+aehA0KICAgICAgICAgIHRoaXMuc3RhdHNEYXRhLmRhbmdlcm91c1Byb2plY3RzID0gZGF0YS53ZGdjcyB8fCAwOyAvLyDljbHlpKflt6XnqIvmlbANCiAgICAgICAgICB0aGlzLnN0YXRzRGF0YS5zYWZldHlJbnZlc3RtZW50ID0gZGF0YS5hcXR6emplIHx8IDA7IC8vIOWuieWFqOaKlei1hOaAu+mHkeminQ0KICAgICAgICAgIHRoaXMuc3RhdHNEYXRhLmRvbWVzdGljUHJvamVjdHMgPSBkYXRhLmduemp4bXMgfHwgMDsgLy8g5Zu95YaF5Zyo5bu66aG555uuDQogICAgICAgICAgdGhpcy5zdGF0c0RhdGEuaW50ZXJuYXRpb25hbFByb2plY3RzID0gZGF0YS5nanpqeG1zIHx8IDA7IC8vIOWbvemZheWcqOW7uumhueebrg0KICAgICAgICAgIHRoaXMuc3RhdHNEYXRhLmxhcmdlRXF1aXBtZW50ID0gZGF0YS5keHNicyB8fCAwOyAvLyDlpKflnovorr7lpIfmlbANCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCLlronlhajnrqHnkIbmgLvop4jmlbDmja7liqDovb3miJDlip86IiwgdGhpcy5zdGF0c0RhdGEpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUud2Fybigi5o6l5Y+j6L+U5Zue5pWw5o2u5qC85byP5byC5bi4OiIsIHJlc3BvbnNlKTsNCiAgICAgICAgICB0aGlzLmhhbmRsZURhdGFMb2FkRXJyb3IoKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5a6J5YWo566h55CG5oC76KeI5pWw5o2u5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy5oYW5kbGVEYXRhTG9hZEVycm9yKCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaVsOaNruWKoOi9vemUmeivrw0KICAgIGhhbmRsZURhdGFMb2FkRXJyb3IoKSB7DQogICAgICBjb25zb2xlLmxvZygi5L2/55So6buY6K6k5pWw5o2uIik7DQogICAgICAvLyDkv53mjIHljp/mnInnmoTpu5jorqTmlbDmja7vvIznoa7kv53pobXpnaLmraPluLjmmL7npLoNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuaVsOaNruWKoOi9veWksei0pe+8jOaYvuekuum7mOiupOaVsOaNriIpOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5botKjph4/nrqHnkIbnu5/orqHmlbDmja4NCiAgICBhc3luYyBsb2FkUXVhbGl0eVN0YXRpc3RpY3MoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zb2xlLmxvZygNCiAgICAgICAgICAi5byA5aeL5Yqg6L296LSo6YeP566h55CG57uf6K6h5pWw5o2uLi4uIiwNCiAgICAgICAgICAidGltZVR5cGU6IiwNCiAgICAgICAgICB0aGlzLnF1YWxpdHlUaW1lVHlwZQ0KICAgICAgICApOw0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFF1YWxpdHlTdGF0aXN0aWNzKHsNCiAgICAgICAgICB0aW1lVHlwZTogdGhpcy5xdWFsaXR5VGltZVR5cGUsDQogICAgICAgIH0pOw0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhOw0KDQogICAgICAgICAgLy8g5pig5bCE5o6l5Y+j5pWw5o2u5Yiw546w5pyJ55qE6LSo6YeP566h55CG5pWw5o2u57uT5p6E77yI6Ieq5Li75LiK5oql6YOo5YiG77yJDQogICAgICAgICAgdGhpcy5xdWFsaXR5TWFuYWdlbWVudC5zZWxmUmVwb3J0UHJvYmxlbXMgPSBkYXRhLmpjenMgfHwgMDsgLy8g6Ieq5Li75LiK5oql6Zeu6aKY5pWwDQogICAgICAgICAgdGhpcy5xdWFsaXR5TWFuYWdlbWVudC5zZWxmUmVwb3J0Rml4ZWQgPSBkYXRhLnl3Y3NsIHx8IDA7IC8vIOW3suaVtOaUueaVsOmHjw0KICAgICAgICAgIHRoaXMucXVhbGl0eU1hbmFnZW1lbnQuc2VsZlJlcG9ydFBlbmRpbmcgPSBkYXRhLmt3Y3NsIHx8IDA7IC8vIOW+heaVtOaUueaVsOmHjw0KICAgICAgICAgIHRoaXMucXVhbGl0eU1hbmFnZW1lbnQuc2VsZlJlcG9ydFJhdGUgPSBkYXRhLmFzemdsDQogICAgICAgICAgICA/IGRhdGEuYXN6Z2wgKiAxMDANCiAgICAgICAgICAgIDogMDsgLy8g5oyJ5pe25pW05pS5546H77yI6L2s5o2i5Li655m+5YiG5q+U77yJDQogICAgICAgICAgdGhpcy5xdWFsaXR5TWFuYWdlbWVudC5zZWxmUmVwb3J0T25UaW1lID0gZGF0YS5hc3pnIHx8IDA7IC8vIOaMieaXtuaVtOaUuQ0KICAgICAgICAgIHRoaXMucXVhbGl0eU1hbmFnZW1lbnQuc2VsZlJlcG9ydE92ZXJkdWUgPSBkYXRhLndhc3pnIHx8IDA7IC8vIOacquaMieaXtuaVtOaUuQ0KDQogICAgICAgICAgY29uc29sZS5sb2coIui0qOmHj+euoeeQhue7n+iuoeaVsOaNruWKoOi9veaIkOWKnzoiLCB0aGlzLnF1YWxpdHlNYW5hZ2VtZW50KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oIui0qOmHj+euoeeQhue7n+iuoeaOpeWPo+i/lOWbnuaVsOaNruagvOW8j+W8guW4uDoiLCByZXNwb25zZSk7DQogICAgICAgICAgdGhpcy5oYW5kbGVRdWFsaXR5RGF0YUxvYWRFcnJvcigpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5botKjph4/nrqHnkIbnu5/orqHmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLmhhbmRsZVF1YWxpdHlEYXRhTG9hZEVycm9yKCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhui0qOmHj+euoeeQhuaVsOaNruWKoOi9vemUmeivrw0KICAgIGhhbmRsZVF1YWxpdHlEYXRhTG9hZEVycm9yKCkgew0KICAgICAgY29uc29sZS5sb2coIuS9v+eUqOi0qOmHj+euoeeQhum7mOiupOaVsOaNriIpOw0KICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi6LSo6YeP566h55CG5pWw5o2u5Yqg6L295aSx6LSl77yM5pi+56S66buY6K6k5pWw5o2uIik7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWuieWFqOeuoeeQhue7n+iuoeaVsOaNrg0KICAgIGFzeW5jIGxvYWRTYWZldHlTdGF0aXN0aWNzKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc29sZS5sb2coDQogICAgICAgICAgIuW8gOWni+WKoOi9veWuieWFqOeuoeeQhue7n+iuoeaVsOaNri4uLiIsDQogICAgICAgICAgInRpbWVUeXBlOiIsDQogICAgICAgICAgdGhpcy5zYWZldHlUaW1lVHlwZQ0KICAgICAgICApOw0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFNhZmV0eVN0YXRpc3RpY3Moew0KICAgICAgICAgIHRpbWVUeXBlOiB0aGlzLnNhZmV0eVRpbWVUeXBlLA0KICAgICAgICB9KTsNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YTsNCg0KICAgICAgICAgIC8vIOaYoOWwhOaOpeWPo+aVsOaNruWIsOeOsOacieeahOWuieWFqOeuoeeQhuaVsOaNrue7k+aehO+8iOiHquS4u+S4iuaKpemDqOWIhu+8iQ0KICAgICAgICAgIHRoaXMuc2FmZXR5TWFuYWdlbWVudC5oYXphcmRDb3VudCA9IGRhdGEuamN6cyB8fCAwOyAvLyDpmpDmgqPmlbDvvIjpl67popjmlbDvvIkNCiAgICAgICAgICB0aGlzLnNhZmV0eU1hbmFnZW1lbnQucmVwb3J0T25UaW1lUmF0ZSA9IGRhdGEuYXN6Z2wNCiAgICAgICAgICAgID8gKGRhdGEuYXN6Z2wgKiAxMDApLnRvRml4ZWQoMSkNCiAgICAgICAgICAgIDogMDsgLy8g5oyJ5pe25pW05pS5546H77yI6L2s5o2i5Li655m+5YiG5q+U77yJDQogICAgICAgICAgLy8g5Li65LqG5L+d5oyB5pWw5o2u5LiA6Ie05oCn77yM5Lmf5Y+v5Lul5a2Y5YKo5pu05aSa6K+m57uG5pWw5o2u5aSH55SoDQogICAgICAgICAgdGhpcy5zYWZldHlNYW5hZ2VtZW50LnNhZmV0eUZpeGVkUHJvYmxlbXMgPSBkYXRhLnl3Y3NsIHx8IDA7IC8vIOW3suaVtOaUueaVsOmHjw0KICAgICAgICAgIHRoaXMuc2FmZXR5TWFuYWdlbWVudC5zYWZldHlQZW5kaW5nUHJvYmxlbXMgPSBkYXRhLmt3Y3NsIHx8IDA7IC8vIOW+heaVtOaUueaVsOmHjw0KICAgICAgICAgIHRoaXMuc2FmZXR5TWFuYWdlbWVudC5zYWZldHlPblRpbWVGaXhlZCA9IGRhdGEuYXN6ZyB8fCAwOyAvLyDmjInml7bmlbTmlLkNCiAgICAgICAgICB0aGlzLnNhZmV0eU1hbmFnZW1lbnQuc2FmZXR5T3ZlcmR1ZUZpeGVkID0gZGF0YS53YXN6ZyB8fCAwOyAvLyDmnKrmjInml7bmlbTmlLkNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCLlronlhajnrqHnkIbnu5/orqHmlbDmja7liqDovb3miJDlip86IiwgdGhpcy5zYWZldHlNYW5hZ2VtZW50KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oIuWuieWFqOeuoeeQhue7n+iuoeaOpeWPo+i/lOWbnuaVsOaNruagvOW8j+W8guW4uDoiLCByZXNwb25zZSk7DQogICAgICAgICAgdGhpcy5oYW5kbGVTYWZldHlEYXRhTG9hZEVycm9yKCk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWuieWFqOeuoeeQhue7n+iuoeaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuaGFuZGxlU2FmZXR5RGF0YUxvYWRFcnJvcigpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblronlhajnrqHnkIbmlbDmja7liqDovb3plJnor68NCiAgICBoYW5kbGVTYWZldHlEYXRhTG9hZEVycm9yKCkgew0KICAgICAgY29uc29sZS5sb2coIuS9v+eUqOWuieWFqOeuoeeQhum7mOiupOaVsOaNriIpOw0KICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi5a6J5YWo566h55CG5pWw5o2u5Yqg6L295aSx6LSl77yM5pi+56S66buY6K6k5pWw5o2uIik7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlumakOaCo+exu+WIq+e7n+iuoeaVsOaNrg0KICAgIGFzeW5jIGxvYWREYW5nZXJUeXBlU3RhdGlzdGljcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCLlvIDlp4vliqDovb3pmpDmgqPnsbvliKvnu5/orqHmlbDmja4uLi4iKTsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXREYW5nZXJUeXBlU3RhdGlzdGljcyh7DQogICAgICAgICAgdGltZVR5cGU6IHRoaXMuc2FmZXR5VGltZVR5cGUsDQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOajgOafpeS4i+WPkeeahOWbvuihqOS9v+eUqOm7mOiupOaVsOaNru+8jOaAu+WSjOetieS6juajgOafpeasoeaVsDU4DQogICAgICAgIGNvbnN0IGRlZmF1bHREYXRhRm9ySW5zcGVjdGlvbiA9IFsNCiAgICAgICAgICB7IHZhbHVlOiAxNSwgbmFtZTogIuWfuuehgOiuvuaWvSIgfSwNCiAgICAgICAgICB7IHZhbHVlOiAxMiwgbmFtZTogIuiuvuWkh+e7tOaKpCIgfSwNCiAgICAgICAgICB7IHZhbHVlOiAxMSwgbmFtZTogIua2iOmYsuWuieWFqCIgfSwNCiAgICAgICAgICB7IHZhbHVlOiAxMCwgbmFtZTogIueUteawlOWuieWFqCIgfSwNCiAgICAgICAgICB7IHZhbHVlOiAxMCwgbmFtZTogIumrmOepuuS9nOS4miIgfSwNCiAgICAgICAgXTsNCg0KICAgICAgICAvLyDlrprkuYnpopzoibLmlbDnu4QNCiAgICAgICAgY29uc3QgY29sb3JMaXN0MSA9IFsNCiAgICAgICAgICAiIzI2NTZGNSIsDQogICAgICAgICAgIiM4RUU5OEYiLA0KICAgICAgICAgICIjRkY5MjBEIiwNCiAgICAgICAgICAiIzU0QzI1NSIsDQogICAgICAgICAgIiNBMUNERkYiLA0KICAgICAgICBdOw0KICAgICAgICBjb25zdCBjb2xvckxpc3QyID0gWw0KICAgICAgICAgICIjRkY5MjBEIiwNCiAgICAgICAgICAiI0U1NDU0NSIsDQogICAgICAgICAgIiM1NEMyNTUiLA0KICAgICAgICAgICIjMjY1NkY1IiwNCiAgICAgICAgICAiIzhFRTk4RiIsDQogICAgICAgIF07DQoNCiAgICAgICAgLy8g5pu05paw5qOA5p+l5LiL5Y+R55qE6ZqQ5oKj57G75Yir57uf6K6h5Zu+6KGo77yI5L2/55So6buY6K6k5pWw5o2u77yJDQogICAgICAgIHRoaXMuc2FmZXR5TWFuYWdlbWVudC5oYXphcmRUeXBlQ2hhcnQxID0gew0KICAgICAgICAgIGNvbG9yTGlzdDogY29sb3JMaXN0MSwNCiAgICAgICAgICBkYXRhOiBkZWZhdWx0RGF0YUZvckluc3BlY3Rpb24sDQogICAgICAgICAgb3B0aW9uOiB7DQogICAgICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIGNlbnRlcjogWyI1MCUiLCAiNTIlIl0sDQogICAgICAgICAgICAgICAgcmFkaXVzOiBbIjQ1JSIsICI3NSUiXSwNCiAgICAgICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAib3V0c2lkZSIsDQogICAgICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICJ7Yn1cbntjfSIsDQogICAgICAgICAgICAgICAgICBmb250U2l6ZTogMTAsDQogICAgICAgICAgICAgICAgICBjb2xvcjogIiM2NjYiLA0KICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogMTQsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICBsYWJlbExpbmU6IHsNCiAgICAgICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgICAgICBsZW5ndGg6IDgsDQogICAgICAgICAgICAgICAgICBsZW5ndGgyOiAxNSwNCiAgICAgICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogIiM2NjYiLA0KICAgICAgICAgICAgICAgICAgICB3aWR0aDogMSwNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIF0sDQogICAgICAgICAgfSwNCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDoh6rkuLvkuIrmiqXnmoTlm77ooajkvb/nlKjmjqXlj6PmlbDmja4NCiAgICAgICAgaWYgKA0KICAgICAgICAgIHJlc3BvbnNlICYmDQogICAgICAgICAgcmVzcG9uc2UuY29kZSA9PT0gMjAwICYmDQogICAgICAgICAgcmVzcG9uc2UuZGF0YSAmJg0KICAgICAgICAgIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkNCiAgICAgICAgKSB7DQogICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgLy8g5a+55pWw5o2u5oyJdmFsdWXlgLzpmY3luo/mjpLluo/vvIzlj6rlj5bliY015LiqDQogICAgICAgICAgY29uc3Qgc29ydGVkRGF0YSA9IGRhdGENCiAgICAgICAgICAgIC5zb3J0KChhLCBiKSA9PiAoYi52YWx1ZSB8fCAwKSAtIChhLnZhbHVlIHx8IDApKQ0KICAgICAgICAgICAgLnNsaWNlKDAsIDUpOw0KDQogICAgICAgICAgLy8g5pu05paw6Ieq5Li75LiK5oql55qE6ZqQ5oKj57G75Yir57uf6K6h5Zu+6KGo77yI5L2/55So5o6l5Y+j5pWw5o2u77yJDQogICAgICAgICAgdGhpcy5zYWZldHlNYW5hZ2VtZW50LmhhemFyZFR5cGVDaGFydDIgPSB7DQogICAgICAgICAgICBjb2xvckxpc3Q6IGNvbG9yTGlzdDIsDQogICAgICAgICAgICBkYXRhOiBzb3J0ZWREYXRhLA0KICAgICAgICAgICAgb3B0aW9uOiB7DQogICAgICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIGNlbnRlcjogWyI1MCUiLCAiNTIlIl0sDQogICAgICAgICAgICAgICAgICByYWRpdXM6IFsiNDUlIiwgIjc1JSJdLA0KICAgICAgICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICJvdXRzaWRlIiwNCiAgICAgICAgICAgICAgICAgICAgZm9ybWF0dGVyOiAie2J9XG57Y30iLA0KICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogMTAsDQogICAgICAgICAgICAgICAgICAgIGNvbG9yOiAiIzY2NiIsDQogICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDE0LA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgICAgICAgICBsZW5ndGg6IDgsDQogICAgICAgICAgICAgICAgICAgIGxlbmd0aDI6IDE1LA0KICAgICAgICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIiM2NjYiLA0KICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxLA0KICAgICAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgY29uc29sZS5sb2coIumakOaCo+exu+WIq+e7n+iuoeaVsOaNruWKoOi9veaIkOWKnzoiLCB7DQogICAgICAgICAgICBjaGFydDE6ICLkvb/nlKjpu5jorqTmlbDmja4iLA0KICAgICAgICAgICAgY2hhcnQyOiB0aGlzLnNhZmV0eU1hbmFnZW1lbnQuaGF6YXJkVHlwZUNoYXJ0MiwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oIumakOaCo+exu+WIq+e7n+iuoeaOpeWPo+i/lOWbnuaVsOaNruagvOW8j+W8guW4uDoiLCByZXNwb25zZSk7DQogICAgICAgICAgdGhpcy5oYW5kbGVEYW5nZXJUeXBlRGF0YUxvYWRFcnJvcigpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bpmpDmgqPnsbvliKvnu5/orqHmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLmhhbmRsZURhbmdlclR5cGVEYXRhTG9hZEVycm9yKCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhumakOaCo+exu+WIq+e7n+iuoeaVsOaNruWKoOi9vemUmeivrw0KICAgIGhhbmRsZURhbmdlclR5cGVEYXRhTG9hZEVycm9yKCkgew0KICAgICAgY29uc29sZS5sb2coIuiHquS4u+S4iuaKpeWbvuihqOS9v+eUqOm7mOiupOaVsOaNriIpOw0KICAgICAgLy8g5aaC5p6c5o6l5Y+j5aSx6LSl77yM6Ieq5Li75LiK5oql5Zu+6KGo5Lmf5L2/55So6buY6K6k5pWw5o2uDQogICAgICBjb25zdCBkZWZhdWx0RGF0YUZvclJlcG9ydCA9IFsNCiAgICAgICAgeyB2YWx1ZTogOCwgbmFtZTogIuWfuuehgOiuvuaWvSIgfSwNCiAgICAgICAgeyB2YWx1ZTogNywgbmFtZTogIuiuvuWkh+e7tOaKpCIgfSwNCiAgICAgICAgeyB2YWx1ZTogNiwgbmFtZTogIua2iOmYsuWuieWFqCIgfSwNCiAgICAgICAgeyB2YWx1ZTogNSwgbmFtZTogIueUteawlOWuieWFqCIgfSwNCiAgICAgICAgeyB2YWx1ZTogNCwgbmFtZTogIumrmOepuuS9nOS4miIgfSwNCiAgICAgIF07DQoNCiAgICAgIGNvbnN0IGNvbG9yTGlzdDIgPSBbDQogICAgICAgICIjRkY5MjBEIiwNCiAgICAgICAgIiNFNTQ1NDUiLA0KICAgICAgICAiIzU0QzI1NSIsDQogICAgICAgICIjMjY1NkY1IiwNCiAgICAgICAgIiM4RUU5OEYiLA0KICAgICAgXTsNCg0KICAgICAgdGhpcy5zYWZldHlNYW5hZ2VtZW50LmhhemFyZFR5cGVDaGFydDIgPSB7DQogICAgICAgIGNvbG9yTGlzdDogY29sb3JMaXN0MiwNCiAgICAgICAgZGF0YTogZGVmYXVsdERhdGFGb3JSZXBvcnQsDQogICAgICAgIG9wdGlvbjogew0KICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBjZW50ZXI6IFsiNTAlIiwgIjUyJSJdLA0KICAgICAgICAgICAgICByYWRpdXM6IFsiNDUlIiwgIjc1JSJdLA0KICAgICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgICAgcG9zaXRpb246ICJvdXRzaWRlIiwNCiAgICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICJ7Yn1cbntjfSIsDQogICAgICAgICAgICAgICAgZm9udFNpemU6IDEwLA0KICAgICAgICAgICAgICAgIGNvbG9yOiAiIzY2NiIsDQogICAgICAgICAgICAgICAgbGluZUhlaWdodDogMTQsDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGxhYmVsTGluZTogew0KICAgICAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICAgICAgbGVuZ3RoOiA4LA0KICAgICAgICAgICAgICAgIGxlbmd0aDI6IDE1LA0KICAgICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICIjNjY2IiwNCiAgICAgICAgICAgICAgICAgIHdpZHRoOiAxLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgIF0sDQogICAgICAgIH0sDQogICAgICB9Ow0KICAgICAgdGhpcy4kbW9kYWwubXNnV2FybmluZygi6ZqQ5oKj57G75Yir57uf6K6h5pWw5o2u5Yqg6L295aSx6LSl77yM5pi+56S66buY6K6k5pWw5o2uIik7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWuieWFqOeUn+S6p+aKleWFpee7n+iuoeaVsOaNrg0KICAgIGFzeW5jIGxvYWRTYWZldHlQcm9kdWN0aW9uU3RhdGlzdGljcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCLlvIDlp4vliqDovb3lronlhajnlJ/kuqfmipXlhaXnu5/orqHmlbDmja4uLi4iKTsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRTYWZldHlQcm9kdWN0aW9uU3RhdGlzdGljcygpOw0KDQogICAgICAgIGlmICgNCiAgICAgICAgICByZXNwb25zZSAmJg0KICAgICAgICAgIHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJg0KICAgICAgICAgIHJlc3BvbnNlLmRhdGEgJiYNCiAgICAgICAgICBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpDQogICAgICAgICkgew0KICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZS5kYXRhOw0KDQogICAgICAgICAgLy8g5o+Q5Y+W5YWs5Y+45ZCN56ew5L2c5Li6WOi9tOagh+etvu+8iOaIquWPluaYvuekuu+8iQ0KICAgICAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IGRhdGEubWFwKChpdGVtKSA9PiB7DQogICAgICAgICAgICAvLyDmiKrlj5blhazlj7jlkI3np7DnlKjkuo5Y6L205pi+56S677yM5L+d5oyB5Zu+6KGo576O6KeCDQogICAgICAgICAgICBjb25zdCBjb21wYW55TmFtZSA9IGl0ZW0uY29tcGFueSB8fCAi5pyq55+l5YWs5Y+4IjsNCiAgICAgICAgICAgIHJldHVybiBjb21wYW55TmFtZS5sZW5ndGggPiAxMA0KICAgICAgICAgICAgICA/IGNvbXBhbnlOYW1lLnN1YnN0cmluZygwLCAxMCkgKyAiLi4uIg0KICAgICAgICAgICAgICA6IGNvbXBhbnlOYW1lOw0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgLy8g5L+d5a2Y5a6M5pW055qE5YWs5Y+45ZCN56ew55So5LqOdG9vbHRpcOaYvuekug0KICAgICAgICAgIGNvbnN0IGZ1bGxDb21wYW55TmFtZXMgPSBkYXRhLm1hcCgNCiAgICAgICAgICAgIChpdGVtKSA9PiBpdGVtLmNvbXBhbnkgfHwgIuacquefpeWFrOWPuCINCiAgICAgICAgICApOw0KDQogICAgICAgICAgLy8g5o+Q5Y+W5bm05bqm6aKE566X6YeR6aKd5pWw5o2u77yM5bm25L+d5a2Y5a6M5pW05YWs5Y+45ZCN56ewDQogICAgICAgICAgY29uc3QgYnVkZ2V0RGF0YSA9IGRhdGEubWFwKChpdGVtLCBpbmRleCkgPT4gKHsNCiAgICAgICAgICAgIHZhbHVlOiBwYXJzZUZsb2F0KGl0ZW0uYW5udWFsQnVkZ2V0QW1vdW50IHx8IDApLA0KICAgICAgICAgICAgZnVsbE5hbWU6IGZ1bGxDb21wYW55TmFtZXNbaW5kZXhdLA0KICAgICAgICAgIH0pKTsNCiAgICAgICAgICAvLyDmj5Dlj5blrp7pmYXmipXlhaXph5Hpop3mlbDmja7vvIzlubbkv53lrZjlrozmlbTlhazlj7jlkI3np7ANCiAgICAgICAgICBjb25zdCBhY3R1YWxEYXRhID0gZGF0YS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoew0KICAgICAgICAgICAgdmFsdWU6IHBhcnNlRmxvYXQoaXRlbS5hY3R1YWxJbnB1dEFtb3VudCB8fCAwKSwNCiAgICAgICAgICAgIGZ1bGxOYW1lOiBmdWxsQ29tcGFueU5hbWVzW2luZGV4XSwNCiAgICAgICAgICB9KSk7DQoNCiAgICAgICAgICAvLyDliqjmgIHorqHnrpdZ6L205pyA5aSn5YC8DQogICAgICAgICAgY29uc3QgbWF4VmFsdWUgPSBNYXRoLm1heCgNCiAgICAgICAgICAgIC4uLmJ1ZGdldERhdGEubWFwKChpdGVtKSA9PiBpdGVtLnZhbHVlKSwNCiAgICAgICAgICAgIC4uLmFjdHVhbERhdGEubWFwKChpdGVtKSA9PiBpdGVtLnZhbHVlKQ0KICAgICAgICAgICk7DQogICAgICAgICAgY29uc3QgeUF4aXNNYXggPSBNYXRoLmNlaWwoKG1heFZhbHVlICogMS4yKSAvIDEwMDApICogMTAwMDsgLy8g5ZCR5LiK5Y+W5pW05Yiw5Y2D5L2NDQoNCiAgICAgICAgICAvLyDorqHnrpfmgLvmipXlhaUNCiAgICAgICAgICBjb25zdCB0b3RhbEludmVzdG1lbnQgPSBkYXRhLnJlZHVjZSgoc3VtLCBpdGVtKSA9PiBzdW0gKyBwYXJzZUZsb2F0KGl0ZW0uYWN0dWFsSW5wdXRBbW91bnQgfHwgMCksIDApOw0KDQogICAgICAgICAgLy8g5pu05paw546v5b2i5Zu+5pWw5o2uDQogICAgICAgICAgY29uc3QgY29sb3JMaXN0ID0gWyIjMjY1NkY1IiwgIiNGRjkyMEQiLCAiIzU0QzI1NSIsICIjRTU0NTQ1IiwgIiM4RUU5OEYiLCAiI0ExQ0RGRiJdOw0KICAgICAgICAgIA0KICAgICAgICAgIHRoaXMuc2FmZXR5SW52ZXN0bWVudFBpZUNoYXJ0ID0gew0KICAgICAgICAgICAgY29sb3JMaXN0OiBjb2xvckxpc3QsDQogICAgICAgICAgICBkYXRhOiBkYXRhLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7DQogICAgICAgICAgICAgIHZhbHVlOiBwYXJzZUZsb2F0KGl0ZW0uYWN0dWFsSW5wdXRBbW91bnQgfHwgMCksDQogICAgICAgICAgICAgIG5hbWU6IGl0ZW0uY29tcGFueSB8fCAi5pyq55+l5YWs5Y+4Ig0KICAgICAgICAgICAgfSkpLA0KICAgICAgICAgICAgb3B0aW9uOiB7DQogICAgICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgIGNlbnRlcjogWyI1MCUiLCAiNTAlIl0sDQogICAgICAgICAgICAgICAgICByYWRpdXM6IFsiNTUlIiwgIjc1JSJdLA0KICAgICAgICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgICBsYWJlbExpbmU6IHsNCiAgICAgICAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIF0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgLy8g5pu05paw5Lit5b+D5paH5a2XDQogICAgICAgICAgdGhpcy5zYWZldHlJbnZlc3RtZW50VG90YWwgPSB7DQogICAgICAgICAgICB2YWx1ZTogTWF0aC5yb3VuZCh0b3RhbEludmVzdG1lbnQpLnRvU3RyaW5nKCksDQogICAgICAgICAgICB1bml0OiAi5oC75oqV5YWlKOS4h+WFgykiLA0KICAgICAgICAgICAgbGFiZWw6ICLlronlhajnlJ/kuqfmipXlhaUiDQogICAgICAgICAgfTsNCg0KICAgICAgICAgIC8vIOabtOaWsOmhueebruWIl+ihqA0KICAgICAgICAgIHRoaXMuc2FmZXR5SW52ZXN0bWVudFByb2plY3RzID0gZGF0YS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoew0KICAgICAgICAgICAgbmFtZTogaXRlbS5jb21wYW55IHx8ICLmnKrnn6Xlhazlj7giLA0KICAgICAgICAgICAgdmFsdWU6IE1hdGgucm91bmQocGFyc2VGbG9hdChpdGVtLmFjdHVhbElucHV0QW1vdW50IHx8IDApKS50b1N0cmluZygpLA0KICAgICAgICAgICAgY29sb3I6IGNvbG9yTGlzdFtpbmRleCAlIGNvbG9yTGlzdC5sZW5ndGhdDQogICAgICAgICAgfSkpOw0KDQogICAgICAgICAgY29uc29sZS5sb2coDQogICAgICAgICAgICAi5a6J5YWo55Sf5Lqn5oqV5YWl57uf6K6h5pWw5o2u5Yqg6L295oiQ5YqfOiIsDQogICAgICAgICAgICB0aGlzLnNhZmV0eUludmVzdG1lbnRDaGFydA0KICAgICAgICAgICk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCLlronlhajnlJ/kuqfmipXlhaXnu5/orqHmjqXlj6Pov5Tlm57mlbDmja7moLzlvI/lvILluLg6IiwgcmVzcG9uc2UpOw0KICAgICAgICAgIHRoaXMuaGFuZGxlU2FmZXR5UHJvZHVjdGlvbkRhdGFMb2FkRXJyb3IoKTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5a6J5YWo55Sf5Lqn5oqV5YWl57uf6K6h5pWw5o2u5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy5oYW5kbGVTYWZldHlQcm9kdWN0aW9uRGF0YUxvYWRFcnJvcigpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblronlhajnlJ/kuqfmipXlhaXnu5/orqHmlbDmja7liqDovb3plJnor68NCiAgICBoYW5kbGVTYWZldHlQcm9kdWN0aW9uRGF0YUxvYWRFcnJvcigpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLkvb/nlKjlronlhajnlJ/kuqfmipXlhaXnu5/orqHpu5jorqTmlbDmja4iKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuWuieWFqOeUn+S6p+aKleWFpee7n+iuoeaVsOaNruWKoOi9veWksei0pe+8jOaYvuekuum7mOiupOaVsOaNriIpOw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bljbHlpKflt6XnqIvnu5/orqHmlbDmja4NCiAgICBhc3luYyBsb2FkRGFuZ2Vyb3VzUHJvU3RhdGlzdGljcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCLlvIDlp4vliqDovb3ljbHlpKflt6XnqIvnu5/orqHmlbDmja4uLi4iKTsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXREYW5nZXJvdXNQcm9TdGF0aXN0aWNzKCk7DQoNCiAgICAgICAgaWYgKA0KICAgICAgICAgIHJlc3BvbnNlICYmDQogICAgICAgICAgcmVzcG9uc2UuY29kZSA9PT0gMjAwICYmDQogICAgICAgICAgcmVzcG9uc2UuZGF0YSAmJg0KICAgICAgICAgIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkNCiAgICAgICAgKSB7DQogICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmRhdGE7DQoNCiAgICAgICAgICAvLyDojrflj5bliY015Liq6aG555uu5pWw5o2uDQogICAgICAgICAgY29uc3QgdG9wUHJvamVjdHMgPSBkYXRhLnNsaWNlKDAsIDUpOw0KDQogICAgICAgICAgLy8g5om+5Ye65pyA5aSn55qEdmFsdWXlgLzvvIznlKjkuo7orqHnrpdY6L205pyA5aSn5YC8DQogICAgICAgICAgY29uc3QgbWF4VmFsdWUgPSBNYXRoLm1heCguLi5kYXRhLm1hcCgoaXRlbSkgPT4gaXRlbS52YWx1ZSkpOw0KDQogICAgICAgICAgLy8g5o+Q5Y+W6aG555uu5ZCN56ew5ZKM5pWw5YC877yM5L+d55WZ5a6M5pW05pWw5o2u55So5LqOdG9vbHRpcA0KICAgICAgICAgIGNvbnN0IHByb2plY3ROYW1lcyA9IHRvcFByb2plY3RzLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgLy8g5oiq5Y+W6aG555uu5ZCN56ew77yM6YG/5YWN6L+H6ZW/DQogICAgICAgICAgICByZXR1cm4gaXRlbS5uYW1lICYmIGl0ZW0ubmFtZS5sZW5ndGggPiAxMA0KICAgICAgICAgICAgICA/IGl0ZW0ubmFtZS5zdWJzdHJpbmcoMCwgMTApICsgIi4uLiINCiAgICAgICAgICAgICAgOiBpdGVtLm5hbWUgfHwgIuacquefpemhueebriI7DQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICAvLyDmnoTlu7rljIXlkKvor6bnu4bkv6Hmga/nmoTmlbDmja4NCiAgICAgICAgICBjb25zdCBwcm9qZWN0RGF0YSA9IHRvcFByb2plY3RzLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7DQogICAgICAgICAgICBuYW1lOiBwcm9qZWN0TmFtZXNbaW5kZXhdLA0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUgfHwgMCwNCiAgICAgICAgICAgIGZ1bGxOYW1lOiBpdGVtLm5hbWUgfHwgIuacquefpemhueebriIsDQogICAgICAgICAgICBkZXRhbExpc3Q6IGl0ZW0uZGV0YWxMaXN0IHx8IFtdLA0KICAgICAgICAgIH0pKTsNCg0KICAgICAgICAgIC8vIOabtOaWsOWbvuihqOmFjee9rg0KICAgICAgICAgIHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0LnlBeGlzLmRhdGEgPSBwcm9qZWN0TmFtZXMucmV2ZXJzZSgpOw0KICAgICAgICAgIHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0LnNlcmllc1swXS5kYXRhID0gcHJvamVjdERhdGEucmV2ZXJzZSgpOw0KICAgICAgICAgIHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0LnhBeGlzLm1heCA9IG1heFZhbHVlOw0KDQogICAgICAgICAgLy8g5L+d5a2Y5Y6f5aeL5pWw5o2u5L6b5YW25LuW55So6YCU5L2/55SoDQogICAgICAgICAgdGhpcy5kYW5nZXJvdXNQcm9qZWN0c0xpc3QgPSB0b3BQcm9qZWN0cy5tYXAoKGl0ZW0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUgfHwgIuacquefpemhueebriIsDQogICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSB8fCAwLA0KICAgICAgICAgICAgb3JpZ2luYWxOYW1lOiBpdGVtLm5hbWUsDQogICAgICAgICAgICBkZXRhbExpc3Q6IGl0ZW0uZGV0YWxMaXN0IHx8IFtdLA0KICAgICAgICAgIH0pKTsNCg0KICAgICAgICAgIC8vIOWIneWni+WMluWbvuihqA0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuaW5pdERhbmdlcm91c1Byb2plY3RDaGFydCgpOw0KICAgICAgICAgIH0pOw0KDQogICAgICAgICAgY29uc29sZS5sb2coIuWNseWkp+W3peeoi+e7n+iuoeaVsOaNruWKoOi9veaIkOWKnzoiLCB0aGlzLmRhbmdlcm91c1Byb2plY3RDaGFydCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCLljbHlpKflt6XnqIvnu5/orqHmjqXlj6Pov5Tlm57mlbDmja7moLzlvI/lvILluLg6IiwgcmVzcG9uc2UpOw0KICAgICAgICAgIHRoaXMuaGFuZGxlRGFuZ2Vyb3VzUHJvRGF0YUxvYWRFcnJvcigpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bljbHlpKflt6XnqIvnu5/orqHmlbDmja7lpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLmhhbmRsZURhbmdlcm91c1Byb0RhdGFMb2FkRXJyb3IoKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aSE55CG5Y2x5aSn5bel56iL57uf6K6h5pWw5o2u5Yqg6L296ZSZ6K+vDQogICAgaGFuZGxlRGFuZ2Vyb3VzUHJvRGF0YUxvYWRFcnJvcigpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLkvb/nlKjljbHlpKflt6XnqIvnu5/orqHpu5jorqTmlbDmja4iKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuWNseWkp+W3peeoi+e7n+iuoeaVsOaNruWKoOi9veWksei0pe+8jOaYvuekuum7mOiupOaVsOaNriIpOw0KICAgICAgLy8g5L2/55So6buY6K6k5pWw5o2u5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdERhbmdlcm91c1Byb2plY3RDaGFydCgpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOWIneWni+WMluWNseWkp+W3peeoi+WbvuihqA0KICAgIGluaXREYW5nZXJvdXNQcm9qZWN0Q2hhcnQoKSB7DQogICAgICBpZiAodGhpcy4kcmVmcy5kYW5nZXJvdXNQcm9qZWN0Q2hhcnQpIHsNCiAgICAgICAgLy8g6ZSA5q+B546w5pyJ5a6e5L6LDQogICAgICAgIGlmICh0aGlzLmRhbmdlcm91c1Byb2plY3RDaGFydEluc3RhbmNlKSB7DQogICAgICAgICAgdGhpcy5kYW5nZXJvdXNQcm9qZWN0Q2hhcnRJbnN0YW5jZS5kaXNwb3NlKCk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDliJvlu7rmlrDnmoTlm77ooajlrp7kvosNCiAgICAgICAgdGhpcy5kYW5nZXJvdXNQcm9qZWN0Q2hhcnRJbnN0YW5jZSA9IGVjaGFydHMuaW5pdCgNCiAgICAgICAgICB0aGlzLiRyZWZzLmRhbmdlcm91c1Byb2plY3RDaGFydA0KICAgICAgICApOw0KDQogICAgICAgIC8vIOiuvue9ruWbvuihqOmAiemhuQ0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgZ3JpZDogdGhpcy5kYW5nZXJvdXNQcm9qZWN0Q2hhcnQuZ3JpZCwNCiAgICAgICAgICB4QXhpczogdGhpcy5kYW5nZXJvdXNQcm9qZWN0Q2hhcnQueEF4aXMsDQogICAgICAgICAgeUF4aXM6IHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0LnlBeGlzLA0KICAgICAgICAgIHNlcmllczogdGhpcy5kYW5nZXJvdXNQcm9qZWN0Q2hhcnQuc2VyaWVzLA0KICAgICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICB0cmlnZ2VyOiAiaXRlbSIsDQogICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICIjZmZmIiwNCiAgICAgICAgICAgIGJvcmRlckNvbG9yOiAiI2NjYyIsDQogICAgICAgICAgICBib3JkZXJXaWR0aDogMSwNCiAgICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogIiMzMzMiLA0KICAgICAgICAgICAgICBmb250U2l6ZTogMTIsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZXh0cmFDc3NUZXh0Og0KICAgICAgICAgICAgICAiYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwwLDAsMC4xNSk7IG1heC13aWR0aDogNDAwcHg7IHdoaXRlLXNwYWNlOiBub3JtYWw7IiwNCiAgICAgICAgICAgIHBvc2l0aW9uOiBmdW5jdGlvbiAocG9pbnQsIHBhcmFtcywgZG9tLCByZWN0LCBzaXplKSB7DQogICAgICAgICAgICAgIC8vIOiOt+WPliB0b29sdGlwIOeahOWunumZheWwuuWvuA0KICAgICAgICAgICAgICBjb25zdCB0b29sdGlwV2lkdGggPSBzaXplLmNvbnRlbnRTaXplWzBdOw0KICAgICAgICAgICAgICBjb25zdCB0b29sdGlwSGVpZ2h0ID0gc2l6ZS5jb250ZW50U2l6ZVsxXTsNCg0KICAgICAgICAgICAgICAvLyDlm77ooajlrrnlmajlsLrlr7gNCiAgICAgICAgICAgICAgY29uc3QgY2hhcnRXaWR0aCA9IHJlY3Qud2lkdGg7DQogICAgICAgICAgICAgIGNvbnN0IGNoYXJ0SGVpZ2h0ID0gcmVjdC5oZWlnaHQ7DQoNCiAgICAgICAgICAgICAgLy8g6K6h566X5Y+z6L655ZKM5bem6L6555qE5Y+v55So56m66Ze0DQogICAgICAgICAgICAgIGNvbnN0IHJpZ2h0U3BhY2UgPSBjaGFydFdpZHRoIC0gcG9pbnRbMF0gLSAxMDsNCiAgICAgICAgICAgICAgY29uc3QgbGVmdFNwYWNlID0gcG9pbnRbMF0gLSAxMDsNCg0KICAgICAgICAgICAgICBsZXQgeCwgeTsNCg0KICAgICAgICAgICAgICAvLyDkvJjlhYjpgInmi6nnqbrpl7Tmm7TlpKfnmoTkuIDovrnvvIzkvYbnoa7kv53kuI3kvJrooqvpga7mjKENCiAgICAgICAgICAgICAgaWYgKHJpZ2h0U3BhY2UgPj0gdG9vbHRpcFdpZHRoIHx8IHJpZ2h0U3BhY2UgPiBsZWZ0U3BhY2UpIHsNCiAgICAgICAgICAgICAgICAvLyDmmL7npLrlnKjlj7PovrkNCiAgICAgICAgICAgICAgICB4ID0gcG9pbnRbMF0gKyAxMDsNCiAgICAgICAgICAgICAgICAvLyDlpoLmnpzlj7PovrnnnJ/nmoTmlL7kuI3kuIvvvIzlsJ3or5XosIPmlbTliLDlm77ooajlj7PovrnnlYzlhoUNCiAgICAgICAgICAgICAgICBpZiAoeCArIHRvb2x0aXBXaWR0aCA+IGNoYXJ0V2lkdGgpIHsNCiAgICAgICAgICAgICAgICAgIHggPSBjaGFydFdpZHRoIC0gdG9vbHRpcFdpZHRoIC0gNTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0gZWxzZSBpZiAobGVmdFNwYWNlID49IHRvb2x0aXBXaWR0aCkgew0KICAgICAgICAgICAgICAgIC8vIOaYvuekuuWcqOW3pui+ue+8jOS9huehruS/neaciei2s+Wkn+epuumXtA0KICAgICAgICAgICAgICAgIHggPSBwb2ludFswXSAtIHRvb2x0aXBXaWR0aCAtIDEwOw0KICAgICAgICAgICAgICAgIC8vIOehruS/neS4jeS8mui2heWHuuW3pui+ueeVjA0KICAgICAgICAgICAgICAgIGlmICh4IDwgMCkgew0KICAgICAgICAgICAgICAgICAgeCA9IDU7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIC8vIOWmguaenOS4pOi+uemDveaUvuS4jeS4i++8jOmAieaLqeWPs+i+ueW5tuW8uuWItuaYvuekuuWcqOWbvuihqOWGhQ0KICAgICAgICAgICAgICAgIHggPSBNYXRoLm1heCgNCiAgICAgICAgICAgICAgICAgIDUsDQogICAgICAgICAgICAgICAgICBNYXRoLm1pbihwb2ludFswXSArIDEwLCBjaGFydFdpZHRoIC0gdG9vbHRpcFdpZHRoIC0gNSkNCiAgICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g5Z6C55u05bGF5Lit77yM5L2G56Gu5L+d5LiN6LaF5Ye66L6555WMDQogICAgICAgICAgICAgIHkgPSBwb2ludFsxXSAtIHRvb2x0aXBIZWlnaHQgLyAyOw0KICAgICAgICAgICAgICBpZiAoeSA8IDEwKSB7DQogICAgICAgICAgICAgICAgeSA9IDEwOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKHkgKyB0b29sdGlwSGVpZ2h0ID4gY2hhcnRIZWlnaHQgLSAxMCkgew0KICAgICAgICAgICAgICAgIHkgPSBjaGFydEhlaWdodCAtIHRvb2x0aXBIZWlnaHQgLSAxMDsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIHJldHVybiBbeCwgeV07DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZm9ybWF0dGVyOiAocGFyYW1zKSA9PiB7DQogICAgICAgICAgICAgIGlmIChwYXJhbXMuZGF0YSAmJiB0eXBlb2YgcGFyYW1zLmRhdGEgPT09ICJvYmplY3QiKSB7DQogICAgICAgICAgICAgICAgY29uc3QgeyBmdWxsTmFtZSwgdmFsdWUsIGRldGFsTGlzdCB9ID0gcGFyYW1zLmRhdGE7DQoNCiAgICAgICAgICAgICAgICBsZXQgaHRtbCA9IGA8ZGl2IHN0eWxlPSJmb250LXdlaWdodDogYm9sZDsgbWFyZ2luLWJvdHRvbTogOHB4OyBjb2xvcjogIzMzMzsiPiR7ZnVsbE5hbWV9PC9kaXY+YDsNCiAgICAgICAgICAgICAgICBodG1sICs9IGA8ZGl2IHN0eWxlPSJtYXJnaW4tYm90dG9tOiA4cHg7Ij7mgLvmlbDph486IDxzcGFuIHN0eWxlPSJjb2xvcjogIzE4OTBmZjsgZm9udC13ZWlnaHQ6IGJvbGQ7Ij4ke3ZhbHVlfTwvc3Bhbj48L2Rpdj5gOw0KDQogICAgICAgICAgICAgICAgaWYgKGRldGFsTGlzdCAmJiBkZXRhbExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgICAgaHRtbCArPQ0KICAgICAgICAgICAgICAgICAgICAnPGRpdiBzdHlsZT0iYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlZWU7IHBhZGRpbmctdG9wOiA4cHg7Ij4nOw0KICAgICAgICAgICAgICAgICAgaHRtbCArPQ0KICAgICAgICAgICAgICAgICAgICAnPGRpdiBzdHlsZT0iZm9udC13ZWlnaHQ6IGJvbGQ7IG1hcmdpbi1ib3R0b206IDZweDsgY29sb3I6ICM2NjY7Ij7or6bnu4bkv6Hmga86PC9kaXY+JzsNCiAgICAgICAgICAgICAgICAgIGRldGFsTGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIGh0bWwgKz0gYDxkaXYgc3R5bGU9Im1hcmdpbi1ib3R0b206IDRweDsgcGFkZGluZy1sZWZ0OiA4cHg7IGJvcmRlci1sZWZ0OiAycHggc29saWQgIzE4OTBmZjsiPg0KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImZvbnQtd2VpZ2h0OiA1MDA7Ij4kew0KICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5uYW1lIHx8ICLmnKrnn6XnsbvlnosiDQogICAgICAgICAgICAgICAgICAgICAgfTwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImNvbG9yOiAjNjY2OyBmb250LXNpemU6IDExcHg7Ij7mlbDph486ICR7DQogICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnZhbHVlIHx8IDANCiAgICAgICAgICAgICAgICAgICAgICB9PC9kaXY+DQogICAgICAgICAgICAgICAgICAgIDwvZGl2PmA7DQogICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgIGh0bWwgKz0gIjwvZGl2PiI7DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIGh0bWwgKz0NCiAgICAgICAgICAgICAgICAgICAgJzxkaXYgc3R5bGU9ImNvbG9yOiAjOTk5OyBmb250LXN0eWxlOiBpdGFsaWM7Ij7mmoLml6Dor6bnu4bkv6Hmga88L2Rpdj4nOw0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIHJldHVybiBodG1sOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g5YWc5bqV5pi+56S6DQogICAgICAgICAgICAgIHJldHVybiBg6aG555uuOiAke3BhcmFtcy5uYW1lfTxici8+5pWw6YePOiAke3BhcmFtcy52YWx1ZX1gOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0SW5zdGFuY2Uuc2V0T3B0aW9uKG9wdGlvbik7DQoNCiAgICAgICAgLy8g6Ieq6YCC5bqU5aSn5bCPDQogICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCJyZXNpemUiLCAoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0SW5zdGFuY2UpIHsNCiAgICAgICAgICAgIHRoaXMuZGFuZ2Vyb3VzUHJvamVjdENoYXJ0SW5zdGFuY2UucmVzaXplKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W5aSn5Z6L6K6+5aSH57uf6K6h5pWw5o2uDQogICAgYXN5bmMgbG9hZExhcmdlRXF1aXBtZW50U3RhdGlzdGljcygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCLlvIDlp4vliqDovb3lpKflnovorr7lpIfnu5/orqHmlbDmja4uLi4iKTsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRMYXJnZUVxdWlwbWVudFN0YXRpc3RpY3MoKTsNCg0KICAgICAgICBpZiAoDQogICAgICAgICAgcmVzcG9uc2UgJiYNCiAgICAgICAgICByZXNwb25zZS5jb2RlID09PSAyMDAgJiYNCiAgICAgICAgICByZXNwb25zZS5kYXRhICYmDQogICAgICAgICAgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKQ0KICAgICAgICApIHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YTsNCg0KICAgICAgICAgIC8vIOaPkOWPluiuvuWkh+WQjeensOS9nOS4uljovbTnsbvliKsNCiAgICAgICAgICBjb25zdCBlcXVpcG1lbnROYW1lcyA9IGRhdGEubWFwKChpdGVtKSA9PiBpdGVtLm5hbWUgfHwgIuacquefpeiuvuWkhyIpOw0KDQogICAgICAgICAgLy8g5o+Q5Y+W6K6+5aSH5pWw6YeP77yM5YWo6YOo6K6+5Li6Iui/kOihjOS4rSLnirbmgIENCiAgICAgICAgICBjb25zdCBydW5uaW5nRGF0YSA9IGRhdGEubWFwKChpdGVtKSA9PiBpdGVtLnZhbHVlIHx8IDApOw0KICAgICAgICAgIC8vIOWKqOaAgeiuoeeul1novbTmnIDlpKflgLzvvIznm7TmjqXkvb/nlKjmjqXlj6PmlbDmja7nmoTmnIDlpKflgLwNCiAgICAgICAgICBjb25zdCBtYXhWYWx1ZSA9IE1hdGgubWF4KC4uLnJ1bm5pbmdEYXRhKTsNCiAgICAgICAgICBjb25zdCB5QXhpc01heCA9IG1heFZhbHVlID4gMCA/IG1heFZhbHVlIDogMTA7IC8vIFnovbTkuIrpmZDlsLHmmK/mjqXlj6Pph4zmnIDlpKfnmoTmlbDmja4NCg0KICAgICAgICAgIC8vIOabtOaWsOWkp+Wei+iuvuWkh+WbvuihqOmFjee9rg0KICAgICAgICAgIHRoaXMubGFyZ2VFcXVpcG1lbnRDaGFydCA9IHsNCiAgICAgICAgICAgIGNvbG9yTGlzdDogWw0KICAgICAgICAgICAgICAiI0ZGOTIwRCIsDQogICAgICAgICAgICAgICIjRkVDRjc3IiwNCiAgICAgICAgICAgICAgIiNGRjc3MzAiLA0KICAgICAgICAgICAgICAiIzU0QzI1NSIsDQogICAgICAgICAgICAgICIjMjY1NkY1IiwNCiAgICAgICAgICAgICAgIiMyQzJDMkMiLA0KICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICAgICAgdG9wOiAzMCwNCiAgICAgICAgICAgICAgbGVmdDogIjglIiwNCiAgICAgICAgICAgICAgcmlnaHQ6ICI4JSIsDQogICAgICAgICAgICAgIGJvdHRvbTogIjI1JSIsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGVnZW5kOiB1bmRlZmluZWQsIC8vIOaYvuW8j+enu+mZpOWbvuS+iw0KICAgICAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICAgICAgdHlwZTogImNhdGVnb3J5IiwNCiAgICAgICAgICAgICAgZGF0YTogZXF1aXBtZW50TmFtZXMsDQogICAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICAgIGludGVydmFsOiAwLA0KICAgICAgICAgICAgICAgIHJvdGF0ZTogMCwNCiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTIsDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICAgICAgdHlwZTogInZhbHVlIiwNCiAgICAgICAgICAgICAgbWF4OiB5QXhpc01heCA+IDAgPyB5QXhpc01heCA6IDEwLA0KICAgICAgICAgICAgICBtaW46IDAsDQogICAgICAgICAgICAgIGludGVydmFsOiB5QXhpc01heCA+IDAgPyBNYXRoLmNlaWwoeUF4aXNNYXggLyA1KSA6IDIsIC8vIOWwhlnovbTnrYnliIbkuLo15Liq5Yy66Ze077yM56Gu5L+d6Ze06ZqU5Li65pW05pWwDQogICAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMiwNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIG5hbWU6ICLmnKrlronoo4UiLA0KICAgICAgICAgICAgICAgIHR5cGU6ICJiYXIiLA0KICAgICAgICAgICAgICAgIHN0YWNrOiAidG90YWwiLA0KICAgICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogIiNGRjkyMEQiIH0sDQogICAgICAgICAgICAgICAgZGF0YTogbmV3IEFycmF5KGRhdGEubGVuZ3RoKS5maWxsKDApLCAvLyDlhajpg6jorr7kuLowDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICBuYW1lOiAi5a6J6KOF5LitIiwNCiAgICAgICAgICAgICAgICB0eXBlOiAiYmFyIiwNCiAgICAgICAgICAgICAgICBzdGFjazogInRvdGFsIiwNCiAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICIjRkVDRjc3IiB9LA0KICAgICAgICAgICAgICAgIGRhdGE6IG5ldyBBcnJheShkYXRhLmxlbmd0aCkuZmlsbCgwKSwgLy8g5YWo6YOo6K6+5Li6MA0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgbmFtZTogIumqjOaUtuS4rSIsDQogICAgICAgICAgICAgICAgdHlwZTogImJhciIsDQogICAgICAgICAgICAgICAgc3RhY2s6ICJ0b3RhbCIsDQogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAiI0ZGNzczMCIgfSwNCiAgICAgICAgICAgICAgICBkYXRhOiBuZXcgQXJyYXkoZGF0YS5sZW5ndGgpLmZpbGwoMCksIC8vIOWFqOmDqOiuvuS4ujANCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIG5hbWU6ICLov5DooYzkuK0iLA0KICAgICAgICAgICAgICAgIHR5cGU6ICJiYXIiLA0KICAgICAgICAgICAgICAgIHN0YWNrOiAidG90YWwiLA0KICAgICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogIiM1NEMyNTUiIH0sDQogICAgICAgICAgICAgICAgZGF0YTogcnVubmluZ0RhdGEsIC8vIOS9v+eUqOecn+WunuaVsOaNrg0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgbmFtZTogIue7tOS/ruS4rSIsDQogICAgICAgICAgICAgICAgdHlwZTogImJhciIsDQogICAgICAgICAgICAgICAgc3RhY2s6ICJ0b3RhbCIsDQogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAiIzI2NTZGNSIgfSwNCiAgICAgICAgICAgICAgICBkYXRhOiBuZXcgQXJyYXkoZGF0YS5sZW5ndGgpLmZpbGwoMCksIC8vIOWFqOmDqOiuvuS4ujANCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIG5hbWU6ICLlt7LmiqXlup8iLA0KICAgICAgICAgICAgICAgIHR5cGU6ICJiYXIiLA0KICAgICAgICAgICAgICAgIHN0YWNrOiAidG90YWwiLA0KICAgICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogIiMyQzJDMkMiIH0sDQogICAgICAgICAgICAgICAgZGF0YTogbmV3IEFycmF5KGRhdGEubGVuZ3RoKS5maWxsKDApLCAvLyDlhajpg6jorr7kuLowDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICBdLA0KICAgICAgICAgIH07DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygi5aSn5Z6L6K6+5aSH57uf6K6h5pWw5o2u5Yqg6L295oiQ5YqfOiIsIHRoaXMubGFyZ2VFcXVpcG1lbnRDaGFydCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCLlpKflnovorr7lpIfnu5/orqHmjqXlj6Pov5Tlm57mlbDmja7moLzlvI/lvILluLg6IiwgcmVzcG9uc2UpOw0KICAgICAgICAgIHRoaXMuaGFuZGxlTGFyZ2VFcXVpcG1lbnREYXRhTG9hZEVycm9yKCk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWkp+Wei+iuvuWkh+e7n+iuoeaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuaGFuZGxlTGFyZ2VFcXVpcG1lbnREYXRhTG9hZEVycm9yKCk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuWkp+Wei+iuvuWkh+e7n+iuoeaVsOaNruWKoOi9vemUmeivrw0KICAgIGhhbmRsZUxhcmdlRXF1aXBtZW50RGF0YUxvYWRFcnJvcigpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLkvb/nlKjlpKflnovorr7lpIfnu5/orqHpu5jorqTmlbDmja4iKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuWkp+Wei+iuvuWkh+e7n+iuoeaVsOaNruWKoOi9veWksei0pe+8jOaYvuekuum7mOiupOaVsOaNriIpOw0KICAgIH0sDQoNCiAgICAvLyDlvILmraXliqDovb3orr7lpIfor6bnu4bkv6Hmga8NCiAgICBhc3luYyBsb2FkRXF1aXBtZW50RGV0YWlscyhlcXVpcG1lbnROYW1lKSB7DQogICAgICAvLyDlpoLmnpzlt7Lnu4/mnInnvJPlrZjvvIznm7TmjqXov5Tlm54NCiAgICAgIGlmICh0aGlzLmVxdWlwbWVudERldGFpbHNDYWNoZVtlcXVpcG1lbnROYW1lXSkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnNvbGUubG9nKCLlvIDlp4vliqDovb3orr7lpIfor6bnu4bkv6Hmga86IiwgZXF1aXBtZW50TmFtZSk7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0TGFyZ2VFcXVpcG1lbnRCeU5hbWVTdGF0aXN0aWNzKGVxdWlwbWVudE5hbWUpOw0KDQogICAgICAgIGlmICgNCiAgICAgICAgICByZXNwb25zZSAmJg0KICAgICAgICAgIHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJg0KICAgICAgICAgIHJlc3BvbnNlLmRhdGEgJiYNCiAgICAgICAgICBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpDQogICAgICAgICkgew0KICAgICAgICAgIC8vIOe8k+WtmOivpue7huaVsOaNrg0KICAgICAgICAgIHRoaXMuZXF1aXBtZW50RGV0YWlsc0NhY2hlW2VxdWlwbWVudE5hbWVdID0gcmVzcG9uc2UuZGF0YS5tYXAoDQogICAgICAgICAgICAoaXRlbSkgPT4gKHsNCiAgICAgICAgICAgICAgbmFtZTogaXRlbS5uYW1lLA0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwNCiAgICAgICAgICAgICAgZXF1aXBtZW50X25hbWU6IGl0ZW0uZXF1aXBtZW50X25hbWUsDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICk7DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygNCiAgICAgICAgICAgICLorr7lpIfor6bnu4bkv6Hmga/liqDovb3miJDlip86IiwNCiAgICAgICAgICAgIGVxdWlwbWVudE5hbWUsDQogICAgICAgICAgICB0aGlzLmVxdWlwbWVudERldGFpbHNDYWNoZVtlcXVpcG1lbnROYW1lXQ0KICAgICAgICAgICk7DQoNCiAgICAgICAgICAvLyDmm7TmlrDlvZPliY3mgqzmta7moYbnmoTmlbDmja4NCiAgICAgICAgICBpZiAoDQogICAgICAgICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAudmlzaWJsZSAmJg0KICAgICAgICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLnRpdGxlID09PSBlcXVpcG1lbnROYW1lDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLmRhdGEgPQ0KICAgICAgICAgICAgICB0aGlzLmVxdWlwbWVudERldGFpbHNDYWNoZVtlcXVpcG1lbnROYW1lXTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g6K6+572u56m65pWw5o2u6YG/5YWN6YeN5aSN6K+35rGCDQogICAgICAgICAgdGhpcy5lcXVpcG1lbnREZXRhaWxzQ2FjaGVbZXF1aXBtZW50TmFtZV0gPSBbXTsNCiAgICAgICAgICBjb25zb2xlLndhcm4oIuiuvuWkh+ivpue7huS/oeaBr+aOpeWPo+i/lOWbnuaVsOaNruagvOW8j+W8guW4uDoiLCByZXNwb25zZSk7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLnZpc2libGUgJiYNCiAgICAgICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC50aXRsZSA9PT0gZXF1aXBtZW50TmFtZQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC5lcnJvciA9ICLmlbDmja7moLzlvI/lvILluLgiOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgLy8g6K6+572u56m65pWw5o2u6YG/5YWN6YeN5aSN6K+35rGCDQogICAgICAgIHRoaXMuZXF1aXBtZW50RGV0YWlsc0NhY2hlW2VxdWlwbWVudE5hbWVdID0gW107DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluiuvuWkh+ivpue7huS/oeaBr+Wksei0pToiLCBlcnJvcik7DQogICAgICAgIGlmICgNCiAgICAgICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAudmlzaWJsZSAmJg0KICAgICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC50aXRsZSA9PT0gZXF1aXBtZW50TmFtZQ0KICAgICAgICApIHsNCiAgICAgICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC5lcnJvciA9ICLliqDovb3lpLHotKUiOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuiuvuWkh+WbvuihqOm8oOagh+enu+WKqA0KICAgIGhhbmRsZUVxdWlwbWVudE1vdXNlTW92ZShldmVudCkgew0KICAgICAgLy8g6I635Y+W6byg5qCH5L2N572uDQogICAgICBjb25zdCByZWN0ID0gZXZlbnQuY3VycmVudFRhcmdldC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTsNCiAgICAgIGNvbnN0IHggPSBldmVudC5jbGllbnRYIC0gcmVjdC5sZWZ0Ow0KICAgICAgY29uc3QgY2hhcnRXaWR0aCA9IHJlY3Qud2lkdGg7DQoNCiAgICAgIC8vIOagueaNrum8oOagh+S9jee9ruaOqOaWreiuvuWkh++8iOeugOWMluWunueOsO+8iQ0KICAgICAgY29uc3QgZXF1aXBtZW50TmFtZXMgPSB0aGlzLmxhcmdlRXF1aXBtZW50Q2hhcnQueEF4aXM/LmRhdGEgfHwgW107DQogICAgICBpZiAoZXF1aXBtZW50TmFtZXMubGVuZ3RoID09PSAwKSByZXR1cm47DQoNCiAgICAgIGNvbnN0IGVxdWlwbWVudEluZGV4ID0gTWF0aC5mbG9vcigNCiAgICAgICAgKHggLyBjaGFydFdpZHRoKSAqIGVxdWlwbWVudE5hbWVzLmxlbmd0aA0KICAgICAgKTsNCiAgICAgIGlmIChlcXVpcG1lbnRJbmRleCA+PSAwICYmIGVxdWlwbWVudEluZGV4IDwgZXF1aXBtZW50TmFtZXMubGVuZ3RoKSB7DQogICAgICAgIGNvbnN0IGVxdWlwbWVudE5hbWUgPSBlcXVpcG1lbnROYW1lc1tlcXVpcG1lbnRJbmRleF07DQoNCiAgICAgICAgLy8g5pi+56S65oKs5rWu5qGGDQogICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC52aXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLnggPSBldmVudC5jbGllbnRYICsgMTA7DQogICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC55ID0gZXZlbnQuY2xpZW50WSAtIDEwOw0KICAgICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAudGl0bGUgPSBlcXVpcG1lbnROYW1lOw0KDQogICAgICAgIC8vIOajgOafpee8k+WtmOaVsOaNrg0KICAgICAgICBjb25zdCBjYWNoZWREYXRhID0gdGhpcy5lcXVpcG1lbnREZXRhaWxzQ2FjaGVbZXF1aXBtZW50TmFtZV07DQogICAgICAgIGlmIChjYWNoZWREYXRhKSB7DQogICAgICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAuZXJyb3IgPSAiIjsNCiAgICAgICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAuZGF0YSA9IGNhY2hlZERhdGE7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC5lcnJvciA9ICIiOw0KICAgICAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC5kYXRhID0gW107DQoNCiAgICAgICAgICAvLyDliqDovb3or6bnu4bmlbDmja4NCiAgICAgICAgICB0aGlzLmxvYWRFcXVpcG1lbnREZXRhaWxzKGVxdWlwbWVudE5hbWUpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuiuvuWkh+WbvuihqOm8oOagh+emu+W8gA0KICAgIGhhbmRsZUVxdWlwbWVudE1vdXNlTGVhdmUoKSB7DQogICAgICB0aGlzLmVxdWlwbWVudFRvb2x0aXAudmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLmRhdGEgPSBbXTsNCiAgICAgIHRoaXMuZXF1aXBtZW50VG9vbHRpcC5lcnJvciA9ICIiOw0KICAgICAgdGhpcy5lcXVpcG1lbnRUb29sdGlwLmxvYWRpbmcgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgLy8g5oiq5Y+W5YWs5Y+45ZCN56ew5pi+56S6DQogICAgdHJ1bmNhdGVDb21wYW55TmFtZShjb21wYW55TmFtZSwgbWF4TGVuZ3RoID0gMjApIHsNCiAgICAgIGlmICghY29tcGFueU5hbWUpIHJldHVybiAiIjsNCiAgICAgIGlmIChjb21wYW55TmFtZS5sZW5ndGggPD0gbWF4TGVuZ3RoKSByZXR1cm4gY29tcGFueU5hbWU7DQogICAgICByZXR1cm4gY29tcGFueU5hbWUuc3Vic3RyaW5nKDAsIG1heExlbmd0aCkgKyAiLi4uIjsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8aA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"app-container home bg\">\r\n    <!-- 顶部统计卡片 -->\r\n    <div class=\"top-stats\">\r\n      <!-- 超危大工程 -->\r\n      <div class=\"stat-card stat-card-1\">\r\n        <div class=\"stat-header\">\r\n          <h3>超危大工程</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">超危工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.superDangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">危大工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.dangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 安全生产 -->\r\n      <div class=\"stat-card stat-card-2\">\r\n        <div class=\"stat-header\">\r\n          <h3>安全生产</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 在建项目 -->\r\n      <div class=\"stat-card stat-card-3\">\r\n        <div class=\"stat-header\">\r\n          <h3>在建项目</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 第二行统计卡片 -->\r\n    <div class=\"second-stats\">\r\n      <div class=\"stat-card-small stat-card-small-1\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-office-building\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">企业分支</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.companyBranches }}</span>\r\n            <span class=\"stat-unit-small\">个</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-2\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">伤亡事故人数</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.casualties }}</span>\r\n            <span class=\"stat-unit-small\">人</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-3\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning-outline\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">重大风险</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.majorHazards }}</span>\r\n            <span class=\"stat-unit-small\">项</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-4\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-cpu\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">大型设备数量</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.largeEquipment }}</span>\r\n            <span class=\"stat-unit-small\">台</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 第三行：超危大工程和安全生产投入 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>超危大工程</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"dangerous-chart-container\">\r\n              <div\r\n                ref=\"dangerousProjectChart\"\r\n                class=\"chart-container\"\r\n                style=\"height: 280px\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>安全生产投入</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"safety-investment-container investment-container-enhanced\">\r\n              <!-- 左侧环形图区域 -->\r\n              <div class=\"chart-section pie-chart-section\">\r\n                <div class=\"chart-wrapper\">\r\n                  <pieChart \r\n                    height=\"280px\" \r\n                    :data=\"safetyInvestmentPieChart\" \r\n                    :showCenterText=\"true\"\r\n                    :centerText=\"safetyInvestmentTotal\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <!-- 右侧数据列表区域 -->\r\n              <div class=\"data-list-section project-list-section\">\r\n                <div class=\"data-list project-list\">\r\n                  <div \r\n                    v-for=\"(item, index) in safetyInvestmentProjects\" \r\n                    :key=\"index\"\r\n                    class=\"safety-investment-item project-item\"\r\n                    @mouseenter=\"handleItemHover(item, $event)\"\r\n                    @mouseleave=\"hideTooltip\"\r\n                  >\r\n                    <div class=\"data-indicator project-dot\" :style=\"{backgroundColor: item.color}\"></div>\r\n                    <div class=\"data-info project-info\">\r\n                      <div class=\"data-label project-name\">{{ item.name }}</div>\r\n                      <div class=\"data-amount project-amount\">{{ item.value }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 底部：安全管理和质量管理 -->\r\n    <el-row :gutter=\"20\" class=\"management-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>安全管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 2 }\"\r\n                  @click=\"changeSafetyTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 1 }\"\r\n                  @click=\"changeSafetyTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 第一行统计数据 - 按设计稿样式 -->\r\n           <div style=\"background-color: #F5F7F9;padding: 10px;\" >\r\n          <div class=\"safety-top-stats-simple\">\r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"安全隐患数\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">安全隐患数</span>\r\n              <span class=\"safety-number\">{{ safetyManagement.hazardCount }}</span>\r\n              <span class=\"safety-unit\">个</span>\r\n            </div>\r\n            \r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"按时整改率\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">按时整改率</span>\r\n              <span class=\"safety-number\">{{ safetyManagement.reportOnTimeRate }}</span>\r\n              <span class=\"safety-unit\">%</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 第二行统计数据 - 一行显示 -->\r\n          <div class=\"safety-second-stats\" style=\"background-color: #fff;padding: 5px;\">\r\n            <div class=\"stats-row-single\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">待整改</span>\r\n                <span class=\"stat-value\">{{ safetyManagement.safetyPendingProblems || 4 }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">已整改</span>\r\n                <span class=\"stat-value\">{{ safetyManagement.safetyFixedProblems || 4 }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">已合格</span>\r\n                <span class=\"stat-value\">50</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">整改率</span>\r\n                <span class=\"stat-value\">{{ safetyManagement.reportOnTimeRate }}</span>\r\n                <span class=\"stat-unit\">%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 饼状图区域 -->\r\n          <div class=\"safety-chart-area\">\r\n            <pieChart\r\n              :data=\"safetyManagement.hazardTypeChart2\"\r\n              height=\"180px\"\r\n              :showCenterText=\"true\"\r\n              :centerText=\"{\r\n                value: safetyManagement.hazardCount.toString(),\r\n                unit: '安全隐患总数',\r\n                label: ''\r\n              }\"\r\n            />\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>质量管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <!-- timeType=1  月  2：年 -->\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 2 }\"\r\n                  @click=\"changeQualityTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 1 }\"\r\n                  @click=\"changeQualityTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div style=\"background-color: #F5F7F9;padding: 10px;\" >\r\n          <!-- 第一行统计数据 - 按安全管理样式 -->\r\n          <div class=\"safety-top-stats-simple\">\r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"质量问题数\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">质量问题数</span>\r\n              <span class=\"safety-number\">{{ qualityManagement.foundProblems + qualityManagement.selfReportProblems }}</span>\r\n              <span class=\"safety-unit\">个</span>\r\n            </div>\r\n            \r\n            <div class=\"safety-stat-item-simple\">\r\n              <img src=\"@/assets/images/home/<USER>\" alt=\"按时整改率\" class=\"safety-icon-simple\" />\r\n              <span class=\"safety-label\">按时整改率</span>\r\n              <span class=\"safety-number\">{{ Math.round((qualityManagement.onTimeRate + qualityManagement.selfReportRate) / 2) }}</span>\r\n              <span class=\"safety-unit\">%</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 第二行统计数据 - 一行显示 -->\r\n          <div class=\"safety-second-stats\" style=\"background-color: #fff;padding: 5px;\">\r\n            <div class=\"stats-row-single\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">待整改</span>\r\n                <span class=\"stat-value\">{{ qualityManagement.pendingProblems + qualityManagement.selfReportPending }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">已整改</span>\r\n                <span class=\"stat-value\">{{ qualityManagement.fixedProblems + qualityManagement.selfReportFixed }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">检查计划</span>\r\n                <span class=\"stat-value\">{{ qualityManagement.inspectionPlans }}</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\"></div>\r\n                <span class=\"stat-label\">整改率</span>\r\n                <span class=\"stat-value\">{{ Math.round((qualityManagement.onTimeRate + qualityManagement.selfReportRate) / 2) }}</span>\r\n                <span class=\"stat-unit\">%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 饼状图区域 - 质量管理图表 -->\r\n          <div class=\"safety-chart-area\">\r\n            <pieChart\r\n              :data=\"qualityManagement.qualityTypeChart\"\r\n              height=\"180px\"\r\n              :showCenterText=\"true\"\r\n              :centerText=\"{\r\n                value: (qualityManagement.foundProblems + qualityManagement.selfReportProblems).toString(),\r\n                unit: '质量问题总数',\r\n                label: ''\r\n              }\"\r\n            />\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 第五行：大型设备图表 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>大型设备</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div\r\n              class=\"equipment-chart-container\"\r\n              @mousemove=\"handleEquipmentMouseMove\"\r\n              @mouseleave=\"handleEquipmentMouseLeave\"\r\n            >\r\n              <barChart\r\n                height=\"300px\"\r\n                :data=\"largeEquipmentChart\"\r\n                :show-tooltip=\"false\"\r\n              />\r\n\r\n              <!-- 大型设备悬浮框 -->\r\n              <div\r\n                v-show=\"equipmentTooltip.visible\"\r\n                class=\"project-tooltip\"\r\n                :style=\"{\r\n                  left: equipmentTooltip.x + 'px',\r\n                  top: equipmentTooltip.y + 'px',\r\n                }\"\r\n              >\r\n                <div class=\"tooltip-header\">{{ equipmentTooltip.title }}</div>\r\n                <div v-if=\"equipmentTooltip.loading\" class=\"tooltip-loading\">\r\n                  加载中...\r\n                </div>\r\n                <div v-else-if=\"equipmentTooltip.error\" class=\"tooltip-error\">\r\n                  {{ equipmentTooltip.error }}\r\n                </div>\r\n                <div\r\n                  v-else-if=\"equipmentTooltip.data.length > 0\"\r\n                  class=\"tooltip-content\"\r\n                >\r\n                  <div\r\n                    v-for=\"item in equipmentTooltip.data\"\r\n                    :key=\"item.name\"\r\n                    class=\"tooltip-item\"\r\n                  >\r\n                    <span class=\"tooltip-name\">{{ item.name }}</span>\r\n                    <span class=\"tooltip-value\">{{ item.value }}</span>\r\n                  </div>\r\n                </div>\r\n                <div v-else class=\"tooltip-empty\">暂无详细数据</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport barChart from \"./components/barChart.vue\";\r\nimport pieChart from \"./components/pieChart.vue\";\r\nimport {\r\n  getManagementOverview,\r\n  getQualityStatistics,\r\n  getSafetyStatistics,\r\n  getDangerTypeStatistics,\r\n  getSafetyProductionStatistics,\r\n  getDangerousProStatistics,\r\n  getLargeEquipmentStatistics,\r\n  getLargeEquipmentByNameStatistics,\r\n} from \"@/api/statistics\";\r\nexport default {\r\n  name: \"Index\",\r\n  components: {\r\n    barChart,\r\n    pieChart,\r\n  },\r\n  data() {\r\n    return {\r\n      // 时间类型选择 (1-月, 2-年)\r\n      qualityTimeType: 2, // 默认为年\r\n      safetyTimeType: 2, // 默认为年\r\n      // 顶部统计数据\r\n      statsData: {\r\n        // 原有数据\r\n        domesticProjects: 1126,\r\n        internationalProjects: 1126,\r\n        safetyInvestment: 1500,\r\n        largeEquipment: 1126,\r\n        dangerousProjects: 1126,\r\n        // 新增数据\r\n        superDangerousProjects: 1126,\r\n        domesticSafetyInvestment: 1126,\r\n        internationalSafetyInvestment: 1126,\r\n        companyBranches: 1126,\r\n        casualties: 0,\r\n        majorHazards: 1126,\r\n      },\r\n      // 质量管理数据\r\n      qualityManagement: {\r\n        // 检查下发\r\n        inspectionPlans: 122,\r\n        dailyInspections: 89,\r\n        specialInspections: 33,\r\n        foundProblems: 31,\r\n        fixedProblems: 29,\r\n        pendingProblems: 2,\r\n        onTimeRate: 96,\r\n        onTimeFixed: 28,\r\n        overdueFixed: 1,\r\n        // 自主上报\r\n        selfReportProblems: 103,\r\n        selfReportFixed: 100,\r\n        selfReportPending: 3,\r\n        selfReportRate: 86.45,\r\n        selfReportOnTime: 31,\r\n        selfReportOverdue: 7,\r\n        // 质量类别统计图表\r\n        qualityTypeChart: {\r\n          colorList: ['#2656F5', '#FF920D', '#54C255', '#E54545', '#8EE98F'],\r\n          data: [\r\n            { value: 30, name: '施工质量' },\r\n            { value: 25, name: '材料质量' },\r\n            { value: 20, name: '工艺质量' },\r\n            { value: 15, name: '设备质量' },\r\n            { value: 10, name: '其他质量' }\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n      },\r\n      // 安全管理数据\r\n      safetyManagement: {\r\n        // 检查下发\r\n        inspectionCount: 58,\r\n        hazardFound: 20,\r\n        onTimeRate: 89,\r\n        // 自主上报\r\n        hazardCount: 103,\r\n        reportOnTimeRate: 86.45,\r\n        // 隐患类别统计图表\r\n        hazardTypeChart1: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#E54545\",\r\n            \"#FECF77\",\r\n            \"#FF7730\",\r\n            \"#B38DFF\",\r\n            \"#A1FFEB\",\r\n          ],\r\n          data: [\r\n            { value: 35, name: \"基础设施\" },\r\n            { value: 28, name: \"设备维护\" },\r\n            { value: 25, name: \"消防安全\" },\r\n            { value: 22, name: \"电气安全\" },\r\n            { value: 18, name: \"高空作业\" },\r\n            { value: 15, name: \"机械操作\" },\r\n            { value: 12, name: \"化学品管理\" },\r\n            { value: 10, name: \"个人防护\" },\r\n            { value: 8, name: \"环境卫生\" },\r\n            { value: 5, name: \"其他\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n        hazardTypeChart2: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n          ],\r\n          data: [\r\n            { value: 15, name: \"基础设施\" },\r\n            { value: 12, name: \"设备维护\" },\r\n            { value: 11, name: \"消防安全\" },\r\n            { value: 10, name: \"电气安全\" },\r\n            { value: 10, name: \"高空作业\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n      },\r\n      // 自主上报数据\r\n      selfReport: {\r\n        reportCount: 103,\r\n        completed: 100,\r\n        pending: 3,\r\n        onTimeRate: 86.45,\r\n        onTimeCompleted: 31,\r\n        overdueCompleted: 7,\r\n      },\r\n      // 统计分析数据\r\n      statisticsAnalysis: {\r\n        overallChart: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#A1FFEB\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#FF920D\",\r\n          ],\r\n          data: [\r\n            { value: 25, name: \"安全基础管理\" },\r\n            { value: 20, name: \"消防安全\" },\r\n            { value: 18, name: \"电气安全\" },\r\n            { value: 15, name: \"特种设备\" },\r\n            { value: 12, name: \"危化品\" },\r\n            { value: 10, name: \"其他\" },\r\n          ],\r\n        },\r\n      },\r\n      // 危大工程项目列表数据\r\n      dangerousProjectsList: [\r\n        { name: \"苏电产业科创园(NO.2010G32)07-13地块项目\", progress: 100 },\r\n        { name: \"未来出行产业园项目（直流分公司）\", progress: 80 },\r\n        { name: \"华为网络石代表处项目\", progress: 70 },\r\n        { name: \"年产3001万件汽车底盘等部件生产线项目\", progress: 30 },\r\n        { name: \"泪源城土壤生产及新客体验中心二期建设项目\", progress: 30 },\r\n      ],\r\n      // 危大工程图表配置\r\n      dangerousProjectChart: {\r\n        colorList: [\"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\"],\r\n        grid: {\r\n          top: 30,\r\n          left: \"35%\",\r\n          right: \"10%\",\r\n          bottom: \"5%\",\r\n        },\r\n        xAxis: {\r\n          type: \"value\",\r\n          max: 10,\r\n          axisLabel: {\r\n            show: false,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"苏电产业科创园(NO.2010G32) 07-13地块项目\",\r\n            \"未来出行产业园项目（直流分公司）\",\r\n            \"华为网络石代表处项目\",\r\n            \"年产3001万件汽车底盘等部件生产线项目\",\r\n            \"泪源城土壤生产及新客体验中心二期建设项目\",\r\n          ],\r\n          axisLabel: {\r\n            fontSize: 12,\r\n            color: \"#333\",\r\n            interval: 0,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"危大工程数量\",\r\n            type: \"bar\",\r\n            data: [10, 8, 7, 3, 3],\r\n            itemStyle: {\r\n              color: \"#5990FD\",\r\n              borderRadius: [0, 4, 4, 0],\r\n            },\r\n            barWidth: \"60%\",\r\n            label: {\r\n              show: true,\r\n              position: \"right\",\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n              formatter: \"{c}\",\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      largeEquipmentChart: {\r\n        colorList: [\r\n          \"#FF920D\",\r\n          \"#FECF77\",\r\n          \"#FF7730\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#2C2C2C\",\r\n        ],\r\n        grid: {\r\n          top: 30,\r\n          left: \"8%\",\r\n          right: \"8%\",\r\n          bottom: \"25%\",\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"设备分类1\",\r\n            \"设备分类2\",\r\n            \"设备分类3\",\r\n            \"设备分类4\",\r\n            \"设备分类5\",\r\n            \"设备分类6\",\r\n          ],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0,\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"value\",\r\n          max: 210,\r\n          axisLabel: {\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"未安装\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF920D\" },\r\n            data: [35, 0, 20, 0, 0, 35],\r\n          },\r\n          {\r\n            name: \"安装中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FECF77\" },\r\n            data: [0, 0, 0, 0, 0, 5],\r\n          },\r\n          {\r\n            name: \"验收中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF7730\" },\r\n            data: [0, 0, 10, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"运行中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#54C255\" },\r\n            data: [175, 120, 150, 30, 180, 150],\r\n          },\r\n          {\r\n            name: \"维修中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2656F5\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"已报废\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2C2C2C\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n        ],\r\n      },\r\n      // 安全生产投入环形图数据\r\n      safetyInvestmentPieChart: {\r\n        colorList: [\r\n          \"#2656F5\",\r\n          \"#FF920D\", \r\n          \"#54C255\",\r\n          \"#E54545\",\r\n          \"#8EE98F\",\r\n          \"#A1CDFF\"\r\n        ],\r\n        data: [\r\n          { value: 2000, name: \"海外工程一公司\" },\r\n          { value: 2000, name: \"海外工程二公司\" },\r\n          { value: 2000, name: \"海外工程三公司\" },\r\n          { value: 2000, name: \"中江国际集团\" },\r\n          { value: 2000, name: \"第五建设分公司\" }\r\n        ],\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"50%\"],\r\n              radius: [\"55%\", \"75%\"],\r\n              label: {\r\n                show: false\r\n              },\r\n              labelLine: {\r\n                show: false\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      // 安全生产投入总金额\r\n      safetyInvestmentTotal: {\r\n        value: \"6000\",\r\n        unit: \"总投入(万元)\",\r\n        label: \"安全生产投入\"\r\n      },\r\n      // 安全生产投入项目列表\r\n      safetyInvestmentProjects: [\r\n        { name: \"海外工程一公司\", value: \"2000\", color: \"#2656F5\" },\r\n        { name: \"海外工程二公司\", value: \"2000\", color: \"#FF920D\" },\r\n        { name: \"海外工程三公司\", value: \"2000\", color: \"#54C255\" },\r\n        { name: \"中江国际集团\", value: \"2000\", color: \"#E54545\" },\r\n        { name: \"第五建设分公司\", value: \"2000\", color: \"#8EE98F\" }\r\n      ],\r\n      // 保留这些旧数据结构以防止报错，后续可以逐步清理\r\n      lineData: {\r\n        grid: {\r\n          top: 10,\r\n          left: \"6%\",\r\n          right: \"6%\",\r\n          bottom: \"12%\",\r\n        },\r\n        xAxisData: [],\r\n        seriesData: [],\r\n      },\r\n      mainData: {},\r\n      yearCount: {},\r\n      dangerList: [],\r\n      echartData: { colorList: [], data: [] },\r\n      cateBarData: { colorList: [], series: [] },\r\n      yearBarData: { series: [] },\r\n      chart2Lengend: [],\r\n      echartType1: 1,\r\n      echartType2: 1,\r\n      echartTypeList1: [],\r\n      echartTypeList2: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 悬浮框相关数据\r\n      tooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      // 危大工程图表实例\r\n      dangerousProjectChartInstance: null,\r\n      // 设备详细信息缓存\r\n      equipmentDetailsCache: {},\r\n      // 设备悬浮框相关数据\r\n      equipmentTooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      requestQueue: new Set(), // 正在请求的项目名称队列\r\n    };\r\n  },\r\n  created() {\r\n    this.loadManagementOverview();\r\n    this.loadQualityStatistics();\r\n    this.loadSafetyStatistics();\r\n    this.loadDangerTypeStatistics();\r\n    this.loadSafetyProductionStatistics();\r\n    this.loadDangerousProStatistics();\r\n    this.loadLargeEquipmentStatistics();\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁图表实例\r\n    if (this.dangerousProjectChartInstance) {\r\n      this.dangerousProjectChartInstance.dispose();\r\n      this.dangerousProjectChartInstance = null;\r\n    }\r\n\r\n    // 清理请求队列\r\n    this.requestQueue.clear();\r\n  },\r\n  methods: {\r\n    // 处理安全生产投入项目hover事件\r\n    handleItemHover(item, event) {\r\n      // 可以在这里添加hover效果的逻辑\r\n      // 例如显示详细信息的tooltip等\r\n      console.log('Hovering over:', item.name, item.value);\r\n    },\r\n\r\n    // 隐藏tooltip\r\n    hideTooltip() {\r\n      // 隐藏tooltip的逻辑\r\n    },\r\n\r\n    // 切换质量管理时间类型\r\n    changeQualityTimeType(timeType) {\r\n      if (this.qualityTimeType !== timeType) {\r\n        this.qualityTimeType = timeType;\r\n        this.loadQualityStatistics();\r\n      }\r\n    },\r\n\r\n    // 切换安全管理时间类型\r\n    changeSafetyTimeType(timeType) {\r\n      if (this.safetyTimeType !== timeType) {\r\n        this.safetyTimeType = timeType;\r\n        this.loadSafetyStatistics();\r\n        this.loadDangerTypeStatistics();\r\n      }\r\n    },\r\n\r\n    // 获取安全管理总览数据\r\n    async loadManagementOverview() {\r\n      try {\r\n        console.log(\"开始加载安全管理总览数据...\");\r\n        const response = await getManagementOverview();\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的数据结构\r\n          this.statsData.dangerousProjects = data.wdgcs || 0; // 危大工程数\r\n          this.statsData.safetyInvestment = data.aqtzzje || 0; // 安全投资总金额\r\n          this.statsData.domesticProjects = data.gnzjxms || 0; // 国内在建项目\r\n          this.statsData.internationalProjects = data.gjzjxms || 0; // 国际在建项目\r\n          this.statsData.largeEquipment = data.dxsbs || 0; // 大型设备数\r\n\r\n          console.log(\"安全管理总览数据加载成功:\", this.statsData);\r\n        } else {\r\n          console.warn(\"接口返回数据格式异常:\", response);\r\n          this.handleDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理总览数据失败:\", error);\r\n        this.handleDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理数据加载错误\r\n    handleDataLoadError() {\r\n      console.log(\"使用默认数据\");\r\n      // 保持原有的默认数据，确保页面正常显示\r\n      this.$modal.msgWarning(\"数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取质量管理统计数据\r\n    async loadQualityStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载质量管理统计数据...\",\r\n          \"timeType:\",\r\n          this.qualityTimeType\r\n        );\r\n        const response = await getQualityStatistics({\r\n          timeType: this.qualityTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的质量管理数据结构（自主上报部分）\r\n          this.qualityManagement.selfReportProblems = data.jczs || 0; // 自主上报问题数\r\n          this.qualityManagement.selfReportFixed = data.ywcsl || 0; // 已整改数量\r\n          this.qualityManagement.selfReportPending = data.kwcsl || 0; // 待整改数量\r\n          this.qualityManagement.selfReportRate = data.aszgl\r\n            ? data.aszgl * 100\r\n            : 0; // 按时整改率（转换为百分比）\r\n          this.qualityManagement.selfReportOnTime = data.aszg || 0; // 按时整改\r\n          this.qualityManagement.selfReportOverdue = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"质量管理统计数据加载成功:\", this.qualityManagement);\r\n        } else {\r\n          console.warn(\"质量管理统计接口返回数据格式异常:\", response);\r\n          this.handleQualityDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取质量管理统计数据失败:\", error);\r\n        this.handleQualityDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理质量管理数据加载错误\r\n    handleQualityDataLoadError() {\r\n      console.log(\"使用质量管理默认数据\");\r\n      this.$modal.msgWarning(\"质量管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全管理统计数据\r\n    async loadSafetyStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载安全管理统计数据...\",\r\n          \"timeType:\",\r\n          this.safetyTimeType\r\n        );\r\n        const response = await getSafetyStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的安全管理数据结构（自主上报部分）\r\n          this.safetyManagement.hazardCount = data.jczs || 0; // 隐患数（问题数）\r\n          this.safetyManagement.reportOnTimeRate = data.aszgl\r\n            ? (data.aszgl * 100).toFixed(1)\r\n            : 0; // 按时整改率（转换为百分比）\r\n          // 为了保持数据一致性，也可以存储更多详细数据备用\r\n          this.safetyManagement.safetyFixedProblems = data.ywcsl || 0; // 已整改数量\r\n          this.safetyManagement.safetyPendingProblems = data.kwcsl || 0; // 待整改数量\r\n          this.safetyManagement.safetyOnTimeFixed = data.aszg || 0; // 按时整改\r\n          this.safetyManagement.safetyOverdueFixed = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"安全管理统计数据加载成功:\", this.safetyManagement);\r\n        } else {\r\n          console.warn(\"安全管理统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理统计数据失败:\", error);\r\n        this.handleSafetyDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全管理数据加载错误\r\n    handleSafetyDataLoadError() {\r\n      console.log(\"使用安全管理默认数据\");\r\n      this.$modal.msgWarning(\"安全管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取隐患类别统计数据\r\n    async loadDangerTypeStatistics() {\r\n      try {\r\n        console.log(\"开始加载隐患类别统计数据...\");\r\n        const response = await getDangerTypeStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        // 检查下发的图表使用默认数据，总和等于检查次数58\r\n        const defaultDataForInspection = [\r\n          { value: 15, name: \"基础设施\" },\r\n          { value: 12, name: \"设备维护\" },\r\n          { value: 11, name: \"消防安全\" },\r\n          { value: 10, name: \"电气安全\" },\r\n          { value: 10, name: \"高空作业\" },\r\n        ];\r\n\r\n        // 定义颜色数组\r\n        const colorList1 = [\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n          \"#FF920D\",\r\n          \"#54C255\",\r\n          \"#A1CDFF\",\r\n        ];\r\n        const colorList2 = [\r\n          \"#FF920D\",\r\n          \"#E54545\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n        ];\r\n\r\n        // 更新检查下发的隐患类别统计图表（使用默认数据）\r\n        this.safetyManagement.hazardTypeChart1 = {\r\n          colorList: colorList1,\r\n          data: defaultDataForInspection,\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        };\r\n\r\n        // 自主上报的图表使用接口数据\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n          // 对数据按value值降序排序，只取前5个\r\n          const sortedData = data\r\n            .sort((a, b) => (b.value || 0) - (a.value || 0))\r\n            .slice(0, 5);\r\n\r\n          // 更新自主上报的隐患类别统计图表（使用接口数据）\r\n          this.safetyManagement.hazardTypeChart2 = {\r\n            colorList: colorList2,\r\n            data: sortedData,\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"52%\"],\r\n                  radius: [\"45%\", \"75%\"],\r\n                  label: {\r\n                    show: true,\r\n                    position: \"outside\",\r\n                    formatter: \"{b}\\n{c}\",\r\n                    fontSize: 10,\r\n                    color: \"#666\",\r\n                    lineHeight: 14,\r\n                  },\r\n                  labelLine: {\r\n                    show: true,\r\n                    length: 8,\r\n                    length2: 15,\r\n                    lineStyle: {\r\n                      color: \"#666\",\r\n                      width: 1,\r\n                    },\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n          };\r\n\r\n          console.log(\"隐患类别统计数据加载成功:\", {\r\n            chart1: \"使用默认数据\",\r\n            chart2: this.safetyManagement.hazardTypeChart2,\r\n          });\r\n        } else {\r\n          console.warn(\"隐患类别统计接口返回数据格式异常:\", response);\r\n          this.handleDangerTypeDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取隐患类别统计数据失败:\", error);\r\n        this.handleDangerTypeDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理隐患类别统计数据加载错误\r\n    handleDangerTypeDataLoadError() {\r\n      console.log(\"自主上报图表使用默认数据\");\r\n      // 如果接口失败，自主上报图表也使用默认数据\r\n      const defaultDataForReport = [\r\n        { value: 8, name: \"基础设施\" },\r\n        { value: 7, name: \"设备维护\" },\r\n        { value: 6, name: \"消防安全\" },\r\n        { value: 5, name: \"电气安全\" },\r\n        { value: 4, name: \"高空作业\" },\r\n      ];\r\n\r\n      const colorList2 = [\r\n        \"#FF920D\",\r\n        \"#E54545\",\r\n        \"#54C255\",\r\n        \"#2656F5\",\r\n        \"#8EE98F\",\r\n      ];\r\n\r\n      this.safetyManagement.hazardTypeChart2 = {\r\n        colorList: colorList2,\r\n        data: defaultDataForReport,\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"52%\"],\r\n              radius: [\"45%\", \"75%\"],\r\n              label: {\r\n                show: true,\r\n                position: \"outside\",\r\n                formatter: \"{b}\\n{c}\",\r\n                fontSize: 10,\r\n                color: \"#666\",\r\n                lineHeight: 14,\r\n              },\r\n              labelLine: {\r\n                show: true,\r\n                length: 8,\r\n                length2: 15,\r\n                lineStyle: {\r\n                  color: \"#666\",\r\n                  width: 1,\r\n                },\r\n              },\r\n            },\r\n          ],\r\n        },\r\n      };\r\n      this.$modal.msgWarning(\"隐患类别统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全生产投入统计数据\r\n    async loadSafetyProductionStatistics() {\r\n      try {\r\n        console.log(\"开始加载安全生产投入统计数据...\");\r\n        const response = await getSafetyProductionStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取公司名称作为X轴标签（截取显示）\r\n          const xAxisData = data.map((item) => {\r\n            // 截取公司名称用于X轴显示，保持图表美观\r\n            const companyName = item.company || \"未知公司\";\r\n            return companyName.length > 10\r\n              ? companyName.substring(0, 10) + \"...\"\r\n              : companyName;\r\n          });\r\n\r\n          // 保存完整的公司名称用于tooltip显示\r\n          const fullCompanyNames = data.map(\r\n            (item) => item.company || \"未知公司\"\r\n          );\r\n\r\n          // 提取年度预算金额数据，并保存完整公司名称\r\n          const budgetData = data.map((item, index) => ({\r\n            value: parseFloat(item.annualBudgetAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n          // 提取实际投入金额数据，并保存完整公司名称\r\n          const actualData = data.map((item, index) => ({\r\n            value: parseFloat(item.actualInputAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n\r\n          // 动态计算Y轴最大值\r\n          const maxValue = Math.max(\r\n            ...budgetData.map((item) => item.value),\r\n            ...actualData.map((item) => item.value)\r\n          );\r\n          const yAxisMax = Math.ceil((maxValue * 1.2) / 1000) * 1000; // 向上取整到千位\r\n\r\n          // 计算总投入\r\n          const totalInvestment = data.reduce((sum, item) => sum + parseFloat(item.actualInputAmount || 0), 0);\r\n\r\n          // 更新环形图数据\r\n          const colorList = [\"#2656F5\", \"#FF920D\", \"#54C255\", \"#E54545\", \"#8EE98F\", \"#A1CDFF\"];\r\n          \r\n          this.safetyInvestmentPieChart = {\r\n            colorList: colorList,\r\n            data: data.map((item, index) => ({\r\n              value: parseFloat(item.actualInputAmount || 0),\r\n              name: item.company || \"未知公司\"\r\n            })),\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"50%\"],\r\n                  radius: [\"55%\", \"75%\"],\r\n                  label: {\r\n                    show: false\r\n                  },\r\n                  labelLine: {\r\n                    show: false\r\n                  }\r\n                }\r\n              ]\r\n            }\r\n          };\r\n\r\n          // 更新中心文字\r\n          this.safetyInvestmentTotal = {\r\n            value: Math.round(totalInvestment).toString(),\r\n            unit: \"总投入(万元)\",\r\n            label: \"安全生产投入\"\r\n          };\r\n\r\n          // 更新项目列表\r\n          this.safetyInvestmentProjects = data.map((item, index) => ({\r\n            name: item.company || \"未知公司\",\r\n            value: Math.round(parseFloat(item.actualInputAmount || 0)).toString(),\r\n            color: colorList[index % colorList.length]\r\n          }));\r\n\r\n          console.log(\r\n            \"安全生产投入统计数据加载成功:\",\r\n            this.safetyInvestmentChart\r\n          );\r\n        } else {\r\n          console.warn(\"安全生产投入统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyProductionDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全生产投入统计数据失败:\", error);\r\n        this.handleSafetyProductionDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全生产投入统计数据加载错误\r\n    handleSafetyProductionDataLoadError() {\r\n      console.log(\"使用安全生产投入统计默认数据\");\r\n      this.$modal.msgWarning(\"安全生产投入统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取危大工程统计数据\r\n    async loadDangerousProStatistics() {\r\n      try {\r\n        console.log(\"开始加载危大工程统计数据...\");\r\n        const response = await getDangerousProStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 获取前5个项目数据\r\n          const topProjects = data.slice(0, 5);\r\n\r\n          // 找出最大的value值，用于计算X轴最大值\r\n          const maxValue = Math.max(...data.map((item) => item.value));\r\n\r\n          // 提取项目名称和数值，保留完整数据用于tooltip\r\n          const projectNames = topProjects.map((item) => {\r\n            // 截取项目名称，避免过长\r\n            return item.name && item.name.length > 10\r\n              ? item.name.substring(0, 10) + \"...\"\r\n              : item.name || \"未知项目\";\r\n          });\r\n\r\n          // 构建包含详细信息的数据\r\n          const projectData = topProjects.map((item, index) => ({\r\n            name: projectNames[index],\r\n            value: item.value || 0,\r\n            fullName: item.name || \"未知项目\",\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 更新图表配置\r\n          this.dangerousProjectChart.yAxis.data = projectNames.reverse();\r\n          this.dangerousProjectChart.series[0].data = projectData.reverse();\r\n          this.dangerousProjectChart.xAxis.max = maxValue;\r\n\r\n          // 保存原始数据供其他用途使用\r\n          this.dangerousProjectsList = topProjects.map((item) => ({\r\n            name: item.name || \"未知项目\",\r\n            value: item.value || 0,\r\n            originalName: item.name,\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 初始化图表\r\n          this.$nextTick(() => {\r\n            this.initDangerousProjectChart();\r\n          });\r\n\r\n          console.log(\"危大工程统计数据加载成功:\", this.dangerousProjectChart);\r\n        } else {\r\n          console.warn(\"危大工程统计接口返回数据格式异常:\", response);\r\n          this.handleDangerousProDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取危大工程统计数据失败:\", error);\r\n        this.handleDangerousProDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理危大工程统计数据加载错误\r\n    handleDangerousProDataLoadError() {\r\n      console.log(\"使用危大工程统计默认数据\");\r\n      this.$modal.msgWarning(\"危大工程统计数据加载失败，显示默认数据\");\r\n      // 使用默认数据初始化图表\r\n      this.$nextTick(() => {\r\n        this.initDangerousProjectChart();\r\n      });\r\n    },\r\n\r\n    // 初始化危大工程图表\r\n    initDangerousProjectChart() {\r\n      if (this.$refs.dangerousProjectChart) {\r\n        // 销毁现有实例\r\n        if (this.dangerousProjectChartInstance) {\r\n          this.dangerousProjectChartInstance.dispose();\r\n        }\r\n\r\n        // 创建新的图表实例\r\n        this.dangerousProjectChartInstance = echarts.init(\r\n          this.$refs.dangerousProjectChart\r\n        );\r\n\r\n        // 设置图表选项\r\n        const option = {\r\n          grid: this.dangerousProjectChart.grid,\r\n          xAxis: this.dangerousProjectChart.xAxis,\r\n          yAxis: this.dangerousProjectChart.yAxis,\r\n          series: this.dangerousProjectChart.series,\r\n          tooltip: {\r\n            show: true,\r\n            trigger: \"item\",\r\n            backgroundColor: \"#fff\",\r\n            borderColor: \"#ccc\",\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n            },\r\n            extraCssText:\r\n              \"box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 400px; white-space: normal;\",\r\n            position: function (point, params, dom, rect, size) {\r\n              // 获取 tooltip 的实际尺寸\r\n              const tooltipWidth = size.contentSize[0];\r\n              const tooltipHeight = size.contentSize[1];\r\n\r\n              // 图表容器尺寸\r\n              const chartWidth = rect.width;\r\n              const chartHeight = rect.height;\r\n\r\n              // 计算右边和左边的可用空间\r\n              const rightSpace = chartWidth - point[0] - 10;\r\n              const leftSpace = point[0] - 10;\r\n\r\n              let x, y;\r\n\r\n              // 优先选择空间更大的一边，但确保不会被遮挡\r\n              if (rightSpace >= tooltipWidth || rightSpace > leftSpace) {\r\n                // 显示在右边\r\n                x = point[0] + 10;\r\n                // 如果右边真的放不下，尝试调整到图表右边界内\r\n                if (x + tooltipWidth > chartWidth) {\r\n                  x = chartWidth - tooltipWidth - 5;\r\n                }\r\n              } else if (leftSpace >= tooltipWidth) {\r\n                // 显示在左边，但确保有足够空间\r\n                x = point[0] - tooltipWidth - 10;\r\n                // 确保不会超出左边界\r\n                if (x < 0) {\r\n                  x = 5;\r\n                }\r\n              } else {\r\n                // 如果两边都放不下，选择右边并强制显示在图表内\r\n                x = Math.max(\r\n                  5,\r\n                  Math.min(point[0] + 10, chartWidth - tooltipWidth - 5)\r\n                );\r\n              }\r\n\r\n              // 垂直居中，但确保不超出边界\r\n              y = point[1] - tooltipHeight / 2;\r\n              if (y < 10) {\r\n                y = 10;\r\n              } else if (y + tooltipHeight > chartHeight - 10) {\r\n                y = chartHeight - tooltipHeight - 10;\r\n              }\r\n\r\n              return [x, y];\r\n            },\r\n            formatter: (params) => {\r\n              if (params.data && typeof params.data === \"object\") {\r\n                const { fullName, value, detalList } = params.data;\r\n\r\n                let html = `<div style=\"font-weight: bold; margin-bottom: 8px; color: #333;\">${fullName}</div>`;\r\n                html += `<div style=\"margin-bottom: 8px;\">总数量: <span style=\"color: #1890ff; font-weight: bold;\">${value}</span></div>`;\r\n\r\n                if (detalList && detalList.length > 0) {\r\n                  html +=\r\n                    '<div style=\"border-top: 1px solid #eee; padding-top: 8px;\">';\r\n                  html +=\r\n                    '<div style=\"font-weight: bold; margin-bottom: 6px; color: #666;\">详细信息:</div>';\r\n                  detalList.forEach((item) => {\r\n                    html += `<div style=\"margin-bottom: 4px; padding-left: 8px; border-left: 2px solid #1890ff;\">\r\n                      <div style=\"font-weight: 500;\">${\r\n                        item.name || \"未知类型\"\r\n                      }</div>\r\n                      <div style=\"color: #666; font-size: 11px;\">数量: ${\r\n                        item.value || 0\r\n                      }</div>\r\n                    </div>`;\r\n                  });\r\n                  html += \"</div>\";\r\n                } else {\r\n                  html +=\r\n                    '<div style=\"color: #999; font-style: italic;\">暂无详细信息</div>';\r\n                }\r\n\r\n                return html;\r\n              }\r\n\r\n              // 兜底显示\r\n              return `项目: ${params.name}<br/>数量: ${params.value}`;\r\n            },\r\n          },\r\n        };\r\n\r\n        this.dangerousProjectChartInstance.setOption(option);\r\n\r\n        // 自适应大小\r\n        window.addEventListener(\"resize\", () => {\r\n          if (this.dangerousProjectChartInstance) {\r\n            this.dangerousProjectChartInstance.resize();\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 获取大型设备统计数据\r\n    async loadLargeEquipmentStatistics() {\r\n      try {\r\n        console.log(\"开始加载大型设备统计数据...\");\r\n        const response = await getLargeEquipmentStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取设备名称作为X轴类别\r\n          const equipmentNames = data.map((item) => item.name || \"未知设备\");\r\n\r\n          // 提取设备数量，全部设为\"运行中\"状态\r\n          const runningData = data.map((item) => item.value || 0);\r\n          // 动态计算Y轴最大值，直接使用接口数据的最大值\r\n          const maxValue = Math.max(...runningData);\r\n          const yAxisMax = maxValue > 0 ? maxValue : 10; // Y轴上限就是接口里最大的数据\r\n\r\n          // 更新大型设备图表配置\r\n          this.largeEquipmentChart = {\r\n            colorList: [\r\n              \"#FF920D\",\r\n              \"#FECF77\",\r\n              \"#FF7730\",\r\n              \"#54C255\",\r\n              \"#2656F5\",\r\n              \"#2C2C2C\",\r\n            ],\r\n            grid: {\r\n              top: 30,\r\n              left: \"8%\",\r\n              right: \"8%\",\r\n              bottom: \"25%\",\r\n            },\r\n            legend: undefined, // 显式移除图例\r\n            xAxis: {\r\n              type: \"category\",\r\n              data: equipmentNames,\r\n              axisLabel: {\r\n                interval: 0,\r\n                rotate: 0,\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            yAxis: {\r\n              type: \"value\",\r\n              max: yAxisMax > 0 ? yAxisMax : 10,\r\n              min: 0,\r\n              interval: yAxisMax > 0 ? Math.ceil(yAxisMax / 5) : 2, // 将Y轴等分为5个区间，确保间隔为整数\r\n              axisLabel: {\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            series: [\r\n              {\r\n                name: \"未安装\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF920D\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"安装中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FECF77\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"验收中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF7730\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"运行中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#54C255\" },\r\n                data: runningData, // 使用真实数据\r\n              },\r\n              {\r\n                name: \"维修中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2656F5\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"已报废\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2C2C2C\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n            ],\r\n          };\r\n\r\n          console.log(\"大型设备统计数据加载成功:\", this.largeEquipmentChart);\r\n        } else {\r\n          console.warn(\"大型设备统计接口返回数据格式异常:\", response);\r\n          this.handleLargeEquipmentDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取大型设备统计数据失败:\", error);\r\n        this.handleLargeEquipmentDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理大型设备统计数据加载错误\r\n    handleLargeEquipmentDataLoadError() {\r\n      console.log(\"使用大型设备统计默认数据\");\r\n      this.$modal.msgWarning(\"大型设备统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 异步加载设备详细信息\r\n    async loadEquipmentDetails(equipmentName) {\r\n      // 如果已经有缓存，直接返回\r\n      if (this.equipmentDetailsCache[equipmentName]) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(\"开始加载设备详细信息:\", equipmentName);\r\n        const response = await getLargeEquipmentByNameStatistics(equipmentName);\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          // 缓存详细数据\r\n          this.equipmentDetailsCache[equipmentName] = response.data.map(\r\n            (item) => ({\r\n              name: item.name,\r\n              value: item.value,\r\n              equipment_name: item.equipment_name,\r\n            })\r\n          );\r\n\r\n          console.log(\r\n            \"设备详细信息加载成功:\",\r\n            equipmentName,\r\n            this.equipmentDetailsCache[equipmentName]\r\n          );\r\n\r\n          // 更新当前悬浮框的数据\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.data =\r\n              this.equipmentDetailsCache[equipmentName];\r\n          }\r\n        } else {\r\n          // 设置空数据避免重复请求\r\n          this.equipmentDetailsCache[equipmentName] = [];\r\n          console.warn(\"设备详细信息接口返回数据格式异常:\", response);\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.error = \"数据格式异常\";\r\n          }\r\n        }\r\n      } catch (error) {\r\n        // 设置空数据避免重复请求\r\n        this.equipmentDetailsCache[equipmentName] = [];\r\n        console.error(\"获取设备详细信息失败:\", error);\r\n        if (\r\n          this.equipmentTooltip.visible &&\r\n          this.equipmentTooltip.title === equipmentName\r\n        ) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"加载失败\";\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标移动\r\n    handleEquipmentMouseMove(event) {\r\n      // 获取鼠标位置\r\n      const rect = event.currentTarget.getBoundingClientRect();\r\n      const x = event.clientX - rect.left;\r\n      const chartWidth = rect.width;\r\n\r\n      // 根据鼠标位置推断设备（简化实现）\r\n      const equipmentNames = this.largeEquipmentChart.xAxis?.data || [];\r\n      if (equipmentNames.length === 0) return;\r\n\r\n      const equipmentIndex = Math.floor(\r\n        (x / chartWidth) * equipmentNames.length\r\n      );\r\n      if (equipmentIndex >= 0 && equipmentIndex < equipmentNames.length) {\r\n        const equipmentName = equipmentNames[equipmentIndex];\r\n\r\n        // 显示悬浮框\r\n        this.equipmentTooltip.visible = true;\r\n        this.equipmentTooltip.x = event.clientX + 10;\r\n        this.equipmentTooltip.y = event.clientY - 10;\r\n        this.equipmentTooltip.title = equipmentName;\r\n\r\n        // 检查缓存数据\r\n        const cachedData = this.equipmentDetailsCache[equipmentName];\r\n        if (cachedData) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = cachedData;\r\n        } else {\r\n          this.equipmentTooltip.loading = true;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = [];\r\n\r\n          // 加载详细数据\r\n          this.loadEquipmentDetails(equipmentName);\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标离开\r\n    handleEquipmentMouseLeave() {\r\n      this.equipmentTooltip.visible = false;\r\n      this.equipmentTooltip.data = [];\r\n      this.equipmentTooltip.error = \"\";\r\n      this.equipmentTooltip.loading = false;\r\n    },\r\n\r\n    // 截取公司名称显示\r\n    truncateCompanyName(companyName, maxLength = 20) {\r\n      if (!companyName) return \"\";\r\n      if (companyName.length <= maxLength) return companyName;\r\n      return companyName.substring(0, maxLength) + \"...\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home {\r\n  padding: 15px; // 默认固定内边距，避免大屏幕下过大\r\n  background: #f5f7fa;\r\n  font-size: 14px; // 默认固定基础字体大小\r\n  /* 响应式基础字体大小 */\r\n\r\n  @media (max-width: 1199px) {\r\n    padding: 1%;\r\n    font-size: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n.el-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 顶部统计卡片样式（第一行 - 3个大卡片）\r\n.top-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card {\r\n    flex: 1;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 20px;\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n    min-height: 120px;\r\n    min-width: 30%;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 3% 2%;\r\n    }\r\n\r\n    .stat-header {\r\n      margin-bottom: 16px;\r\n      \r\n      h3 {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        margin: 0;\r\n        \r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(1rem, 1.8vw, 1.125rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-dual {\r\n      display: flex;\r\n      gap: 20px;\r\n      \r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n      \r\n      .stat-item {\r\n        flex: 1;\r\n        \r\n        .stat-title {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n          line-height: 1.2;\r\n          \r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n\r\n        .stat-number-row {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 4px;\r\n\r\n          .stat-number {\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            color: #2656F5;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(1rem, 2.2vw, 1.75rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 14px;\r\n            color: #666;\r\n            font-weight: normal;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.1vw, 0.875rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-1 {\r\n      background: linear-gradient(120deg, #F74A34 0%, #FF7C5E 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-2 {\r\n      background: linear-gradient(137deg, #1688E6 0%, #46ABFF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-3 {\r\n      background: linear-gradient(137deg, #F5873E 0%, #F5A645 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 第二行统计卡片样式（4个小卡片）\r\n.second-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card-small {\r\n    display: flex;\r\n    align-items: center;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 16px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n    flex: 1;\r\n    min-width: 22%;\r\n    min-height: 110px;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 2% 1.5%;\r\n    }\r\n\r\n    .stat-icon {\r\n      margin-right: 12px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 40px;\r\n      height: 40px;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 8px;\r\n\r\n      @media (max-width: 1199px) {\r\n        width: 35px;\r\n        height: 35px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      i {\r\n        font-size: 20px;\r\n        color: #666;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-small {\r\n      flex: 1;\r\n\r\n      .stat-title-small {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-bottom: 4px;\r\n        line-height: 1.2;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n\r\n      .stat-number-row-small {\r\n        display: flex;\r\n        align-items: baseline;\r\n        gap: 4px;\r\n\r\n        .stat-number-small {\r\n          font-size: 20px;\r\n          font-weight: bold;\r\n          color: #333;\r\n          line-height: 1;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(1rem, 1.6vw, 1.25rem);\r\n          }\r\n        }\r\n\r\n        .stat-unit-small {\r\n          font-size: 12px;\r\n          color: #666;\r\n          font-weight: normal;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-1 {\r\n      background: linear-gradient(137deg, #156BF6 0%, #4681FF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-2 {\r\n      background: linear-gradient(137deg, #FC9920 0%, #F5AC45 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-3 {\r\n      background: linear-gradient(137deg, #9D59FF 0%, #CA79F5 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-4 {\r\n      background: linear-gradient(147deg, #18C68C 0%, #2BD181 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 管理卡片样式\r\n.management-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 15px; // 减少内边距，默认固定内边距\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 35  0px;\r\n    overflow: hidden; // 防止内容超出\r\n    box-sizing: border-box; // 确保内边距计算在内\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 1%;\r\n    }\r\n\r\n    .management-header {\r\n      margin-bottom: 10px; // 减少间距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1%;\r\n      }\r\n\r\n      .header-content {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n\r\n      h3 {\r\n        margin: 0;\r\n        font-size: 16px; // 默认固定字体大小\r\n        font-weight: 400;\r\n        color: #333;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n        }\r\n      }\r\n\r\n      .time-tabs {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 4px;\r\n\r\n        .tab-item {\r\n          font-size: 12px;\r\n          color: #666;\r\n          cursor: pointer;\r\n          padding: 2px 6px;\r\n          border-radius: 4px;\r\n          transition: all 0.3s ease;\r\n          user-select: none;\r\n\r\n          &:hover {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n          }\r\n\r\n          &.active {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n            font-weight: 500;\r\n          }\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            padding: 1px 4px;\r\n          }\r\n        }\r\n\r\n        .tab-divider {\r\n          font-size: 12px;\r\n          color: #ccc;\r\n          margin: 0 2px;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 质量管理样式\r\n    .section-header {\r\n      margin-bottom: 1%;\r\n\r\n      .section-title {\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: #f5f7fa;\r\n        border-radius: 0.25rem;\r\n      }\r\n    }\r\n\r\n    .quality-stats {\r\n      margin-bottom: 1%;\r\n\r\n      .stat-group {\r\n        display: flex;\r\n        gap: 1.5%;\r\n        align-items: flex-start;\r\n\r\n        .stat-item {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          &.stat-placeholder {\r\n            visibility: hidden;\r\n          }\r\n\r\n          .stat-header {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 0.5rem;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 28px;\r\n\r\n            .stat-icon {\r\n              width: 1.25rem;\r\n              height: 1.25rem;\r\n              border-radius: 0.25rem;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              color: white;\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n              &.blue {\r\n                background: #2656f5;\r\n              }\r\n\r\n              &.orange {\r\n                background: #ff920d;\r\n              }\r\n\r\n              &.green {\r\n                background: #54c255;\r\n              }\r\n            }\r\n\r\n            .stat-label {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n              color: #666;\r\n              font-weight: 500;\r\n            }\r\n          }\r\n\r\n          .stat-number {\r\n            font-size: clamp(0.75rem, 1.3vw, 1.25rem);\r\n            font-weight: bold;\r\n            color: #2656f5;\r\n            line-height: 1;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 24px;\r\n            text-align: left;\r\n          }\r\n\r\n          .stat-detail {\r\n            display: flex;\r\n            gap: 1.5%;\r\n            align-items: flex-start;\r\n\r\n            span {\r\n              font-size: clamp(0.5rem, 0.7vw, 0.625rem);\r\n              color: #999;\r\n              line-height: 1.4;\r\n\r\n              .number {\r\n                color: #333;\r\n                font-weight: 500;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 安全管理专用样式 - 简单版本按设计稿\r\n    .safety-top-stats-simple {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20px;\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n\r\n      .safety-stat-item-simple {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        @media (max-width: 1199px) {\r\n          gap: 0.8%;\r\n        }\r\n\r\n        .safety-icon-simple {\r\n          width: 20px;\r\n          height: 20px;\r\n          object-fit: contain;\r\n          flex-shrink: 0;\r\n\r\n          @media (max-width: 1199px) {\r\n            width: clamp(16px, 2vw, 20px);\r\n            height: clamp(16px, 2vw, 20px);\r\n          }\r\n        }\r\n\r\n        .safety-label {\r\n          font-size: 14px;\r\n          color: #666;\r\n          margin-right: 4px;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n          }\r\n        }\r\n\r\n        .safety-number {\r\n          font-size: 24px;\r\n          font-weight: bold;\r\n          color: #2656F5;\r\n          line-height: 1;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(1.25rem, 2vw, 1.5rem);\r\n          }\r\n        }\r\n\r\n        .safety-unit {\r\n          font-size: 16px;\r\n          color: #666;\r\n          font-weight: normal;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.875rem, 1.2vw, 1rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 新的第二行统计数据样式 - 一行显示\r\n    .safety-second-stats {\r\n      margin-bottom: 20px;\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n\r\n      .stats-row-single {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 6px;\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 0.6%;\r\n          }\r\n\r\n          .stat-dot {\r\n            width: 6px;\r\n            height: 6px;\r\n            border-radius: 50%;\r\n            background-color: #686C7F;\r\n            flex-shrink: 0;\r\n\r\n            @media (max-width: 1199px) {\r\n              width: clamp(4px, 0.6vw, 6px);\r\n              height: clamp(4px, 0.6vw, 6px);\r\n            }\r\n          }\r\n\r\n          .stat-label {\r\n            font-size: 12px;\r\n            color: #686C7F;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n\r\n          .stat-value {\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            color: #000;\r\n            margin-left: 2px;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n            color: #686C7F;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-chart-area {\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n    }\r\n\r\n    .safety-bottom-stats {\r\n      .stats-row {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-bottom: 12px;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 8px;\r\n          flex: 1;\r\n          justify-content: center;\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 0.8%;\r\n          }\r\n\r\n          .stat-dot {\r\n            width: 10px;\r\n            height: 10px;\r\n            border-radius: 50%;\r\n            flex-shrink: 0;\r\n\r\n            @media (max-width: 1199px) {\r\n              width: clamp(8px, 1vw, 10px);\r\n              height: clamp(8px, 1vw, 10px);\r\n            }\r\n          }\r\n\r\n          .stat-label {\r\n            font-size: 12px;\r\n            color: #666;\r\n            min-width: 40px;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n              min-width: clamp(30px, 3vw, 40px);\r\n            }\r\n          }\r\n\r\n          .stat-value {\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            color: #333;\r\n            min-width: 20px;\r\n            text-align: right;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n            color: #666;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-sections-container {\r\n      display: flex;\r\n      gap: 15px; // 减少间距，默认固定间距\r\n      width: 100%;\r\n      overflow: hidden; // 防止内容超出\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n    }\r\n\r\n    .safety-section-independent {\r\n      flex: 1;\r\n      background: #f5f7f9;\r\n      padding: 10px 15px 12px 15px; // 减少内边距，默认固定内边距\r\n      border-radius: 0.5rem;\r\n      min-width: 0; // 允许flex项目缩小到内容大小以下\r\n      box-sizing: border-box; // 确保内边距计算在内\r\n\r\n      @media (max-width: 1199px) {\r\n        padding: 0.8% 1.5% 1% 1.5%;\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px; // 默认固定间距\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .section-title {\r\n          font-size: 12px; // 默认固定字体大小\r\n          color: #666;\r\n          padding: 0.25rem 0.5rem;\r\n          background: rgba(245, 247, 250, 0.8);\r\n          border-radius: 0.25rem;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-stats-row {\r\n      display: flex;\r\n      gap: 8px; // 进一步减少间距，默认固定间距\r\n      margin-top: 10px; // 减少上边距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n        margin-top: 1%;\r\n      }\r\n\r\n      .safety-stat-card {\r\n        flex: 1;\r\n        background: white;\r\n        border-radius: 0.5rem;\r\n        padding: 10px 12px; // 大幅减少内边距，默认固定内边距\r\n        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);\r\n        min-width: 0; // 允许flex项目缩小\r\n        box-sizing: border-box; // 确保内边距计算在内\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.2% 1.6%;\r\n        }\r\n\r\n        .safety-stat-title {\r\n          font-size: 10px; // 默认固定字体大小\r\n          color: #666666;\r\n          margin-bottom: 0.5rem;\r\n          line-height: 1.2;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n          }\r\n        }\r\n\r\n        .safety-stat-content-row {\r\n          display: flex;\r\n          align-items: flex-end;\r\n          justify-content: space-between;\r\n          gap: 15px; // 默认固定间距\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 1.2%;\r\n          }\r\n        }\r\n\r\n        .safety-stat-main {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 0.25rem;\r\n          margin-bottom: 0;\r\n\r\n          .safety-stat-number {\r\n            font-size: 22px; // 调整默认固定字体大小，适合1300px+屏幕\r\n            font-weight: bold;\r\n            color: #2656F5;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.5vw, 1.5rem);\r\n              /* 减小响应式字体：12px-24px */\r\n            }\r\n          }\r\n\r\n          .safety-stat-unit {\r\n            font-size: 10px; // 默认固定字体大小\r\n            color: #666666;\r\n            font-weight: normal;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n            }\r\n          }\r\n        }\r\n\r\n        .safety-stat-subtitle {\r\n          font-size: 9px; // 默认固定字体大小\r\n          color: #999999;\r\n          line-height: 1.2;\r\n          white-space: nowrap;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.4375rem, 0.7vw, 0.5625rem);\r\n          }\r\n\r\n          .safety-highlight-number {\r\n            color: #ff920d;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 内嵌图表样式\r\n    .chart-section-in-section {\r\n      .chart-title {\r\n        font-size: 12px; // 默认固定字体大小\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: rgba(245, 247, 250, 0.8);\r\n        border-radius: 0.25rem;\r\n        margin-bottom: 0.5rem;\r\n        text-align: left;\r\n        display: inline-block;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .chart-section {\r\n      .chart-row {\r\n        display: flex;\r\n        gap: 1.5%;\r\n\r\n        .chart-item {\r\n          flex: 1;\r\n\r\n          .chart-title {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            color: #666;\r\n            margin-bottom: 0.5rem;\r\n            text-align: center;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .pie-chart-section {\r\n      .chart-title {\r\n        font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n        color: #666;\r\n        margin-bottom: 1rem;\r\n        text-align: center;\r\n      }\r\n\r\n      &.full-height {\r\n        height: calc(100% - 5rem);\r\n\r\n        .chart-container {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 底部图表卡片样式\r\n.charts-row {\r\n  .chart-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 2.5%;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 340px;\r\n\r\n    .chart-header {\r\n      margin-bottom: 1.5%;\r\n\r\n      h4 {\r\n        margin: 0;\r\n        font-size: clamp(0.875rem, 1.4vw, 1rem);\r\n        font-weight: 600;\r\n        color: #333;\r\n      }\r\n    }\r\n\r\n    .chart-content {\r\n      height: calc(100% - 4rem);\r\n    }\r\n  }\r\n}\r\n\r\n// 图表容器样式\r\n.chart-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n// 设备图表容器样式\r\n.equipment-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n// 危大工程图表容器样式\r\n.dangerous-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* ===========================================\r\n   投入管理样式模块 - 可扩展设计\r\n   支持多种投入类型的统一样式管理\r\n   =========================================== */\r\n\r\n// 基础投入容器增强样式\r\n.investment-container-enhanced {\r\n  border-radius: 12px;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(135deg, rgba(20, 121, 252, 0.02) 0%, transparent 50%);\r\n    pointer-events: none;\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: 0 8px 24px rgba(20, 121, 252, 0.15);\r\n    transform: translateY(-2px);\r\n  }\r\n}\r\n\r\n// 安全生产投入容器样式\r\n.safety-investment-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  gap: 32px;\r\n  align-items: center;\r\n  padding: 20px;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 2%;\r\n    padding: 2%;\r\n  }\r\n\r\n  // 图表区域增强样式\r\n  .chart-section {\r\n    width: 280px;\r\n    height: 280px;\r\n    flex-shrink: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: relative;\r\n    \r\n    // 添加装饰性边框效果\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: -4px;\r\n      left: -4px;\r\n      right: -4px;\r\n      bottom: -4px;\r\n      background: linear-gradient(45deg, #1479FC, #54C255, #FF920D, #E54545);\r\n      border-radius: 50%;\r\n      opacity: 0;\r\n      transition: opacity 0.3s ease;\r\n      z-index: 0;\r\n    }\r\n\r\n    &:hover::before {\r\n      opacity: 0.1;\r\n    }\r\n    \r\n    @media (max-width: 1199px) {\r\n      width: 35%;\r\n      height: auto;\r\n      aspect-ratio: 1;\r\n    }\r\n    \r\n    .chart-wrapper {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: relative;\r\n      z-index: 1;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n      background: rgba(255, 255, 255, 0.9);\r\n      backdrop-filter: blur(10px);\r\n    }\r\n  }\r\n\r\n  // 数据列表区域增强样式\r\n  .data-list-section {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-left: 32px;\r\n    position: relative;\r\n\r\n    // 添加分割线装饰\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      left: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      width: 3px;\r\n      height: 60%;\r\n      background: linear-gradient(to bottom, #1479FC, rgba(20, 121, 252, 0.3), transparent);\r\n      border-radius: 2px;\r\n    }\r\n\r\n    @media (max-width: 1199px) {\r\n      padding-left: 3.2%;\r\n      \r\n      &::before {\r\n        left: 1.6%;\r\n        width: 0.3vw;\r\n      }\r\n    }\r\n\r\n    .data-list {\r\n      width: 100%;\r\n      max-width: 320px;\r\n      \r\n      .project-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 18px;\r\n        padding: 10px 0;\r\n        transition: background-color 0.2s ease;\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1.8%;\r\n          padding: 1% 0;\r\n        }\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: rgba(20, 121, 252, 0.05);\r\n          border-radius: 4px;\r\n          padding-left: 8px;\r\n          padding-right: 8px;\r\n        }\r\n\r\n        .project-dot {\r\n          width: 14px;\r\n          height: 14px;\r\n          border-radius: 50%;\r\n          margin-right: 15px;\r\n          flex-shrink: 0;\r\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n\r\n          @media (max-width: 1199px) {\r\n            width: 1.4vw;\r\n            height: 1.4vw;\r\n            margin-right: 1.5%;\r\n          }\r\n        }\r\n\r\n        .project-info {\r\n          flex: 1;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          min-width: 0;\r\n\r\n          .project-name {\r\n            font-size: 15px;\r\n            color: #333;\r\n            font-weight: 500;\r\n            flex: 1;\r\n            margin-right: 12px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            line-height: 1.4;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.8rem, 1.5vw, 0.9375rem);\r\n            }\r\n          }\r\n\r\n          .project-amount {\r\n            font-size: 16px;\r\n            color: #2656F5;\r\n            font-weight: 700;\r\n            white-space: nowrap;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n            }\r\n\r\n            &::after {\r\n              content: '万';\r\n              font-size: 13px;\r\n              color: #666;\r\n              margin-left: 3px;\r\n              font-weight: 400;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(0.7rem, 1.3vw, 0.8125rem);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 悬浮框样式\r\n.project-tooltip {\r\n  position: fixed;\r\n  z-index: 1000;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  padding: 0;\r\n  min-width: 12.5rem;\r\n  max-width: 50rem;\r\n  width: auto;\r\n  font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n  .tooltip-header {\r\n    background: #f8f9fa;\r\n    padding: 0.75rem 1rem;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    font-weight: 600;\r\n    color: #374151;\r\n    border-radius: 0.5rem 0.5rem 0 0;\r\n    font-size: clamp(0.75rem, 1.1vw, 0.8125rem);\r\n    word-wrap: break-word;\r\n    word-break: break-all;\r\n    white-space: normal;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .tooltip-loading,\r\n  .tooltip-error,\r\n  .tooltip-empty {\r\n    padding: 1rem;\r\n    text-align: center;\r\n    color: #6b7280;\r\n  }\r\n\r\n  .tooltip-error {\r\n    color: #ef4444;\r\n  }\r\n\r\n  .tooltip-content {\r\n    padding: 0.5rem 0;\r\n    max-height: 18.75rem;\r\n    overflow-y: auto;\r\n\r\n    .tooltip-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0.5rem 1rem;\r\n      border-bottom: 1px solid #f3f4f6;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &:hover {\r\n        background: #f8f9fa;\r\n      }\r\n\r\n      .tooltip-name {\r\n        flex: 1;\r\n        color: #374151;\r\n        line-height: 1.4;\r\n        margin-right: 0.75rem;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        text-align: left;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .tooltip-value {\r\n        color: #2563eb;\r\n        font-weight: 600;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        min-width: 1.875rem;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式图标样式\r\n.stat-icon-img {\r\n  width: 40px; // 默认固定大小\r\n  height: 40px;\r\n  object-fit: contain;\r\n  flex-shrink: 0;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(24px, 3vw, 48px);\r\n    height: clamp(24px, 3vw, 48px);\r\n  }\r\n}\r\n\r\n// 小卡片图标样式（现在使用字体图标，此样式已不需要）\r\n// .stat-icon-img-small {\r\n//   width: 24px;\r\n//   height: 24px;\r\n//   object-fit: contain;\r\n//   flex-shrink: 0;\r\n\r\n//   @media (max-width: 1199px) {\r\n//     width: clamp(20px, 2.5vw, 28px);\r\n//     height: clamp(20px, 2.5vw, 28px);\r\n//   }\r\n// }\r\n\r\n.check-icon-img {\r\n  width: 14px; // 默认固定大小\r\n  height: 14px;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(12px, 1.5vw, 16px);\r\n    height: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n// 响应式媒体查询 - 针对1200px-1920px范围优化，让1200px也有1920px的效果\r\n@media (min-width: 1200px) and (max-width: 1920px) {\r\n  .home {\r\n    font-size: 14px;\r\n    /* 固定字体大小 */\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      .stat-icon {\r\n        width: 50px;\r\n        height: 50px;\r\n        min-width: 50px;\r\n        min-height: 50px;\r\n        max-width: 50px;\r\n        max-height: 50px;\r\n      }\r\n\r\n      .stat-content {\r\n        .stat-title {\r\n          font-size: 12px;\r\n        }\r\n\r\n        .stat-number-row {\r\n          .stat-number {\r\n            font-size: 24px;\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .management-card {\r\n    .management-header h3 {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .section-title {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .quality-stats .stat-group .stat-item {\r\n      .stat-header {\r\n        .stat-icon {\r\n          width: 18px;\r\n          height: 18px;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 13px;\r\n        }\r\n      }\r\n\r\n      .stat-number {\r\n        font-size: 20px;\r\n      }\r\n\r\n      .stat-detail span {\r\n        font-size: 10px;\r\n      }\r\n    }\r\n\r\n    // 添加安全管理数字字体大小设置\r\n    .safety-stat-card {\r\n      .safety-stat-main {\r\n        .safety-stat-number {\r\n          font-size: 22px;\r\n          /* 1300-1920px下适中的字体大小 */\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .check-icon-img {\r\n    width: 14px;\r\n    height: 14px;\r\n  }\r\n\r\n  .chart-card {\r\n    .chart-header h4 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .project-tooltip {\r\n    font-size: 12px;\r\n\r\n    .tooltip-header {\r\n      font-size: 13px;\r\n    }\r\n\r\n    .tooltip-content .tooltip-item {\r\n      .tooltip-name {\r\n        font-size: 12px;\r\n      }\r\n\r\n      .tooltip-value {\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 固定间距和布局\r\n  .top-stats {\r\n    gap: 20px;\r\n    /* 固定间距 */\r\n\r\n    .stat-card {\r\n      padding: 20px 15px;\r\n      /* 固定内边距 */\r\n      min-height: 120px;\r\n    }\r\n  }\r\n\r\n  .management-row {\r\n    margin-bottom: 20px;\r\n\r\n    .management-card {\r\n      padding: 15px;\r\n      /* 与默认样式保持一致 */\r\n      height: 350px;\r\n      /* 确保高度一致 */\r\n\r\n      .management-header {\r\n        margin-bottom: 10px;\r\n        /* 与默认样式保持一致 */\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .quality-stats {\r\n        margin-bottom: 15px;\r\n\r\n        .stat-group {\r\n          gap: 20px;\r\n          /* 固定间距 */\r\n        }\r\n      }\r\n\r\n      .safety-sections-container {\r\n        gap: 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定间距 */\r\n      }\r\n\r\n      .safety-section-independent {\r\n        padding: 10px 15px 12px 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定内边距 */\r\n\r\n        .safety-stats-row {\r\n          gap: 8px;\r\n          /* 与默认样式保持一致 */\r\n          /* 固定间距 */\r\n          margin-top: 10px;\r\n          /* 与默认样式保持一致 */\r\n\r\n          .safety-stat-card {\r\n            padding: 10px 12px;\r\n            /* 与默认样式保持一致 */\r\n            /* 固定内边距 */\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .charts-row {\r\n    .chart-card {\r\n      padding: 20px;\r\n      height: 340px;\r\n      /* 确保高度一致 */\r\n\r\n      .chart-header {\r\n        margin-bottom: 15px;\r\n      }\r\n\r\n      .chart-content {\r\n        height: calc(100% - 50px);\r\n        /* 固定计算高度 */\r\n      }\r\n    }\r\n  }\r\n\r\n  // 确保栅格系统间距一致\r\n  .el-row {\r\n    margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  // 统一进度条样式\r\n  .project-progress {\r\n    .progress-bar {\r\n      height: 12px;\r\n      /* 固定高度 */\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .home {\r\n    padding: 1%;\r\n    font-size: clamp(10px, 2vw, 14px);\r\n  }\r\n\r\n  .top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card {\r\n      min-height: 140px;\r\n      min-width: 100%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(1rem, 3vw, 1.25rem) !important;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1.25rem, 4vw, 2rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.875rem, 2.5vw, 1.125rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card-small {\r\n      min-width: 100%;\r\n      min-height: 70px;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(1rem, 3.5vw, 1.5rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1.125rem) !important;\r\n    /* 减小字体：12px-18px */\r\n  }\r\n\r\n  .management-row {\r\n    .el-col {\r\n      margin-bottom: 1%;\r\n    }\r\n  }\r\n\r\n  .safety-sections-container {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    height: 350px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 300px;\r\n  }\r\n\r\n  .project-tooltip {\r\n    max-width: 95vw;\r\n    min-width: 85vw;\r\n    width: auto;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .home {\r\n    font-size: clamp(8px, 3vw, 12px);\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      min-height: 120px;\r\n      padding: 3%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(0.875rem, 4vw, 1.125rem) !important;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1rem, 5vw, 1.75rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    .stat-card-small {\r\n      min-height: 60px;\r\n      padding: 2.5%;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(0.875rem, 4vw, 1.25rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 32px !important;\r\n    height: 32px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.625rem, 3vw, 1rem) !important;\r\n    /* 进一步减小：10px-16px */\r\n  }\r\n\r\n  // 新的安全管理移动端样式\r\n  .safety-top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .safety-stat-card-new {\r\n      padding: 3%;\r\n\r\n      .safety-stat-icon {\r\n        width: 40px !important;\r\n        height: 40px !important;\r\n\r\n        i {\r\n          font-size: 20px !important;\r\n        }\r\n      }\r\n\r\n      .safety-stat-content-new {\r\n        .safety-stat-label {\r\n          font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n        }\r\n\r\n        .safety-stat-number-new {\r\n          .number {\r\n            font-size: clamp(1rem, 4vw, 1.5rem) !important;\r\n          }\r\n\r\n          .unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .safety-bottom-stats {\r\n    .stats-row {\r\n      .stat-item {\r\n        gap: 1%;\r\n\r\n        .stat-label {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          min-width: 25px !important;\r\n        }\r\n\r\n        .stat-value {\r\n          font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-unit {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .management-card {\r\n    height: 320px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 280px;\r\n  }\r\n\r\n  .safety-stats-row {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .quality-stats .stat-group {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n}\r\n\r\n// 全局样式覆盖\r\n::v-deep .el-card {\r\n  border: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .el-card__header {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  padding: 1rem 1.25rem;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  padding: 1.25rem;\r\n}\r\n</style>\r\n"]}]}