<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">隐患统计</div>
            <div class="top-right">
              <el-switch v-model="isShowMX" /><span
                style="
                  font-size: 14px;
                  line-height: 30px;
                  margin-left: 10px;
                  margin-right: 26px;
                "
                >明细数据</span
              >
              <el-button
                type="primary"
                icon="el-icon-download"
                size="small"
                @click="exportExcel"
                >导出</el-button
              >
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="statqueryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="120px"
          >
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="statqueryParams.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                size="small"
              />
            </el-form-item>
            <!-- <el-form-item label="检查类型" prop="examsid">
                            <el-select v-model="queryParams.examid" placeholder="请选择检查类型">
                                <el-option v-for="item in checkTypeOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="整改状态" prop="rectificationstatus">
                            <el-select v-model="queryParams.rectificationstatus" placeholder="请选择整改状态">
                                <el-option v-for="item in yinhuanStatusOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="隐患等级" prop="gradepitfall">
                            <el-select v-model="queryParams.gradepitfall" placeholder="请选择整改状态">
                                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="具体描述" prop="refer">
                            <el-input v-model="queryParams.refer" placeholder="请输入具体描述" clearable
                                @keyup.enter.native="handleQuery" />
                        </el-form-item>
                        <el-form-item label="是否复查 " prop="check">
                            <el-input v-model="queryParams.check" placeholder="请输入是否复查 " clearable
                                @keyup.enter.native="handleQuery" />
                        </el-form-item>
                        <el-form-item label="检查人员" prop="inspectors">
                            <el-input v-model="queryParams.inspectors" placeholder="请输入检查人员" clearable
                                @keyup.enter.native="handleQuery" />
                        </el-form-item> -->
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button
                icon="el-icon-refresh"
                size="mini"
                @click="resetQueryStat"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <el-table
            v-loading="loading"
            :data="rectificationList"
            height="calc(100vh - 250px)"
            style="width: 100%"
          >
            <el-table-column label="上报人" align="center" prop="name" />
            <el-table-column label="检查单位数" align="center" prop="jcdw" />
            <el-table-column label="查出隐患数" align="center" prop="yhsl" />
            <el-table-column label="复查单位数" align="center" prop="fcdw" />
            <el-table-column label="隐患整改数 " align="center" prop="yhzg" />
            <el-table-column label="整改率" align="center" prop="zgl" />
          </el-table>

          <el-form
            style="margin-top: 30px"
            v-if="isShowMX"
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="120px"
          >
            <el-form-item label="检查类型" prop="examsid">
              <el-select
                v-model="queryParams.examid"
                placeholder="请选择检查类型"
                clearable
              >
                <el-option
                  v-for="item in checkTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="整改状态" prop="rectificationstatus">
              <el-select
                v-model="queryParams.rectificationstatus"
                placeholder="请选择整改状态"
                clearable
              >
                <el-option
                  v-for="item in yinhuanStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="隐患等级" prop="gradepitfall">
              <el-select
                v-model="queryParams.gradepitfall"
                placeholder="请选择整改状态"
                clearable
              >
                <el-option
                  v-for="item in levelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否复查 " prop="check">
              <el-select
                v-model="queryParams.check"
                placeholder="请选择复查状态"
                clearable
              >
                <el-option
                  v-for="item in checkOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <el-table
            v-if="isShowMX"
            v-loading="loading"
            :data="projectrectificationList"
            @selection-change="handleSelectionChange"
            height="calc(100vh - 250px)"
            style="width: 100%"
          >
            <el-table-column
              label="日期"
              align="center"
              prop="notificationtime"
            />
            <el-table-column
              label="单位名称"
              align="center"
              prop="corporatename"
            />
            <el-table-column label="责任人" align="center" prop="liaison" />
            <el-table-column
              label="责任电话"
              align="center"
              prop="phonenumber"
            />
            <el-table-column
              label="地址"
              align="center"
              prop="companyaddress"
            />
            <el-table-column
              label="检查隐患内容"
              align="center"
              prop="safetypitfall"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.safetypitfall }}</span>
              </template>
            </el-table-column>

            <el-table-column
              label="整改状态"
              align="center"
              prop="rectificationstatusName"
            />

            <el-table-column label="整改日期" align="center" prop="zgTime" />
            <el-table-column
              label="隐患类型"
              align="center"
              prop="pitfalltype"
            />
            <el-table-column
              label="隐患等级"
              align="center"
              prop="gradepitfallName"
            />
            <el-table-column
              label="督查负责人 "
              align="center"
              prop="nickName"
            />
            <el-table-column label="检查项" align="center" prop="examtype" />
          </el-table>

          <pagination
            v-if="isShowMX"
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  listProjectrectification,
  getProjectrectification,
  delProjectrectification,
  addProjectrectification,
  updateProjectrectification,
  statListProjectrectification,
  exportProjectrectification,
} from "@/api/system/projectrectification";
import { formatYMD } from "@/utils";

export default {
  name: "Projectrectification",
  data() {
    return {
      // 隐患等级 1：重大 2：较大 3：一般
      levelOptions: [
        { label: "重大", value: "1" },
        { label: "较大", value: "2" },
        { label: "一般", value: "3" },
        { label: "其他", value: "0" },
      ],
      // 整改状态  --1：未整改，2：已整改 3:已提交 4:未通过审核 5:急需整改 6：已逾期
      yinhuanStatusOptions: [
        { label: "未整改", value: 1 },
        { label: "已整改", value: 2 },
        { label: "已提交", value: 3 },
        { label: "未通过审核", value: 4 },
        { label: "急需整改", value: 5 },
        { label: "已逾期", value: 6 },
      ],
      // 检查类型
      checkTypeOptions: [
        { label: "安全生产", value: 1 },
        { label: "在建工地", value: 2 },
        { label: "燃气检查", value: 3 },
        { label: "食品安全", value: 4 },
      ],
      // 是否复查
      checkOptions: [
        { label: "需要复查", value: "需要复查" },
        { label: "不需要复查", value: "不需要复查" },
      ],
      // 统计数组
      rectificationList: [],
      // 是否显示明细列表
      isShowMX: true,
      // 编辑表单内容
      editForm: {},
      // 详情表单内容
      detailForm: {},
      // 显示详情表单
      // 显示详情表单
      showDetailForm: false,
      // 显示编辑表单
      showEditForm: false,
      // 隐患等级 1：重大 2：较大 3：一般
      levelOptions: [
        { label: "重大", value: "1" },
        { label: "较大", value: "2" },
        { label: "一般", value: "3" },
        { label: "其他", value: "0" },
      ],
      // 整改状态  --1：未整改，2：已整改 3:已提交 4:未通过审核 5:急需整改 6：已逾期
      yinhuanStatusOptions: [
        { label: "未整改", value: 1 },
        { label: "已整改", value: 2 },
        { label: "已提交", value: 3 },
        { label: "未通过审核", value: 4 },
        { label: "急需整改", value: 5 },
        { label: "已逾期", value: 6 },
      ],
      // 检查类型
      checkTypeOptions: [
        { label: "安全生产", value: 1 },
        { label: "在建工地", value: 2 },
        { label: "燃气检查", value: 3 },
        { label: "食品安全", value: 4 },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患表格数据
      projectrectificationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},

      statqueryParams: {
        timeRange: [new Date().getTime() - 3600 * 1000 * 24 * 7, new Date()],
      },
    };
  },
  created() {
    this.getStatList();
    this.getList();
  },
  methods: {
    // 导出表格
    exportExcel() {
      window.location.href =
        process.env.VUE_APP_BASE_API +
        "/pc/projectrectification/exportHazardStatistics?beginTime=" +
        formatYMD(this.statqueryParams.timeRange[0]) +
        "&endTime=" +
        formatYMD(this.statqueryParams.timeRange[1]);
    },

    // 查询隐患统计列表
    getStatList() {
      statListProjectrectification({
        beginTime: formatYMD(this.statqueryParams.timeRange[0]),
        endTime: formatYMD(this.statqueryParams.timeRange[1]),
      }).then((res) => {
        this.rectificationList = res.data;
      });
    },
    /** 查询隐患列表 */
    getList() {
      this.loading = true;
      listProjectrectification({
        ...this.queryParams,
        beginTime: formatYMD(this.statqueryParams.timeRange[0]),
        endTime: formatYMD(this.statqueryParams.timeRange[1]),
      }).then((res) => {
        res.rows.forEach((item) => {
          item.gradepitfallName = this.levelOptions.find(
            (grade) => grade.value == item.gradepitfall
          ).label;
          item.examName = this.checkTypeOptions.find(
            (exam) => exam.value == item.examsid
          ).label;
          item.rectificationstatusName = this.yinhuanStatusOptions.find(
            (status) => status.value == item.rectificationstatus
          ).label;
          item.isDX = item.isTypical == 1 ? true : false;
        });
        this.projectrectificationList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.queryParams = {
        projectrectificationid: null,
        examsid: null,
        userId: null,
        categoryid: null,
        pitfallid: null,
        pitfalltype: null,
        gradepitfall: null,
        safetypitfall: null,
        regulatory: null,
        refer: null,
        resultant: null,
        suggestions: null,
        check: null,
        deadline: null,
        superintendent: null,
        inspectors: null,
        dangerimages: null,
        dangerimagescount: null,
        rectificationstatus: null,
        rectificationphotos: null,
        notificationtime: null,
        checkdetailsid: null,
        overduestatus: null,
        rejected: null,
        remarks: null,
        reviewstatus: null,
        pageNum: 1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
      this.getStatList();
    },

    // 统计重置
    resetQueryStat() {
      this.queryParams.pageNum = 1;
      this.statqueryParams = {
        timeRange: [new Date().getTime() - 3600 * 1000 * 24 * 7, new Date()],
      };
      this.getList();
      this.getStatList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.reset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.projectrectificationid);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隐患";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const projectrectificationid = row.projectrectificationid || this.ids;
      getProjectrectification(projectrectificationid).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改隐患";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.projectrectificationid != null) {
            updateProjectrectification(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProjectrectification(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const projectrectificationids = row.projectrectificationid || this.ids;
      this.$modal
        .confirm(
          '是否确认删除隐患编号为"' + projectrectificationids + '"的数据项？'
        )
        .then(function () {
          return delProjectrectification(projectrectificationids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/projectrectification/export",
        {
          ...this.queryParams,
        },
        `projectrectification_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 100px);
  // overflow-y: auto;

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      .detail-center {
        display: flex;

        .detail-center-left {
          .detail-center-item {
            display: flex;
            line-height: 24px;
            margin: 12px 0;

            .detail-center-item-left {
              width: 70px;
            }

            .detail-center-item-right {
              flex: 1;
            }
          }
        }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 60%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
    align-items: center;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  height: calc(100vh - 150px);
  overflow-y: auto;
  font-size: 14px;
}
</style>
