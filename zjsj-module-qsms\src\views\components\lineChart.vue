<template>
    <div :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },
    watch: {
        data: {
            handler(newVal, oldVal) {
                if (this.chart) {
                    this.data = newVal
                    this.setOption()
                } else {
                    this.$nextTick(() => {
                        this.initChart()
                    })
                }
            },
            immediate: true
        }
    },
    data() {
        return {
            chart: null,
        }
    },
    mounted() {

    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
        window.removeEventListener('resize', this.chartResize)
    },
    methods: {
        chartResize() {
            this.chart.resize();
        },
        initChart() {
            let that = this
            that.chart = echarts.init(that.$el)
            window.addEventListener("resize", that.chartResize);
            that.chart.on('click', (params) => {
                let obj = {
                    seriesName: params.seriesName,
                    ...params.data
                }
                that.$emit('lineClick', obj)
            })
            that.setOption()
        },
        setOption() {
            let option = {
                tooltip: {
                    
                },
                grid: this.data.grid,
                xAxis: {
                    type: 'category',
                    data: this.data.xAxisData,
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',
                        }
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#666666'
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['#fff', 'rgba(0,0,0,0.02)']
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: "#EBEBEB",
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',
                        }
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#666666'
                        }
                    }
                },
                series: []
            }
            option.series = this.data.seriesData.map((item, index) => {
                return {
                    name: item.name,
                    type: 'line',
                    data: item.data,
                    label: {
                        show: true,
                        distance: 0,
                        offset: [-10, 0],
                        formatter: (params) => {
                            return `{a|${params.value}}`;
                        },
                        position: 'insideLeft',
                        rich: {
                            a: {
                                color: '#fff',
                                backgroundColor: item.backgroundColor,
                                padding: [4, 6, 2, 6],
                            }
                        }
                    },
                    itemStyle: {
                        color: item.backgroundColor,
                    }
                }
            })
            this.chart.setOption(option)

        }
    }
}
</script>