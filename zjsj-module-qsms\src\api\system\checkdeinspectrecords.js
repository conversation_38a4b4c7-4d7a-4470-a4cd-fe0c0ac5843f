import request from '@/utils/request'

// 查询项目检查列表
export function listCheckdeinspectrecords(query) {
  return request({
    url: '/pc/checkdeinspectrecords/list',
    method: 'get',
    params: query
  })
}

// 查询项目检查详细
export function getCheckdeinspectrecords(inspectrecordsid1) {
  return request({
    url: '/system/checkdeinspectrecords/' + inspectrecordsid1,
    method: 'get'
  })
}

// 新增项目检查
export function addCheckdeinspectrecords(data) {
  return request({
    url: '/system/checkdeinspectrecords',
    method: 'post',
    data: data
  })
}

// 修改项目检查
export function updateCheckdeinspectrecords(data) {
  return request({
    url: '/system/checkdeinspectrecords',
    method: 'put',
    data: data
  })
}

// 删除项目检查
export function delCheckdeinspectrecords(inspectrecordsid1) {
  return request({
    url: '/system/checkdeinspectrecords/' + inspectrecordsid1,
    method: 'delete'
  })
}

// 查询项目检查列表  inspectrecordsid1=207
export function queryByInSpectrecordsid(query) {
  return request({
    url: '/pc/projectrectification/queryByInSpectrecordsid',
    method: 'get',
    params: query
  })
}