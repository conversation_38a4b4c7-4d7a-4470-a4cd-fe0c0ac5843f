import request from "@/utils/request";

// 查询质量问题库列表
export function listZjQualityProblemInfo(query) {
  return request({
    url: "/inspection/zjQualityProblemInfo/list",
    method: "get",
    params: query,
  });
}

// 查询质量问题库详细
export function getZjQualityProblemInfo(qualityId) {
  return request({
    url: "/inspection/zjQualityProblemInfo/" + qualityId,
    method: "get",
  });
}

// 新增质量问题库
export function addZjQualityProblemInfo(data) {
  return request({
    url: "/inspection/zjQualityProblemInfo",
    method: "post",
    data: data,
  });
}

// 修改质量问题库
export function updateZjQualityProblemInfo(data) {
  return request({
    url: "/inspection/zjQualityProblemInfo",
    method: "put",
    data: data,
  });
}

// 删除质量问题库
export function delZjQualityProblemInfo(qualityId) {
  return request({
    url: "/inspection/zjQualityProblemInfo/" + qualityId,
    method: "delete",
  });
}
// 质量问题库类别
export function listZjQualityProblemCategory(query) {
  return request({
    url: "/inspection/zjQualityProblemInfo/tree",
    method: "get",
    params: query,
  });
}
// 一级质量问题库类别
export function listZjQualityProblemCategoryFirst(query) {
  return request({
    url: "/inspection/zjQualityProblemInfo/zjQualityProblemInfoList",
    method: "get",
    params: query,
  });
}
