import request from '@/utils/request'

// 查询应急设施装备列表
export function listZjEmergencyFacilities(query) {
  return request({
    url: '/inspection/zjEmergencyFacilities/list',
    method: 'get',
    params: query
  })
}

// 查询应急设施装备详细
export function getZjEmergencyFacilities(id) {
  return request({
    url: '/inspection/zjEmergencyFacilities/' + id,
    method: 'get'
  })
}

// 新增应急设施装备
export function addZjEmergencyFacilities(data) {
  return request({
    url: '/inspection/zjEmergencyFacilities',
    method: 'post',
    data: data
  })
}

// 修改应急设施装备
export function updateZjEmergencyFacilities(data) {
  return request({
    url: '/inspection/zjEmergencyFacilities',
    method: 'put',
    data: data
  })
}

// 删除应急设施装备
export function delZjEmergencyFacilities(id) {
  return request({
    url: '/inspection/zjEmergencyFacilities/' + id,
    method: 'delete'
  })
}
