import request from '@/utils/request'

// 查询超危大工程列表
export function listZjExtremelyDangerousProject(query) {
  return request({
    url: '/inspection/zjExtremelyDangerousProject/list',
    method: 'get',
    params: query
  })
}

// 查询超危大工程详细
export function getZjExtremelyDangerousProject(id) {
  return request({
    url: '/inspection/zjExtremelyDangerousProject/' + id,
    method: 'get'
  })
}

// 新增超危大工程
export function addZjExtremelyDangerousProject(data) {
  return request({
    url: '/inspection/zjExtremelyDangerousProject',
    method: 'post',
    data: data
  })
}

// 修改超危大工程
export function updateZjExtremelyDangerousProject(data) {
  return request({
    url: '/inspection/zjExtremelyDangerousProject',
    method: 'put',
    data: data
  })
}

// 删除超危大工程
export function delZjExtremelyDangerousProject(id) {
  return request({
    url: '/inspection/zjExtremelyDangerousProject/' + id,
    method: 'delete'
  })
}
