import request from "@/utils/request";

// 查询随手报列表
export function listReportcasuallyinfo(query) {
  return request({
    url: "/inspection/reportcasuallyinfo/list",
    method: "get",
    params: query,
  });
}

// 查询随手报详细
export function getReportcasuallyinfo(id) {
  return request({
    url: "/inspection/reportcasuallyinfo/getById/" + id,
    method: "get",
  });
}

// 新增随手报
export function addReportcasuallyinfo(data) {
  return request({
    url: "/inspection/reportcasuallyinfo",
    method: "post",
    data: data,
  });
}

// 修改随手报
export function updateReportcasuallyinfo(data) {
  return request({
    url: "/inspection/reportcasuallyinfo",
    method: "put",
    data: data,
  });
}

// 删除随手报
export function delReportcasuallyinfo(id) {
  return request({
    url: "/inspection/reportcasuallyinfo/" + id,
    method: "delete",
  });
}
