<template>
  <div class="app-container">
    <el-row>
      <el-col :span="3">
        <!-- 左侧树结构 -->
        <!-- <div class="title mb-2" @click="goToHazardCategory"></div> -->
        <el-tree
          :data="treeData"
          :props="defaultProps"
          @node-click="handleNodeClick"
          v-loading="treeLoading"
          :current-node-key="queryParams.typeId"
          highlight-current
          node-key="id"
        >
          <template #default="{ node, data }">
            <el-tooltip effect="dark" :content="data.label" placement="top">
              <span
                :ref="(el) => setLabelRef(el, node)"
                class="el-tree-node__label"
              >
                {{ node.label }}
              </span>
            </el-tooltip>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="20" style="margin-left: 10px">
        <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租户ID" prop="tenantId">
        <el-input
          v-model="queryParams.tenantId"
          placeholder="请输入租户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目ID" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="删除状态 0 未删除 1 已删除" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入删除状态 0 未删除 1 已删除"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新时间" prop="updatTime">
        <el-date-picker clearable
          v-model="queryParams.updatTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="辨识人ID" prop="creatorId">
        <el-input
          v-model="queryParams.creatorId"
          placeholder="请输入辨识人ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="辨识人姓名" prop="creatorName">
        <el-input
          v-model="queryParams.creatorName"
          placeholder="请输入辨识人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="危大工程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入危大工程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="危大类别ID" prop="typeId">
        <el-input
          v-model="queryParams.typeId"
          placeholder="请输入危大类别ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="危大类别名称" prop="typeName">
        <el-input
          v-model="queryParams.typeName"
          placeholder="请输入危大类别名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否超危 0 否 1 是" prop="limit">
        <el-input
          v-model="queryParams.limit"
          placeholder="请输入是否超危 0 否 1 是"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="施工时间-开始" prop="startDate">
        <el-date-picker clearable
          v-model="queryParams.startDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择施工时间-开始">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="施工时间-结束" prop="endDate">
        <el-date-picker clearable
          v-model="queryParams.endDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择施工时间-结束">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划时间-开始" prop="planStartDate">
        <el-date-picker clearable
          v-model="queryParams.planStartDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划时间-开始">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划时间-结束" prop="planEndDate">
        <el-date-picker clearable
          v-model="queryParams.planEndDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划时间-结束">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否完成安全技术交底 0 否 1 是" prop="safetyTechnologyDisclosure">
        <el-input
          v-model="queryParams.safetyTechnologyDisclosure"
          placeholder="请输入是否完成安全技术交底 0 否 1 是"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否完成安全专项施工方案 0 否 1是" prop="specialSfetyConstructionPlan">
        <el-input
          v-model="queryParams.specialSfetyConstructionPlan"
          placeholder="请输入是否完成安全专项施工方案 0 否 1是"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否专家论证 0 否 1 是" prop="expertArgumentation">
        <el-input
          v-model="queryParams.expertArgumentation"
          placeholder="请输入是否专家论证 0 否 1 是"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否完成专项施工方案交底 0 否 1 是" prop="specialConstructionPlan">
        <el-input
          v-model="queryParams.specialConstructionPlan"
          placeholder="请输入是否完成专项施工方案交底 0 否 1 是"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="未销项隐患数量" prop="unsoldItem">
        <el-input
          v-model="queryParams.unsoldItem"
          placeholder="请输入未销项隐患数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管控要点总数" prop="totalPoint">
        <el-input
          v-model="queryParams.totalPoint"
          placeholder="请输入管控要点总数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="已完成的管控要点数" prop="executedPoint">
        <el-input
          v-model="queryParams.executedPoint"
          placeholder="请输入已完成的管控要点数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form> -->

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >导出</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-col>

          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjDangerousBigProjectList"
          @selection-change="handleSelectionChange"
          height="calc(100vh - 200px)"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" type="index" width="50" align="center" />
          <el-table-column label="类别名称" align="center" prop="typeName" />
          <el-table-column label="类型描述" align="center" prop="content" show-overflow-tooltip />
          <el-table-column label="是否监测" align="center" prop="monitoring">
            <template slot-scope="scope">
              <el-tag :type="scope.row.monitoring === '1' ? 'success' : 'danger'">
                {{ scope.row.monitoring === '1' ? '监测' : '不监测' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否超危" align="center" prop="expertArgumentation">
            <template slot-scope="scope">
              <el-tag :type="scope.row.expertArgumentation === '1' ? 'danger' : 'success'">
                {{ scope.row.expertArgumentation === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="管控要点数" align="center" prop="controlPointsNumber" />
          <el-table-column label="管控要点" align="center" width="120">
            <template slot-scope="scope">
              <el-button 
                size="mini" 
                type="text" 
                @click="handleControlPointsClick(scope.row)"
                :disabled="!scope.row.controlPoints || scope.row.controlPoints.length === 0"
              >
                查看详情({{ scope.row.controlPointsNumber || 0 }})
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
          <!-- 
      <el-table-column
        label="施工时间-开始"
        align="center"
        prop="startDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="施工时间-结束"
        align="center"
        prop="endDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划时间-开始"
        align="center"
        prop="planStartDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planStartDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划时间-结束"
        align="center"
        prop="planEndDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planEndDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="是否完成安全技术交底 0 否 1 是"
        align="center"
        prop="safetyTechnologyDisclosure"
      />
      <el-table-column
        label="是否完成安全专项施工方案 0 否 1是"
        align="center"
        prop="specialSfetyConstructionPlan"
      /> -->
          <!-- <el-table-column
        label="是否专家论证 0 否 1 是"
        align="center"
        prop="expertArgumentation"
      />
      <el-table-column
        label="是否完成专项施工方案交底 0 否 1 是"
        align="center"
        prop="specialConstructionPlan"
      />
      <el-table-column
        label="未销项隐患数量"
        align="center"
        prop="unsoldItem"
      />

      <el-table-column
        label="已完成的管控要点数"
        align="center"
        prop="executedPoint"
      />
      <el-table-column
        label="最近一条验收记录状态 0 不合格 1合格"
        align="center"
        prop="lastAcceptanceRecordStatus"
      /> -->

        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改同步危大工程主记录记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="租户ID" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户ID" />
        </el-form-item>
        <el-form-item label="项目ID" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目ID" />
        </el-form-item>
        <!--  0 未删除 1 已删除 -->
        <el-form-item label="删除状态 " prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入删除状态" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updatTime">
          <el-date-picker
            clearable
            v-model="form.updatTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="辨识人ID" prop="creatorId">
          <el-input v-model="form.creatorId" placeholder="请输入辨识人ID" />
        </el-form-item>
        <el-form-item label="辨识人姓名" prop="creatorName">
          <el-input v-model="form.creatorName" placeholder="请输入辨识人姓名" />
        </el-form-item>
        <el-form-item label="危大工程名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入危大工程名称" />
        </el-form-item>
        <el-form-item label="危大工程概况">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item label="危大类别ID" prop="typeId">
          <el-input v-model="form.typeId" placeholder="请输入危大类别ID" />
        </el-form-item>
        <el-form-item label="危大类别名称" prop="typeName">
          <el-input v-model="form.typeName" placeholder="请输入危大类别名称" />
        </el-form-item>
        <el-form-item label="是否超危 0 否 1 是" prop="limit">
          <el-input
            v-model="form.limit"
            placeholder="请输入是否超危 0 否 1 是"
          />
        </el-form-item>
        <el-form-item label="施工时间-开始" prop="startDate">
          <el-date-picker
            clearable
            v-model="form.startDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择施工时间-开始"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="施工时间-结束" prop="endDate">
          <el-date-picker
            clearable
            v-model="form.endDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择施工时间-结束"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划时间-开始" prop="planStartDate">
          <el-date-picker
            clearable
            v-model="form.planStartDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划时间-开始"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划时间-结束" prop="planEndDate">
          <el-date-picker
            clearable
            v-model="form.planEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划时间-结束"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="是否完成安全技术交底 0 否 1 是"
          prop="safetyTechnologyDisclosure"
        >
          <el-input
            v-model="form.safetyTechnologyDisclosure"
            placeholder="请输入是否完成安全技术交底 0 否 1 是"
          />
        </el-form-item>
        <el-form-item
          label="是否完成安全专项施工方案 0 否 1是"
          prop="specialSfetyConstructionPlan"
        >
          <el-input
            v-model="form.specialSfetyConstructionPlan"
            placeholder="请输入是否完成安全专项施工方案 0 否 1是"
          />
        </el-form-item>
        <el-form-item label="是否专家论证 0 否 1 是" prop="expertArgumentation">
          <el-input
            v-model="form.expertArgumentation"
            placeholder="请输入是否专家论证 0 否 1 是"
          />
        </el-form-item>
        <el-form-item
          label="是否完成专项施工方案交底 0 否 1 是"
          prop="specialConstructionPlan"
        >
          <el-input
            v-model="form.specialConstructionPlan"
            placeholder="请输入是否完成专项施工方案交底 0 否 1 是"
          />
        </el-form-item>
        <el-form-item label="未销项隐患数量" prop="unsoldItem">
          <el-input
            v-model="form.unsoldItem"
            placeholder="请输入未销项隐患数量"
          />
        </el-form-item>
        <el-form-item label="管控要点总数" prop="totalPoint">
          <el-input
            v-model="form.totalPoint"
            placeholder="请输入管控要点总数"
          />
        </el-form-item>
        <el-form-item label="已完成的管控要点数" prop="executedPoint">
          <el-input
            v-model="form.executedPoint"
            placeholder="请输入已完成的管控要点数"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 管控要点详情弹窗 -->
    <el-dialog 
      title="管控要点详情" 
      :visible.sync="controlPointsDialog" 
      width="80%" 
      append-to-body
      :close-on-click-modal="false"
    >
      <div v-if="currentControlPoints.length > 0">
        <el-table 
          :data="currentControlPoints" 
          border 
          stripe 
          max-height="500"
          style="width: 100%"
        >
          <el-table-column 
            label="序号" 
            type="index" 
            width="60" 
            align="center"
          />
          <el-table-column 
            label="分类" 
            prop="controlTypeName" 
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.controlTypeName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column 
            label="管控内容" 
            prop="content" 
            align="left"
            min-width="300"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div class="content-cell">
                {{ scope.row.content }}
              </div>
            </template>
          </el-table-column>
          <el-table-column 
            label="合格状态" 
            prop="yesStatus" 
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <el-tag 
                :type="scope.row.yesStatus === '已完成' || scope.row.yesStatus === '合格' ? 'success' : 'info'"
              >
                {{ scope.row.yesStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column 
            label="不合格状态" 
            prop="noStatus" 
            align="center"
            width="100"
          >
            <template slot-scope="scope">
              <el-tag 
                :type="scope.row.noStatus === '未完成' || scope.row.noStatus === '不合格' ? 'warning' : 'info'"
              >
                {{ scope.row.noStatus }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <div v-else class="no-data">
        <el-empty description="暂无管控要点数据"></el-empty>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="controlPointsDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjDangerousBigProject,
  getZjDangerousBigProject,
  delZjDangerousBigProject,
  addZjDangerousBigProject,
  updateZjDangerousBigProject,
} from "@/api/inspection/zjDangerousBigProject";
import { listZjHazardousProjectCategory } from "@/api/inspection/zjHazardousProjectCategory";

export default {
  name: "ZjDangerousBigProject",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 危险工程分类表格数据
      zjDangerousBigProjectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        typeId: null,
        typeName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 树形数据（保持原有的硬编码数据）
      treeData: [
        {
          id: '128621949831229440',
          label: "模板工程及支撑体系",
        },
        {
          id: '128621951169212416',
          label: "脚手架工程",
        },
        {
          id: '128621949592154112',
          label: "基坑工程",
        },
        {
          id: '128621951043383296',
          label: "起重吊装及起重机械安装拆卸工程",
        },
        {
          id: '128621951345373184',
          label: "拆除工程",
        },
        {
          id: '128621951416676352',
          label: "暗挖工程",
        },
        {
          id: '128621951454425088',
          label: "其它",
        },
      ],
      treeLoading: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 管控要点弹窗相关
      controlPointsDialog: false,
      currentControlPoints: [],
      currentTypeName: '',
    };
  },
  created() {
    // 默认选中第一个树节点
    if (this.treeData && this.treeData.length > 0) {
      this.queryParams.typeId = this.treeData[0].id;
    }
    this.getList();
  },
  methods: {
    handleNodeClick(node) {
      this.queryParams.typeId = node.id;
      this.getList();
    },
    /** 管控要点点击事件 */
    handleControlPointsClick(row) {
      if (!row.controlPoints || row.controlPoints.length === 0) {
        this.$modal.msgWarning("该项目暂无管控要点数据");
        return;
      }
      
      this.currentControlPoints = row.controlPoints;
      this.currentTypeName = row.typeName || '未知类别';
      this.controlPointsDialog = true;
    },
    /** 查询危险工程分类列表 */
    getList() {
      this.loading = true;
      listZjHazardousProjectCategory(this.queryParams).then((response) => {
        this.zjDangerousBigProjectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectCode: null,
        projectName: null,
        tenantId: null,
        projectId: null,
        deleted: null,
        createTime: null,
        updatTime: null,
        creatorId: null,
        creatorName: null,
        name: null,
        content: null,
        typeId: null,
        typeName: null,
        limit: null,
        startDate: null,
        endDate: null,
        planStartDate: null,
        planEndDate: null,
        safetyTechnologyDisclosure: null,
        specialSfetyConstructionPlan: null,
        expertArgumentation: null,
        specialConstructionPlan: null,
        unsoldItem: null,
        totalPoint: null,
        executedPoint: null,
        lastAcceptanceRecordStatus: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.typeId = null;
      this.queryParams.typeName = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加同步危大工程主记录记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjDangerousBigProject(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改同步危大工程主记录记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjDangerousBigProject(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjDangerousBigProject(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除同步危大工程主记录记录编号为"' + ids + '"的数据项？'
        )
        .then(function () {
          return delZjDangerousBigProject(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjHazardousProjectCategory/export",
        {
          ...this.queryParams,
        },
        `zjHazardousProjectCategory_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped>
.title {
  font-size: 16px;
  cursor: pointer;
}
::v-deep .el-tree-node__label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}

/* 管控要点弹窗样式 */
.control-points-header {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.control-points-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.control-points-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.content-cell {
  line-height: 1.5;
  word-break: break-word;
  white-space: pre-wrap;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

/* 树形结构选中样式 */
.el-tree-node.is-current > .el-tree-node__content {
  background-color: #409EFF !important;
  color: white !important;
}

.el-tree-node.is-current > .el-tree-node__content:hover {
  background-color: #337ecc !important;
}

/* 树形结构节点悬停效果 */
.el-tree-node__content:hover {
  background-color: #f5f7fa;
}
</style>
