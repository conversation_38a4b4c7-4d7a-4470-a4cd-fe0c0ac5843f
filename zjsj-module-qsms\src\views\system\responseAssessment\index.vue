<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <!-- <el-form-item label="考核对象ID" prop="targetId">
        <el-input
          v-model="queryParams.targetId"
          placeholder="请输入考核对象ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="考核对象名称" prop="targetName">
        <el-input
          v-model="queryParams.targetName"
          placeholder="请输入考核对象名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="周期开始日期" prop="cycleStart">
        <el-date-picker
          clearable
          v-model="queryParams.cycleStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择周期开始日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="周期结束日期" prop="cycleEnd">
        <el-date-picker
          clearable
          v-model="queryParams.cycleEnd"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择周期结束日期"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="履职完成率" prop="dutyCompletion">
        <el-input
          v-model="queryParams.dutyCompletion"
          placeholder="请输入履职完成率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="违规次数" prop="violationCount">
        <el-input
          v-model="queryParams.violationCount"
          placeholder="请输入违规次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故次数" prop="accidentCount">
        <el-input
          v-model="queryParams.accidentCount"
          placeholder="请输入事故次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核总分" prop="totalScore">
        <el-input
          v-model="queryParams.totalScore"
          placeholder="请输入考核总分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核人" prop="assessor">
        <el-input
          v-model="queryParams.assessor"
          placeholder="请输入考核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:assessment:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:assessment:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:assessment:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:assessment:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="assessmentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="考核记录ID" align="center" prop="id" /> -->
      <el-table-column
        label="考核对象类型"
        align="center"
        prop="assessmentType"
      />
      <!-- <el-table-column label="考核对象ID" align="center" prop="targetId" /> -->
      <el-table-column label="考核对象名称" align="center" prop="targetName" />
      <el-table-column label="考核周期" align="center" prop="cycleType" />
      <el-table-column
        label="周期开始日期"
        align="center"
        prop="cycleStart"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.cycleStart, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="周期结束日期"
        align="center"
        prop="cycleEnd"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.cycleEnd, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="履职完成率"
        align="center"
        prop="dutyCompletion"
      />
      <el-table-column label="违规次数" align="center" prop="violationCount" />
      <el-table-column label="事故次数" align="center" prop="accidentCount" />
      <el-table-column
        label="最高事故等级"
        align="center"
        prop="accidentLevel"
      />
      <el-table-column label="考核总分" align="center" prop="totalScore" />
      <el-table-column
        label="考核结果"
        align="center"
        prop="assessmentResult"
      />
      <el-table-column
        label="考核结果说明"
        align="center"
        prop="assessmentReason"
      />
      <el-table-column label="考核人" align="center" prop="assessor" />
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:assessment:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:assessment:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全生产责任制考核对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="考核对象ID" prop="targetId">
          <el-input v-model="form.targetId" placeholder="请输入考核对象ID" />
        </el-form-item>
        <el-form-item label="考核对象名称" prop="targetName">
          <el-input
            v-model="form.targetName"
            placeholder="请输入考核对象名称"
          />
        </el-form-item>
        <el-form-item label="周期开始日期" prop="cycleStart">
          <el-date-picker
            clearable
            v-model="form.cycleStart"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择周期开始日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="周期结束日期" prop="cycleEnd">
          <el-date-picker
            clearable
            v-model="form.cycleEnd"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择周期结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="履职完成率" prop="dutyCompletion">
          <el-input
            v-model="form.dutyCompletion"
            placeholder="请输入履职完成率"
          />
        </el-form-item>
        <el-form-item label="违规次数" prop="violationCount">
          <el-input
            v-model="form.violationCount"
            placeholder="请输入违规次数"
          />
        </el-form-item>
        <el-form-item label="事故次数" prop="accidentCount">
          <el-input v-model="form.accidentCount" placeholder="请输入事故次数" />
        </el-form-item>
        <el-form-item label="考核总分" prop="totalScore">
          <el-input v-model="form.totalScore" placeholder="请输入考核总分" />
        </el-form-item>
        <el-form-item label="考核结果说明" prop="assessmentReason">
          <el-input
            v-model="form.assessmentReason"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="考核人" prop="assessor">
          <el-input v-model="form.assessor" placeholder="请输入考核人" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAssessment,
  getAssessment,
  delAssessment,
  addAssessment,
  updateAssessment,
} from "@/api/system/responseAssessment/index";

export default {
  name: "Assessment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全生产责任制考核表格数据
      assessmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assessmentType: null,
        targetId: null,
        targetName: null,
        cycleType: null,
        cycleStart: null,
        cycleEnd: null,
        dutyCompletion: null,
        violationCount: null,
        accidentCount: null,
        accidentLevel: null,
        totalScore: null,
        assessmentResult: null,
        assessmentReason: null,
        assessor: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        assessmentType: [
          {
            required: true,
            message: "考核对象类型：部门/个人不能为空",
            trigger: "change",
          },
        ],
        targetId: [
          { required: true, message: "考核对象ID不能为空", trigger: "blur" },
        ],
        targetName: [
          { required: true, message: "考核对象名称不能为空", trigger: "blur" },
        ],
        cycleType: [
          {
            required: true,
            message: "考核周期：月度/季度/年度不能为空",
            trigger: "change",
          },
        ],
        cycleStart: [
          { required: true, message: "周期开始日期不能为空", trigger: "blur" },
        ],
        cycleEnd: [
          { required: true, message: "周期结束日期不能为空", trigger: "blur" },
        ],
        totalScore: [
          { required: true, message: "考核总分不能为空", trigger: "blur" },
        ],
        assessmentResult: [
          { required: true, message: "考核结果不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全生产责任制考核列表 */
    getList() {
      this.loading = true;
      listAssessment(this.queryParams).then((response) => {
        this.assessmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        assessmentType: null,
        targetId: null,
        targetName: null,
        cycleType: null,
        cycleStart: null,
        cycleEnd: null,
        dutyCompletion: null,
        violationCount: null,
        accidentCount: null,
        accidentLevel: null,
        totalScore: null,
        assessmentResult: null,
        assessmentReason: null,
        assessor: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全生产责任制考核";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAssessment(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全生产责任制考核";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAssessment(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAssessment(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全生产责任制考核编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAssessment(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/assessment/export",
        {
          ...this.queryParams,
        },
        `assessment_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
