<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input
          v-model="queryParams.equipmentName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="型号规格" prop="modelSpec">
        <el-input
          v-model="queryParams.modelSpec"
          placeholder="请输入型号规格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产厂家" prop="manufacturer">
        <el-input
          v-model="queryParams.manufacturer"
          placeholder="请输入生产厂家"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="设备出厂编号" prop="productionSerialNo">
        <el-input
          v-model="queryParams.productionSerialNo"
          placeholder="请输入设备出厂编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产日期" prop="productionDate">
        <el-date-picker
          clearable
          v-model="queryParams.productionDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择生产日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="特种设备使用登记证号" prop="registrationNo">
        <el-input
          v-model="queryParams.registrationNo"
          placeholder="请输入特种设备使用登记证号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="登记日期" prop="registrationDate">
        <el-date-picker
          clearable
          v-model="queryParams.registrationDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择登记日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="安装使用位置" prop="installationLocation">
        <el-input
          v-model="queryParams.installationLocation"
          placeholder="请输入安装使用位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备入库/进场日期" prop="checkinDate">
        <el-date-picker
          clearable
          v-model="queryParams.checkinDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择设备入库/进场日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="设备出库/退场日期" prop="checkoutDate">
        <el-date-picker
          clearable
          v-model="queryParams.checkoutDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择设备出库/退场日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="最新检测报告编号" prop="latestTestReportNo">
        <el-input
          v-model="queryParams.latestTestReportNo"
          placeholder="请输入最新检测报告编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最新检测机构" prop="latestTestOrg">
        <el-input
          v-model="queryParams.latestTestOrg"
          placeholder="请输入最新检测机构"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最新检测日期" prop="latestTestDate">
        <el-date-picker
          clearable
          v-model="queryParams.latestTestDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择最新检测日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="检测有效期至" prop="testValidUntil">
        <el-date-picker
          clearable
          v-model="queryParams.testValidUntil"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择检测有效期至"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="提前预警天数" prop="earlyWarningDays">
        <el-input
          v-model="queryParams.earlyWarningDays"
          placeholder="请输入提前预警天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备管理员" prop="manager">
        <el-input
          v-model="queryParams.manager"
          placeholder="请输入设备管理员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管理员联系方式" prop="managerContact">
        <el-input
          v-model="queryParams.managerContact"
          placeholder="请输入管理员联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:account:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:account:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:account:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:account:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="accountList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="所属公司" align="center" prop="company" />
      <el-table-column label="设备名称" align="center" prop="equipmentName" />
      <el-table-column label="设备类型" align="center" prop="equipmentType" />
      <el-table-column label="型号规格" align="center" prop="modelSpec" />
      <el-table-column label="生产厂家" align="center" prop="manufacturer" />
      <el-table-column
        label="设备出厂编号"
        align="center"
        prop="productionSerialNo"
      />
      <el-table-column
        label="生产日期"
        align="center"
        prop="productionDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.productionDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
        <el-table-column
        label="下次检查日期"
        align="center"
        prop="dateNextInspection"
      />
        <el-table-column
        label="检验周期(月)"
        align="center"
        prop="inspectionCycle"
      />

      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:account:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:account:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属公司" prop="company">
              <!-- <el-input v-model="form.company" placeholder="请输入所属公司" /> -->
              <selectPeopleTree
                v-model="form.company"
                :peopleList="companyList"
                placeholder="请搜索或选择所属公司"
                @change="handleChange"
                ref="chargePersonName"
              ></selectPeopleTree>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="equipmentName">
              <el-input v-model="form.equipmentName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号规格" prop="modelSpec">
              <el-input v-model="form.modelSpec" placeholder="请输入型号规格" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产厂家" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入生产厂家" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备出厂编号" prop="productionSerialNo">
              <el-input
                v-model="form.productionSerialNo"
                placeholder="请输入设备出厂编号"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产日期" prop="productionDate">
              <el-date-picker
                clearable
                v-model="form.productionDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择生产日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登记日期" prop="registrationDate">
              <el-date-picker
                clearable
                v-model="form.registrationDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择登记日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="下次检查日期" prop="dateNextInspection">
              <el-date-picker
                clearable
                v-model="form.dateNextInspection"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择下次检查日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查周期(月)" prop="inspectionCycle">
              <el-input
                v-model="form.inspectionCycle"
                placeholder="请输入检查周期(月)"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="安装使用位置" prop="installationLocation">
              <el-input
                v-model="form.installationLocation"
                placeholder="请输入安装使用位置"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备入库/进场日期" prop="checkinDate">
              <el-date-picker
                clearable
                v-model="form.checkinDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择设备入库/进场日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备出库/退场日期" prop="checkoutDate">
              <el-date-picker
                clearable
                v-model="form.checkoutDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择设备出库/退场日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最新检测报告编号" prop="latestTestReportNo">
              <el-input
                v-model="form.latestTestReportNo"
                placeholder="请输入最新检测报告编号"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最新检测机构" prop="latestTestOrg">
              <el-input
                v-model="form.latestTestOrg"
                placeholder="请输入最新检测机构"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最新检测日期" prop="latestTestDate">
              <el-date-picker
                clearable
                v-model="form.latestTestDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择最新检测日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检测有效期至" prop="testValidUntil">
              <el-date-picker
                clearable
                v-model="form.testValidUntil"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择检测有效期至"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提前预警天数" prop="earlyWarningDays">
              <el-input
                v-model="form.earlyWarningDays"
                placeholder="请输入提前预警天数"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备管理员" prop="manager">
              <el-input v-model="form.manager" placeholder="请输入设备管理员" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理员联系方式" prop="managerContact">
              <el-input
                v-model="form.managerContact"
                placeholder="请输入管理员联系方式"
              />
            </el-form-item>
          </el-col>
        </el-row>
        

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAccount,
  getAccount,
  delAccount,
  addAccount,
  updateAccount,
} from "@/api/system/equipmentLedgerManagement/index";
import { getEnterpriseInfo } from "@/api/system/info";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
export default {
  name: "Account",
  components: {
    AttachmentDialog,
    selectPeopleTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 大型设备（含特种设备）台账管理（支持检测过期预警）表格数据
      accountList: [],
      companyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        equipmentName: null,
        equipmentType: null,
        modelSpec: null,
        manufacturer: null,
        productionSerialNo: null,
        productionDate: null,
        registrationNo: null,
        registrationDate: null,
        installationLocation: null,
        useStatus: null,
        checkinDate: null,
        checkoutDate: null,
        latestTestReportNo: null,
        latestTestOrg: null,
        latestTestDate: null,
        testValidUntil: null,
        testReportUrl: null,
        earlyWarningDays: null,
        warningStatus: null,
        manager: null,
        managerContact: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "所属公司不能为空", trigger: "change" },
        ],
        equipmentName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
        ],
        equipmentType: [
          { required: true, message: "设备类型不能为空", trigger: "change" },
        ],
        modelSpec: [
          { required: true, message: "型号规格不能为空", trigger: "blur" },
        ],
        manufacturer: [
          { required: true, message: "生产厂家不能为空", trigger: "blur" },
        ],
        productionDate: [
          { required: true, message: "生产日期不能为空", trigger: "blur" },
        ],
        installationLocation: [
          { required: true, message: "安装使用位置不能为空", trigger: "blur" },
        ],
        checkinDate: [
          {
            required: true,
            message: "设备入库/进场日期不能为空",
            trigger: "blur",
          },
        ],
        manager: [
          { required: true, message: "设备管理员不能为空", trigger: "blur" },
        ],

      },
    };
  },
  created() {
    this.getList();
    this.getCompanyList();
  },
  methods: {
        handleChange(selectedItem) {
      if (selectedItem) {
        this.form.company = selectedItem.label;
      } else {
        this.form.company = null;
      }
    },
        getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    /** 查询大型设备（含特种设备）台账管理（支持检测过期预警）列表 */
    getList() {
      this.loading = true;
      listAccount(this.queryParams).then((response) => {
        this.accountList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectName: null,
        company: null,
        inspectionCycle: null,
        dateNextInspection: null,
        equipmentName: null,
        equipmentType: null,
        modelSpec: null,
        manufacturer: null,
        productionSerialNo: null,
        productionDate: null,
        registrationNo: null,
        registrationDate: null,
        installationLocation: null,
        useStatus: null,
        checkinDate: null,
        checkoutDate: null,
        latestTestReportNo: null,
        latestTestOrg: null,
        latestTestDate: null,
        testValidUntil: null,
        testReportUrl: null,
        earlyWarningDays: null,
        warningStatus: null,
        manager: null,
        managerContact: null,

        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加大型设备（含特种设备）台账管理（支持检测过期预警）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAccount(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改大型设备（含特种设备）台账管理（支持检测过期预警）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAccount(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAccount(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除大型设备（含特种设备）台账管理（支持检测过期预警）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delAccount(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/account/export",
        {
          ...this.queryParams,
        },
        `account_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
