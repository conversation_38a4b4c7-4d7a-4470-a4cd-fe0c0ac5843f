<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">隐患列表</div>
            <div class="top-right"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="120px"
          >
            <el-form-item label="检查类型" prop="examsid">
              <el-select
                v-model="queryParams.examid"
                placeholder="请选择检查类型"
              >
                <el-option
                  v-for="item in checkTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="整改状态" prop="rectificationstatus">
              <el-select
                v-model="queryParams.rectificationstatus"
                placeholder="请选择整改状态"
              >
                <el-option
                  v-for="item in yinhuanStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="隐患等级" prop="gradepitfall">
              <el-select
                v-model="queryParams.gradepitfall"
                placeholder="请选择整改状态"
              >
                <el-option
                  v-for="item in levelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否复查 " prop="check">
              <el-select
                v-model="queryParams.check"
                placeholder="请选择复查状态"
                clearable
              >
                <el-option
                  v-for="item in checkOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:projectrectification:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:projectrectification:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:projectrectification:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:projectrectification:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

          <el-table
            v-loading="loading"
            :data="projectrectificationList"
            @selection-change="handleSelectionChange"
            height="calc(100vh - 250px)"
            style="width: 100%"
          >
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column label="设为典型" align="center">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.isDX"
                  @change="updataRow(scope.row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column
              label="项目名称"
              align="center"
              prop="corporatename"
            />
            <el-table-column label="检查类型" align="center" prop="examName" />
            <el-table-column
              label="隐患类型"
              align="center"
              prop="pitfalltype"
            />
            <el-table-column
              label="隐患等级"
              align="center"
              prop="gradepitfallName"
            />
            <el-table-column
              label="安全隐患"
              align="center"
              prop="safetypitfall"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.safetypitfall }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否复查 " align="center" prop="check" />
            <el-table-column
              label="检查人员"
              align="center"
              prop="inspectors"
            />
            <el-table-column
              label="整改状态"
              align="center"
              prop="rectificationstatusName"
            />
            <el-table-column
              label="操作"
              fixed="right"
              width="150"
              align="center"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click="handleCheck(scope.row)"
                  v-hasPermi="['system:yihuan:check']"
                  >查看</el-button
                >
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:yihuan:check']"
                  >删除</el-button
                >
                <!-- <el-button size="mini" type="primary" plain @click="handleEdit(scope.row)"
                                    v-hasPermi="['system:yihuan:edit']">编辑</el-button> -->
                <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:projectrectification:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:projectrectification:remove']">删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改隐患对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="检查id​     安全生产，环保检查，在建工地" prop="examsid">
            <el-input v-model="form.examsid" placeholder="请输入检查id​     安全生产，环保检查，在建工地" />
          </el-form-item>
          <el-form-item label="登录人id" prop="userId">
            <el-input v-model="form.userId" placeholder="请输入登录人id" />
          </el-form-item>
          <el-form-item label="大类id" prop="categoryid">
            <el-input v-model="form.categoryid" placeholder="请输入大类id" />
          </el-form-item>
          <el-form-item label="隐患id" prop="pitfallid">
            <el-input v-model="form.pitfallid" placeholder="请输入隐患id" />
          </el-form-item>
          <el-form-item label="隐患等级 1：重大 2：较大 3：一般" prop="gradepitfall">
            <el-input v-model="form.gradepitfall" placeholder="请输入隐患等级 1：重大 2：较大 3：一般" />
          </el-form-item>
          <el-form-item label="安全隐患" prop="safetypitfall">
            <el-input v-model="form.safetypitfall" placeholder="请输入安全隐患" />
          </el-form-item>
          <el-form-item label="法规条款" prop="regulatory">
            <el-input v-model="form.regulatory" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="具体描述，整改要求" prop="refer">
            <el-input v-model="form.refer" placeholder="请输入具体描述，整改要求" />
          </el-form-item>
          <el-form-item label="可能产生后果" prop="resultant">
            <el-input v-model="form.resultant" placeholder="请输入可能产生后果" />
          </el-form-item>
          <el-form-item label="整改建议 " prop="suggestions">
            <el-input v-model="form.suggestions" placeholder="请输入整改建议 " />
          </el-form-item>
          <el-form-item label="是否复查 " prop="check">
            <el-input v-model="form.check" placeholder="请输入是否复查 " />
          </el-form-item>
          <el-form-item label="整改期限" prop="deadline">
            <el-input v-model="form.deadline" placeholder="请输入整改期限" />
          </el-form-item>
          <el-form-item label="整改负责人  " prop="superintendent">
            <el-input v-model="form.superintendent" placeholder="请输入整改负责人  " />
          </el-form-item>
          <el-form-item label="检查人员" prop="inspectors">
            <el-input v-model="form.inspectors" placeholder="请输入检查人员" />
          </el-form-item>
          <el-form-item label="隐患图片" prop="dangerimages">
            <el-input v-model="form.dangerimages" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="隐患图片数量" prop="dangerimagescount">
            <el-input v-model="form.dangerimagescount" placeholder="请输入隐患图片数量" />
          </el-form-item>
          <el-form-item label="整改照片" prop="rectificationphotos">
            <el-input v-model="form.rectificationphotos" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="通报时间" prop="notificationtime">
            <el-input v-model="form.notificationtime" placeholder="请输入通报时间" />
          </el-form-item>
          <el-form-item label="隶属检查项目" prop="checkdetailsid">
            <el-input v-model="form.checkdetailsid" placeholder="请输入隶属检查项目" />
          </el-form-item>
          <el-form-item label="1：驳回 2：通过" prop="rejected">
            <el-input v-model="form.rejected" placeholder="请输入1：驳回 2：通过" />
          </el-form-item>
          <el-form-item label="整改备注" prop="remarks">
            <el-input v-model="form.remarks" placeholder="请输入整改备注" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog> -->

    <el-dialog :visible.sync="showDetailForm" width="80%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>{{ detailForm.safetypitfall }}</div>
      </div>
      <div class="detail-content">
        <div class="detail-item">
          <div class="detail-top">
            <span class="state red-bg white">{{
              detailForm.rectificationstatusName
            }}</span>
            <div class="name">
              {{ detailForm.examName }} {{ detailForm.pitfalltype }}
            </div>
            <div class="state red red-bg-10">
              {{ detailForm.gradepitfallName }}
            </div>
          </div>
          <div class="detail-center">
            <div class="detail-center-left">
              <div class="detail-center-item">
                <div class="detail-center-item-left">安全隐患：</div>
                <div class="detail-center-item-right">
                  {{ detailForm.safetypitfall }}
                </div>
              </div>
              <div class="detail-center-item">
                <div class="detail-center-item-left">法规条款：</div>
                <div class="detail-center-item-right">
                  {{ detailForm.regulatory }}
                </div>
              </div>
              <div class="detail-center-item">
                <div class="detail-center-item-left">可能后果：</div>
                <div class="detail-center-item-right">
                  {{ detailForm.resultant }}
                </div>
              </div>
              <div class="detail-center-item" v-if="detailForm.dangerimages">
                <div class="detail-center-item-left">隐患图片：</div>
                <div class="detail-center-item-right">
                  <el-image
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="img"
                    fit="cover"
                    :preview-src-list="detailForm.imgList"
                    v-for="(img, index) in detailForm.imgList"
                    :key="index"
                  >
                  </el-image>
                </div>
              </div>
              <div
                class="detail-center-item"
                v-if="detailForm.corporatenamesign"
              >
                <div class="detail-center-item-left">签字图片：</div>
                <div class="detail-center-item-right">
                  <el-image
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="img"
                    fit="cover"
                    :preview-src-list="detailForm.imgListsign"
                    v-for="(img, index) in detailForm.imgListsign"
                    :key="index"
                  >
                  </el-image>
                </div>
              </div>
              <div
                class="detail-center-item"
                v-if="detailForm.rectificationphotos"
              >
                <div class="detail-center-item-left">整改图片：</div>
                <div class="detail-center-item-right">
                  <el-image
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="img"
                    fit="cover"
                    :preview-src-list="detailForm.imgListgai"
                    v-for="(img, index) in detailForm.imgListgai"
                    :key="index"
                  >
                  </el-image>
                </div>
              </div>
            </div>
            <!-- <div class="detail-center-right">
                            <el-image style="width: 100px; height: 100px" :src="img" fit="cover"
                                :preview-src-list="detailForm.imgList" v-for="(img, index) in detailForm.imgList">
                            </el-image>
                        </div> -->
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="showEditForm" width="80%">
      <div
        slot="title"
        class="dialog-title"
        style="
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          line-height: 32px;
        "
      >
        <div>编辑: {{ editForm.safetypitfall }}</div>
      </div>
      <div class="detail-content">
        <div class="detail-edit-content">
          <el-form ref="form" :model="editForm" label-width="80px">
            <el-form-item label="隐患名称">
              <el-input v-model="editForm.safetypitfall"></el-input>
            </el-form-item>
            <el-form-item label="法律条款">
              <el-input
                type="textarea"
                v-model="editForm.regulatory"
                :autosize="{ minRows: 5, maxRows: 7 }"
              ></el-input>
            </el-form-item>
            <el-form-item label="可能后果">
              <el-input
                type="textarea"
                v-model="editForm.resultant"
                :autosize="{ minRows: 3, maxRows: 5 }"
              ></el-input>
            </el-form-item>
            <el-form-item
              style="display: flex; justify-content: center; margin-left: -80px"
            >
              <el-button @click="showEditForm = false">取消</el-button>
              <el-button type="primary" @click="onSubmit">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProjectrectification,
  getProjectrectification,
  delProjectrectification,
  addProjectrectification,
  updateProjectrectification,
  setTypical,
} from "@/api/system/projectrectification";
import { stringToList } from "@/utils/index";

export default {
  name: "Projectrectification",
  data() {
    return {
      // 编辑表单内容
      editForm: {},
      // 详情表单内容
      detailForm: {},
      // 显示详情表单
      // 显示详情表单
      showDetailForm: false,
      // 显示编辑表单
      showEditForm: false,
      // 隐患等级 1：重大 2：较大 3：一般
      levelOptions: [
        { label: "重大", value: "1" },
        { label: "较大", value: "2" },
        { label: "一般", value: "3" },
        { label: "其他", value: "0" },
      ],
      // 整改状态  --1：未整改，2：已整改 3:已提交 4:未通过审核 5:急需整改 6：已逾期
      yinhuanStatusOptions: [
        { label: "未整改", value: 1 },
        { label: "已整改", value: 2 },
        { label: "已提交", value: 3 },
        { label: "未通过审核", value: 4 },
        { label: "急需整改", value: 5 },
        { label: "已逾期", value: 6 },
      ],
      // 检查类型
      checkTypeOptions: [
        { label: "安全生产", value: 1 },
        { label: "在建工地", value: 2 },
        { label: "燃气检查", value: 3 },
        { label: "食品安全", value: 4 },
      ],
      // 是否复查
      checkOptions: [
        { label: "需要复查", value: "需要复查" },
        { label: "不需要复查", value: "不需要复查" },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患表格数据
      projectrectificationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        examsid: null,
        userId: null,
        categoryid: null,
        pitfallid: null,
        pitfalltype: null,
        gradepitfall: null,
        safetypitfall: null,
        regulatory: null,
        refer: null,
        resultant: null,
        suggestions: null,
        check: null,
        deadline: null,
        superintendent: null,
        inspectors: null,
        dangerimages: null,
        dangerimagescount: null,
        rectificationstatus: null,
        rectificationphotos: null,
        notificationtime: null,
        checkdetailsid: null,
        overduestatus: null,
        rejected: null,
        remarks: null,
        reviewstatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 设为典型
    updataRow(row) {
      // if (row.isTypical) {
      //     row.isTypical = 1
      // } else {
      //     row.isTypical = 0
      // }
      setTypical({
        projectrectificationid: row.projectrectificationid,
        isTypical: row.isDX ? 1 : 0,
      }).then((response) => {
        this.$modal.msgSuccess("修改成功");
      });
    },
    // 编辑表单提价
    onSubmit() {
      updateProjectrectification(this.editForm).then((response) => {
        this.$modal.msgSuccess("修改成功");
        this.showEditForm = false;
        this.getList();
      });
    },
    // 查看检查记录详情
    handleCheck(row) {
      this.detailForm = row;
      this.showDetailForm = true;
    },
    // 编辑检查详情
    handleEdit(row) {
      this.editForm = JSON.parse(JSON.stringify(row));
      this.editForm.imgList = stringToList(this.editForm.dangerimages);
      this.editForm.imgListgai = stringToList(
        this.editForm.rectificationphotos
      );
      this.editForm.imgListsign = stringToList(this.editForm.corporatenamesign);
      this.showEditForm = true;
    },
    // 字符串转列表
    fileToList(str) {
      let arr = [];
      if (str) {
        arr = str.split(",");
      }
      return arr;
    },
    /** 查询隐患列表 */
    getList() {
      this.loading = true;
      listProjectrectification(this.queryParams).then((res) => {
        res.rows.forEach((item) => {
          item.gradepitfallName = this.levelOptions.find(
            (grade) => grade.value == item.gradepitfall
          ).label;
          item.examName = this.checkTypeOptions.find(
            (exam) => exam.value == item.examsid
          ).label;
          item.rectificationstatusName = this.yinhuanStatusOptions.find(
            (status) => status.value == item.rectificationstatus
          ).label;
          (item.isDX = item.isTypical == 1 ? true : false),
            (item.imgList = stringToList(item.dangerimages));
          item.imgListgai = stringToList(item.rectificationphotos);
          item.imgListsign = stringToList(item.corporatenamesign);
        });
        this.projectrectificationList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.queryParams = {
        projectrectificationid: null,
        examsid: null,
        userId: null,
        categoryid: null,
        pitfallid: null,
        pitfalltype: null,
        gradepitfall: null,
        safetypitfall: null,
        regulatory: null,
        refer: null,
        resultant: null,
        suggestions: null,
        check: null,
        deadline: null,
        superintendent: null,
        inspectors: null,
        dangerimages: null,
        dangerimagescount: null,
        rectificationstatus: null,
        rectificationphotos: null,
        notificationtime: null,
        checkdetailsid: null,
        overduestatus: null,
        rejected: null,
        remarks: null,
        reviewstatus: null,
        pageNum: 1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // this.resetForm("queryForm");
      this.reset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.projectrectificationid);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隐患";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const projectrectificationid = row.projectrectificationid || this.ids;
      getProjectrectification(projectrectificationid).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改隐患";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.projectrectificationid != null) {
            updateProjectrectification(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProjectrectification(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const projectrectificationids = row.projectrectificationid || this.ids;
      this.$modal
        .confirm("是否确认删除？")
        .then(function () {
          return delProjectrectification(projectrectificationids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    // handleExport() {
    //     this.download('system/projectrectification/export', {
    //         ...this.queryParams
    //     }, `projectrectification_${new Date().getTime()}.xlsx`)
    // }
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 100px);
  // overflow-y: auto;

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      .detail-center {
        display: flex;

        .detail-center-left {
          .detail-center-item {
            display: flex;
            line-height: 24px;
            margin: 12px 0;

            .detail-center-item-left {
              width: 70px;
            }

            .detail-center-item-right {
              flex: 1;
            }
          }
        }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 60%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  height: calc(100vh - 150px);
  overflow-y: auto;
  font-size: 14px;
}
</style>
