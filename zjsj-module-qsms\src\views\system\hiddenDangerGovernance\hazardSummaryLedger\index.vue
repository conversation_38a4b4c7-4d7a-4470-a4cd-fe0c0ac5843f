<template>
  <div class="app-container">
    <!-- 查询表单和数据表格 -->
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <!-- 查询表单 -->
          <el-form
            v-show="showSearch"
            ref="queryForm"
            :model="queryParams"
            size="small"
            :inline="true"
            label-width="88px"
          >
            <el-form-item label="隐患类型" prop="type">
              <el-select
                v-model="queryParams.type"
                placeholder="请选择隐患类型"
                clearable
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="隐患等级" prop="level">
              <el-select
                v-model="queryParams.level"
                placeholder="请选择隐患等级"
                clearable
              >
                <el-option
                  v-for="item in levelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="处理状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择处理状态"
                clearable
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="发现日期" prop="foundDate">
              <el-date-picker
                v-model="queryParams.foundDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <!-- 操作按钮区域 -->
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-view"
                size="mini"
                @click="handleFlowChart"
                >流程图</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="info"
                plain
                icon="el-icon-setting"
                size="mini"
                :disabled="multiple"
                @click="handleBatchManage"
                >批量管理</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExport"
                >导出</el-button
              >
            </el-col>
            <right-toolbar
              :show-search.sync="showSearch"
              @queryTable="getList"
            />
          </el-row>

          <!-- 数据表格 -->
          <el-table
            v-loading="loading"
            :data="ledgerList"
            @selection-change="handleSelectionChange"
            height="calc(100vh - 300px)"
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="隐患编号"
              align="center"
              prop="hazardCode"
              width="140"
            />
            <el-table-column
              label="隐患等级"
              align="center"
              prop="level"
              width="100"
            >
              <template slot-scope="scope">
                <el-tag :type="getLevelType(scope.row.level)">{{
                  scope.row.level
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="发现时间"
              align="center"
              prop="foundTime"
              width="150"
            />
            <el-table-column
              label="隐患描述"
              align="center"
              prop="description"
              :show-overflow-tooltip="true"
              min-width="200"
            />
            <el-table-column
              label="所在区域/位置"
              align="center"
              prop="location"
              width="120"
            />
            <el-table-column
              label="责任单位"
              align="center"
              prop="responsibleUnit"
              width="120"
            />
            <el-table-column
              label="责任人"
              align="center"
              prop="responsible"
              width="100"
            />
            <el-table-column
              label="整改期限"
              align="center"
              prop="deadline"
              width="110"
            />
            <el-table-column
              label="状态"
              align="center"
              prop="status"
              width="100"
            >
              <template slot-scope="scope">
                <el-tag :type="getStatusType(scope.row.status)">{{
                  scope.row.status
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="超时时间"
              align="center"
              prop="overtimeHours"
              width="100"
            />
            <el-table-column
              label="操作"
              fixed="right"
              width="150"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                  >查看</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  >导出</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="隐患详情窗口字段示例"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <div class="detail-content">
        <div class="detail-section">
          <div class="detail-item">
            <span class="detail-label">【隐患编号】</span>
            <span class="detail-value">{{ viewForm.hazardCode }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【隐患描述】</span>
            <span class="detail-value">{{ viewForm.description }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【发现方式】</span>
            <span class="detail-value">{{ viewForm.discoveryMethod }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【发现人】</span>
            <span class="detail-value">{{ viewForm.discoverer }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【发现时间】</span>
            <span class="detail-value">{{ viewForm.discoveryTime }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【整改人】</span>
            <span class="detail-value">{{ viewForm.rectifier }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【整改说明】</span>
            <span class="detail-value">{{
              viewForm.rectificationDescription
            }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【整改图片】</span>
            <span class="detail-value">{{ viewForm.rectificationImages }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【复查人】</span>
            <span class="detail-value">{{ viewForm.reviewer }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【复查结论】</span>
            <span class="detail-value">{{ viewForm.reviewResult }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【当前状态】</span>
            <span class="detail-value">{{ viewForm.currentStatus }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">【流程描述】</span>
            <span class="detail-value link-text" @click="viewFlowChart">{{
              viewForm.flowDescription
            }}</span>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "HazardSummaryLedger",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 查看详情对话框
      viewOpen: false,
      // 查看表单
      viewForm: {},
      // 统计数据
      stats: {
        total: 156,
        processing: 45,
        completed: 98,
        overdue: 13,
      },
      // 隐患汇总台账表格数据
      ledgerList: [
        {
          id: 1,
          hazardCode: "YH2024061001",
          level: "一般",
          foundTime: "2025-05-12 12:00:00",
          description: "施工违规放置杂物存在消防风险",
          location: "工区三标项目部",
          responsibleUnit: "三分公司项目部",
          responsible: "章三",
          deadline: "2025-07-11",
          status: "已关闭",
          overtimeHours: "2025-07-11",
          discoveryMethod: "日常检查",
          discoverer: "李四",
          discoveryTime: "2025/06/10 09:33",
          rectifier: "张三",
          rectificationDescription: "杂物已清理，通道恢复畅通",
          rectificationImages: "图片1、图片2",
          reviewer: "王五",
          reviewResult: "已通过合格",
          currentStatus: "已关闭",
          flowDescription: "查看流程图（一般级-复查-关闭）",
        },
        {
          id: 2,
          hazardCode: "YH2025061001",
          level: "一般",
          foundTime: "2025-05-12 12:00:00",
          description: "施工违规放置杂物存在消防风险",
          location: "工区三标项目部",
          responsibleUnit: "三分公司项目部",
          responsible: "章三",
          deadline: "2025-07-11",
          status: "已关闭",
          overtimeHours: "2025-07-11",
          discoveryMethod: "日常检查",
          discoverer: "李四",
          discoveryTime: "2025/06/10 09:33",
          rectifier: "张三",
          rectificationDescription: "杂物已清理，通道恢复畅通",
          rectificationImages: "图片1、图片2",
          reviewer: "王五",
          reviewResult: "已通过合格",
          currentStatus: "已关闭",
          flowDescription: "查看流程图（一般级-复查-关闭）",
        },
        {
          id: 3,
          hazardCode: "YH2025061001",
          level: "一般",
          foundTime: "2025-05-12 12:00:00",
          description: "施工违规放置杂物存在消防风险",
          location: "工区三标项目部",
          responsibleUnit: "三分公司项目部",
          responsible: "章三",
          deadline: "2025-07-11",
          status: "已关闭",
          overtimeHours: "2025-07-11",
          discoveryMethod: "日常检查",
          discoverer: "李四",
          discoveryTime: "2025/06/10 09:33",
          rectifier: "张三",
          rectificationDescription: "杂物已清理，通道恢复畅通",
          rectificationImages: "图片1、图片2",
          reviewer: "王五",
          reviewResult: "已通过合格",
          currentStatus: "已关闭",
          flowDescription: "查看流程图（一般级-复查-关闭）",
        },
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: undefined,
        level: undefined,
        status: undefined,
        foundDate: undefined,
      },
      // 隐患类型选项
      typeOptions: [
        { label: "安全生产", value: "安全生产" },
        { label: "消防安全", value: "消防安全" },
        { label: "环境安全", value: "环境安全" },
        { label: "设备安全", value: "设备安全" },
      ],
      // 隐患等级选项
      levelOptions: [
        { label: "重大", value: "重大" },
        { label: "较大", value: "较大" },
        { label: "一般", value: "一般" },
      ],
      // 状态选项
      statusOptions: [
        { label: "待处理", value: "待处理" },
        { label: "整改中", value: "整改中" },
        { label: "待审核", value: "待审核" },
        { label: "已完成", value: "已完成" },
        { label: "已关闭", value: "已关闭" },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询隐患汇总台账列表 */
    getList() {
      this.loading = true;
      // 模拟数据加载
      setTimeout(() => {
        this.total = this.ledgerList.length;
        this.loading = false;
      }, 500);
    },
    // 获取隐患等级标签类型
    getLevelType(level) {
      const levelMap = {
        重大: "danger",
        较大: "warning",
        一般: "info",
      };
      return levelMap[level] || "info";
    },
    // 获取状态标签类型
    getStatusType(status) {
      const statusMap = {
        待处理: "danger",
        整改中: "warning",
        待审核: "primary",
        已完成: "success",
        已关闭: "info",
      };
      return statusMap[status] || "info";
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = { ...row };
      this.viewOpen = true;
    },
    /** 编辑按钮操作 */
    handleUpdate(row) {
      this.$message.info("管出功能待实现");
    },
    /** 流程图按钮操作 */
    handleFlowChart() {
      this.$message.info("流程图功能待实现");
    },
    /** 批量管理按钮操作 */
    handleBatchManage() {
      this.$message.info("批量管理功能待实现");
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$message.info("导出功能待实现");
    },
    /** 查看流程图 */
    viewFlowChart() {
      this.$message.info("查看流程图功能待实现");
    },
  },
};
</script>

<style scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-left {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.stat-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-value.danger {
  color: #f56c6c;
}

.stat-value.warning {
  color: #e6a23c;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.info {
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.detail-content {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
}

.detail-section {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
}

.detail-item {
  margin-bottom: 10px;
  line-height: 1.6;
}

.detail-label {
  color: #8b4513;
  font-weight: 500;
  margin-right: 8px;
}

.detail-value {
  color: #333;
}

.link-text {
  color: #409eff;
  cursor: pointer;
}

.link-text:hover {
  text-decoration: underline;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin: 0 10px;
  min-width: 80px;
}
</style>
