<template>
  <div class="app-container">
    <el-row>
      <el-col :span="4">
        <org-tree :type="'1'" @nodeClick="handleOrgTreeNodeClick" />
      </el-col>
      <el-col :span="20">
        <el-form
          v-show="showSearch"
          ref="queryForm"
          :model="queryParams"
          size="small"
          :inline="true"
          label-width="100px"
        >
          <el-form-item label="应急人员姓名" prop="employeeName">
            <el-input
              v-model="queryParams.employeeName"
              placeholder="请输入应急人员姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="应急人员工号" prop="employeeWorkNumber">
            <el-input
              v-model="queryParams.employeeWorkNumber"
              placeholder="请输入应急人员工号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="所属小组" prop="companyName">
            <el-input v-model="queryParams.companyName" placeholder="请输入所属小组" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item> -->
          <!-- <el-form-item label="职务" prop="employeePost">
            <el-input v-model="queryParams.employeePost" placeholder="请输入职务" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item> -->
          <!-- <el-form-item label="联系方式" prop="employeePhone">
            <el-input
              v-model="queryParams.employeePhone"
              placeholder="请输入联系方式"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <!-- <el-form-item label="是否主要负责人 0-否 1-是" prop="isFzr">
            <el-input
              v-model="queryParams.isFzr"
              placeholder="请输入是否主要负责人 0-否 1-是"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="备注" prop="emergencyRemarks">
            <el-input
              v-model="queryParams.emergencyRemarks"
              placeholder="请输入备注"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="附件" prop="emergencyUrl">
            <el-input
              v-model="queryParams.emergencyUrl"
              placeholder="请输入附件"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:emergencyAgenciesTeams:add']"
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:emergencyAgenciesTeams:edit']"
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:emergencyAgenciesTeams:remove']"
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:emergencyAgenciesTeams:export']"
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >导出</el-button
            >
          </el-col>
          <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
        </el-row>

        <el-table
          v-loading="loading"
          :data="emergencyAgenciesTeamsList"
          height="calc(100vh - 230px)"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
          <el-table-column
            label="应急人员姓名"
            align="center"
            prop="employeeName"
          />
          <el-table-column
            label="应急人员工号"
            align="center"
            prop="employeeWorkNumber"
          />
          <el-table-column
            label="所属公司/部门"
            align="center"
            prop="companyName"
          />
          <!-- <el-table-column label="职务" align="center" prop="employeePost" /> -->
          <el-table-column
            label="联系方式"
            align="center"
            prop="employeePhone"
          />
          <!-- 0-否 1-是 -->
          <!-- <el-table-column label="是否主要负责人" align="center" prop="employeeZz" />
          <el-table-column label="备注" align="center" prop="safetyTraining" />
          <el-table-column label="附件" align="center" prop="anquanlvli">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleView(scope.row.anquanlvli)">查看</el-button>
            </template>
</el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['inspection:emergencyAgenciesTeams:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:emergencyAgenciesTeams:edit']"
                >查看</el-button
              > -->
              <el-button
                v-hasPermi="['inspection:emergencyAgenciesTeams:remove']"
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改应急机构与队伍对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="应急人员姓名" prop="employeeName">
          <el-input
            v-model="form.employeeName"
            placeholder="请输入应急人员姓名"
          />
        </el-form-item>
        <el-form-item label="应急人员工号" prop="employeeWorkNumber">
          <el-input
            v-model="form.employeeWorkNumber"
            placeholder="请输入应急人员工号"
          />
        </el-form-item>
        <el-form-item label="所属小组" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入所属小组" />
        </el-form-item>
        <el-form-item label="职务" prop="employeePost">
          <el-input v-model="form.employeePost" placeholder="请输入职务" />
        </el-form-item>
        <el-form-item label="联系方式" prop="employeePhone">
          <el-input v-model="form.employeePhone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="是否主要负责人" prop="employeeZz">
          <el-select
            v-model="form.employeeZz"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="否" value="0" />
            <el-option label="是" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="safetyTraining">
          <el-input v-model="form.safetyTraining" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="附件" prop="anquanlvli">
          <file-upload v-model="form.anquanlvli" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjEmployeeInfo,
  getZjEmployeeInfo,
  delZjEmployeeInfo,
  addZjEmployeeInfo,
  updateZjEmployeeInfo,
} from "@/api/inspection/zjEmployeeInfo";
import OrgTree from "@/views/components/orgTree.vue";

export default {
  name: "EmergencyAgenciesTeams",
  components: {
    OrgTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应急机构与队伍表格数据
      emergencyAgenciesTeamsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        employeeName: null,
        employeeWorkNumber: null,
        companyName: null,
        employeePost: null,
        employeePhone: null,
        employeeZz: null,
        safetyTraining: null,
        anquanlvli: null,
        deptId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        employeeName: [
          { required: true, message: "应急人员姓名不能为空", trigger: "blur" },
        ],
        employeeWorkNumber: [
          { required: true, message: "应急人员工号不能为空", trigger: "blur" },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_URL,
      currentDeptId: null,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleOrgTreeNodeClick(nodeData) {
      // 在这里处理接收到的子组件数据
      console.log("接收到子组件数据:", nodeData);
      // 可以根据nodeData更新查询参数或其他状态
      this.queryParams.deptId = nodeData.id; // 假设nodeData中有id字段
      this.currentDeptId = nodeData.id;
      this.handleQuery(); // 触发查询
    },

    // 查看
    async handleView(value) {
      if (!value) {
        this.$message.warning("该记录没有附件");
        return;
      }
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const response = await fetch(fileUrl);
          const buffer = await response.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    /** 查询应急机构与队伍列表 */
    getList() {
      this.loading = true;
      listZjEmployeeInfo(this.queryParams).then((response) => {
        this.emergencyAgenciesTeamsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        employeeName: null,
        employeeWorkNumber: null,
        companyName: null,
        employeePost: null,
        employeePhone: null,
        employeeZz: null,
        safetyTraining: null,
        anquanlvli: null,
        deptId: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 保持当前选中的部门ID，不重置左侧树的选择状态
      // this.queryParams.deptId = "";  // 注释掉这行，保持当前选中的部门
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      if (this.currentDeptId) {
        this.form.deptId = this.currentDeptId;
      }
      this.title = "添加应急机构与队伍";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjEmployeeInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改应急机构与队伍";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjEmployeeInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEmployeeInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除应急机构与队伍编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjEmployeeInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 根据选中状态决定导出内容和参数
      const hasSelection = this.ids.length > 0;
      const confirmMessage = hasSelection
        ? `是否确认导出选中的${this.ids.length}条应急机构与队伍数据项?`
        : "是否确认导出所有应急机构与队伍数据项?";

      // 准备导出参数
      const exportParams = hasSelection
        ? { ids: this.ids.join(",") } // 选中了行，只传ids参数
        : { ...this.queryParams }; // 没选中，传查询参数

      this.$modal
        .confirm(confirmMessage)
        .then(() => {
          const fileName = hasSelection
            ? `应急机构与队伍预警数据_选中${
                this.ids.length
              }条_${new Date().getTime()}.xlsx`
            : `应急机构与队伍预警数据_${new Date().getTime()}.xlsx`;

          // 使用正确的导出方法
          this.download(
            "inspection/zjEmployeeInfo/exportyj",
            exportParams,
            fileName
          );
        })
        .catch(() => {});
    },
  },
};
</script>
