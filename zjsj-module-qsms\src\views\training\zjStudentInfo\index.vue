<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="83px"
    >
      <!-- 新增筛选项 记得修改表格height -->
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item label="身份证" prop="identityno">
        <el-input
          v-model="queryParams.identityno"
          placeholder="请输入身份证"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="企业名称" prop="entpname">
        <el-input
          v-model="queryParams.entpname"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="不合格次数" prop="unqualifiedQuantity">
        <el-input-number
          v-model="queryParams.unqualifiedQuantity"
          placeholder="请输入不合格次数"
          :min="0"
          controls-position="right"
          style="width: 100px"
        />
      </el-form-item>
      <!-- <el-form-item label="注册时间" prop="createdate">
        <el-date-picker
          v-model="queryParams.createdate"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 330px"
        />
      </el-form-item>
      <el-form-item
        label="最后一次更新时间"
        prop="lasttime"
        label-width="130px"
      >
        <el-date-picker
          v-model="queryParams.lasttime"
          type="datetimerange"
          value-format="yyyy-MM-dd "
          style="width: 330px"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjStudentInfo:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
     
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjStudentInfo:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleBatchDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjStudentInfo:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 表格横向滚动容器 -->
    <div class="table-scroll-container">
      <el-table
        v-loading="loading"
        :data="zjStudentInfoList"
        height="calc(100vh - 240px)"
        class="scroll-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" align="center" width="65" />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="姓名" align="center" width="105" prop="username" />
        <el-table-column label="身份证" align="center" prop="identityno" />
        <el-table-column label="企业名称" align="center" prop="entpname" />
        <el-table-column label="注册时间" align="center" width="180" prop="createdate" />
        <el-table-column
          label="最后一次更新时间"
          align="center"
          prop="lasttime"
        />
        <el-table-column
          label="历次考试不合格"
          align="center"
          prop="unqualifiedQuantity"
          width="130"
        >
          <template slot-scope="scope">
            {{
              scope.row.unqualifiedQuantity === 0
                ? "无"
                : scope.row.unqualifiedQuantity + "次"
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          width="150"
          align="center"
          class-name="small-padding"
        >
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['training:zjStudentInfo:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              v-hasPermi="['training:zjStudentInfo:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      class="pagination"
      @pagination="getList"
    />

    <!-- 添加或修改学员信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="姓名" prop="username">
          <el-input v-model="form.username" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="身份证" prop="identityno">
          <el-input v-model="form.identityno" placeholder="请输入身份证" />
        </el-form-item>
        <el-form-item label="企业名称" prop="entpname">
          <el-input v-model="form.entpname" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="注册时间" prop="createdate">
          <el-input v-model="form.createdate" placeholder="请输入注册时间" />
        </el-form-item>
        <el-form-item label="最后一次更新时间" prop="lasttime">
          <el-input
            v-model="form.lasttime"
            placeholder="请输入最后一次更新时间"
          />
        </el-form-item>
        <el-form-item label="历次考试不合格" prop="unqualifiedQuantity">
          <el-input-number
            v-model="form.unqualifiedQuantity"
            placeholder="请输入历次考试不合格次数"
            :min="0"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjStudentInfo,
  getZjStudentInfo,
  delZjStudentInfo,
  addZjStudentInfo,
  updateZjStudentInfo,
} from "@/api/training/zjStudentInfo";

export default {
  name: "ZjStudentInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 学员信息表格数据
      zjStudentInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: null,
        identityno: null,
        entpname: null,
        createdate: null,
        lasttime: null,
        unqualifiedQuantity: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        username: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        identityno: [
          { required: true, message: "身份证不能为空", trigger: "blur" },
        ],
        entpname: [
          { required: true, message: "企业名称不能为空", trigger: "blur" },
        ],
        createdate: [
          { required: true, message: "注册时间不能为空", trigger: "blur" },
        ],
        lasttime: [
          {
            required: true,
            message: "最后一次更新时间不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询学员信息列表 */
    getList() {
      this.loading = true;
      listZjStudentInfo(this.queryParams).then((response) => {
        this.zjStudentInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        username: null,
        identityno: null,
        entpname: null,
        createdate: null,
        lasttime: null,
        unqualifiedQuantity: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加学员信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjStudentInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改学员信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjStudentInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjStudentInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 批量删除
    handleBatchDelete() {
      const ids = this.ids;
      this.$modal
        .confirm('是否确认删除' + this.ids.length + '条学员的数据项？')
        .then(function () {
          return delZjStudentInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除学员"' + row.username + '"的数据项？')
        .then(function () {
          return delZjStudentInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "training/zjStudentInfo/export",
        {
          ...this.queryParams,
        },
        `zjStudentInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
// 表格横向滚动容器样式
.table-scroll-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  // 美化滚动条
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // 确保表格能够横向滚动
  ::v-deep .el-table {
    min-width: 100%;

    .el-table__body-wrapper {
      overflow-x: visible;
    }

    // 固定列阴影效果
    .el-table__fixed-right {
      box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// 表格单元格样式优化
::v-deep .el-table {
  .el-table__cell {
    padding: 12px 8px;

    .cell {
      word-break: break-word;
      white-space: normal;
    }
  }

  // 优化tooltip显示
  .el-tooltip {
    max-width: 300px;
  }
}

// 分页组件样式
.pagination {
  margin-top: 20px;
  text-align: center;
}

// 响应式设计
@media (max-width: 1200px) {
  .table-scroll-container {
    ::v-deep .el-table {
      .el-table-column--selection {
        width: 50px !important;
      }
    }
  }
}
</style>
