<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item label="项目编码" prop="xmbm">
        <el-input v-model="queryParams.xmbm" placeholder="请输入项目编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="项目名称" prop="xmmc">
        <el-input v-model="queryParams.xmmc" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="所属国家" prop="gb">
        <el-input v-model="queryParams.gb" placeholder="请输入所属国家" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:xmxx:add']" type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:xmxx:edit']" type="success" plain icon="el-icon-edit" size="mini"
          :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:xmxx:remove']" type="danger" plain icon="el-icon-delete" size="mini"
          :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:xmxx:export']" type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="xmxxList" height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编码" align="center" prop="xmbm" />
      <!-- <el-table-column label="项目简称" align="center" prop="xmjc" /> -->
      <el-table-column label="项目名称" align="center" prop="xmmc" />
      <el-table-column label="所属国家" align="center" prop="gb" />
      <!-- <el-table-column label="所属大洲" align="center" prop="dz" /> -->
      <el-table-column label="项目地址" align="center" prop="xmdz" />

      <el-table-column label="项目金额" align="center" prop="zbjemy" />
      <el-table-column label="项目开工日期" align="center" prop="kgrq" />
      <el-table-column label="计划竣工日期" align="center" prop="jhjgrq" />
      <el-table-column label="总工期(天)" align="center" prop="htgq" />
      <!-- <el-table-column label="累计产值" align="center" prop="ljwcczmy" /> -->
      <!-- <el-table-column label="累计收款" align="center" prop="ljsk" /> -->
      <!-- <el-table-column label="项目累计成本" align="center" prop="ljcb" /> -->
      <!-- <el-table-column label="施工进度百分比" align="center" prop="zsgjd" /> -->
      <!-- <el-table-column label="项目视频" align="center" prop="sp" /> -->
      <!-- <el-table-column label="项目照片" align="center" prop="xczp" /> -->
      <!-- <el-table-column label="中方管理人员人数" align="center" prop="xmzfryzrs" />
      <el-table-column label="外籍管理人员人数" align="center" prop="xmwjryzrs" />
      <el-table-column label="中方普通工人人数" align="center" prop="zfptgrrs" />
      <el-table-column label="外籍普通工人人数" align="center" prop="wjptgrrs" /> -->
      <!-- <el-table-column label="项目业主方" align="center" prop="dwmczw" />
      <el-table-column label="项目资金来源" align="center" prop="zjly" />
      <el-table-column label="建设内容" align="center" prop="jsnr" />
      <el-table-column label="预付款(万美元)" align="center" prop="yfkwmy" />
      <el-table-column label="合同类型" align="center" prop="htlxmc" />

      <el-table-column label="剩余工期" align="center" prop="sygqt" />
      <el-table-column label="形象进度描述" align="center" prop="xxjdms" />
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-hasPermi="['inspection:xmxx:edit']" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['inspection:xmxx:remove']" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改国外在建项目信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px" class="three-column-form">
        <el-form-item v-if="form.id" label="项目id" prop="id">
          <el-input v-model="form.id" placeholder="请输入项目id" readonly />
        </el-form-item>
        <el-form-item label="项目编码" prop="xmbm">
          <el-input v-model="form.xmbm" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="项目简称" prop="xmjc">
          <el-input v-model="form.xmjc" placeholder="请输入项目简称" />
        </el-form-item>
        <el-form-item label="项目名称" prop="xmmc">
          <el-input v-model="form.xmmc" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="所属国家" prop="gb">
          <el-input v-model="form.gb" placeholder="请输入所属国家" />
        </el-form-item>
        <el-form-item label="所属大洲" prop="dz">
          <el-input v-model="form.dz" placeholder="请输入所属大洲" />
        </el-form-item>
        <el-form-item label="项目地址" prop="xmdz">
          <el-input v-model="form.xmdz" placeholder="请输入项目地址" />
        </el-form-item>

        <el-form-item label="项目金额" prop="zbjemy">
          <el-input v-model="form.zbjemy" placeholder="请输入项目金额" @input="validateAmountInput('zbjemy', $event)" />
        </el-form-item>
        <el-form-item label="项目开工日期" prop="kgrq">
          <el-date-picker v-model="form.kgrq" type="date" placeholder="请选择项目开工日期" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" />
        </el-form-item>

        <el-form-item label="总工期(天)" prop="htgq">
          <el-input v-model="form.htgq" placeholder="请输入总工期(天)" @input="validateNumberInput('htgq', $event)" />
        </el-form-item>
        <el-form-item label="累计产值" prop="ljwcczmy">
          <el-input v-model="form.ljwcczmy" placeholder="请输入累计产值" @input="validateAmountInput('ljwcczmy', $event)" />
        </el-form-item>
        <el-form-item label="累计收款" prop="ljsk">
          <el-input v-model="form.ljsk" placeholder="请输入累计收款" @input="validateAmountInput('ljsk', $event)" />
        </el-form-item>
        <el-form-item label="项目累计成本" prop="ljcb">
          <el-input v-model="form.ljcb" placeholder="请输入项目累计成本" @input="validateAmountInput('ljcb', $event)" />
        </el-form-item>
        <el-form-item label="施工进度百分比" prop="zsgjd">
          <el-input v-model="form.zsgjd" placeholder="请输入施工进度百分比" @input="validatePercentageInput('zsgjd', $event)" />
        </el-form-item>
        <el-form-item label="项目视频" prop="sp">
          <el-input v-model="form.sp" placeholder="请输入项目视频" />
        </el-form-item>
        <el-form-item label="项目照片" prop="xczp">
          <el-input v-model="form.xczp" placeholder="请输入项目照片" />
        </el-form-item>
        <el-form-item label="中方管理人员人数" prop="xmzfryzrs">
          <el-input v-model="form.xmzfryzrs" placeholder="请输入中方管理人员人数"
            @input="validateNumberInput('xmzfryzrs', $event)" />
        </el-form-item>
        <el-form-item label="外籍管理人员人数" prop="xmwjryzrs">
          <el-input v-model="form.xmwjryzrs" placeholder="请输入外籍管理人员人数"
            @input="validateNumberInput('xmwjryzrs', $event)" />
        </el-form-item>
        <el-form-item label="中方普通工人人数" prop="zfptgrrs">
          <el-input v-model="form.zfptgrrs" placeholder="请输入中方普通工人人数"
            @input="validateNumberInput('zfptgrrs', $event)" />
        </el-form-item>
        <el-form-item label="外籍普通工人人数" prop="wjptgrrs">
          <el-input v-model="form.wjptgrrs" placeholder="请输入外籍普通工人人数"
            @input="validateNumberInput('wjptgrrs', $event)" />
        </el-form-item>
        <el-form-item label="项目业主方" prop="dwmczw">
          <el-input v-model="form.dwmczw" placeholder="请输入项目业主方" />
        </el-form-item>
        <el-form-item label="项目资金来源" prop="zjly">
          <el-input v-model="form.zjly" placeholder="请输入项目资金来源" />
        </el-form-item>
        <el-form-item label="建设内容" prop="jsnr">
          <el-input v-model="form.jsnr" placeholder="请输入建设内容" />
        </el-form-item>
        <el-form-item label="预付款(万美元)" prop="yfkwmy">
          <el-input v-model="form.yfkwmy" placeholder="请输入预付款(万美元)" @input="validateAmountInput('yfkwmy', $event)" />
        </el-form-item>
        <el-form-item label="合同类型" prop="htlxmc">
          <el-input v-model="form.htlxmc" placeholder="请输入合同类型" />
        </el-form-item>
        <el-form-item label="计划竣工日期" prop="jhjgrq">
          <el-date-picker v-model="form.jhjgrq" type="date" placeholder="请选择计划竣工日期" format="yyyy-MM-dd"
            value-format="yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="剩余工期" prop="sygqt">
          <el-input v-model="form.sygqt" placeholder="请输入剩余工期" @input="validateNumberInput('sygqt', $event)" />
        </el-form-item>
        <el-form-item label="形象进度描述" prop="xxjdms">
          <el-input v-model="form.xxjdms" placeholder="请输入形象进度描述" />
        </el-form-item>

        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listXmxx, getXmxx, delXmxx, addXmxx, updateXmxx } from '@/api/inspection/xmxx'

export default {
  name: 'Xmxx',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 国外在建项目信息表格数据
      xmxxList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        xmbm: null,
        xmjc: null,
        xmmc: null,
        gb: null,
        dz: null,
        xmdz: null,
        zbjemy: null,
        kgrq: null,

        htgq: null,
        ljwcczmy: null,
        ljsk: null,
        ljcb: null,
        zsgjd: null,
        sp: null,
        xczp: null,
        xmzfryzrs: null,
        xmwjryzrs: null,
        zfptgrrs: null,
        wjptgrrs: null,
        dwmczw: null,
        zjly: null,
        jsnr: null,
        yfkwmy: null,
        htlxmc: null,
        jhjgrq: null,
        sygqt: null,
        xxjdms: null,
        createTime: null,
        longitude: null,
        latitude: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        xmbm: [
          { required: true, message: '项目编码不能为空', trigger: 'blur' }
        ],
        xmjc: [
          { required: true, message: '项目简称不能为空', trigger: 'blur' }
        ],
        xmmc: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        gb: [
          { required: true, message: '所属国家不能为空', trigger: 'blur' }
        ],
        dz: [
          { required: true, message: '所属大洲不能为空', trigger: 'blur' }
        ],
        zbjemy: [
          { validator: this.validateAmount, trigger: 'blur' }
        ],
        htgq: [
          { validator: this.validatePositiveInteger, trigger: 'blur' }
        ],
        ljwcczmy: [
          { validator: this.validateAmount, trigger: 'blur' }
        ],
        ljsk: [
          { validator: this.validateAmount, trigger: 'blur' }
        ],
        ljcb: [
          { validator: this.validateAmount, trigger: 'blur' }
        ],
        zsgjd: [
          { validator: this.validatePercentage, trigger: 'blur' }
        ],
        xmzfryzrs: [
          { validator: this.validatePositiveInteger, trigger: 'blur' }
        ],
        xmwjryzrs: [
          { validator: this.validatePositiveInteger, trigger: 'blur' }
        ],
        zfptgrrs: [
          { validator: this.validatePositiveInteger, trigger: 'blur' }
        ],
        wjptgrrs: [
          { validator: this.validatePositiveInteger, trigger: 'blur' }
        ],
        yfkwmy: [
          { validator: this.validateAmount, trigger: 'blur' }
        ],
        sygqt: [
          { validator: this.validatePositiveInteger, trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询国外在建项目信息列表 */
    getList() {
      this.loading = true
      listXmxx(this.queryParams).then(response => {
        this.xmxxList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        xmbm: null,
        xmjc: null,
        xmmc: null,
        gb: null,
        dz: null,
        xmdz: null,
        zbjemy: null,
        kgrq: null,

        htgq: null,
        ljwcczmy: null,
        ljsk: null,
        ljcb: null,
        zsgjd: null,
        sp: null,
        xczp: null,
        xmzfryzrs: null,
        xmwjryzrs: null,
        zfptgrrs: null,
        wjptgrrs: null,
        dwmczw: null,
        zjly: null,
        jsnr: null,
        yfkwmy: null,
        htlxmc: null,
        jhjgrq: null,
        sygqt: null,
        xxjdms: null,
        createTime: null,
        longitude: null,
        latitude: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加国外在建项目信息'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || (this.ids.length > 0 ? this.ids[0] : null)
      if (!id) {
        this.$modal.msgError('请选择要修改的数据')
        return
      }
      getXmxx(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改国外在建项目信息'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log(this.form.id)

          if (this.form.id != null) {
            updateXmxx(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addXmxx(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      if (!ids || (Array.isArray(ids) && ids.length === 0)) {
        this.$modal.msgError('请选择要删除的数据')
        return
      }
      const idsText = Array.isArray(ids) ? ids.join(',') : ids
      this.$modal.confirm('是否确认删除国外在建项目信息编号为"' + idsText + '"的数据项？').then(function () {
        return delXmxx(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inspection/xmxx/export', {
        ...this.queryParams
      }, `xmxx_${new Date().getTime()}.xlsx`)
    },

    /** 金额输入验证 - 只允许数字和小数点 */
    validateAmountInput(field, value) {
      const numericValue = value.replace(/[^0-9.]/g, '')
      // 确保只有一个小数点
      const parts = numericValue.split('.')
      if (parts.length > 2) {
        const validValue = parts[0] + '.' + parts.slice(1).join('')
        this.form[field] = validValue
      } else {
        this.form[field] = numericValue
      }
    },

    /** 纯数字输入验证 - 只允许正整数 */
    validateNumberInput(field, value) {
      const numericValue = value.replace(/[^0-9]/g, '')
      this.form[field] = numericValue
    },

    /** 百分比输入验证 - 允许数字和小数点，限制0-100 */
    validatePercentageInput(field, value) {
      let numericValue = value.replace(/[^0-9.]/g, '')
      // 确保只有一个小数点
      const parts = numericValue.split('.')
      if (parts.length > 2) {
        numericValue = parts[0] + '.' + parts.slice(1).join('')
      }
      // 限制范围0-100
      const num = parseFloat(numericValue)
      if (!isNaN(num) && num > 100) {
        numericValue = '100'
      }
      this.form[field] = numericValue
    },

    /** 金额字段验证规则 */
    validateAmount(rule, value, callback) {
      if (value && !/^[0-9]+(\.[0-9]+)?$/.test(value)) {
        callback(new Error('请输入有效的金额，只能包含数字和小数点'))
      } else {
        callback()
      }
    },

    /** 正整数验证规则 */
    validatePositiveInteger(rule, value, callback) {
      if (value && !/^[0-9]+$/.test(value)) {
        callback(new Error('请输入有效的数字'))
      } else {
        callback()
      }
    },

    /** 百分比验证规则 */
    validatePercentage(rule, value, callback) {
      if (value) {
        if (!/^[0-9]+(\.[0-9]+)?$/.test(value)) {
          callback(new Error('请输入有效的百分比，只能包含数字和小数点'))
        } else {
          const num = parseFloat(value)
          if (num < 0 || num > 100) {
            callback(new Error('百分比必须在0-100之间'))
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped>
.three-column-form {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
}

.three-column-form .el-form-item {
  margin-bottom: 18px;
}
</style>
