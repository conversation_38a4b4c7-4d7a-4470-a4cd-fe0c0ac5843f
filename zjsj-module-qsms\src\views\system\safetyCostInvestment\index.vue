<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="费用记录编号" prop="costNo">
        <el-input
          v-model="queryParams.costNo"
          placeholder="请输入费用记录编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计周期" prop="statPeriod">
        <el-input
          v-model="queryParams.statPeriod"
          placeholder="请输入统计周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计范围" prop="statScope">
        <el-input
          v-model="queryParams.statScope"
          placeholder="请输入统计范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="安全设备购置费用" prop="safetyEquipmentPurchase">
        <el-input
          v-model="queryParams.safetyEquipmentPurchase"
          placeholder="请输入安全设备购置费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全设施维护费用" prop="safetyFacilityMaintenance">
        <el-input
          v-model="queryParams.safetyFacilityMaintenance"
          placeholder="请输入安全设施维护费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="劳动防护用品费用" prop="laborProtectionProducts">
        <el-input
          v-model="queryParams.laborProtectionProducts"
          placeholder="请输入劳动防护用品费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全培训教育费用" prop="safetyTrainingEducation">
        <el-input
          v-model="queryParams.safetyTrainingEducation"
          placeholder="请输入安全培训教育费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应急救援准备费用" prop="emergencyRescuePreparation">
        <el-input
          v-model="queryParams.emergencyRescuePreparation"
          placeholder="请输入应急救援准备费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全评价检测费用" prop="safetyEvaluationDetection">
        <el-input
          v-model="queryParams.safetyEvaluationDetection"
          placeholder="请输入安全评价检测费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="其他安全费用" prop="otherSafetyCost">
        <el-input
          v-model="queryParams.otherSafetyCost"
          placeholder="请输入其他安全费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全费用总计" prop="totalSafetyCost">
        <el-input
          v-model="queryParams.totalSafetyCost"
          placeholder="请输入安全费用总计"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划安全费用" prop="plannedSafetyCost">
        <el-input
          v-model="queryParams.plannedSafetyCost"
          placeholder="请输入计划安全费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="费用执行率" prop="costExecutionRate">
        <el-input
          v-model="queryParams.costExecutionRate"
          placeholder="请输入费用执行率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="同期项目总费用" prop="totalProjectCost">
        <el-input
          v-model="queryParams.totalProjectCost"
          placeholder="请输入同期项目总费用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全费用占项目总费用比例" prop="safetyCostRatio">
        <el-input
          v-model="queryParams.safetyCostRatio"
          placeholder="请输入安全费用占项目总费用比例"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制人" prop="compiler">
        <el-input
          v-model="queryParams.compiler"
          placeholder="请输入编制人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="reviewer">
        <el-input
          v-model="queryParams.reviewer"
          placeholder="请输入审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审批人" prop="approver">
        <el-input
          v-model="queryParams.approver"
          placeholder="请输入审批人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制时间" prop="compileTime">
        <el-date-picker
          clearable
          v-model="queryParams.compileTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择编制时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审批时间" prop="approveTime">
        <el-date-picker
          clearable
          v-model="queryParams.approveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审批时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:list:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:list:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:list:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:list:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="listList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="费用记录编号" align="center" prop="costNo" />
      <el-table-column label="统计周期" align="center" prop="statPeriod" />
      <!-- 月度/季度/年度 -->
      <el-table-column label="统计类型" align="center" prop="statType" />
      <el-table-column label="统计范围" align="center" prop="statScope" />
      <el-table-column
        label="安全设备购置费用"
        align="center"
        prop="safetyEquipmentPurchase"
      />
      <el-table-column
        label="安全设施维护费用"
        align="center"
        prop="safetyFacilityMaintenance"
      />
      <el-table-column
        label="劳动防护用品费用"
        align="center"
        prop="laborProtectionProducts"
      />
      <el-table-column
        label="安全培训教育费用"
        align="center"
        prop="safetyTrainingEducation"
      />
      <el-table-column
        label="应急救援准备费用"
        align="center"
        prop="emergencyRescuePreparation"
      />
      <el-table-column
        label="安全评价检测费用"
        align="center"
        prop="safetyEvaluationDetection"
      />
      <el-table-column
        label="其他安全费用"
        align="center"
        prop="otherSafetyCost"
      />
      <el-table-column
        label="安全费用总计"
        align="center"
        prop="totalSafetyCost"
      />
      <el-table-column
        label="计划安全费用"
        align="center"
        prop="plannedSafetyCost"
      />
      <el-table-column
        label="费用执行率"
        align="center"
        prop="costExecutionRate"
      />
      <!-- <el-table-column
        label="同期项目总费用"
        align="center"
        prop="totalProjectCost"
      />
      <el-table-column
        label="安全费用占项目总费用比例"
        align="center"
        prop="safetyCostRatio"
      /> -->
      <!-- <el-table-column label="费用说明" align="center" prop="costDesc" />
      <el-table-column
        label="费用凭证文件地址"
        align="center"
        prop="costVoucherUrl"
      />
      <el-table-column
        label="费用明细表地址"
        align="center"
        prop="attachmentUrl"
      />
      <el-table-column
        label="费用状态：草稿/审核中/已审核/已归档"
        align="center"
        prop="costStatus"
      />
      <el-table-column label="编制人" align="center" prop="compiler" />
      <el-table-column label="审核人" align="center" prop="reviewer" />
      <el-table-column label="审批人" align="center" prop="approver" />
      <el-table-column
        label="编制时间"
        align="center"
        prop="compileTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.compileTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approveTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:list:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:list:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全费用投入一览（按周期、范围汇总安全费用明细与占比）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="费用记录编号" prop="costNo">
          <el-input v-model="form.costNo" placeholder="请输入费用记录编号" />
        </el-form-item>
        <el-form-item label="统计周期" prop="statPeriod">
          <el-input v-model="form.statPeriod" placeholder="请输入统计周期" />
        </el-form-item>
        <el-form-item label="统计范围" prop="statScope">
          <el-input v-model="form.statScope" placeholder="请输入统计范围" />
        </el-form-item>
        <el-form-item label="安全设备购置费用" prop="safetyEquipmentPurchase">
          <el-input
            v-model="form.safetyEquipmentPurchase"
            placeholder="请输入安全设备购置费用"
          />
        </el-form-item>
        <el-form-item label="安全设施维护费用" prop="safetyFacilityMaintenance">
          <el-input
            v-model="form.safetyFacilityMaintenance"
            placeholder="请输入安全设施维护费用"
          />
        </el-form-item>
        <el-form-item label="劳动防护用品费用" prop="laborProtectionProducts">
          <el-input
            v-model="form.laborProtectionProducts"
            placeholder="请输入劳动防护用品费用"
          />
        </el-form-item>
        <el-form-item label="安全培训教育费用" prop="safetyTrainingEducation">
          <el-input
            v-model="form.safetyTrainingEducation"
            placeholder="请输入安全培训教育费用"
          />
        </el-form-item>
        <el-form-item
          label="应急救援准备费用"
          prop="emergencyRescuePreparation"
        >
          <el-input
            v-model="form.emergencyRescuePreparation"
            placeholder="请输入应急救援准备费用"
          />
        </el-form-item>
        <el-form-item label="安全评价检测费用" prop="safetyEvaluationDetection">
          <el-input
            v-model="form.safetyEvaluationDetection"
            placeholder="请输入安全评价检测费用"
          />
        </el-form-item>
        <el-form-item label="其他安全费用" prop="otherSafetyCost">
          <el-input
            v-model="form.otherSafetyCost"
            placeholder="请输入其他安全费用"
          />
        </el-form-item>
        <el-form-item label="安全费用总计" prop="totalSafetyCost">
          <el-input
            v-model="form.totalSafetyCost"
            placeholder="请输入安全费用总计"
          />
        </el-form-item>
        <el-form-item label="计划安全费用" prop="plannedSafetyCost">
          <el-input
            v-model="form.plannedSafetyCost"
            placeholder="请输入计划安全费用"
          />
        </el-form-item>
        <el-form-item label="费用执行率" prop="costExecutionRate">
          <el-input
            v-model="form.costExecutionRate"
            placeholder="请输入费用执行率"
          />
        </el-form-item>
        <el-form-item label="同期项目总费用" prop="totalProjectCost">
          <el-input
            v-model="form.totalProjectCost"
            placeholder="请输入同期项目总费用"
          />
        </el-form-item>
        <el-form-item label="安全费用占项目总费用比例" prop="safetyCostRatio">
          <el-input
            v-model="form.safetyCostRatio"
            placeholder="请输入安全费用占项目总费用比例"
          />
        </el-form-item>
        <el-form-item label="费用说明" prop="costDesc">
          <el-input
            v-model="form.costDesc"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="费用凭证文件地址" prop="costVoucherUrl">
          <el-input
            v-model="form.costVoucherUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="费用明细表地址" prop="attachmentUrl">
          <el-input
            v-model="form.attachmentUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="编制人" prop="compiler">
          <el-input v-model="form.compiler" placeholder="请输入编制人" />
        </el-form-item>
        <el-form-item label="审核人" prop="reviewer">
          <el-input v-model="form.reviewer" placeholder="请输入审核人" />
        </el-form-item>
        <el-form-item label="审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="请输入审批人" />
        </el-form-item>
        <el-form-item label="编制时间" prop="compileTime">
          <el-date-picker
            clearable
            v-model="form.compileTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择编制时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批时间" prop="approveTime">
          <el-date-picker
            clearable
            v-model="form.approveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审批时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listList,
  getList,
  delList,
  addList,
  updateList,
} from "@/api/system/safetyCostInvestment/index";

export default {
  name: "List",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全费用投入一览（按周期、范围汇总安全费用明细与占比）表格数据
      listList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        costNo: null,
        statPeriod: null,
        statType: null,
        statScope: null,
        safetyEquipmentPurchase: null,
        safetyFacilityMaintenance: null,
        laborProtectionProducts: null,
        safetyTrainingEducation: null,
        emergencyRescuePreparation: null,
        safetyEvaluationDetection: null,
        otherSafetyCost: null,
        totalSafetyCost: null,
        plannedSafetyCost: null,
        costExecutionRate: null,
        totalProjectCost: null,
        safetyCostRatio: null,
        costDesc: null,
        costVoucherUrl: null,
        attachmentUrl: null,
        costStatus: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        costNo: [
          { required: true, message: "费用记录编号不能为空", trigger: "blur" },
        ],
        statPeriod: [
          { required: true, message: "统计周期不能为空", trigger: "blur" },
        ],
        statType: [
          {
            required: true,
            message: "统计类型：月度/季度/年度不能为空",
            trigger: "change",
          },
        ],
        statScope: [
          { required: true, message: "统计范围不能为空", trigger: "blur" },
        ],
        compiler: [
          { required: true, message: "编制人不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全费用投入一览（按周期、范围汇总安全费用明细与占比）列表 */
    getList() {
      this.loading = true;
      listList(this.queryParams).then((response) => {
        this.listList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        costNo: null,
        statPeriod: null,
        statType: null,
        statScope: null,
        safetyEquipmentPurchase: null,
        safetyFacilityMaintenance: null,
        laborProtectionProducts: null,
        safetyTrainingEducation: null,
        emergencyRescuePreparation: null,
        safetyEvaluationDetection: null,
        otherSafetyCost: null,
        totalSafetyCost: null,
        plannedSafetyCost: null,
        costExecutionRate: null,
        totalProjectCost: null,
        safetyCostRatio: null,
        costDesc: null,
        costVoucherUrl: null,
        attachmentUrl: null,
        costStatus: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全费用投入一览（按周期、范围汇总安全费用明细与占比）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getList(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title =
          "修改安全费用投入一览（按周期、范围汇总安全费用明细与占比）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateList(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addList(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除安全费用投入一览（按周期、范围汇总安全费用明细与占比）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delList(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/list/export",
        {
          ...this.queryParams,
        },
        `list_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
