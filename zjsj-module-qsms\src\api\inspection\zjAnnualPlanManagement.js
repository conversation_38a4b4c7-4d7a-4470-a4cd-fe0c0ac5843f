import request from '@/utils/request'

// 查询年度计划管理列表
export function listZjAnnualPlanManagement(query) {
  return request({
    url: '/inspection/zjAnnualPlanManagement/list',
    method: 'get',
    params: query
  })
}

// 查询年度计划管理详细
export function getZjAnnualPlanManagement(id) {
  return request({
    url: '/inspection/zjAnnualPlanManagement/' + id,
    method: 'get'
  })
}

// 新增年度计划管理
export function addZjAnnualPlanManagement(data) {
  return request({
    url: '/inspection/zjAnnualPlanManagement',
    method: 'post',
    data: data
  })
}

// 修改年度计划管理
export function updateZjAnnualPlanManagement(data) {
  return request({
    url: '/inspection/zjAnnualPlanManagement',
    method: 'put',
    data: data
  })
}

// 删除年度计划管理
export function delZjAnnualPlanManagement(id) {
  return request({
    url: '/inspection/zjAnnualPlanManagement/' + id,
    method: 'delete'
  })
}
