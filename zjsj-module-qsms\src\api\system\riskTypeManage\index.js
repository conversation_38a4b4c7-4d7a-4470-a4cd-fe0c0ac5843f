import request from '@/utils/request'

// 查询风险类型管理（用于分类管理各类风险）列表
export function listManagement(query) {
  return request({
    url: '/system/typeManagement/list',
    method: 'get',
    params: query
  })
}

// 查询风险类型管理（用于分类管理各类风险）详细
export function getManagement(id) {
  return request({
    url: '/system/typeManagement/' + id,
    method: 'get'
  })
}

// 新增风险类型管理（用于分类管理各类风险）
export function addManagement(data) {
  return request({
    url: '/system/typeManagement',
    method: 'post',
    data: data
  })
}

// 修改风险类型管理（用于分类管理各类风险）
export function updateManagement(data) {
  return request({
    url: '/system/typeManagement',
    method: 'put',
    data: data
  })
}

// 删除风险类型管理（用于分类管理各类风险）
export function delManagement(id) {
  return request({
    url: '/system/typeManagement/' + id,
    method: 'delete'
  })
}

// 查询风险类型名称列表
export function getRiskNameList() {
  return request({
    url: '/system/typeManagement/getRiskNamelist',
    method: 'get'
  })
}

// 查询风险类型管理列表（用于下拉选择器）
export function getRiskTypeManagementList(query) {
  return request({
    url: '/system/typeManagement/riskTypeManagementlist',
    method: 'get',
    params: query
  })
}