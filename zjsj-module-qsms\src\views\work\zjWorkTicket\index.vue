<template>
  <div class="app-container">
    <div v-if="step == 1">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="90px"
      >
        <el-form-item label="作业票编号" prop="ticketNumber">
          <el-input
            v-model="queryParams.ticketNumber"
            placeholder="请输入作业票编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="作业票名称" prop="ticketName">
          <el-input
            v-model="queryParams.ticketName"
            placeholder="请输入作业票名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="作业负责人" prop="chargePerson">
        <el-input
          v-model="queryParams.chargePerson"
          placeholder="请输入作业负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作业票编号" prop="ticketNumber">
        <el-input
          v-model="queryParams.ticketNumber"
          placeholder="请输入作业票编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请单位" prop="applicantOrgan">
        <el-input
          v-model="queryParams.applicantOrgan"
          placeholder="请输入申请单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属区域" prop="workArea">
        <el-input
          v-model="queryParams.workArea"
          placeholder="请输入所属区域"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作业地址" prop="homeworkLocation">
        <el-input
          v-model="queryParams.homeworkLocation"
          placeholder="请输入作业地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否是承包商作业" prop="contractorJob">
        <el-input
          v-model="queryParams.contractorJob"
          placeholder="请输入是否是承包商作业"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作业单位" prop="workUnit">
        <el-input
          v-model="queryParams.workUnit"
          placeholder="请输入作业单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用电原因" prop="lsydUseElectricityReason">
        <el-input
          v-model="queryParams.lsydUseElectricityReason"
          placeholder="请输入用电原因"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用电人" prop="lsydElectricityUsers">
        <el-input
          v-model="queryParams.lsydElectricityUsers"
          placeholder="请输入用电人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用电人id" prop="lsydElectricityUsersId">
        <el-input
          v-model="queryParams.lsydElectricityUsersId"
          placeholder="请输入用电人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用电设备名称" prop="lsydElectricalEquipment">
        <el-input
          v-model="queryParams.lsydElectricalEquipment"
          placeholder="请输入用电设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备额定功率" prop="lsydPowerEquipmentRated">
        <el-input
          v-model="queryParams.lsydPowerEquipmentRated"
          placeholder="请输入设备额定功率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电源接入点" prop="lsydPowerAccessPoint">
        <el-input
          v-model="queryParams.lsydPowerAccessPoint"
          placeholder="请输入电源接入点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="许可用电功率" prop="lsydPermittedPowerConsumption">
        <el-input
          v-model="queryParams.lsydPermittedPowerConsumption"
          placeholder="请输入许可用电功率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作电压" prop="lsydWorkingVoltage">
        <el-input
          v-model="queryParams.lsydWorkingVoltage"
          placeholder="请输入工作电压"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="风险辨识" prop="riskIdentification">
        <el-input
          v-model="queryParams.riskIdentification"
          placeholder="请输入风险辨识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联设备" prop="linkedDevice">
        <el-input
          v-model="queryParams.linkedDevice"
          placeholder="请输入关联设备"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划开始时间" prop="planBeginTime">
        <el-date-picker
          clearable
          v-model="queryParams.planBeginTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划结束时间" prop="planEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.planEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否主票  0-否1-是" prop="isMainTicket">
        <el-input
          v-model="queryParams.isMainTicket"
          placeholder="请输入是否主票  0-否1-是"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="动火作业级别" prop="dhWorkOperationLeve">
        <el-input
          v-model="queryParams.dhWorkOperationLeve"
          placeholder="请输入动火作业级别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="动火部位" prop="hotWorkArea">
        <el-input
          v-model="queryParams.hotWorkArea"
          placeholder="请输入动火部位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="受限空间名称" prop="sxkjRestrictedSpaceName">
        <el-input
          v-model="queryParams.sxkjRestrictedSpaceName"
          placeholder="请输入受限空间名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="受限空间内原有介质名称"
        prop="sxkjOriginalMediumName"
      >
        <el-input
          v-model="queryParams.sxkjOriginalMediumName"
          placeholder="请输入受限空间内原有介质名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="盲板作业类别" prop="mbBlindPlateHomeworkCategory">
        <el-input
          v-model="queryParams.mbBlindPlateHomeworkCategory"
          placeholder="请输入盲板作业类别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备、管道名称" prop="mbEquipmentPipelineName">
        <el-input
          v-model="queryParams.mbEquipmentPipelineName"
          placeholder="请输入设备、管道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管道介质" prop="mbPipelineMedium">
        <el-input
          v-model="queryParams.mbPipelineMedium"
          placeholder="请输入管道介质"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管道温度" prop="mbPipeTemperature">
        <el-input
          v-model="queryParams.mbPipeTemperature"
          placeholder="请输入管道温度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管道压力" prop="mbPipelinePressure">
        <el-input
          v-model="queryParams.mbPipelinePressure"
          placeholder="请输入管道压力"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="盲板材质" prop="mbBlindPlateMaterial">
        <el-input
          v-model="queryParams.mbBlindPlateMaterial"
          placeholder="请输入盲板材质"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="盲板规格" prop="mbBlindPlateSpecifications">
        <el-input
          v-model="queryParams.mbBlindPlateSpecifications"
          placeholder="请输入盲板规格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="盲板编号" prop="mbBlindPlateNumber">
        <el-input
          v-model="queryParams.mbBlindPlateNumber"
          placeholder="请输入盲板编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="盲板位置图" prop="mbBlindPlatePositionUrl">
        <el-input
          v-model="queryParams.mbBlindPlatePositionUrl"
          placeholder="请输入盲板位置图"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监管人姓名" prop="supervisorName">
        <el-input
          v-model="queryParams.supervisorName"
          placeholder="请输入监管人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监管人id" prop="supervisorId">
        <el-input
          v-model="queryParams.supervisorId"
          placeholder="请输入监管人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="指挥人员id" prop="dzCommanderId">
        <el-input
          v-model="queryParams.dzCommanderId"
          placeholder="请输入指挥人员id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="指挥人员" prop="dzCommanderName">
        <el-input
          v-model="queryParams.dzCommanderName"
          placeholder="请输入指挥人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="司索人id" prop="dzSisuoRenId">
        <el-input
          v-model="queryParams.dzSisuoRenId"
          placeholder="请输入司索人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="司索人名称" prop="dzSisuoRenName">
        <el-input
          v-model="queryParams.dzSisuoRenName"
          placeholder="请输入司索人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="吊具名称" prop="dzLiftingEquipment">
        <el-input
          v-model="queryParams.dzLiftingEquipment"
          placeholder="请输入吊具名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="吊物质量" prop="dzHangingWeight">
        <el-input
          v-model="queryParams.dzHangingWeight"
          placeholder="请输入吊物质量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="作业级别:1-一级 2-二级 3-三级"
        prop="dzHomeworkLevel"
      >
        <el-input
          v-model="queryParams.dzHomeworkLevel"
          placeholder="请输入作业级别:1-一级 2-二级 3-三级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="断路原因" prop="dlCircuitBreakReason">
        <el-input
          v-model="queryParams.dlCircuitBreakReason"
          placeholder="请输入断路原因"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="断路地段相关说明" prop="dlBrokenSectionInstructions">
        <el-input
          v-model="queryParams.dlBrokenSectionInstructions"
          placeholder="请输入断路地段相关说明"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="断路地段示意图" prop="dlBrokenSectionSketchMap">
        <el-input
          v-model="queryParams.dlBrokenSectionSketchMap"
          placeholder="请输入断路地段示意图"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="涉及相关单位" prop="dlRelatedUnitsInvolved">
        <el-input
          v-model="queryParams.dlRelatedUnitsInvolved"
          placeholder="请输入涉及相关单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="高处作业级别：1-Ⅰ 2-Ⅱ 3-Ⅲ 4-Ⅳ"
        prop="gcHomeworkLevel"
      >
        <el-input
          v-model="queryParams.gcHomeworkLevel"
          placeholder="请输入高处作业级别：1-Ⅰ 2-Ⅱ 3-Ⅲ 4-Ⅳ"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作业高度" prop="gcWorkingAltitude">
        <el-input
          v-model="queryParams.gcWorkingAltitude"
          placeholder="请输入作业高度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模版名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模版名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['work:zjWorkTicket:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['work:zjWorkTicket:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['work:zjWorkTicket:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['work:zjWorkTicket:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="zjWorkTicketList"
        @selection-change="handleSelectionChange"
        height="calc(100vh - 240px)"
      >
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column
          label="作业票名称"
          align="center"
          prop="ticketName"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="作业票编号"
          align="center"
          prop="ticketNumber"
          width="120"
          show-overflow-tooltip
        />
        <!-- 所属作业活动 -->
        <el-table-column
          label="所属作业活动"
          align="center"
          prop="homeworkName"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="模版名称"
          align="center"
          prop="templateName"
          width="180"
          show-overflow-tooltip
        />
        <el-table-column
          label="计划开始时间"
          align="center"
          prop="planBeginTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planBeginTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="计划结束时间"
          align="center"
          prop="planEndTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.planEndTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <!-- 作业类型 -->
        <el-table-column
          label="作业类型"
          align="center"
          prop="assignmentType"
          width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ getWorkTypeLabel(scope.row.assignmentType) }}
          </template>
        </el-table-column>
        <el-table-column
          label="作业申请单位"
          align="center"
          prop="applicantOrgan"
          width="120"
        />
        <el-table-column
          label="所属区域"
          align="center"
          prop="workArea"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="作业负责人"
          align="center"
          prop="chargePerson"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column label="关联设备" align="center" prop="linkedDevice" />

        <!-- <el-table-column
        label="作业地址"
        align="center"
        prop="homeworkLocation"
      />
      <el-table-column
        label="是否是承包商作业"
        align="center"
        prop="contractorJob"
      />
      <el-table-column label="作业单位" align="center" prop="workUnit" />
      <el-table-column
        label="用电原因"
        align="center"
        prop="lsydUseElectricityReason"
      />
      <el-table-column
        label="用电人"
        align="center"
        prop="lsydElectricityUsers"
      />
     <el-table-column
        label="用电人id"
        align="center"
        prop="lsydElectricityUsersId"
      /> 
      <el-table-column
        label="用电设备名称"
        align="center"
        prop="lsydElectricalEquipment"
      />
      <el-table-column
        label="设备额定功率"
        align="center"
        prop="lsydPowerEquipmentRated"
      />
      <el-table-column
        label="电源接入点"
        align="center"
        prop="lsydPowerAccessPoint"
      />
      <el-table-column
        label="许可用电功率"
        align="center"
        prop="lsydPermittedPowerConsumption"
      />
      <el-table-column
        label="工作电压"
        align="center"
        prop="lsydWorkingVoltage"
      />
      <el-table-column label="作业内容" align="center" prop="homeworkContent" />
      <el-table-column
        label="风险辨识"
        align="center"
        prop="riskIdentification"
      />
       -->

        <!-- 0-否1-是 -->
        <!-- <el-table-column label="是否主票" align="center" prop="isMainTicket" />
      <el-table-column
        label="动火作业级别"
        align="center"
        prop="dhWorkOperationLeve"
      />
      <el-table-column
        label="动火方式"
        align="center"
        prop="dhWorkOperationType"
      />
      <el-table-column label="动火部位" align="center" prop="hotWorkArea" />
      <el-table-column
        label="受限空间名称"
        align="center"
        prop="sxkjRestrictedSpaceName"
      />
      <el-table-column
        label="受限空间内原有介质名称"
        align="center"
        prop="sxkjOriginalMediumName"
      />
      <el-table-column
        label="盲板作业类别"
        align="center"
        prop="mbBlindPlateHomeworkCategory"
      />
      <el-table-column
        label="设备、管道名称"
        align="center"
        prop="mbEquipmentPipelineName"
      />
      <el-table-column
        label="管道介质"
        align="center"
        prop="mbPipelineMedium"
      />
      <el-table-column
        label="管道温度"
        align="center"
        prop="mbPipeTemperature"
      />
      <el-table-column
        label="管道压力"
        align="center"
        prop="mbPipelinePressure"
      />
      <el-table-column
        label="盲板材质"
        align="center"
        prop="mbBlindPlateMaterial"
      />
      <el-table-column
        label="盲板规格"
        align="center"
        prop="mbBlindPlateSpecifications"
      />
      <el-table-column
        label="盲板编号"
        align="center"
        prop="mbBlindPlateNumber"
      />
      <el-table-column
        label="盲板位置图"
        align="center"
        prop="mbBlindPlatePositionUrl"
      />
      <el-table-column
        label="监管人姓名"
        align="center"
        prop="supervisorName"
      /> -->
        <!-- <el-table-column label="监管人id" align="center" prop="supervisorId" />
      <el-table-column label="指挥人员id" align="center" prop="dzCommanderId" /> -->
        <!-- <el-table-column label="指挥人员" align="center" prop="dzCommanderName" /> -->
        <!-- <el-table-column label="司索人id" align="center" prop="dzSisuoRenId" /> -->
        <!-- <el-table-column
        label="司索人名称"
        align="center"
        prop="dzSisuoRenName"
      />
      <el-table-column
        label="吊具名称"
        align="center"
        prop="dzLiftingEquipment"
      />
      <el-table-column
        label="吊物内容"
        align="center"
        prop="dzHangingObjectContent"
      />
      <el-table-column label="吊物质量" align="center" prop="dzHangingWeight" /> -->
        <!-- :1-一级 2-二级 3-三级 -->
        <!-- <el-table-column label="作业级别" align="center" prop="dzHomeworkLevel" />
      <el-table-column
        label="断路原因"
        align="center"
        prop="dlCircuitBreakReason"
      />
      <el-table-column
        label="断路地段相关说明"
        align="center"
        prop="dlBrokenSectionInstructions"
      />
      <el-table-column
        label="断路地段示意图"
        align="center"
        prop="dlBrokenSectionSketchMap"
      />
      <el-table-column
        label="涉及相关单位"
        align="center"
        prop="dlRelatedUnitsInvolved"
      /> -->
        <!-- ：1-Ⅰ 2-Ⅱ 3-Ⅲ 4-Ⅳ -->
        <!-- <el-table-column
        label="高处作业级别"
        align="center"
        prop="gcHomeworkLevel"
      />
      <el-table-column
        label="作业高度"
        align="center"
        prop="gcWorkingAltitude"
      />
       -->

        <el-table-column
          label="操作"
          fixed="right"
          width="180"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <!-- 查看 -->
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['work:zjWorkTicket:view']"
              >查看</el-button
            >

            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['work:zjWorkTicket:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['work:zjWorkTicket:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <div v-if="step == 2">
      <selectWorkVue
        :step="2"
        @back="handleAreaBack"
        @add="workList"
      ></selectWorkVue>
    </div>
    <div v-if="step == 3">
      <workActivities
        :workType="formType"
        ref="workActivities"
        @back="handleBack"
        @confirm="workAdd"
      ></workActivities>
    </div>

    <!-- 添加或修改作业票对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
    </el-dialog> -->
    <addWorkTicket
      :isAdd="isAdd"
      :isView="isView"
      :formType="formType"
      v-if="step == 4"
      :form="form"
      ref="addWorkTicket"
      @close="closeAddWorkTicket"
      @refreshList="refreshList"
    ></addWorkTicket>
    <!-- 选择作业区域弹窗 -->
    <el-dialog
      title="选择作业区域"
      :visible.sync="areaDialog"
      width="50%"
      append-to-body
    >
      <el-form
        :model="areaForm"
        :rules="areaFormRules"
        ref="areaForm"
        label-width="120px"
      >
        <el-form-item label="作业区域" prop="workArea">
          <selectPeopleTree
            v-model="areaForm.workArea"
            :peopleList="workAreaList"
            placeholder="请搜索或选择作业区域"
            @change="handleWorkAreaChange"
          ></selectPeopleTree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAreaForm">确 定</el-button>
        <el-button @click="closeAreaDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjWorkTicket,
  getZjWorkTicket,
  delZjWorkTicket,
  addZjWorkTicket,
  updateZjWorkTicket,
  getWorkAreaList,
} from "@/api/work/zjWorkTicket";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { listPeople } from "@/api/system/info";
import selectWorkVue from "@/views/work/zjWorkTicket/components/SelectWork.vue";
import addWorkTicket from "@/views/work/zjWorkTicket/components/AddWorkTicket.vue";
import workActivities from "@/views/work/zjWorkTicket/components/workActivities.vue";

export default {
  name: "ZjWorkTicket",
  components: {
    selectPeopleTree,
    selectWorkVue,
    addWorkTicket,
    workActivities,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 作业票表格数据
      zjWorkTicketList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ticketName: null,
        chargePerson: null,
        ticketNumber: null,
        applicantOrgan: null,
        workArea: null,
        homeworkLocation: null,
        contractorJob: null,
        workUnit: null,
        lsydUseElectricityReason: null,
        lsydElectricityUsers: null,
        lsydElectricityUsersId: null,
        lsydElectricalEquipment: null,
        lsydPowerEquipmentRated: null,
        lsydPowerAccessPoint: null,
        lsydPermittedPowerConsumption: null,
        lsydWorkingVoltage: null,
        homeworkContent: null,
        riskIdentification: null,
        linkedDevice: null,
        planBeginTime: null,
        planEndTime: null,
        isMainTicket: null,
        dhWorkOperationLeve: null,
        dhWorkOperationType: null,
        hotWorkArea: null,
        sxkjRestrictedSpaceName: null,
        sxkjOriginalMediumName: null,
        mbBlindPlateHomeworkCategory: null,
        mbEquipmentPipelineName: null,
        mbPipelineMedium: null,
        mbPipeTemperature: null,
        mbPipelinePressure: null,
        mbBlindPlateMaterial: null,
        mbBlindPlateSpecifications: null,
        mbBlindPlateNumber: null,
        mbBlindPlatePositionUrl: null,
        supervisorName: null,
        supervisorId: null,
        dzCommanderId: null,
        dzCommanderName: null,
        dzSisuoRenId: null,
        dzSisuoRenName: null,
        dzLiftingEquipment: null,
        dzHangingObjectContent: null,
        dzHangingWeight: null,
        dzHomeworkLevel: null,
        dlCircuitBreakReason: null,
        dlBrokenSectionInstructions: null,
        dlBrokenSectionSketchMap: null,
        dlRelatedUnitsInvolved: null,
        gcHomeworkLevel: null,
        gcWorkingAltitude: null,
        templateName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 人员树结构
      peopleList: [],
      areaDialog: false,
      areaForm: {},
      areaFormRules: {
        workArea: [
          { required: true, message: "请选择作业区域", trigger: "blur" },
        ],
      },
      workAreaList: [],
      treeProps: {
        children: "children",
        label: "name",
      },
      // 1列表 2 作业票类型 3活动列表 4新增或编辑作业票
      step: 1,
      isAdd: false,
      isView: false,
      workTicketId: null,
      formType: "",
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    // this.getPeopleList();
  },
  methods: {
    getWorkTypeLabel(type) {
      if (type == "lsyd") {
        return "临时用电安全作业";
      } else if (type == "dh") {
        return "动火安全作业";
      } else if (type == "sx") {
        return "受限空间安全作业";
      } else if (type == "mb") {
        return "盲板抽堵安全作业";
      } else if (type == "dz") {
        return "吊装安全作业";
      } else if (type == "dt") {
        return "动土安全作业";
      } else if (type == "dl") {
        return "断路安全作业";
      } else if (type == "gc") {
        return "高处安全作业";
      } else if (type == "qt") {
        return "其他";
      }
    },

    handleBack() {
      this.step = 2;
    },
    workList(item, item2) {
      console.log(item, item2);

      this.formType = item.type;
      this.form.assignmentType = item.type;

      this.form.templateName = item2.title;
      this.step = 3;
    },
    workAdd(val) {
      this.step = 4;
      this.isAdd = true;
      // console.log(val, "4");
      // 所属工作活动
      this.form.ticketName = this.getWorkTypeLabel(this.formType) + "票";
      this.form.homeworkName = val.homeworkActivityName;
    },

    closeAddWorkTicket() {
      this.step = 1;
      this.isView = false;
    },
    refreshList() {
      this.step = 1;
      this.getList();
    },

    // 选择作业区域
    handleAreaBack() {
      this.step = 1;
    },

    // 获取作业区域
    getWorkAreaList() {
      getWorkAreaList().then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.workAreaList = res.data;
          // console.log(this.workAreaList, "workAreaList");
        }
      });
    },

    // 获取人员
    // getPeopleList() {
    //   listPeople().then((res) => {
    //     // console.log(res);
    //     if (res.code == 200) {
    //       this.peopleList = res.data;
    //       // console.log(this.peopleList, "peopleList");
    //     }
    //   });
    // },
    handleWorkAreaChange(selectedItem) {
      if (selectedItem) {
        this.areaForm.workAreaId = selectedItem.id;
        this.areaForm.workArea = selectedItem.label;
        this.form.workArea = selectedItem.label;
        this.form.workAreaId = selectedItem.id;
      } else {
        this.areaForm.workAreaId = null;
        this.areaForm.workArea = null;
      }
    },
    submitAreaForm() {
      this.$refs["areaForm"].validate().then((valid) => {
        if (valid) {
          this.areaDialog = false;
          this.step = 2;
        } else {
          console.log("校验失败");
          return false;
        }
      });
    },

    closeAreaDialog() {
      this.areaDialog = false;
      this.areaForm.workArea = "";
    },

    /** 查询作业票列表 */
    getList() {
      this.loading = true;
      listZjWorkTicket(this.queryParams).then((res) => {
        this.zjWorkTicketList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        ticketName: null,
        chargePerson: null,
        ticketNumber: null,
        applicantOrgan: null,
        workArea: null,
        homeworkLocation: null,
        contractorJob: null,
        workUnit: null,
        lsydUseElectricityReason: null,
        lsydElectricityUsers: null,
        lsydElectricityUsersId: null,
        lsydElectricalEquipment: null,
        lsydPowerEquipmentRated: null,
        lsydPowerAccessPoint: null,
        lsydPermittedPowerConsumption: null,
        lsydWorkingVoltage: null,
        homeworkContent: null,
        riskIdentification: null,
        linkedDevice: null,
        planBeginTime: null,
        planEndTime: null,
        isMainTicket: null,
        dhWorkOperationLeve: null,
        dhWorkOperationType: null,
        hotWorkArea: null,
        sxkjRestrictedSpaceName: null,
        sxkjOriginalMediumName: null,
        mbBlindPlateHomeworkCategory: null,
        mbEquipmentPipelineName: null,
        mbPipelineMedium: null,
        mbPipeTemperature: null,
        mbPipelinePressure: null,
        mbBlindPlateMaterial: null,
        mbBlindPlateSpecifications: null,
        mbBlindPlateNumber: null,
        mbBlindPlatePositionUrl: null,
        supervisorName: null,
        supervisorId: null,
        dzCommanderId: null,
        dzCommanderName: null,
        dzSisuoRenId: null,
        dzSisuoRenName: null,
        dzLiftingEquipment: null,
        dzHangingObjectContent: null,
        dzHangingWeight: null,
        dzHomeworkLevel: null,
        dlCircuitBreakReason: null,
        dlBrokenSectionInstructions: null,
        dlBrokenSectionSketchMap: null,
        dlRelatedUnitsInvolved: null,
        gcHomeworkLevel: null,
        gcWorkingAltitude: null,
        templateName: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // this.open = true;
      // this.title = "添加作业票";
      // 选择作业区域弹窗
      this.areaForm = {
        workArea: null,
        workAreaId: null,
      };

      this.areaDialog = true;
      this.getWorkAreaList();
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjWorkTicket(id).then((res) => {
        if (res.code == 200) {
          this.form = {
            ...res.data,
            riskIdentification: res.data.riskIdentification
              ? res.data.riskIdentification
                  .split(",")
                  .filter((item) => item.trim())
              : [],
            dhWorkOperationType: res.data.dhWorkOperationType
              ? res.data.dhWorkOperationType
                  .split(",")
                  .filter((item) => item.trim())
              : [],
          };
          // console.log(this.form, "qqq");
          this.formType = this.form.assignmentType;

          this.step = 4;
          this.isView = true;
        }
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjWorkTicket(id).then((res) => {
        if (res.code == 200) {
          this.form = {
            ...res.data,
            riskIdentification: res.data.riskIdentification
              ? res.data.riskIdentification
                  .split(",")
                  .filter((item) => item.trim())
              : [],
            dhWorkOperationType: res.data.dhWorkOperationType
              ? res.data.dhWorkOperationType
                  .split(",")
                  .filter((item) => item.trim())
              : [],
          };
          // console.log(this.form, "qqq");
          this.formType = this.form.assignmentType;

          this.step = 4;
        }

        // this.form.riskIdentification =
        //   this.form.riskIdentification?.split(",") || [];
        // this.form.dhWorkOperationType =
        //   this.form.dhWorkOperationType?.split(",") || [];

        // this.open = true;
        // this.title = "修改作业票";
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除作业票编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjWorkTicket(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "work/zjWorkTicket/export",
        {
          ...this.queryParams,
        },
        `zjWorkTicket_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
