import request from '@/utils/request'

// 查询培训计划管理列表
export function listZjTrainingPlanManagement(query) {
  return request({
    url: '/inspection/zjTrainingPlanManagement/list',
    method: 'get',
    params: query
  })
}

// 查询培训计划管理详细
export function getZjTrainingPlanManagement(id) {
  return request({
    url: '/inspection/zjTrainingPlanManagement/' + id,
    method: 'get'
  })
}

// 新增培训计划管理
export function addZjTrainingPlanManagement(data) {
  return request({
    url: '/inspection/zjTrainingPlanManagement',
    method: 'post',
    data: data
  })
}

// 修改培训计划管理
export function updateZjTrainingPlanManagement(data) {
  return request({
    url: '/inspection/zjTrainingPlanManagement',
    method: 'put',
    data: data
  })
}

// 删除培训计划管理
export function delZjTrainingPlanManagement(id) {
  return request({
    url: '/inspection/zjTrainingPlanManagement/' + id,
    method: 'delete'
  })
}
