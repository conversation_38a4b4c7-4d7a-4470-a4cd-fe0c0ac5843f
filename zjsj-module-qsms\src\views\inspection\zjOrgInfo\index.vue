<template>
  <div class="app-container">
    <el-row>
      <el-col :span="3">
        <orgTree :type="'1'" @node-click="handleOrgTreeNodeClick"></orgTree>
      </el-col>
      <el-col :span="21">
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
        <!-- <el-tab-pane label="组织成员管理" name="first">
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              v-show="showSearch"
              label-width="120px"
            >
              <el-form-item label="姓名" prop="orgName">
                <el-input
                  v-model="queryParams.orgName"
                  placeholder="请输入姓名"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="工号" prop="employeeId">
                <el-input
                  v-model="queryParams.employeeId"
                  placeholder="请输入工号"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="部门" prop="orgType">
                <el-input
                  v-model="queryParams.orgType"
                  placeholder="请输入部门"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAdd"
                  v-hasPermi="['inspection:zjOrgInfo:add']"
                  >新增</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdate"
                  v-hasPermi="['inspection:zjOrgInfo:edit']"
                  >修改</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['inspection:zjOrgInfo:remove']"
                  >删除</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="warning"
                  plain
                  icon="el-icon-download"
                  size="mini"
                  @click="handleExport"
                  v-hasPermi="['inspection:zjOrgInfo:export']"
                  >导出</el-button
                >
              </el-col>
              <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getList"
              ></right-toolbar>
            </el-row>

            <el-table
              v-loading="loading"
              :data="zjOrgInfoList"
              @selection-change="handleSelectionChange"
              height="calc(100vh - 250px)"
              style="width: 100%"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="姓名" align="center" prop="name" />
              <el-table-column
                label="机构类型(1-集团2-/子公司3-/部门)"
                align="center"
                prop="orgType"
              /> 
              <el-table-column label="工号" align="center" prop="employeeId" />
              <el-table-column label="部门" align="center" prop="department" />
              <el-table-column label="职务" align="center" prop="position" />
              <el-table-column label="联系方式" align="center" prop="contact" />
              <el-table-column label="安全职责" align="center" prop="duty" />
              <el-table-column
                label="安全履历"
                align="center"
                prop="anquanlvli"
              />
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['inspection:zjOrgInfo:edit']"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['inspection:zjOrgInfo:remove']"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-tab-pane> -->
        <!-- <el-tab-pane label="组织文件管理" name="second"> -->
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="120px"
        >
          <el-form-item label="文件名" prop="fileName">
            <el-input
              v-model="queryParams.fileName"
              placeholder="请输入文件名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="大小" prop="size">
            <el-input
              v-model="queryParams.size"
              placeholder="请输入大小"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="上传时间" prop="uploadTime">
            <el-date-picker
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              v-model="queryParams.uploadTime"
              placeholder="请输入上传时间"
              clearable
              @keyup.enter.native="handleQuery"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="来源" prop="deptId">
            <!-- <el-input
              v-model="queryParams.source"
              placeholder="请输入来源"
              clearable
              @keyup.enter.native="handleQuery"
            /> -->
            <el-select v-model="form.deptId" style="width: 100%">
              <template>
                <div v-for="group in deptList" :key="group.id">
                  <el-option
                    :label="group.enterpriseName"
                    :value="group.id"
                    style="padding-left: 20px"
                  />
                  <template v-if="group.children && group.children.length">
                    <el-option
                      v-for="child in renderDeptOptions(group.children)"
                      :key="child.id"
                      :label="child.enterpriseName"
                      :value="child.id"
                      :style="{ paddingLeft: child.level * 20 + 'px' }"
                    />
                  </template>
                </div>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjOrgInfo:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjOrgInfo:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjOrgInfo:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjOrgInfo:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjOrgInfoList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="文件名" align="center" prop="fileName" />
          <el-table-column label="大小" align="center" prop="size" />
          <el-table-column label="上传时间" align="center" prop="createTime" />
          <el-table-column label="来源" align="center" prop="source" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjOrgInfo:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjOrgInfo:edit']"
                >预览</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjOrgInfo:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
        <!-- </el-tab-pane> -->
        <!-- </el-tabs> -->
      </el-col>
    </el-row>

    <!-- 添加或修改机构信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- <el-form-item label="主键" prop="id">
          <el-input v-model="form.id" placeholder="请输入主键" />
        </el-form-item> -->
        <!-- <div v-show="activeName == 'first'">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="form.name" placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="工号" prop="employeeId">
            <el-input v-model="form.employeeId" placeholder="请输入工号" />
          </el-form-item>
          <el-form-item label="部门" prop="department">
            <el-select
              v-model="form.department"
              placeholder="请选择部门"
              style="width: 100%"
            >
              <el-option label="部门1" value="1" />
              <el-option label="部门2" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="职务" prop="position">
            <el-input v-model="form.position" placeholder="请输入职务" />
          </el-form-item>
          <el-form-item label="联系方式" prop="contact">
            <el-input v-model="form.contact" placeholder="请输入联系方式" />
          </el-form-item>
          <el-form-item label="安全职责" prop="safetyResponsibility">
            <file-upload v-model="form.safetyResponsibility" />
          </el-form-item>
          <el-form-item label="安全履历" prop="anquanlvli">
            <file-upload v-model="form.anquanlvli" />
          </el-form-item>
        </div> -->
        <div v-show="activeName == 'second'">
          <el-form-item label="文件名" prop="fileName">
            <!-- <el-input v-model="form.fileName" placeholder="请输入文件名" /> -->
            <file-upload v-model="form.fileName"></file-upload>
          </el-form-item>
          <el-form-item label="大小" prop="size">
            <el-input v-model="form.size" placeholder="请输入大小" />
          </el-form-item>
          <el-form-item label="来源" prop="deptId">
            <!-- <el-input v-model="form.source" placeholder="请输入来源" /> -->
            <el-select v-model="form.deptId" style="width: 100%">
              <template>
                <div v-for="group in deptList" :key="group.id">
                  <el-option
                    :label="group.enterpriseName"
                    :value="group.id"
                    style="padding-left: 20px"
                  />
                  <template v-if="group.children && group.children.length">
                    <el-option
                      v-for="child in renderDeptOptions(group.children)"
                      :key="child.id"
                      :label="child.enterpriseName"
                      :value="child.id"
                      :style="{ paddingLeft: child.level * 20 + 'px' }"
                    />
                  </template>
                </div>
              </template>
            </el-select>
          </el-form-item>
        </div>

        <!-- <el-form-item label="机构职责" prop="responsibility">
          <el-input
            v-model="form.responsibility"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="机构网络图" prop="networkDiagramUrl">
          <el-input
            v-model="form.networkDiagramUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="成立文件" prop="establishDocumentsUrl">
          <el-input
            v-model="form.establishDocumentsUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="会议纪要" prop="meetingMinutesUrl">
          <el-input
            v-model="form.meetingMinutesUrl"
            placeholder="请输入会议纪要"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjOrgInfo,
  getZjOrgInfo,
  delZjOrgInfo,
  addZjOrgInfo,
  updateZjOrgInfo,
} from "@/api/inspection/zjOrgInfo";
import { listInfo } from "@/api/system/info";
import orgTree from "../../components/orgTree.vue";

export default {
  name: "ZjOrgInfo",
  components: {
    orgTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 机构信息表格数据
      zjOrgInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orgName: null,
        orgType: null,
        employeeId: null,
        responsibility: null,
        networkDiagramUrl: null,
        establishDocumentsUrl: null,
        meetingMinutesUrl: null,
        deptId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      activeName: "second",
      deptId: [],
      deptParams: {
        enterpriseName: undefined,
        status: undefined,
      },
    };
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    handleOrgTreeNodeClick(nodeData) {
      // 在这里处理接收到的子组件数据
      // console.log("接收到子组件数据:", nodeData);
      // 可以根据nodeData更新查询参数或其他状态
      this.queryParams.deptId = nodeData.id; // 假设nodeData中有id字段
      this.handleQuery(); // 触发查询
    },
    getDeptList() {
      listInfo(this.deptParams).then((response) => {
        this.deptList = this.handleTree(response.data, "id");
        this.loading = false;
      });
    },
    renderDeptOptions(children, level = 1) {
      let result = [];
      children.forEach((child) => {
        result.push({
          ...child,
          level,
          enterpriseName: " ".repeat(level * 2) + child.enterpriseName,
        });
        if (child.children && child.children.length) {
          result = result.concat(
            this.renderDeptOptions(child.children, level + 1)
          );
        }
      });
      return result;
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    /** 查询机构信息列表 */
    getList() {
      this.loading = true;
      listZjOrgInfo(this.queryParams).then((response) => {
        this.zjOrgInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orgName: null,
        orgType: null,
        employeeId: null,
        responsibility: null,
        networkDiagramUrl: null,
        establishDocumentsUrl: null,
        meetingMinutesUrl: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加文件信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjOrgInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改文件信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjOrgInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjOrgInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除机构信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjOrgInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjOrgInfo/export",
        {
          ...this.queryParams,
        },
        `zjOrgInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
