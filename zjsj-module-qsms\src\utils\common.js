// 先定义processFileName函数
export function processFileName(fileName) {
  const [nameWithoutExt, ext] = fileName.split(".");
  const nameParts = nameWithoutExt.split("_");
  nameParts.pop();
  return `${nameParts.join("_")}.${ext || ''}`; // 处理没有扩展名的情况
}

// 再定义getFileOrignalName并引用processFileName
export function getFileOrignalName(fileName) {
  if (!fileName) return "";
  // 显式调用当前模块内的processFileName函数
  if (fileName.lastIndexOf("/") > -1) {
    return processFileName(fileName.slice(fileName.lastIndexOf("/") + 1));
  } else {
    return processFileName(fileName);
  }
}
