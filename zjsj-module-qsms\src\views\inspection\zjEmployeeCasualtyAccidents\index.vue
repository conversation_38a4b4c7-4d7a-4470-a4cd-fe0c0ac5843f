<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司id" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入公司id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目id" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故日期" prop="accidentDate">
        <el-date-picker clearable
          v-model="queryParams.accidentDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择事故日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="事故件数" prop="accidentsNum">
        <el-input
          v-model="queryParams.accidentsNum"
          placeholder="请输入事故件数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="死亡人数" prop="casualtiesTotalNum">
        <el-input
          v-model="queryParams.casualtiesTotalNum"
          placeholder="请输入死亡人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重伤人数" prop="seriousInjuryTotalNum">
        <el-input
          v-model="queryParams.seriousInjuryTotalNum"
          placeholder="请输入重伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="轻伤人数" prop="minorInjuryTotalNum">
        <el-input
          v-model="queryParams.minorInjuryTotalNum"
          placeholder="请输入轻伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="zjEmployeeCasualtyAccidentsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公司" align="center" prop="companyName" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="事故日期" align="center" prop="accidentDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.accidentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="事故件数" align="center" prop="accidentsNum" />
      <el-table-column label="伤亡人数" align="center">
        <el-table-column label="死亡" align="center" prop="casualtiesTotalNum" />
        <el-table-column label="重伤" align="center" prop="seriousInjuryTotalNum" />
        <el-table-column label="轻伤" align="center" prop="minorInjuryTotalNum" />
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-wrapper">
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改职工伤亡事故对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司id" prop="companyId">
          <el-input v-model="form.companyId" placeholder="请输入公司id" />
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目id" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目id" />
        </el-form-item>
        <el-form-item label="事故日期" prop="accidentDate">
          <el-date-picker clearable
            v-model="form.accidentDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择事故日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="事故件数" prop="accidentsNum">
          <el-input v-model="form.accidentsNum" placeholder="请输入事故件数" />
        </el-form-item>
        <el-form-item label="死亡人数" prop="casualtiesTotalNum">
          <el-input v-model="form.casualtiesTotalNum" placeholder="请输入死亡人数" />
        </el-form-item>
        <el-form-item label="重伤人数" prop="seriousInjuryTotalNum">
          <el-input v-model="form.seriousInjuryTotalNum" placeholder="请输入重伤人数" />
        </el-form-item>
        <el-form-item label="轻伤人数" prop="minorInjuryTotalNum">
          <el-input v-model="form.minorInjuryTotalNum" placeholder="请输入轻伤人数" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listZjEmployeeCasualtyAccidents, getZjEmployeeCasualtyAccidents, delZjEmployeeCasualtyAccidents, addZjEmployeeCasualtyAccidents, updateZjEmployeeCasualtyAccidents } from "@/api/inspection/zjEmployeeCasualtyAccidents";

export default {
  name: "ZjEmployeeCasualtyAccidents",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 职工伤亡事故表格数据
      zjEmployeeCasualtyAccidentsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyId: null,
        companyName: null,
        projectName: null,
        projectId: null,
        accidentDate: null,
        accidentsNum: null,
        casualtiesTotalNum: null,
        seriousInjuryTotalNum: null,
        minorInjuryTotalNum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询职工伤亡事故列表 */
    getList() {
      this.loading = true;
      listZjEmployeeCasualtyAccidents(this.queryParams).then(response => {
        this.zjEmployeeCasualtyAccidentsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyId: null,
        companyName: null,
        projectName: null,
        projectId: null,
        accidentDate: null,
        accidentsNum: null,
        casualtiesTotalNum: null,
        seriousInjuryTotalNum: null,
        minorInjuryTotalNum: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加职工伤亡事故";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getZjEmployeeCasualtyAccidents(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改职工伤亡事故";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateZjEmployeeCasualtyAccidents(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEmployeeCasualtyAccidents(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let ids;
      if (row && row.id) {
        // 单行删除
        ids = [row.id];
      } else {
        // 多行删除
        ids = this.ids;
      }

      if (!ids || (Array.isArray(ids) && ids.length === 0)) {
        this.$modal.msgError("请选择要删除的数据");
        return;
      }

      this.$modal
        .confirm('是否确认删除' + ids.length + '条职工伤亡事故数据项？')
        .then(function() {
          return delZjEmployeeCasualtyAccidents(ids);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 根据选中状态决定导出内容和参数
      const hasSelection = this.ids.length > 0;
      const confirmMessage = hasSelection
        ? `是否确认导出选中的${this.ids.length}条职工伤亡事故数据项?`
        : "是否确认导出所有职工伤亡事故数据项?";

      // 准备导出参数
      const exportParams = hasSelection
        ? { ids: this.ids.join(",") } // 选中了行，只传ids参数
        : { ...this.queryParams }; // 没选中，传查询参数

      // 如果导出全部，移除分页参数
      if (!hasSelection) {
        delete exportParams.pageNum;
        delete exportParams.pageSize;
      }

      this.$modal
        .confirm(confirmMessage)
        .then(() => {
          this.download(
            'inspection/zjEmployeeCasualtyAccidents/export',
            exportParams,
            `zjEmployeeCasualtyAccidents_${hasSelection ? 'selected_' : ''}${new Date().getTime()}.xlsx`
          );
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
/* 确保分页固定在底部，参考其他页面的实现 */
.app-container {
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  padding: 20px;
}

/* 搜索表单区域 */
.app-container .el-form {
  flex-shrink: 0;
  margin-bottom: 10px;
}

/* 工具栏区域 */
.app-container .el-row.mb8 {
  flex-shrink: 0;
  margin-bottom: 8px;
}

/* 表格区域 - 占据剩余空间 */
.app-container .el-table {
  flex: 1;
  margin-bottom: 20px;
}

/* 分页样式 - 固定在底部 */
.pagination-wrapper {
  flex-shrink: 0;
  text-align: center;
  padding: 16px 0;
  margin-top: auto;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

/* 分页组件响应式样式 */
.pagination-wrapper ::v-deep .el-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
  margin-right: 16px;
  order: 1;
}

.pagination-wrapper ::v-deep .el-pagination .btn-prev {
  order: 2;
}

.pagination-wrapper ::v-deep .el-pagination .el-pager {
  order: 3;
}

.pagination-wrapper ::v-deep .el-pagination .btn-next {
  order: 4;
}

/* 中等屏幕适配 (平板) */
@media (max-width: 1024px) and (min-width: 769px) {
  .pagination-wrapper {
    padding: 10px 0 15px;
    border-top: 1px solid #ebeef5;
    background: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
    font-size: 13px;
  }

  .pagination-wrapper ::v-deep .el-pagination .btn-prev,
  .pagination-wrapper ::v-deep .el-pagination .btn-next {
    padding: 0 10px;
    min-width: 36px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pager li {
    min-width: 36px;
    height: 36px;
    line-height: 36px;
    font-size: 13px;
  }
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .app-container {
    height: calc(100vh - 84px);
    padding: 10px;
  }

  .pagination-wrapper {
    margin-bottom: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    border-top: 2px solid #ebeef5;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .pagination-wrapper ::v-deep .el-pagination {
    flex-direction: row;
    justify-content: center;
    gap: 4px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
    font-size: 12px;
    margin-right: 8px;
  }

  .pagination-wrapper ::v-deep .el-pagination .btn-prev,
  .pagination-wrapper ::v-deep .el-pagination .btn-next {
    padding: 0 8px;
    min-width: 32px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }

  /* 为小屏幕预留底部分页空间 */
  .el-table {
    margin-bottom: 60px;
  }
}
</style>
