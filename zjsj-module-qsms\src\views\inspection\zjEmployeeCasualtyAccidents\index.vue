<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司id" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入公司id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目id" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="事故日期" prop="accidentDate">
        <el-date-picker clearable
          v-model="queryParams.accidentDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择事故日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="事故件数" prop="accidentsNum">
        <el-input
          v-model="queryParams.accidentsNum"
          placeholder="请输入事故件数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="死亡人数" prop="casualtiesTotalNum">
        <el-input
          v-model="queryParams.casualtiesTotalNum"
          placeholder="请输入死亡人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="重伤人数" prop="seriousInjuryTotalNum">
        <el-input
          v-model="queryParams.seriousInjuryTotalNum"
          placeholder="请输入重伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="轻伤人数" prop="minorInjuryTotalNum">
        <el-input
          v-model="queryParams.minorInjuryTotalNum"
          placeholder="请输入轻伤人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="zjEmployeeCasualtyAccidentsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="公司id" align="center" prop="companyId" />
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="项目id" align="center" prop="projectId" />
      <el-table-column label="事故日期" align="center" prop="accidentDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.accidentDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="事故件数" align="center" prop="accidentsNum" />
      <el-table-column label="死亡人数" align="center" prop="casualtiesTotalNum" />
      <el-table-column label="重伤人数" align="center" prop="seriousInjuryTotalNum" />
      <el-table-column label="轻伤人数" align="center" prop="minorInjuryTotalNum" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjEmployeeCasualtyAccidents:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改职工伤亡事故对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司id" prop="companyId">
          <el-input v-model="form.companyId" placeholder="请输入公司id" />
        </el-form-item>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目id" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目id" />
        </el-form-item>
        <el-form-item label="事故日期" prop="accidentDate">
          <el-date-picker clearable
            v-model="form.accidentDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择事故日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="事故件数" prop="accidentsNum">
          <el-input v-model="form.accidentsNum" placeholder="请输入事故件数" />
        </el-form-item>
        <el-form-item label="死亡人数" prop="casualtiesTotalNum">
          <el-input v-model="form.casualtiesTotalNum" placeholder="请输入死亡人数" />
        </el-form-item>
        <el-form-item label="重伤人数" prop="seriousInjuryTotalNum">
          <el-input v-model="form.seriousInjuryTotalNum" placeholder="请输入重伤人数" />
        </el-form-item>
        <el-form-item label="轻伤人数" prop="minorInjuryTotalNum">
          <el-input v-model="form.minorInjuryTotalNum" placeholder="请输入轻伤人数" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listZjEmployeeCasualtyAccidents, getZjEmployeeCasualtyAccidents, delZjEmployeeCasualtyAccidents, addZjEmployeeCasualtyAccidents, updateZjEmployeeCasualtyAccidents } from "@/api/inspection/zjEmployeeCasualtyAccidents";

export default {
  name: "ZjEmployeeCasualtyAccidents",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 职工伤亡事故表格数据
      zjEmployeeCasualtyAccidentsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyId: null,
        companyName: null,
        projectName: null,
        projectId: null,
        accidentDate: null,
        accidentsNum: null,
        casualtiesTotalNum: null,
        seriousInjuryTotalNum: null,
        minorInjuryTotalNum: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询职工伤亡事故列表 */
    getList() {
      this.loading = true;
      listZjEmployeeCasualtyAccidents(this.queryParams).then(response => {
        this.zjEmployeeCasualtyAccidentsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyId: null,
        companyName: null,
        projectName: null,
        projectId: null,
        accidentDate: null,
        accidentsNum: null,
        casualtiesTotalNum: null,
        seriousInjuryTotalNum: null,
        minorInjuryTotalNum: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加职工伤亡事故";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getZjEmployeeCasualtyAccidents(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改职工伤亡事故";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateZjEmployeeCasualtyAccidents(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjEmployeeCasualtyAccidents(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除职工伤亡事故编号为"' + ids + '"的数据项？').then(function() {
        return delZjEmployeeCasualtyAccidents(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inspection/zjEmployeeCasualtyAccidents/export', {
        ...this.queryParams
      }, `zjEmployeeCasualtyAccidents_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
