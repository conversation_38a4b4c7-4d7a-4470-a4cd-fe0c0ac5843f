import request from '@/utils/request'

// 查询安全生产责任制考核列表
export function listAssessment(query) {
  return request({
    url: '/system/assessment/list',
    method: 'get',
    params: query
  })
}

// 查询安全生产责任制考核详细
export function getAssessment(id) {
  return request({
    url: '/system/assessment/' + id,
    method: 'get'
  })
}

// 新增安全生产责任制考核
export function addAssessment(data) {
  return request({
    url: '/system/assessment',
    method: 'post',
    data: data
  })
}

// 修改安全生产责任制考核
export function updateAssessment(data) {
  return request({
    url: '/system/assessment',
    method: 'put',
    data: data
  })
}

// 删除安全生产责任制考核
export function delAssessment(id) {
  return request({
    url: '/system/assessment/' + id,
    method: 'delete'
  })
}
