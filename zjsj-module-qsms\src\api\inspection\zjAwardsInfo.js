import request from "@/utils/request";

// 查询创优的奖项列表
export function listZjAwardsInfo(query) {
  return request({
    url: "/inspection/zjAwardsInfo/list",
    method: "get",
    params: query,
  });
}

// 查询创优的奖项详细
export function getZjAwardsInfo(id) {
  return request({
    url: "/inspection/zjAwardsInfo/" + id,
    method: "get",
  });
}

// 新增创优的奖项
export function addZjAwardsInfo(data) {
  return request({
    url: "/inspection/zjAwardsInfo",
    method: "post",
    data: data,
  });
}

// 修改创优的奖项
export function updateZjAwardsInfo(data) {
  return request({
    url: "/inspection/zjAwardsInfo",
    method: "put",
    data: data,
  });
}

// 删除创优的奖项
export function delZjAwardsInfo(id) {
  return request({
    url: "/inspection/zjAwardsInfo/" + id,
    method: "delete",
  });
}
// 查询奖项类别
export function getZjAwardsType(query) {
  return request({
    url: "system/dict/data/list",
    method: "get",
    params: query,
  });
}

// 奖项列表
export function getZjAwardsList(query) {
  return request({
    url: "/api/getAwardsInfoList",
    method: "get",
    params: query,
  });
}
// 奖项详情
