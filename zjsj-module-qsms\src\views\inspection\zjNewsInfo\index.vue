<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
    >
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="作者" prop="author">
        <el-input
          v-model="queryParams.author"
          placeholder="请输入作者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 0 否 1-是 -->
      <!-- <el-form-item label="逻辑删除 " prop="isDeleted">
        <el-input
          v-model="queryParams.isDeleted"
          placeholder="请输入逻辑删除"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- 0 否 1-是 -->
      <!-- <el-form-item label="是否轮播 " prop="isCarousel">
        <el-select v-model="queryParams.isCarousel" placeholder="请选择">
          <el-option label="否" value="0" />
          <el-option label="是" value="1" />
        </el-select>
      </el-form-item> -->
      <!-- 1-公司要闻2-通知公告3-法律法规4-行业动态 5-EHS动态 6-安全视频 -->
      <el-form-item label="新闻类型" prop="newType">
        <el-select v-model="queryParams.newType" placeholder="请选择" clearable>
          <el-option label="公司要闻" value="1" />
          <el-option label="通知公告" value="2" />
          <el-option label="法律法规" value="3" />
          <el-option label="行业动态" value="4" />
          <el-option label="EHS动态" value="5" />
          <el-option label="安全视频" value="6" />
        </el-select>
      </el-form-item>
      <!-- 1-草稿 2-发布 3-撤销 -->
      <el-form-item label="新闻状态" prop="newStatus">
        <el-select
          v-model="queryParams.newStatus"
          placeholder="请选择"
          clearable
        >
          <el-option label="草稿" value="1" />
          <el-option label="发布" value="2" />
          <el-option label="撤销" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="来源" prop="newSource">
        <el-input
          v-model="queryParams.newSource"
          placeholder="请输入来源"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker
          clearable
          v-model="queryParams.publishTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择发布时间"
        >
        </el-date-picker>
      </el-form-item>
      <!-- 0-否 1-是 -->
      <el-form-item label="是否置顶" prop="isTop">
        <!-- <el-input
          v-model="queryParams.isTop"
          placeholder="请输入是否置顶"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <el-select v-model="queryParams.isTop" placeholder="请选择" clearable>
          <el-option label="否" :value="0" />
          <el-option label="是" :value="1" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="附件url" prop="attachmentUrl">
        <el-input
          v-model="queryParams.attachmentUrl"
          placeholder="请输入附件url"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="附件名称" prop="attachmentName">
        <el-input
          v-model="queryParams.attachmentName"
          placeholder="请输入附件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="备注" prop="newRemark">
        <el-input
          v-model="queryParams.newRemark"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjNewsInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjNewsInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjNewsInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjNewsInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjNewsInfoList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 290px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column
        label="标题"
        align="center"
        prop="title"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <!-- <el-table-column
        label="富文本内容"
        align="center"
        prop="content"
        width="120"
      /> -->
      <el-table-column label="封面图" align="center" prop="coverImg">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.coverImg"
            :src="`${baseUrl}${scope.row.coverImg}`"
            fit="cover"
          />
          <span v-else>-</span>
          <!-- {{ baseUrl }}{{ scope.row.coverImg }} -->
        </template>
      </el-table-column>

      <el-table-column label="作者" align="center" prop="author" />
      <!-- <el-table-column label="逻辑删除 " align="center" prop="isDeleted" /> -->
      <el-table-column label="是否轮播" align="center" prop="isCarousel">
        <template slot-scope="scope">
          <span v-if="scope.row.isCarousel === '0' || scope.row.isCarousel === 0">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column>
      <!-- 1-公司要闻2-通知公告3-法律法规4-行业动态 5-EHS动态 6-安全视频 -->
      <el-table-column label="新闻类型" align="center" prop="newType">
        <template slot-scope="scope">
          <span v-if="scope.row.newType == '1'">公司要闻</span>
          <span v-else-if="scope.row.newType === '2'">通知公告</span>
          <span v-else-if="scope.row.newType === '3'">法律法规</span>
          <span v-else-if="scope.row.newType === '4'">行业动态</span>
          <span v-else-if="scope.row.newType === '5'">EHS动态</span>
          <span v-else-if="scope.row.newType === '6'">安全视频</span>
        </template>
      </el-table-column>
      <el-table-column label="新闻状态" align="center" prop="newStatus">
        <!--1-草稿 2-发布 3-撤销 -->
        <template slot-scope="scope">
          <span v-if="scope.row.newStatus == 1">草稿</span>
          <span v-else-if="scope.row.newStatus == 2">发布</span>
          <span v-else>撤销</span>
        </template>
      </el-table-column>
      <el-table-column label="来源" align="center" prop="newSource" />

      <el-table-column
        label="发布时间"
        align="center"
        prop="publishTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- 0-否 1-是 -->
      <el-table-column label="是否置顶" align="center" prop="isTop">
        <template slot-scope="scope">
          <span v-if="scope.row.isTop === 0">否</span>
          <span v-else>是</span>
        </template>
      </el-table-column>

      <el-table-column label="附件url" align="center" prop="attachmentUrl">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.attachmentUrl"
            size="mini"
            type="text"
            @click="handleViewAttachment(scope.row.attachmentUrl)"
            >查看</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="附件名称" align="center" prop="attachmentName">
      </el-table-column> -->
      <el-table-column label="备注" align="center" prop="newRemark" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjNewsInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjNewsInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改新闻管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="富文本内容">
          <editor
            ref="editorRef"
            v-model="form.content"
            :min-height="192"
            :upload-config="{
              action: `${baseUrl}/common/upload`,
              onSuccess: this.handleImageUploadSuccess,
              onError: this.handleImageUploadError,
            }"
          />
        </el-form-item>
        <el-form-item label="封面图" prop="coverImg">
          <image-upload v-model="form.coverImg" :limit="1" />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <!-- 0 否 1-是 -->
        <!-- <el-form-item label="逻辑删除 " prop="isDeleted">
          <el-input v-model="form.isDeleted" placeholder="请输入逻辑删除 " />
        </el-form-item> -->
        <!-- 0 否 1-是 -->
        <el-form-item label="是否轮播" prop="isCarousel">
          <!-- <el-input v-model="form.isCarousel" placeholder="请输入是否轮播 " /> -->
          <el-select v-model="form.isCarousel" style="width: 100%">
            <el-option label="否" value="0" />
            <el-option label="是" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="新闻类型" prop="newType">
          <el-select
            v-model="form.newType"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="公司要闻" value="1" />
            <el-option label="通知公告" value="2" />
            <el-option label="法律法规" value="3" />
            <el-option label="行业动态" value="4" />
            <el-option label="EHS动态" value="5" />
            <el-option label="安全视频" value="6" />
          </el-select>
        </el-form-item>
        <!-- 1-草稿 2-发布 3-撤销 -->
        <!-- <el-form-item label="新闻状态" prop="newStatus">
          <el-select v-model="form.newStatus" placeholder="请选择">
            <el-option label="草稿" value="1" />
            <el-option label="发布" value="2" />
            <el-option label="撤销" value="3" />
          </el-select>
        </el-form-item> -->
        <el-form-item label="来源" prop="newSource">
          <el-input v-model="form.newSource" placeholder="请输入来源" />
        </el-form-item>

        <!--  0-否 1-是 -->
        <el-form-item label="是否置顶" prop="isTop">
          <el-select v-model="form.isTop" style="width: 100%">
            <el-option label="否" :value="0" />
            <el-option label="是" :value="1" />
          </el-select>
        </el-form-item>
        <!-- 发布时间 -->
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            v-model="form.publishTime"
            type="date"
            placeholder="选择发布时间"
            style="width: 100%"
            clearable
            value-format="yyyy-MM-dd"
          />
        </el-form-item>

        <el-form-item label="上传附件" prop="attachmentUrl">
          <!-- <el-input v-model="form.attachmentUrl" placeholder="请输入附件url" /> -->
          <file-upload v-model="form.attachmentUrl"></file-upload>
        </el-form-item>
        <!-- <el-form-item label="附件名称" prop="attachmentName">
          <el-input
            v-model="form.attachmentName"
            placeholder="请输入附件名称"
          />
        </el-form-item> -->
        <el-form-item label="备注" prop="newRemark">
          <el-input v-model="form.newRemark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm('1')">暂存</el-button>
        <el-button type="primary" @click="submitForm('2')">发布</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 附件信息弹窗 -->
    <el-dialog
      title="附件信息"
      :visible.sync="attachmentDialogVisible"
      width="40%"
      append-to-body
    >
      <div v-for="(item, index) in attachmentList" :key="index">
        <div class="demo-list-item__name">
          附件{{ index + 1 }}:
          <span @click="handleView(item)" class="attachment-item">
            <i class="el-icon-document"></i>
            {{ item.split("/").pop() }}
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjNewsInfo,
  getZjNewsInfo,
  delZjNewsInfo,
  addZjNewsInfo,
  updateZjNewsInfo,
} from "@/api/inspection/zjNewsInfo";

export default {
  name: "ZjNewsInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻管理表格数据
      zjNewsInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 弹出层是否显示附件信息
      attachmentDialogVisible: false,
      // 附件列表
      attachmentList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        content: null,
        coverImg: null,
        author: null,
        isDeleted: null,
        isCarousel: null,
        newType: null,
        newStatus: null,
        newSource: null,
        publishTime: null,
        isTop: null,
        attachmentUrl: null,
        attachmentName: null,
        newRemark: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        newType: [
          { required: true, message: "请选择新闻类型", trigger: "change" },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.editorRef && this.$refs.editorRef.quill) {
        console.log("this.$refs.editorRef.quill", this.$refs.editorRef.quill);
        this.$refs.editorRef.quill.on(
          "text-change",
          (delta, oldDelta, source) => {
            console.log("text-change", delta, oldDelta, source);
            if (source === "user") {
              this.checkForPastedImages();
            }
          }
        );
      }
    });
  },
  methods: {
    /** 图片上传成功回调 */
    handleImageUploadSuccess(res, file) {
      // 图片上传成功后的处理逻辑
      console.log("图片上传成功:", res, file);
      // 可以在这里添加成功提示或其他处理逻辑
      // this.$message.success("图片上传成功");
    },
    
    /** 图片上传失败回调 */
    handleImageUploadError(error) {
      this.$message.error("图片上传失败: " + error.message);
    },

    handleViewAttachment(value) {
      this.attachmentDialogVisible = true;
      console.log(value, "附件");
      this.attachmentList = value.split(",");
    },
    async handleView(value) {
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    /** 查询新闻管理列表 */
    getList() {
      this.loading = true;
      listZjNewsInfo(this.queryParams).then((res) => {
        this.zjNewsInfoList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        content: null,
        coverImg: null,
        author: null,
        isDeleted: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        isCarousel: "0",
        newType: null,
        newStatus: null,
        newSource: null,
        publishTime: null,
        isTop: 0,
        attachmentUrl: null,
        attachmentName: null,
        newRemark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加新闻管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjNewsInfo(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改新闻管理";
      });
    },
    /** 提交按钮 */
    submitForm(type) {
      this.form.newStatus = type;
      // this.form.publishTime = new Date();
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjNewsInfo(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjNewsInfo(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除新闻管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjNewsInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjNewsInfo/export",
        {
          ...this.queryParams,
        },
        `zjNewsInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep .demo-list-item__name {
  .attachment-item {
    cursor: pointer;
    color: #409eff;
    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>
