<template>
  <div class="articleList-container">
    <NavBar />
    <!-- 轮播图 -->
    <!-- <div class="swiper">
      <el-carousel trigger="click" height="100%">
       
        <el-carousel-item v-for="(item, index) in swiperList" :key="index">
          <img :src="item.src" alt="" class="swiper-image" />
        </el-carousel-item>
      </el-carousel>
    </div> -->
    <div class="article-list-container">
      <!-- 新增导航 -->
      <div class="breadcrumb">
        <span class="goBack" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span>返回</span>
        </span>

        <span class="breadcrumb-item" @click="goHome">首页</span>
        <span>/</span>
        <span class="breadcrumb-item">{{ breadTitle }}</span>
        <span v-if="$route.query.type == 'awards'">
          <span>/</span>
          <span class="breadcrumb-item">{{ awardsTypeLabel }}</span>
        </span>
      </div>
      <!-- 文章列表 带封面 -->
      <div class="article-list" v-if="$route.query.type == 'news'">
        <div
          class="article-item"
          v-for="(article, index) in articleList"
          :key="index"
        >
          <div class="article-content">
            <div class="article-left" v-if="article.coverImg">
              <img :src="`${baseUrl}${article.coverImg}`" alt="" />
            </div>
            <div class="article-right">
              <div class="article-top">
                <div class="article-title">
                  <router-link :to="'/articleDetail?id=' + article.id">{{
                    article.title
                  }}</router-link>
                </div>
                <div class="article-date">{{ article.publishTime }}</div>
              </div>

              <div
                v-if="article.content"
                class="article-summary1"
                v-html="article.content"
              ></div>
              <div
                v-else-if="article.attachmentUrl"
                class="article-attachments"
              >
                <ul>
                  <li
                    v-for="(attachment, index2) in getAttachment(
                      article.attachmentUrl
                    )"
                    :key="index2"
                  >
                    <i class="el-icon-document"></i>
                    <el-button
                      size="mini"
                      type="text"
                      @click="handleAttachment(attachment)"
                      >{{ attachment.split("/").pop() }}
                    </el-button>
                  </li>
                </ul>
              </div>
              <!-- <div v-else class="no-content">暂无内容和附件</div> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 奖项列表 -->
      <div class="article-list" v-if="$route.query.type == 'awards'">
        <div
          class="article-item"
          v-for="(article, index) in articleList"
          :key="index"
        >
          <div class="article-content">
            <div class="article-left" v-if="article.awardsUrl">
              <img
                :src="`${baseUrl}${getFirstImageUrl(article.awardsUrl)}`"
                alt=""
              />
            </div>
            <div class="article-right">
              <div>
                <div class="article-title">
                  <router-link
                    :to="`/articleDetail?type=awards&id=${article.id}`"
                    >{{ article.awardsName }}</router-link
                  >
                </div>
                <div class="article-awardInfo">
                  <div class="article-date">
                    获奖项目：{{ article.awardWinningProjects }}
                  </div>
                  <div class="article-date">
                    获奖时间：{{ article.awardsTime }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="article-list" v-else>
        <div
          class="article-item"
          v-for="(article, index) in articleList"
          :key="index"
        >
          <div class="article-top">
            <div class="article-title">
              <router-link :to="'/articleDetail?id=' + article.id">{{
                article.title
              }}</router-link>
            </div>
            <div class="article-date">{{ article.publishTime }}</div>
          </div>
          <div
            class="article-summary"
            v-if="article.content"
            v-html="article.content"
          ></div>
          <div v-else-if="article.attachmentUrl" class="article-attachments">
            <ul>
              <li
                v-for="(attachment, index2) in getAttachment(
                  article.attachmentUrl
                )"
                :key="index2"
              >
                <i class="el-icon-document"></i>
                <el-button
                  size="mini"
                  type="text"
                  @click="handleAttachment(attachment)"
                  >{{ attachment.split("/").pop() }}
                </el-button>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <el-pagination
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import swiper01 from "@/assets/images/menhu/swiper01.jpg";
import swiper02 from "@/assets/images/menhu/swiper02.jpg";
import { listZjNewsInfoPortal } from "@/api/inspection/zjNewsInfo";
import {
  getZjAwardsList,
  getZjAwardsType,
} from "@/api/inspection/zjAwardsInfo";
import NavBar from "@/views/components/navBar.vue";

export default {
  components: {
    NavBar,
  },
  data() {
    return {
      breadTitle: "",
      swiperList: [
        { src: swiper01, title: "中江集团" },
        { src: swiper02, title: "中江集团" },
      ],
      // 文章列表数据
      articleList: [],
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      isAdmin: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      zjAwardsTypeList: [],
    };
  },
  computed: {
    ...mapGetters(["avatar"]),
    userName() {
      return this.$store.state.user.name || "管理员";
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
    awardsTypeLabel() {
      if (this.$route.query.type !== "awards") return "";
      const type = this.$route.query.awardsType;
      return (
        this.zjAwardsTypeList.find((item) => item.dictValue == type)
          ?.dictLabel || ""
      );
    },
  },
  async created() {
    // 组件创建时获取文章列表
    if (this.$route.query.type == "awards") {
      // 获取奖项类别的接口
      // alert(this.$route.query.awardsType);
      await this.fetchAwardsList();
      await this.getZjAwardsTypeList();
    } else {
      await this.fetchArticleList();
    }
  },
  mounted() {
    const permissions = this.$store.getters.permissions;
    const roles = this.$store.getters.roles;
    this.isAdmin =
      (Array.isArray(permissions) && permissions.includes("system:pcadmin")) ||
      roles.includes("admin");
    this.breadTitle =
      this.getNewTypeNameByString(this.$route.query.type) || "文章列表";
  },
  methods: {
    getZjAwardsTypeList(value) {
      getZjAwardsType({
        dictType: "zj_awards_type",
      }).then((res) => {
        // console.log(res, "res");
        if (res.code == 200) {
          this.zjAwardsTypeList = res.rows;
        }
      });
    },
    async handleAttachment(value) {
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    // 处理附件
    getAttachment(url) {
      // console.log(url, "url");
      let newUrl = url ? url.split(",") : [];
      return newUrl;
    },
    // 返回附件中第一个为列表图片的url
    getFirstImageUrl(url) {
      const urls = this.getAttachment(url);
      for (const item of urls) {
        if (
          item.toLowerCase().endsWith(".jpg") ||
          item.toLowerCase().endsWith(".png") ||
          item.toLowerCase().endsWith(".jpeg")
        ) {
          return item;
        }
      }
      return "";
    },
    async handleSizeChange(val) {
      this.pageSize = val;
      if (this.$route.query.type == "awards") {
        await this.fetchAwardsList();
      } else {
        await this.fetchArticleList();
      }
    },
    async handleCurrentChange(val) {
      this.currentPage = val;
      if (this.$route.query.type == "awards") {
        await this.fetchAwardsList();
      } else {
        await this.fetchArticleList();
      }
    },
    // <!-- 1-公司要闻2-通知公告3-法律法规4-行业动态 5-EHS动态 6-安全视频 -->
    getNewTypeNameByString(type) {
      if (!type) {
        return "";
      }
      console.log("type", type);
      const typeMap = {
        news: "公司要闻",
        notice: "通知公告",
        law: "法律法规",
        industry: "行业动态",
        ehs: "EHS动态",
        video: "安全视频",
        awards: "创优奖项",
      };
      return typeMap[type] || "";
    },
    getNewTypeByString(type) {
      const typeMap = {
        news: "1",
        notice: "2",
        law: "3",
        industry: "4",
        ehs: "5",
        video: "6",
      };
      return typeMap[type] || "";
    },
    // 已有方法
    switchMode() {
      window.open("/index", "_blank");
    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/qsms/login";
          });
        })
        .catch(() => {});
    },

    // 新增方法
    async fetchArticleList() {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        newType: this.getNewTypeByString(this.$route.query.type),
      };
      try {
        const res = await listZjNewsInfoPortal(params);
        this.articleList = res.rows;
        this.total = res.total;
      } catch (error) {
        console.error("获取文章列表失败:", error);
      }
    },
    // 奖项列表
    async fetchAwardsList() {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        awardsType: this.$route.query.awardsType,
      };
      try {
        const res = await getZjAwardsList(params);
        this.articleList = res.rows;
        this.total = res.total;
      } catch (error) {
        console.error("获取文章列表失败:", error);
      }
    },
    goHome() {
      this.$router.push("/menhu");
    },
    goToArticleDetail(id) {
      this.$router.push({ name: "ArticleDetail", params: { id } });
    },
  },
};
</script>

<style lang="scss" scoped>
.articleList-container {
  min-height: 100%;
  background-color: #f4f4f5;

  .top-navbar {
    height: 60px;

    z-index: 1000;
    position: relative;

    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(35, 69, 103, 0.15);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #ebebeb;

    .nav-container {
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 20px;
      width: 100%;
      box-sizing: border-box;
      margin: 0 auto;
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-right: 100px;

      .logo-img {
        height: 85px;
        width: auto;
      }

      .system-title {
        width: 270px;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.9);
      }
    }

    .nav-buttons {
      flex-grow: 1;
      display: flex;
      justify-content: flex-end;
      gap: 100px;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 6px;
      padding: 4px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .nav-btn {
        width: 160px;
        padding: 8px 24px;
        border: none;
        background: transparent;
        font-weight: normal;
        font-size: 16px;
        color: #33373b;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;
        font-weight: 500;
        position: relative;
        overflow: hidden;

        &.active {
          background: #0974ff;
          border-radius: 38px 38px 38px 38px;
          color: #ffffff;
        }
      }
    }

    .user-section {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-left: auto;

      .right-menu-item {
        color: #333;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }
      }

      .avatar-container {
        .avatar-wrapper {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
          padding: 6px 12px;
          border-radius: 20px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 0, 0, 0.1);
          }

          .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;

            &:hover {
              border-color: #333;
            }
          }

          .user-name {
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    // 响应式处理
    @media (max-width: 768px) {
      .nav-container {
        padding: 0 16px;
      }

      .logo-section .system-title {
        font-size: 16px;
      }

      .nav-buttons .nav-btn {
        padding: 6px 16px;
        font-size: 12px;
      }
    }
  }
  .swiper {
    width: 100%;
    height: 45vh;
    .swiper-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .el-carousel--horizontal {
      height: 45vh;
    }
    .el-carousel__item {
      color: #475669;
      font-size: 14px;
      opacity: 0.75;
      line-height: 150px;
      margin: 0;
    }
  }
  .article-list-container {
    font-family: "Microsoft YaHei", sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    margin-top: 10px;
    background-color: #fff;
    // height: calc(100vh - 60px);
  }

  .breadcrumb {
    color: #666;
    font-size: 14px;
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: #ebf5ff;
  }
  .goBack {
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: #0974ff;
    }
  }

  .breadcrumb-item {
    cursor: pointer;
    transition: color 0.3s ease;
    margin: 10px;

    &:hover {
      color: #0974ff;
    }
  }
  .article-list {
    border-top: 1px solid #eee;
    margin-bottom: 20px;
    padding: 20px;
  }

  .article-item {
    padding: 15px 0;
    border-bottom: 1px dashed #ddd;

    .article-content {
      display: flex;

      .article-left {
        img {
          // width: 200px;
          max-height: 80px;
          object-fit: contain;
          margin-right: 20px;
        }
      }
      .article-right {
        flex-grow: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .article-top {
      display: flex;
      justify-content: space-between;
    }
  }

  .article-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .article-title a {
    color: #333;
    text-decoration: none;
  }

  .article-title a:hover {
    color: #1e50a2;
    text-decoration: underline;
  }
  .article-awardInfo {
    display: flex;
    gap: 10px;
    margin-top: 10px;
  }
  .article-date {
    color: #999;
    font-size: 14px;
    margin-bottom: 8px;
  }
  .article-summary1 {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    text-align: justify;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .article-summary {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    text-align: justify;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .article-attachments {
    margin-top: 10px;
    h4 {
      font-size: 14px;
      color: #333;
      margin-bottom: 5px;
    }
    ul {
      list-style-type: none;
      padding-left: 0;
      li {
        margin-bottom: 5px;
        i {
          color: #0974ff;
          text-decoration: none;
          &:hover {
            text-decoration: underline;
          }
        }
        a {
          color: #0974ff;
          text-decoration: none;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .no-content {
      color: #999;
      font-size: 14px;
      margin-top: 10px;
    }
  }
}
.el-pagination {
  padding-bottom: 20px;
}
</style>
