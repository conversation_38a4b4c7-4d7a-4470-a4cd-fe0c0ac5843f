<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

export default {
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        },
        data: {
            type: Object,
            default: () => ({})
        },
        showTooltip: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            chart: null
        }
    },
    watch: {
        data: {
            handler(newVal, oldVal) {
                if (this.chart) {
                    // 更新图表
                    this.setOption()
                } else {
                    // 初始化图表
                    this.$nextTick(() => {
                        this.initChart()
                    })
                }
            },
            immediate: true
        }
    },
    mounted() {

    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
        window.removeEventListener('resize', this.chartResize)
    },
    methods: {
        chartResize() {
            this.chart.resize()
        },
        initChart() {
            const that = this
            that.chart = echarts.init(that.$el)
            window.addEventListener('resize', that.chartResize)
            that.chart.on('click', (params) => {
                const obj = {
                    seriesName: params.seriesName,
                    ...params.data
                }
                that.$emit('barClick', obj)
            })
            that.setOption()
        },
        setOption() {
            this.chart.setOption({
                tooltip: this.showTooltip ? {
                    trigger: 'item',
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#e5e7eb',
                    borderWidth: 1,
                    borderRadius: 8,
                    textStyle: {
                        color: '#374151',
                        fontSize: 12,
                        lineHeight: 18
                    },
                    padding: [12, 16],
                    extraCssText: 'max-width: 600px; white-space: normal; word-wrap: break-word; word-break: break-all; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);',
                    formatter: function (params) {
                        // 获取完整的公司名称，如果数据中包含fullName则使用，否则使用X轴标签
                        let fullCompanyName = params.name
                        if (Array.isArray(params) && params.length > 0 && params[0].data && params[0].data.fullName) {
                            fullCompanyName = params[0].data.fullName
                        } else if (params.data && params.data.fullName) {
                            fullCompanyName = params.data.fullName
                        }

                        let html = '<div style="font-weight: 600; margin-bottom: 8px; line-height: 1.4;">' + fullCompanyName + '</div>'
                        if (Array.isArray(params)) {
                            params.forEach(item => {
                                const value = typeof item.value === 'object' ? item.value.value : item.value
                                html += '<div style="margin-bottom: 4px; line-height: 1.4;">'
                                html += '<span style="display: inline-block; width: 10px; height: 10px; background-color: ' + item.color + '; border-radius: 2px; margin-right: 8px;"></span>'
                                html += '<span style="margin-right: 16px;">' + item.seriesName + '</span>'
                                html += '<span style="font-weight: 600; color: #1f2937;">' + value + '万元</span>'
                                html += '</div>'
                            })
                        } else {
                            const value = typeof params.value === 'object' ? params.value.value : params.value
                            html += '<div style="line-height: 1.4;">'
                            html += '<span style="display: inline-block; width: 10px; height: 10px; background-color: ' + params.color + '; border-radius: 2px; margin-right: 8px;"></span>'
                            html += '<span style="margin-right: 16px;">' + params.seriesName + '</span>'
                            html += '<span style="font-weight: 600; color: #1f2937;">' + value + '万元</span>'
                            html += '</div>'
                        }
                        return html
                    }
                } : { show: false },
                legend: this.data.legend ? {
                    ...this.data.legend,
                    orient: 'horizontal',
                    top: undefined,
                    bottom: 10,
                    left: 'center',
                    itemGap: 20,
                    itemWidth: 14,
                    itemHeight: 8,
                    textStyle: {
                        color: '#666666',
                        fontSize: 12
                    }
                } : undefined,
                grid: this.data.grid,
                xAxis: {
                    ...this.data.xAxis,
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB'
                        }
                    },
                    axisLabel: {
                        ...this.data.xAxis.axisLabel,
                        show: true,
                        interval: 0,
                        textStyle: {
                            color: '#666666'
                        }
                    }
                },
                yAxis: {
                    ...this.data.yAxis,
                    // 优先使用传入的interval配置，否则使用splitNumber进行等分
                    splitNumber: this.data.yAxis.interval ? undefined : 4,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#EBEBEB'
                        }
                    },
                    axisLine: {
                        show: false
                    },
                    axisLabel: {
                        show: true,
                        textStyle: {
                            color: '#666666'
                        }
                    }
                },
                series: this.data.series
            })
        },
        downloadImgSVG() {
            var img = new Image()
            // pieMyChart1 要导出的图表
            img.src = this.chart.getDataURL({
                type: 'svg',
                pixelRatio: 1, // 放大2倍
                backgroundColor: '#fff'
            })
            img.onload = function () {
                var canvas = document.createElement('canvas')
                canvas.width = img.width
                canvas.height = img.height
                var ctx = canvas.getContext('2d')
                ctx.drawImage(img, 0, 0)
                var dataURL = canvas.toDataURL('image/svg')

                var a = document.createElement('a')
                var event = new MouseEvent('click')
                a.download = '图片.svg' || '下载图片名称'
                // 将生成的URL设置为a.href属性
                a.href = dataURL
                // 触发a的单击事件
                a.dispatchEvent(event)
                a.remove()
            }
        },
        downloadImgPNG() {
            var img = new Image()
            // pieMyChart1 要导出的图表
            img.src = this.chart.getDataURL({
                type: 'png',
                pixelRatio: 1, // 放大2倍
                backgroundColor: '#fff'
            })
            img.onload = function () {
                var canvas = document.createElement('canvas')
                canvas.width = img.width
                canvas.height = img.height
                var ctx = canvas.getContext('2d')
                ctx.drawImage(img, 0, 0)
                var dataURL = canvas.toDataURL('image/png')

                var a = document.createElement('a')
                var event = new MouseEvent('click')
                a.download = '图片.png' || '下载图片名称'
                // 将生成的URL设置为a.href属性
                a.href = dataURL
                // 触发a的单击事件
                a.dispatchEvent(event)
                a.remove()
            }
        }
    }
}
</script>
