<template>
  <div class="top-navbar">
    <div class="nav-container">
      <!-- 左侧系统标题 -->
      <div class="logo-section">
        <img src="@/assets/images/menhu/logo.png" alt="logo" class="logo-img">
        <span class="system-title">
          <img src="@/assets/images/menhu/navTitle.png" class="title-img" alt=""></span>
      </div>

      <!-- 中间模式切换按钮 -->
      <div class="nav-buttons">
        <button :class="['nav-btn', { active: currentMode === 'portal' }]" @click="switchMode('portal')">
          安全综合门户
        </button>
        <!-- <button
          v-if="currentMode === 'portal'"
          :class="['nav-btn', { active: currentMode === 'admin' }]"
          @click="switchMode('admin')"
        >
          后台管理
        </button> -->
      </div>

      <!-- 右侧用户信息区域 -->
      <div class="user-section">
        <!-- 部门切换 -->
        <!-- <el-select v-model="department" placeholder="请选择部门">
          <el-option
            v-for="item in departmentList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select> -->
        <!--
        <template v-if="device !== 'mobile'">
          <screenfull class="right-menu-item hover-effect" />
        </template> -->
        <div class="nav-buttons">
          <span class="nav-btn" style="width: 100px;" @click="goBack">一部一屏</span>
        </div>
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <!-- <img :src="avatar" class="user-avatar" /> -->
            <span class="user-name">{{ userName }}</span>
          </div>
          <el-dropdown-menu slot="dropdown">
            <!-- <el-dropdown-item @click.native="setting = true">
              <span>布局设置</span>
            </el-dropdown-item> -->
            <el-dropdown-item divided @click.native="logout">
              <span>退出登录</span>
            </el-dropdown-item>
            <el-dropdown-item divided @click.native="toPersonInfo">
              <span>个人信息</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  data() {
    return {
      currentMode: 'admin', // 当前模式：portal（安全综合门户）或 admin（后台管理）
      department: '中江集团总部',
      departmentList: [{ value: '1', label: '中江集团总部' }],
      isFirstVisit: localStorage.getItem('isFirstVisit') !== 'false' // 从本地存储获取首次访问标记
    }
  },
  watch: {
    // $route(to, from) {
    //   // 监听路由变化
    //   if (to.path === "/menhu") {
    //     this.currentMode = "portal";
    //     this.$store.dispatch("app/toggleSideBarHide", true);
    //   } else {
    //     this.currentMode = "admin";
    //     this.$store.dispatch("app/toggleSideBarHide", false);
    //   }
    // },
  },
  mounted() {
    // 首次访问时设置标记
    // if (this.isFirstVisit) {
    //   if (this.$route.path === "/" || this.$route.path === "/index") {
    //     this.$router.push("/menhu").catch((e) => {
    //       console.log("首次跳转失败:", e);
    //     });
    //     this.$store.dispatch("app/toggleSideBarHide", true);
    //     localStorage.setItem("isFirstVisit", "false"); // 标记首次访问已完成
    //   }
    // }
    // // 根据当前路径设置模式，但不跳转
    // if (this.$route.path === "/menhu") {
    //   this.currentMode = "portal";
    //   this.$store.dispatch("app/toggleSideBarHide", true);
    // } else {
    //   this.currentMode = "admin";
    //   this.$store.dispatch("app/toggleSideBarHide", false);
    // }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device']),
    userName() {
      return this.$store.state.user.name || '管理员'
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  methods: {
    goBack() {
      window.location.href = 'http://192.168.13.68:8099/#/auto-detail'
    },

    // 切换模式
    switchMode(mode) {
      this.currentMode = mode

      // 根据模式控制侧边栏显示/隐藏
      if (mode === 'portal') {
        // 安全综合门户模式：隐藏侧边栏
        this.$store.dispatch('app/toggleSideBarHide', true)
      } else if (mode === 'admin') {
        // 后台管理模式：显示侧边栏
        this.$store.dispatch('app/toggleSideBarHide', false)
        // 如果侧边栏是折叠状态，则展开它
        if (!this.sidebar.opened) {
          this.$store.dispatch('app/toggleSideBar')
        }
      }
      // // 从 menhu 页面切换到 admin 模式时，在新页面打开 /index
      // if (this.$route.path === "/menhu") {
      //   window.open("/index", "_blank");
      //   return;
      // }

      // 根据模式跳转到不同页面
      if (mode === 'portal') {
        // 安全综合门户模式：跳转到 menhu/index
        this.$router.push('/menhu').catch((e) => {
          console.log('路由跳转失败:', e)
        })
      } else if (mode === 'admin') {
        // 后台管理模式：跳转到首页
        this.$router.push('/').catch((e) => {
          console.log('路由跳转失败:', e)
        })
      }

      // 向父组件发送模式变化事件
      this.$emit('mode-change', mode)
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/qsms/login'
          })
        })
        .catch(() => { })
    },
    toPersonInfo() {
      this.$router.push({ path: '/user/profile' })
    }
    // /user/profile
  }
}
</script>

<style lang="scss" scoped>
.top-navbar {
  height: 60px;
  // background: linear-gradient(135deg, #4A90E2 0%, #5BA3F5 100%);
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  position: relative;

  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(35, 69, 103, 0.15);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #ebebeb;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  //   opacity: 0.9;
  //   z-index: -1;
  // }

  .nav-container {
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 20px;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-right: 100px;

    .logo-img {
      height: 85px;
      width: auto;
    }

    .system-title {
      // width: 270px;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.9);
    }

    .title-img {
      height: 25px;
      margin-top: 8px;
      margin-left: -35px;
    }
  }

  .nav-buttons {
    flex-grow: 1;
    display: flex;
    justify-content: flex-end;
    gap: 100px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    padding: 4px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .nav-btn {
      width: 120px;
      padding: 8px 10px;
      border: none;
      background: transparent;
      font-weight: normal;
      font-size: 16px;
      color: #33373b;
      cursor: pointer;
      border-radius: 20px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      font-family: "MiSans-Regular";
      // &::before {
      //   content: '';
      //   position: absolute;
      //   top: 0;
      //   left: -100%;
      //   width: 100%;
      //   height: 100%;
      //   background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      //   transition: left 0.5s;
      // }

      &.active {
        background: #0974ff;
        border-radius: 38px 38px 38px 38px;
        color: #ffffff;
      }

      &:hover {
        background: rgba(0, 0, 0, 0.1);
        // font-family: "MiSans-Semibold";
      }
    }
  }

  .user-section {
    display: flex;
    align-items: center;
    margin-left: auto;

    .right-menu-item {
      color: #333;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }

    .avatar-container {
      .avatar-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 20px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 2px solid rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;

          &:hover {
            border-color: #333;
          }
        }

        .user-name {
          color: #333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }

    .back-btn {
      display: flex;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 25px;
      transition: all 0.3s ease;
      background: rgba(64, 158, 255, 0.1);
      color: #409eff;

      .back-icon {
        font-size: 14px;
      }
    }
  }

  // 响应式处理
  @media (max-width: 768px) {
    .nav-container {
      padding: 0 16px;
    }

    .logo-section .system-title {
      font-size: 16px;
    }

    .nav-buttons .nav-btn {
      padding: 6px 16px;
      font-size: 12px;
    }

    .logo-img {
      height: 64px;
    }

    .title-img {
      height: 18px;
    }
  }
}
</style>
