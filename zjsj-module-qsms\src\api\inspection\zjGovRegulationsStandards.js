import request from '@/utils/request'

// 查询政府法律法规与标准列表
export function listZjGovRegulationsStandards(query) {
  return request({
    url: '/inspection/zjGovRegulationsStandards/list',
    method: 'get',
    params: query
  })
}

// 查询政府法律法规与标准详细
export function getZjGovRegulationsStandards(id) {
  return request({
    url: '/inspection/zjGovRegulationsStandards/' + id,
    method: 'get'
  })
}

// 新增政府法律法规与标准
export function addZjGovRegulationsStandards(data) {
  return request({
    url: '/inspection/zjGovRegulationsStandards',
    method: 'post',
    data: data
  })
}

// 修改政府法律法规与标准
export function updateZjGovRegulationsStandards(data) {
  return request({
    url: '/inspection/zjGovRegulationsStandards',
    method: 'put',
    data: data
  })
}

// 删除政府法律法规与标准
export function delZjGovRegulationsStandards(id) {
  return request({
    url: '/inspection/zjGovRegulationsStandards/' + id,
    method: 'delete'
  })
}
