<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      class="search"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="承包商类型:" prop="contractorType">
        <el-select
          v-model="queryParams.contractorType"
          placeholder="请选择"
          clearable
          style="width: 250px"
        >
          <el-option
            v-for="(dict, index) in dict.type.sys_contractor_type"
            :key="`contractor_type_${dict.value}_${index}`"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="承包商名称:" prop="contractorName">
        <el-input
          style="width: 250px"
          v-model="queryParams.contractorName"
          placeholder="请输入内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <div style="float: right" class="right-group">
        <!-- <div class="expend" @click="toggleExpand">
          <span class="text-[14px] text-[#1890ff]">展开</span>
          <i class="el-icon-arrow-down" color="#1890ff" style="margin: 0 10px;" v-if="!isExpand"></i>
          <i class="el-icon-arrow-up" color="#1890ff" style="margin: 0 10px;" v-if="isExpand"></i>
        </div> -->
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >查询</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </div>
    </el-form>

    <div class="info">
      <p>承包商信息</p>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['contractor:zjContractorInfo:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['contractor:zjContractorInfo:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['contractor:zjContractorInfo:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
    </div>

    <el-table
      v-loading="loading"
      :data="zjContractorInfoList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      height="calc(100vh - 250px)"
    >
      <el-table-column type="selection" width="50" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column
        label="承包商名称"
        width="120"
        align="center"
        prop="contractorName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="统一社会信用代码"
        width="150"
        align="center"
        prop="creditCode"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="承包商类型" align="center" prop="contractorType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_contractor_type"
            :value="scope.row.contractorType"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="contractorCategory"
        label="承包商类别"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.contractorCategory === 1">准承包商</span>
          <span v-else-if="scope.row.contractorCategory === 2">合格承包商</span>
          <span v-else-if="scope.row.contractorCategory === 3"
            >不合格承包商</span
          >
          <span v-else>未知状态</span>
        </template>
      </el-table-column>
      <el-table-column
        label="管理人"
        align="center"
        prop="administratorName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="负责人"
        align="center"
        prop="contractorManager"
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column label="管理人id" align="center" prop="administratorId" /> -->
      <!-- <el-table-column label="服务开始时间" align="center" prop="serviceStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.serviceStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="服务结束时间" align="center" prop="serviceEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.serviceEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->

      <!-- <el-table-column label="承包商类别 1-准承包商 2-合格承包商 3-不合格承包商" align="center" prop="contractorCategory" /> -->

      <!-- <el-table-column label="公司简介" align="left" prop="companyProfile" /> -->
      <!-- <el-table-column label="负责人联系电话" align="left" prop="chargeContactNumber" /> -->
      <!-- <el-table-column label="单位性质" align="left" prop="unitNature" /> -->
      <!-- <el-table-column label="法定代表人" align="left" prop="legalRepresentative" /> -->
      <!-- <el-table-column label="公司地址" align="left" prop="companyAddress" /> -->
      <!-- <el-table-column label="法定代表人联系电话" align="left" prop="legalRepresentativePhone" /> -->
      <!-- <el-table-column label="公司邮箱" align="left" prop="companyEmail" /> -->
      <el-table-column
        label="是否准入"
        align="center"
        prop="accessStatus"
        width="100"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.accessStatus == 0">否</span>
          <span v-else-if="scope.row.accessStatus == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column
        label="更新者"
        align="center"
        prop="updateBy"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="更新时间"
        align="center"
        prop="updateTime"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="是否黑名单"
        align="center"
        prop="blacklistStatus"
        width="120"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.blacklistStatus == 1">否</span>
          <span v-else-if="scope.row.blacklistStatus == 2">是</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="黑名单原因" align="left" prop="blacklistReason" /> -->
      <el-table-column
        label="操作"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['contractor:zjContractorInfo:edit']"
            >查看详情</el-button
          >
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:zjContractorInfo:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:zjContractorInfo:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:zjContractorInfo:remove']"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 分页组件 - 固定在底部 -->
    <div class="pagination-wrapper">
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改承包商信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <!-- 第一行：承包商名称 | 管理人 -->
          <el-col :span="12">
            <el-form-item label="承包商名称" prop="contractorName">
              <el-input
                v-model="form.contractorName"
                placeholder="承包商名称不能为空"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="管理人" prop="administratorId">
              <el-select
                v-model="form.administratorId"
                placeholder="管理人不能为空"
                style="width: 100%"
                @change="handleManagerChange"
              >
                <el-option
                  v-for="(item, index) in managerOptions"
                  :key="`form_manager_${item.value}_${index}`"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第二行：统一社会信用代码 | 服务起止时间 -->
          <el-col :span="12">
            <el-form-item label="统一社会信用代码" prop="creditCode">
              <el-input
                v-model="form.creditCode"
                placeholder="统一社会信用代码不能为空"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务起止时间" prop="serviceTimeRange">
              <el-date-picker
                v-model="form.serviceTimeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                @change="handleServiceTimeChange"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第三行：承包商负责人 | 承包商类别 -->
          <el-col :span="12">
            <el-form-item label="承包商负责人" prop="contractorManager">
              <el-input
                v-model="form.contractorManager"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="承包商类别" prop="contractorCategory">
              <el-select
                v-model="form.contractorCategory"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.contractor_category"
                  :key="`form_contractor_category_${dict.value}_${index}`"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第四行：承包商类型 | 公司简介（经营范围） -->
          <el-col :span="12">
            <el-form-item label="承包商类型" prop="contractorType">
              <el-select
                v-model="form.contractorType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in dict.type.sys_contractor_type"
                  :key="`form_contractor_type_${dict.value}_${index}`"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司简介" prop="companyProfile">
              <el-input
                v-model="form.companyProfile"
                type="textarea"
                :rows="4"
                placeholder="请输入内容"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第五行：负责人联系电话 | 单位性质 -->
          <el-col :span="12">
            <el-form-item label="负责人联系电话" prop="chargeContactNumber">
              <el-input
                v-model="form.chargeContactNumber"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位性质" prop="unitNature">
              <el-input v-model="form.unitNature" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第六行：法定代表人 | 公司地址 -->
          <el-col :span="12">
            <el-form-item label="法定代表人" prop="legalRepresentative">
              <el-input
                v-model="form.legalRepresentative"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司地址" prop="companyAddress">
              <el-input
                v-model="form.companyAddress"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 第七行：法定代表人联系电话 | 公司邮箱 -->
          <el-col :span="12">
            <el-form-item
              label="法定代表人联系电话"
              prop="legalRepresentativePhone"
            >
              <el-input
                v-model="form.legalRepresentativePhone"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司邮箱" prop="companyEmail">
              <el-input v-model="form.companyEmail" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjContractorInfo,
  getZjContractorInfo,
  delZjContractorInfo,
  addZjContractorInfo,
  updateZjContractorInfo,
  queryUserTree,
} from "@/api/contractor/zjContractorInfo";
import { getUserInfo } from "@/api/contractor/zjContractorBlaklist";

export default {
  name: "ZjContractorInfo",
  dicts: ["contractor_category", "sys_contractor_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 承包商信息表格数据
      zjContractorInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorName: null,
        creditCode: null,
        serviceStartTime: null,
        serviceEndTime: null,
        contractorManager: null,
        contractorType: null,
        companyProfile: null,
        chargeContactNumber: null,
        unitNature: null,
        legalRepresentative: null,
        companyAddress: null,
        legalRepresentativePhone: null,
        companyEmail: null,
        blacklistStatus: null,
        accessStatus: null,
        blacklistReason: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        contractorName: [
          { required: true, message: "承包商名称不能为空", trigger: "blur" },
        ],
        administratorId: [
          { required: true, message: "管理人不能为空", trigger: "change" },
        ],
        creditCode: [
          {
            required: true,
            message: "统一社会信用代码不能为空",
            trigger: "blur",
          },
        ],
        serviceTimeRange: [
          {
            required: true,
            message: "服务起止时间不能为空",
            trigger: "change",
          },
        ],
        contractorManager: [
          { required: true, message: "承包商负责人不能为空", trigger: "blur" },
        ],
      },
      isExpand: false,
      // 管理人选项
      managerOptions: [],
    };
  },
  created() {
    this.getList();
    this.loadManagerOptions();
  },
  methods: {
    // 展开取消
    toggleExpand() {
      this.isExpand = !this.isExpand;
    },
    /** 加载管理人选项 */
    loadManagerOptions() {
      // 使用getUserInfo接口获取管理人员数据，传递type=1表示获取管理人员
      getUserInfo(1)
        .then((response) => {
          // 根据接口返回的数据结构处理
          const data = response.data || response.rows || response || [];
          // 使用 Map 去重并确保键的唯一性
          const uniqueManagers = new Map();
          data.forEach((manager, index) => {
            const value = manager.userId || manager.id || `temp_${index}`;
            const label =
              manager.nickName ||
              manager.name ||
              manager.userName ||
              `未命名_${index}`;
            const nickName =
              manager.nickName ||
              manager.name ||
              manager.userName ||
              `未命名_${index}`;
            if (!uniqueManagers.has(value)) {
              uniqueManagers.set(value, {
                value,
                label,
                nickName,
              });
            }
          });
          this.managerOptions = Array.from(uniqueManagers.values());
        })
        .catch((error) => {
          console.error("获取管理人选项失败:", error);
          this.$modal.msgError("获取管理人选项失败");
          // 失败时使用原有接口作为备选
          this.loadManagerOptionsFallback();
        });
    },
    /** 备用方法：使用原有接口获取管理人选项 */
    loadManagerOptionsFallback() {
      queryUserTree()
        .then((res) => {
          console.log(res);
          if (res.code == 200) {
            // 从树形结构中提取管理人员数据
            const flattenData = this.flattenUserTree(res.data);
            // 使用 Map 去重并确保键的唯一性
            const uniqueUsers = new Map();
            flattenData.forEach((user, index) => {
              const value = user.id || `tree_temp_${index}`;
              const label =
                user.label || user.name || user.userName || `未命名_${index}`;
              const nickName =
                user.label || user.name || user.userName || `未命名_${index}`;
              if (!uniqueUsers.has(value)) {
                uniqueUsers.set(value, {
                  value,
                  label,
                  nickName,
                });
              }
            });
            this.managerOptions = Array.from(uniqueUsers.values());
          } else {
            this.managerOptions = [];
          }
        })
        .catch(() => {
          this.managerOptions = [];
        });
    },
    /** 扁平化用户树 */
    flattenUserTree(data) {
      let result = [];
      data.forEach((item) => {
        result.push(item);
        if (item.children && item.children.length > 0) {
          result = result.concat(this.flattenUserTree(item.children));
        }
      });
      return result;
    },
    //管理员
    /** 查询承包商信息列表 */
    getList() {
      this.loading = true;
      listZjContractorInfo(this.queryParams).then((response) => {
        this.zjContractorInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        contractorName: null,
        administratorId: null,
        administratorName: null,
        creditCode: null,
        serviceStartTime: null,
        serviceEndTime: null,
        serviceTimeRange: null,
        contractorManager: null,
        contractorCategory: null,
        contractorType: null,
        companyProfile: null,
        chargeContactNumber: null,
        unitNature: null,
        legalRepresentative: null,
        companyAddress: null,
        legalRepresentativePhone: null,
        companyEmail: null,
        blacklistStatus: null,
        accessStatus: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        blacklistReason: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 处理服务时间范围变化 */
    handleServiceTimeChange(value) {
      if (value && value.length === 2) {
        this.form.serviceStartTime = value[0];
        this.form.serviceEndTime = value[1];
      } else {
        this.form.serviceStartTime = null;
        this.form.serviceEndTime = null;
      }
    },
    /** 处理管理人变化 */
    handleManagerChange(value) {
      if (value) {
        // 根据选择的管理人ID查找对应的nickName
        const selectedManager = this.managerOptions.find(
          (manager) => manager.value == value
        );
        if (selectedManager) {
          this.form.administratorName = selectedManager.nickName;
        }
      } else {
        this.form.administratorName = null;
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加承包商信息";
    },
    /** 查看详情操作 */
    handleViewDetail(row) {
      console.log(`/contractor/detail/${row.id}`);
      this.$router.push({
        path: `/contractor/detail/${row.id}`,
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjContractorInfo(id).then((response) => {
        this.form = response.data;
        // 设置服务时间范围
        if (this.form.serviceStartTime && this.form.serviceEndTime) {
          this.form.serviceTimeRange = [
            this.form.serviceStartTime,
            this.form.serviceEndTime,
          ];
        } else {
          this.form.serviceTimeRange = null;
        }
        // 设置管理人名称，如果已有管理人ID但没有名称，则从选项中获取
        if (this.form.administratorId && !this.form.administratorName) {
          const selectedManager = this.managerOptions.find(
            (manager) => manager.value == this.form.administratorId
          );
          if (selectedManager) {
            this.form.administratorName = selectedManager.nickName;
          }
        }
        this.open = true;
        this.title = "修改承包商信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjContractorInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjContractorInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除承包商信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjContractorInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "contractor/zjContractorInfo/export",
        {
          ...this.queryParams,
        },
        `zjContractorInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style class="scss" scoped>
/* 主容器 - 使用flex布局确保分页固定在底部 */
.app-container {
  width: 100%;
  height: calc(100vh - 84px);
  background-color: rgb(247, 248, 250);
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}

/* 搜索表单区域 */
.search {
  background-color: #fff;
  box-sizing: border-box;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
  margin-bottom: 10px;
}

.right-group {
  display: flex !important;
}

.expend {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #1890ff;
  cursor: pointer;
}

/* 工具栏区域 */
.info {
  margin-top: 0;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  box-sizing: border-box;
  padding: 10px 20px;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

/* 表格区域 - 占据剩余空间 */
.el-table {
  flex: 1;
  margin-bottom: 0;
}

/* 分页样式 - 固定在底部 */
.pagination-wrapper {
  flex-shrink: 0;
  text-align: center;
  padding: 16px 0;
  background: #fff;
  border-top: 1px solid #ebeef5;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  margin-top: 10px;
}

/* 分页组件响应式样式 */
.pagination-wrapper ::v-deep .el-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
  margin-right: 16px;
  order: 1;
}

.pagination-wrapper ::v-deep .el-pagination .btn-prev {
  order: 2;
}

.pagination-wrapper ::v-deep .el-pagination .el-pager {
  order: 3;
}

.pagination-wrapper ::v-deep .el-pagination .btn-next {
  order: 4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .app-container {
    padding: 15px;
  }

  .search {
    padding: 15px;
  }

  .info {
    padding: 8px 15px;
  }
}

/* 中等屏幕适配 (平板) */
@media (max-width: 1024px) and (min-width: 769px) {
  .pagination-wrapper {
    padding: 10px 0 15px;
    border-top: 1px solid #ebeef5;
    background: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
    font-size: 13px;
  }

  .pagination-wrapper ::v-deep .el-pagination .btn-prev,
  .pagination-wrapper ::v-deep .el-pagination .btn-next {
    padding: 0 10px;
    min-width: 36px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pager li {
    min-width: 36px;
    height: 36px;
    line-height: 36px;
    font-size: 13px;
  }
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .app-container {
    height: calc(100vh - 84px);
    padding: 10px;
  }

  .pagination-wrapper {
    margin-bottom: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    border-top: 2px solid #ebeef5;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }

  .pagination-wrapper ::v-deep .el-pagination {
    flex-direction: row;
    justify-content: center;
    gap: 4px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
    font-size: 12px;
    margin-right: 8px;
  }

  .pagination-wrapper ::v-deep .el-pagination .btn-prev,
  .pagination-wrapper ::v-deep .el-pagination .btn-next {
    padding: 0 8px;
    min-width: 32px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }

  /* 为小屏幕预留底部分页空间 */
  .el-table {
    margin-bottom: 60px;
  }
}
</style>
