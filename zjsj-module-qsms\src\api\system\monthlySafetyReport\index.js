import request from '@/utils/request'

// 查询安全月报管理（存储月度安全统计数据及报信息）列表
export function listReport(query) {
  return request({
    url: '/system/report/list',
    method: 'get',
    params: query
  })
}

// 查询安全月报管理（存储月度安全统计数据及报信息）详细
export function getReport(id) {
  return request({
    url: '/system/report/' + id,
    method: 'get'
  })
}

// 新增安全月报管理（存储月度安全统计数据及报信息）
export function addReport(data) {
  return request({
    url: '/system/report',
    method: 'post',
    data: data
  })
}

// 修改安全月报管理（存储月度安全统计数据及报信息）
export function updateReport(data) {
  return request({
    url: '/system/report',
    method: 'put',
    data: data
  })
}

// 删除安全月报管理（存储月度安全统计数据及报信息）
export function delReport(id) {
  return request({
    url: '/system/report/' + id,
    method: 'delete'
  })
}
