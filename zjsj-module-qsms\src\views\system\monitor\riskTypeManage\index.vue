<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="风险类型名称" prop="riskName">
        <el-select
          v-model="queryParams.riskName"
          placeholder="请选择风险类型名称"
          clearable
          style="width: 100%"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="item in riskNameList"
            :key="item.riskName"
            :label="item.riskName"
            :value="item.riskName"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="父级风险类型ID" prop="parentId">
        <el-input
          v-model="queryParams.parentId"
          placeholder="请输入父级风险类型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="排序序号" prop="sortOrder">
        <el-input
          v-model="queryParams.sortOrder"
          placeholder="请输入排序序号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="isActive">
        <el-input
          v-model="queryParams.isActive"
          placeholder="请输入是否启用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:management:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:management:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:management:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:management:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="managementList"
      height="calc(100vh - 230px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="风险类型ID" align="center" prop="id" /> -->

      <el-table-column label="风险类型名称" align="center" prop="riskName" />
      <!-- <el-table-column label="父级风险类型ID" align="center" prop="parentId" /> -->
      <el-table-column label="风险类型描述" align="center" prop="description" />
      <el-table-column
        label="风险特征"
        align="center"
        prop="riskCharacteristics"
      />

      <el-table-column
        label="风险管控措施"
        align="center"
        prop="riskControlAttachment"
        width="300"
      >
        <template slot-scope="scope">
          <!-- <div
            v-if="scope.row.riskControlAttachment"
            v-html="getTextPreview(scope.row.riskControlAttachment)"
            style="max-height: 40px; overflow: hidden"
          ></div> -->
          <div
            v-if="scope.row.riskControlAttachment"
            :ref="'richTextContainer_' + scope.row.id"
            class="rich-text-content rich-text-ellipsis"
            v-html="scope.row.riskControlAttachment"
          ></div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="常见场景" align="center" prop="commonScene" />
      <!-- <el-table-column label="排序序号" align="center" prop="sortOrder" /> -->
      <!-- <el-table-column label="是否启用" align="center" prop="isActive" /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="180"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['system:management:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleView(scope.row)"
          >
            查看</el-button
          >
          <el-button
            v-hasPermi="['system:management:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['system:management:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改风险类型管理（用于分类管理各类风险）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="风险类型名称" prop="riskName">
          <el-input v-model="form.riskName" placeholder="请输入风险类型名称" />
        </el-form-item>
        <!-- <el-form-item label="父级风险类型ID" prop="parentId">
          <el-input
            v-model="form.parentId"
            placeholder="请输入父级风险类型ID"
          />
        </el-form-item> -->
        <el-form-item label="风险类型描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="风险特征" prop="riskCharacteristics">
          <el-input
            v-model="form.riskCharacteristics"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="常见场景" prop="commonScene">
          <el-input
            v-model="form.commonScene"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="风险管控措施" prop="riskControlAttachment">
          <editor v-model="form.riskControlAttachment" :min-height="200" />
        </el-form-item>
        <!-- <el-form-item label="排序序号" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序序号" />
        </el-form-item>
        <el-form-item label="是否启用" prop="isActive">
          <el-input v-model="form.isActive" placeholder="请输入是否启用" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 查看 -->
    <el-dialog
      title="查看风险类型及措施管理"
      :visible.sync="viewOpen"
      width="700px"
      append-to-body
    >
      <el-form ref="form" :model="viewForm" label-width="130px">
        <el-form-item label="风险类型名称：" prop="riskName">
          <span>{{ viewForm.riskName || "-" }}</span>
        </el-form-item>

        <el-form-item label="风险类型描述：" prop="description">
          <span>{{ viewForm.description || "-" }}</span>
        </el-form-item>
        <el-form-item label="风险特征：" prop="riskCharacteristics">
          <span>{{ viewForm.riskCharacteristics || "-" }}</span>
        </el-form-item>
        <el-form-item label="常见场景：" prop="commonScene">
          <span>{{ viewForm.commonScene || "-" }}</span>
        </el-form-item>
        <el-form-item label="风险管控措施：" prop="riskControlAttachment">
          <span
            v-html="viewForm.riskControlAttachment"
            class="rich-text-content"
          ></span>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  listManagement,
  getManagement,
  delManagement,
  addManagement,
  updateManagement,
  getRiskNameList,
} from "@/api/system/riskTypeManage/index";
import Editor from "@/components/Editor";

export default {
  name: "Management",
  components: {
    Editor,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 风险类型管理（用于分类管理各类风险）表格数据
      managementList: [],
      // 风险类型名称列表
      riskNameList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        riskName: null,
        parentId: null,
        description: null,
        riskCharacteristics: null,
        commonScene: null,
        sortOrder: null,
        isActive: null,
        riskControlAttachment: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        riskName: [
          { required: true, message: "风险类型名称不能为空", trigger: "blur" },
        ],
      },

      // 是否显示查看弹出层
      viewOpen: false,
      // 查看表单参数
      viewForm: {},
    };
  },
  created() {
    this.getList();
    this.getRiskNameListData();
  },
  methods: {
    /** 查询风险类型管理（用于分类管理各类风险）列表 */
    getList() {
      this.loading = true;
      listManagement(this.queryParams).then((response) => {
        this.managementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        riskName: null,
        parentId: null,
        description: null,
        riskCharacteristics: null,
        commonScene: null,
        sortOrder: null,
        isActive: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        riskControlAttachment: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加风险类型管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getManagement(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改风险类型管理";
      });
    },
    handleView(row) {
      const id = row.id || this.ids;
      getManagement(id).then((response) => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateManagement(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addManagement(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除风险类型管理（用于分类管理各类风险）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delManagement(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/management/export",
        {
          ...this.queryParams,
        },
        `management_${new Date().getTime()}.xlsx`
      );
    },
    getTextPreview(htmlContent) {
      if (!htmlContent) return "";
      // 移除HTML标签并截取前50个字符作为预览
      const text = htmlContent.replace(/<[^>]*>/g, "");
      return text.length > 50 ? text.substring(0, 50) + "..." : text;
    },
    /** 获取风险类型名称列表 */
    getRiskNameListData() {
      getRiskNameList()
        .then((response) => {
          if (response.code === 200 && response.rows) {
            this.riskNameList = response.rows;
          }
        })
        .catch(() => {
          this.riskNameList = [];
        });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .rich-text-content {
  text-align: left;
  word-break: break-word;
  line-height: 1.5;
  overflow: visible;

  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 10px auto;
    display: block;
  }

  p {
    margin: 8px 0;
    span {
      color: #606266 !important;
    }
  }
}

::v-deep .rich-text-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 96px;
  line-height: 1.6;
  word-break: break-word;
  font-size: 14px;

  p {
    margin: 2px 0;
    line-height: 1.6;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  img {
    display: none;
  }
}
</style>
