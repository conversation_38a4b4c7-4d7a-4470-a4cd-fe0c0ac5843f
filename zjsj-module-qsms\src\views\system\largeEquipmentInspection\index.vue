<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="检查单号" prop="recordCode">
        <el-input
          v-model="queryParams.recordCode"
          placeholder="请输入检查单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联设备" prop="equipmentId">
        <el-select
          v-model="queryParams.equipmentId"
          placeholder="请选择关联设备"
          style="width: 100%"
        >
          <el-option
            v-for="item in equipmentList"
            :key="item.id"
            :label="item.equipmentName"
            :value="item.id"
          />
        </el-select>
        <!-- <el-input
          v-model="queryParams.equipmentName"
          placeholder="关联设备"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
      </el-form-item>
      <el-form-item label="检查类型" prop="inspectionType">
        <el-select
          v-model="queryParams.inspectionType"
          placeholder="请选择检查类型"
        >
          <el-option
            v-for="item in inspectionTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="检查时间" prop="inspectionTime">
        <el-date-picker
          clearable
          v-model="queryParams.inspectionTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择检查时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="检查人" prop="inspectorUserId">
        <el-input
          v-model="queryParams.inspectorUserId"
          placeholder="请输入检查人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="天气情况" prop="weather">
        <el-input
          v-model="queryParams.weather"
          placeholder="请输入天气情况"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <!-- <el-form-item label="关联的隐患" prop="hiddenDangerId">
        <el-input
          v-model="queryParams.hiddenDangerId"
          placeholder="请输入关联的隐患"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:records:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:records:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:records:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="recordsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="检查单号" align="center" prop="recordCode" />
      <el-table-column label="关联设备" align="center" prop="equipmentName" />
      <!-- <el-table-column
        label="关联的设备名称"
        align="center"
        prop="equipmentId"
      /> -->
      <el-table-column
        label="检查类型"
        align="center"
        prop="inspectionType"
        :formatter="formatStatus"
      />
      <el-table-column
        label="检查时间"
        align="center"
        prop="inspectionTime"
        width="180"
      >
      </el-table-column>
      <el-table-column label="检查人" align="center" prop="inspectorUserName" />
      <!-- <el-table-column label="天气情况" align="center" prop="weather" /> -->
      <!-- <el-table-column
        label="检查内容 JSON"
        align="center"
        prop="contentJson"
      />
      <el-table-column label="总体结论" align="center" prop="overallResult" />
      <el-table-column
        label="异常情况描述"
        align="center"
        prop="problemDescription"
      /> -->
      <!-- <el-table-column label="相关附件" align="center" prop="imageUrls" /> -->
      <el-table-column label="相关附件" align="center" prop="imageUrls">
        <template slot-scope="scope">
          <div v-if="scope.row.imageUrls" class="contract-file">
            <div
              v-for="(item, index) in scope.row.imageUrls.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="关联的隐患"
        align="center"
        prop="hiddenDangerName"
      />
      <el-table-column
        label="关联的隐患"
        align="center"
        prop="hiddenDangerId"
      /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:records:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:records:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备检查记录对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="检查单号" prop="recordCode">
          <el-input
            v-model="form.recordCode"
            placeholder="请输入检查单号"
            maxlength="50"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="关联设备" prop="equipmentId">
          <el-select
            v-model="form.equipmentId"
            placeholder="请选择关联设备"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in equipmentList"
              :key="item.id"
              :label="item.equipmentName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查类型" prop="inspectionType">
          <el-select
            v-model="form.inspectionType"
            placeholder="请选择检查类型"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in inspectionTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检查时间" prop="inspectionTime">
          <el-date-picker
            clearable
            v-model="form.inspectionTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择检查时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查人" prop="inspectorUserId">
          <!-- <el-input v-model="form.inspectorUserId" placeholder="请选择检查人" /> -->
          <el-select
            v-model="form.inspectorUserId"
            placeholder="请选择检查人"
            style="width: 100%"
            filterable
            :filter-method="handleFilter"
            @visible-change="handleVisibleChange"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="天气情况" prop="weather">
          <el-input
            v-model="form.weather"
            placeholder="请输入天气情况"
            maxlength="50"
            show-word-limit
            :disabled="isCheck"
          />
        </el-form-item>
        <!-- <el-form-item label="检查内容">
          <div v-if="!isCheck">
            <el-button type="primary" @click="addCheckItem"
              >新增检查项</el-button
            >
          </div>
          <el-table
            ref="tableRef"
            :data="contentJsonList"
            border
            style="width: 100%; margin-top: 10px"
            max-height="400"
          >
            <el-table-column type="index" width="50" label="序号">
            </el-table-column>
            <el-table-column label="检查项" prop="item" align="center">
              <template slot-scope="scope">
                <el-input
                  :disabled="isCheck"
                  v-model="scope.row.item"
                  placeholder="请输入"
                />
              </template>
            </el-table-column>
            <el-table-column label="检查结果" prop="result" align="center">
              <template slot-scope="scope">
                <el-input
                  :disabled="isCheck"
                  v-model="scope.row.result"
                  placeholder="请输入"
                />
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="remark" align="center">
              <template slot-scope="scope">
                <el-input
                  :disabled="isCheck"
                  v-model="scope.row.remark"
                  placeholder="请输入"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template slot-scope="scope">
                <el-button
                  v-if="
                    contentJsonList
                      ? contentJsonList.length != 1 && !isCheck
                      : false
                  "
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  style="color: red"
                  @click="handleDeleteItems(scope.row, scope.$index)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-form-item> -->
        <el-form-item label="总体结论" prop="overallResult">
          <el-select
            v-model="form.overallResult"
            placeholder="请选择总体结论"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in overallResultOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="异常情况描述" prop="problemDescription">
          <el-input
            v-model="form.problemDescription"
            type="textarea"
            placeholder="请输入异常情况描述"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="相关附件" prop="imageUrls">
          <file-upload v-model="form.imageUrls" :disabled="isCheck" />
        </el-form-item>
        <el-form-item label="关联的隐患" prop="hiddenDangerId">
          <el-input
            v-model="form.hiddenDangerId"
            placeholder="请选择关联的隐患"
            :disabled="isCheck"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRecords,
  getRecords,
  delRecords,
  addRecords,
  updateRecords,
} from "@/api/system/largeEquipmentInspection/index";
import { listUser } from "@/api/system/user";
import { listEquipment } from "@/api/system/equipmentLedgerManagement/index";
import { getFileOrignalName } from "@/utils/common.js";
export default {
  name: "Records",
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      overallResultOptions: [
        { label: "异常", value: 0 },
        { label: "正常", value: 1 },
      ],
      inspectionTypeOptions: [
        { label: "日常检查", value: 1 },
        { label: "每周检查", value: 2 },
        { label: "每月检查", value: 3 },
        { label: "专项检查", value: 4 },
      ],
      isCheck: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备检查记录表格数据
      recordsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        recordCode: null,
        equipmentId: null,
        inspectionType: null,
        inspectionTime: null,
        inspectorUserId: null,
        weather: null,
        overallResult: null,
        problemDescription: null,
        imageUrls: null,
        hiddenDangerId: null,
      },
      // 表单参数
      form: {},
      // 检查内容
      contentJsonList: [
        {
          item: "",
          result: "",
          remark: "",
        },
      ],
      // 表单校验
      rules: {
        recordCode: [
          { required: true, message: "检查单号不能为空", trigger: "blur" },
        ],
        equipmentId: [
          { required: true, message: "关联的设备不能为空", trigger: "blur" },
        ],
        inspectionType: [
          { required: true, message: "检查类型不能为空", trigger: "change" },
        ],
        inspectionTime: [
          { required: true, message: "检查时间不能为空", trigger: "blur" },
        ],
        inspectorUserId: [
          {
            required: true,
            message: "检查人不能为空",
            trigger: "change",
          },
        ],
        contentJson: [
          { required: true, message: "检查内容不能为空", trigger: "blur" },
        ],
        overallResult: [
          { required: true, message: "总体结论不能为空", trigger: "blur" },
        ],
      },
      equipmentList: [],
      userList: [],
      selectedValue: this.value,
      allOptions: [], // 备份所有选项
      isSearching: false, // 是否正在搜索
      showCount: 500, // 初始默认显示的条数
    };
  },
  created() {
    this.getList();
    this.getEquipmentList();
    this.getUserList();
  },
  methods: {
    // formatStatus(row, column, value) {
    //   const option = this.statusOptions.find((item) => item.value == value);
    //   return option ? option.label : value;
    // },
    handleFilter(query) {
      this.isSearching = true;
      if (!query) {
        this.userList = this.allOptions.slice(0, this.showCount);
      } else {
        const lowerQuery = query.toString().toLowerCase();
        this.userList = this.allOptions.filter((item) =>
          item.nickName.toLowerCase().includes(lowerQuery)
        );
      }
    },
    handleVisibleChange(visible) {
      if (!visible) {
        this.isSearching = false;
        this.userList = this.allOptions.slice(0, this.showCount);
      }
    },
    getUserList() {
      listUser({
        pageNum: 1,
        pageSize: 9999999,
      }).then((response) => {
        let tempUserList = response.rows;
        this.allOptions = tempUserList.map((item) => {
          return {
            userId: item.userId,
            userName: item.userName,
            nickName: item.nickName,
          };
        });
        this.userList = this.allOptions.slice(0, this.showCount);
      });
    },
    handleDeleteItems(row, index) {
      this.contentJsonList.splice(index, 1);
      this.$forceUpdate();
    },
    addCheckItem() {
      this.contentJsonList.push({
        item: "",
        result: "",
        remark: "",
      });
      this.$forceUpdate();
    },
    getEquipmentList() {
      listEquipment({
        pageNum: 1,
        pageSize: 9999999,
      }).then((response) => {
        this.equipmentList = response.rows;
        // console.log("aaa获取设备列表", this.equipmentList);
      });
    },
    getFileOrignalName,
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    formatStatus(row, column, value) {
      const option = this.inspectionTypeOptions.find(
        (item) => item.value == value
      );
      return option ? option.label : value;
    },
    /** 查询设备检查记录列表 */
    getList() {
      this.loading = true;
      listRecords(this.queryParams).then((response) => {
        this.recordsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        recordCode: null,
        equipmentId: null,
        equipmentName: null,
        inspectionType: null,
        inspectionTime: null,
        inspectorUserId: null,
        inspectorUserName: null,
        weather: null,
        contentJson: null,
        overallResult: null,
        problemDescription: null,
        imageUrls: null,
        hiddenDangerId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.projectId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.contentJsonList = [
        {
          item: "",
          result: "",
          remark: "",
        },
      ];
      this.open = true;
      this.isCheck = false;
      this.title = "添加设备检查记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRecords(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.contentJsonList = JSON.parse(this.form.contentJson);
        this.title = "修改设备检查记录";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getRecords(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.contentJsonList = JSON.parse(this.form.contentJson);
        this.title = "查看设备检查记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // let isContentJson = false;
          // this.contentJsonList.forEach((item) => {
          //   if (item.item == "") {
          //     isContentJson = true;
          //   }
          // });
          // if (isContentJson) {
          //   this.$message({
          //     showClose: true,
          //     message: "存在检查内容为空情况",
          //     type: "warning",
          //   });
          //   return;
          // }
          this.form.contentJson = JSON.stringify(this.contentJsonList);
          if (this.form.id != null) {
            updateRecords(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRecords(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除设备检查记录编号为"' + ids + '"的数据项？')
        .then(function () {
          return delRecords(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/equipmentInspectionRecords/export",
        {
          ...this.queryParams,
        },
        `大型设备检查.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>

