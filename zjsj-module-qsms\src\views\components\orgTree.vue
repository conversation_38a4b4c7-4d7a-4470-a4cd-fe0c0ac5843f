<template>
  <div class="tree">
    <div v-if="type == '2'" class="title mb-2">目标方针结构图</div>
    <div v-else class="title mb-2">组织架构</div>
    <div class="tree-scroll-container">
      <el-tree v-loading="loading" :type="type" :data="type == '1' ? data1 : type == '2' ? data2 : []"
        :props="defaultProps" highlight-current @node-click="handleNodeClick">
        <template #default="{ node, data }">
          <el-tooltip effect="dark" :content="data.label" placement="top">
            <span :ref="(el) => setLabelRef(el, node)" class="el-tree-node__label">
              {{ node.label }}
            </span>
          </el-tooltip>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { getEnterpriseInfo } from '@/api/system/info'
export default {
  name: 'OrgTree',
  props: {
    type: {
      type: String,
      default: '1'
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      data1: [],
      data2: [
        {
          label: '总公司2025(4)',
          children: [
            {
              label: '安全目标1'
            },
            {
              label: '安全目标2'
            },
            {
              label: '安全目标3'
            },
            {
              label: '安全目标4'
            },
            {
              label: '子公司1：已分解',
              children: [
                {
                  label: '部门1：培训覆盖'
                },
                {
                  label: '部门2：隐患整改'
                }
              ]
            },
            {
              label: '子公司2：未分解',
              children: [
                {
                  label: '安全目标1'
                },
                {
                  label: '安全目标2'
                },
                {
                  label: '安全目标3'
                },
                {
                  label: '安全目标4'
                }
              ]
            }
          ]
        }
      ],

      labelRefs: new Map(),
      defaultProps: {
        children: 'children',
        label: 'label',
        id: 'id',
        isLeaf: 'isLeaf'
      },
      loading: false,
      queryParams: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询部门列表 */
    getList() {
      this.loading = true
      // const params = {
      //   parentId: "0",
      // };
      getEnterpriseInfo()
        .then((res) => {
          this.deptList = res.data
          this.data1 = []
          this.deptList.forEach((item) => {
            this.data1.push({
              label: item.label,
              id: item.id,
              children: item.children
              // isLeaf: !!item.lastNode,
            })
          })
          this.loading = false
        })
        .catch((err) => {
          this.loading = false
          console.error(err)
        })
    },
    handleNodeClick(data, node) {
      // console.log("data", data);
      this.$emit('nodeClick', data)
    },

    processTreeData(data) {
      return data.map((item) => ({
        id: item.id,
        label: item.name,
        children: null,
        isLeaf: item.isLeaf.toLowerCase() === 'true'
      }))
    },
    setLabelRef(el, node) {
      if (el) {
        this.labelRefs.set(node.id || node.label, el)
      }
    },
    isEllipsisActive(node) {
      const labelEl = this.labelRefs.get(node.id || node.label)
      return labelEl ? labelEl.scrollWidth > labelEl.clientWidth : false
    }
  }
}
</script>

<style scoped lang="scss">
.tree {
  height: 80vh;
  overflow: auto;
}

.title {
  font-size: 16px;
}

.tree-scroll-container {
  overflow-x: auto;
  overflow-y: hidden;
  min-width: 100%;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 隐藏滚动条 - Chrome, Safari 和 Opera */
  -ms-overflow-style: none;
  /* IE 和 Edge */
  scrollbar-width: none;
  /* Firefox */
}

/* 隐藏滚动条 - Chrome, Safari 和 Opera */
::v-deep .el-tree-node__label::-webkit-scrollbar {
  display: none;
}

::v-deep .el-tree-node.is-current>.el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}
</style>
