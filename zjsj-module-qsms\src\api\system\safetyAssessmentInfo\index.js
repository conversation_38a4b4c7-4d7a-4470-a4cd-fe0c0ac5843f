import request from '@/utils/request'

// 查询安全生产责任制考核页列表
export function listAssessment(query) {
  return request({
    url: '/system/projectAssessment/list',
    method: 'get',
    params: query
  })
}

// 查询安全生产责任制考核页详细
export function getAssessment(id) {
  return request({
    url: '/system/projectAssessment/' + id,
    method: 'get'
  })
}
export function getAssessmentDetail(id) {
  return request({
    url: '/system/detail/list?projectAssessmentId=' + id,
    method: 'get'
  })
}

// 新增安全生产责任制考核页
export function addAssessment(data) {
  return request({
    url: '/system/projectAssessment',
    method: 'post',
    data: data
  })
}

// 修改安全生产责任制考核页
export function updateAssessment(data) {
  return request({
    url: '/system/projectAssessment',
    method: 'put',
    data: data
  })
}

// 删除安全生产责任制考核页
export function delAssessment(id) {
  return request({
    url: '/system/projectAssessment/' + id,
    method: 'delete'
  })
}
