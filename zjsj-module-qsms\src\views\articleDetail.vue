<template>
  <div class="articleList-container">
    <Navbar />
    <div class="article-list-container">
      <!-- 新增导航 -->
      <div class="breadcrumb">
        <span class="goBack" @click="$router.go(-1)">
          <i class="el-icon-arrow-left"></i>
          <span>返回</span>
        </span>
        <span class="breadcrumb-item" @click="goHome">首页</span>
        <span>/</span>
        <span class="breadcrumb-item" @click="goTolist">{{ breadTitle }}</span>
      </div>
      <!-- 奖项详情 -->
      <div class="article-list" v-if="this.$route.query.type == 'awards'">
        <div class="article-item">
          <div class="article-top gap-3">
            <div class="article-title">
              {{ articleDetail.awardsName }}
            </div>
            <div class="flex justify-center gap-10">
              <div
                class="article-date"
                v-show="articleDetail.awardWinningProjects"
              >
                获奖项目：{{ articleDetail.awardWinningProjects }}
              </div>
              <div class="article-date" v-show="articleDetail.winningUnits">
                获奖单位：{{ articleDetail.winningUnits }}
              </div>
              <div class="article-date" v-show="articleDetail.awardsTime">
                获奖时间：{{ articleDetail.awardsTime }}
              </div>
            </div>
          </div>
          <!-- 附件为图片展示 为pdf等文件 展示文件名 -->
          <div v-if="articleDetail.awardsUrl">
            <img
              v-for="(file, index) in imageAttachments"
              :key="index"
              :src="baseUrl + file"
              alt=""
              class="w-full h-auto"
            />
            <div v-if="fileAttachments.length > 0" class="attachment-container">
              <h3>相关附件:</h3>
              <ul class="attachment-list">
                <li v-for="(file, index) in fileAttachments" :key="index">
                  <i class="el-icon-document"></i>
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleAttachment(file)"
                    >{{ file.split("/").pop() }}</el-button
                  >
                  <!-- <span class="file-size">({{ file.size }})</span> -->
                </li>
              </ul>
            </div>
          </div>
          <div class="flex justify-center gap-10">
            <div class="article-date" v-show="articleDetail.awardNumber">
              颁奖文号：{{ articleDetail.awardNumber }}
            </div>
            <div class="article-date" v-show="articleDetail.awardingUnit">
              颁奖单位：{{ articleDetail.awardingUnit }}
            </div>
          </div>
          <!-- 如果附件是视频类型，显示视频 -->
          <!-- <div v-if="isVideoType" class="video-container">
            <video
              controls
              width="100%"
              v-for="(videoUrl, index) in getAttachment(
                articleDetail.attachmentUrl
              )"
              :key="index"
            >
              <source :src="`${baseUrl}${videoUrl}`" type="video/mp4" />
              您的浏览器不支持 HTML5 视频。
            </video>
          </div> -->
        </div>
      </div>
      <!-- 文章详情 -->
      <div class="article-list" v-else>
        <div class="article-item">
          <div class="article-top">
            <div class="article-title">
              {{ articleDetail.title }}
            </div>
            <div class="article-date">{{ articleDetail.publishTime }}</div>
          </div>
          <div
            class="article-summary"
            v-if="articleDetail.content"
            v-html="articleDetail.content"
          ></div>
          <!-- 如果附件是视频类型，显示视频 -->
          <div v-if="isVideoType" class="video-container">
            <video
              controls
              width="100%"
              v-for="(videoUrl, index) in getAttachment(
                articleDetail.attachmentUrl
              )"
              :key="index"
            >
              <source :src="`${baseUrl}${videoUrl}`" type="video/mp4" />
              您的浏览器不支持 HTML5 视频。
            </video>
          </div>

          <div
            class="attachment-container"
            v-if="!isVideoType && attachments.length > 0"
          >
            <h3>相关附件:</h3>
            <ul class="attachment-list">
              <li
                v-for="(file, index) in attachments"
                :key="index"
                class="attachment-item"
              >
                <i class="el-icon-document"></i>
                <el-button
                  size="mini"
                  type="text"
                  @click="handleAttachment(file)"
                  >{{ file.split("/").pop() }}</el-button
                >
                <!-- <span class="file-size">({{ file.size }})</span> -->
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import swiper01 from "@/assets/images/menhu/swiper01.jpg";
import swiper02 from "@/assets/images/menhu/swiper02.jpg";
import { getZjNewsInfoPortal } from "@/api/menhu/index.js";
// 首页的导航栏
import Navbar from "@/views/components/navBar.vue";
import {
  getZjAwardsInfo,
  getZjAwardsType,
} from "@/api/inspection/zjAwardsInfo";
export default {
  components: {
    Navbar,
  },
  data() {
    return {
      swiperList: [
        { src: swiper01, title: "中江集团" },
        { src: swiper02, title: "中江集团" },
      ],
      breadTitle: "",
      articleDetail: {
        title: "",
        content: "",
        publishTime: "",
      },
      isVideoType: false,
      attachments: [],
      imageAttachments: [],
      fileAttachments: [],
      videoExtensions: ["mp4", "avi", "mov", "mkv", "flv", "wmv"],
      isAdmin: false,
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  computed: {},
  mounted() {
    const permissions = this.$store.getters.permissions;
    const roles = this.$store.getters.roles;
    this.isAdmin =
      (Array.isArray(permissions) && permissions.includes("system:pcadmin")) ||
      roles.includes("admin");
    if (this.$route.query.type == "awards") {
      this.breadTitle = "创优奖项";
      this.fetchAwardsDetail();
    } else {
      this.fetchArticleDetail();
    }
  },
  methods: {
    // 处理视频附件
    getAttachment(url) {
      // console.log(url, "url");
      let newUrl = url ? url.split(",") : [];
      return newUrl;
    },
    async handleAttachment(value) {
      // if (value) {
      //   window.open(this.baseUrl + value);
      // } else {
      //   this.$message.warning("该记录没有附件");
      // }
      if (!value) {
        this.$message.warning("该记录没有附件");
        return;
      }
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    // <!-- 1-公司要闻2-通知公告3-法律法规4-行业动态 5-EHS动态 6-安全视频 -->
    getNewTypeNameByString(type) {
      if (!type) {
        return "";
      }
      // console.log("type", type);
      const typeMap = {
        1: "公司要闻",
        2: "通知公告",
        3: "法律法规",
        4: "行业动态",
        5: "EHS动态",
        6: "安全视频",
      };
      return typeMap[type] || "";
    },

    async fetchArticleDetail() {
      try {
        const res = await getZjNewsInfoPortal(this.$route.query.id);
        this.articleDetail = res.data;
        this.breadTitle = this.getNewTypeNameByString(res.data.newType);
        this.isVideoType = res.data.newType === "6";
        // this.attachments = res.data.attachmentUrl?.split(",");
        // console.log(this.attachmentUrl, "attachmentUrl");
        this.attachments = this.isVideoType
          ? []
          : res.data.attachmentUrl
          ? res.data.attachmentUrl.split(",")
          : [];
      } catch (error) {
        console.error("获取文章列表失败:", error);
      }
    },
    async fetchAwardsDetail() {
      try {
        getZjAwardsInfo(this.$route.query.id).then((res) => {
          this.articleDetail = res.data;
          const allAttachments = res.data.awardsUrl
            ? res.data.awardsUrl.split(",")
            : [];

          // 分隔图片和其他文件
          this.imageAttachments = allAttachments.filter((file) => {
            const ext = file.split(".").pop().toLowerCase();
            return ["jpg", "png", "jpeg"].includes(ext);
          });

          this.fileAttachments = allAttachments.filter((file) => {
            const ext = file.split(".").pop().toLowerCase();
            return !["jpg", "png", "jpeg"].includes(ext);
          });
        });
      } catch (error) {
        console.error("获取奖项详情失败:", error);
      }
    },
    goHome() {
      this.$router.push("/menhu");
    },
    goTolist() {
      console.log(this.articleDetail);
      if (this.articleDetail.awardsType) {
        this.$router.push({
          name: "ArticleList",
          query: { type: "awards", awardsType: this.articleDetail.awardsType },
        });
      } else {
        this.$router.push({
          name: "ArticleList",
          query: {
            type: this.getNewTypeNameByType(this.articleDetail.newType),
          },
        });
      }
      // this.$router.push({ name: "awards" });
    },
    getNewTypeNameByType(type) {
      if (!type) {
        return "";
      }
      // console.log("type", type);
      const typeMap = {
        1: "news",
        2: "notice",
        3: "law",
        4: "industry",
        5: "ehs",
        6: "video",
      };
      return typeMap[type] || "";
    },
    goToArticleDetail(id) {
      this.$router.push({
        name: "ArticleDetail",
        query: { type: this.articleDetail.newType },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.articleList-container {
  min-height: 100%;
  background-color: #f4f4f5;

  .article-list-container {
    font-family: "Microsoft YaHei", sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    margin-top: 10px;
    background-color: #fff;
    .breadcrumb {
      color: #666;
      font-size: 14px;
      padding: 20px;
      border-bottom: 1px solid #eee;
      background: #ebf5ff;
    }
    .goBack {
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #0974ff;
      }
    }
    .breadcrumb-item {
      cursor: pointer;
      transition: color 0.3s ease;
      margin: 10px;

      &:hover {
        color: #0974ff;
      }
    }
    .article-list {
      border-top: 1px solid #eee;
      margin-bottom: 20px;
      padding: 20px;
    }
    .article-item {
      padding: 15px 0;
      border-bottom: 1px dashed #ddd;
    }
    .article-top {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .article-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .article-title a {
      color: #333;
      text-decoration: none;
    }

    .article-title a:hover {
      color: #1e50a2;
      text-decoration: underline;
    }

    .article-date {
      color: #999;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .article-summary {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      text-align: justify;
      text-indent: 2em;
      ::v-deep p {
        display: flex;
        flex-direction: column;
        // justify-content: center;
        align-content: center;
        img {
          width: 960px;
          object-fit: contain;
        }
      }
      ::v-deep .ql-align-center {
        flex-wrap: wrap;
        align-items: center;
        text-indent: 0em !important;
      }
    }
    .attachment-container {
      margin-top: 30px;
      padding: 15px;
      border-top: 1px solid #eee;
    }

    .attachment-list {
      list-style: none;
      padding: 0;
    }

    .attachment-item {
      padding: 8px 0;
      display: flex;
      align-items: center;
    }

    .attachment-item i {
      margin-right: 10px;
      color: #409eff;
    }

    .attachment-item a {
      color: #333;
      text-decoration: none;
      margin-right: 10px;
    }

    .attachment-item a:hover {
      color: #409eff;
      text-decoration: underline;
    }

    .file-size {
      color: #999;
      font-size: 12px;
    }
  }
}
</style>
