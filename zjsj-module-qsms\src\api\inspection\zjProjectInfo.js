import request from '@/utils/request'

// 查询项目信息列表
export function listZjProjectInfo(query) {
  return request({
    url: '/inspection/zjProjectInfo/list',
    method: 'get',
    params: query
  })
}

// 查询项目信息详细
export function getZjProjectInfo(id) {
  return request({
    url: '/inspection/zjProjectInfo/' + id,
    method: 'get'
  })
}

// 新增项目信息
export function addZjProjectInfo(data) {
  return request({
    url: '/inspection/zjProjectInfo',
    method: 'post',
    data: data
  })
}

// 修改项目信息
export function updateZjProjectInfo(data) {
  return request({
    url: '/inspection/zjProjectInfo',
    method: 'put',
    data: data
  })
}

// 删除项目信息
export function delZjProjectInfo(id) {
  return request({
    url: '/inspection/zjProjectInfo/' + id,
    method: 'delete'
  })
}
