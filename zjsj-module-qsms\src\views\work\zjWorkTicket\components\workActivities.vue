<template>
  <div class="work-ticket-list">
    <div class="header">
      <el-button icon="el-icon-arrow-left" class="back-btn" @click="handleBack">
        返回
      </el-button>
    </div>

    <div class="ticket-list" v-if="ticketList.length > 0">
      <el-card
        v-for="ticket in ticketList"
        :key="ticket.id"
        class="ticket-card"
        shadow="hover"
      >
        <div class="card-item">
          <div class="card-left">
            <div class="card-header">
              <h3 class="ticket-title">{{ ticket.homeworkActivityName }}</h3>
            </div>
            <div class="card-content">
              <div class="info-item">
                <span class="label">所属区域：</span>
                <span class="value">{{ ticket.workArea || "暂无" }}</span>
              </div>
              <div class="info-item">
                <span class="label">申请单位：</span>
                <span class="value">{{ ticket.applicant || "暂无" }}</span>
              </div>
              <div class="info-item">
                <span class="label">涉及作业类型：</span>
                <span class="value">{{
                  getWorkTypeLabel(ticket.workType) || "暂无"
                }}</span>
              </div>
              <div class="info-item">
                <span class="label">已开作业票数：</span>
                <span class="value">{{ ticket.homeworkNum }}</span>
              </div>
            </div>
          </div>
          <div class="card-actions">
            <!-- <el-button type="primary" class="detail-btn" @click="handleDetail(ticket)">

              作业票详情
            </el-button> -->
            <el-button
              type="text"
              class="confirm-btn"
              @click="handleConfirm(ticket)"
            >
              确定
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
    <div v-else class="ticket-list">
      <el-empty :image-size="200"></el-empty>
    </div>
    <!-- 分页器 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="pageNum"
      :limit.sync="pageSize"
      @pagination="getWorkActivities"
      style="position: absolute; bottom: 20px; right: 20px; width: 100%"
    />
  </div>
</template>

<script>
import { listZjWorkActivities } from "@/api/work/zjWorkActivities";

export default {
  name: "WorkTicketList",
  props: {
    workType: {
      type: [String, Number],
      default: () => {},
    },
  },
  data() {
    return {
      ticketList: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
    };
  },
  mounted() {
    this.getWorkActivities();
  },

  methods: {
    getWorkTypeLabel(type) {
      if (type == "lsyd") {
        return "临时用电安全作业";
      } else if (type == "dh") {
        return "动火安全作业";
      } else if (type == "sx") {
        return "受限空间安全作业";
      } else if (type == "mb") {
        return "盲板抽堵安全作业";
      } else if (type == "dz") {
        return "吊装安全作业";
      } else if (type == "dt") {
        return "动土安全作业";
      } else if (type == "dl") {
        return "断路安全作业";
      } else if (type == "gc") {
        return "高处安全作业";
      } else if (type == "qt") {
        return "其他";
      }
    },

    getWorkActivities() {
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        workType: this.workType,
      };

      listZjWorkActivities(params).then((res) => {
        this.ticketList = res.rows;
        this.total = res.total;
      });
    },

    handleBack() {
      this.$emit("back");
    },

    handleDetail(ticket) {
      this.$emit("detail", ticket);
    },
    handleConfirm(ticket) {
      this.$emit("confirm", ticket);
    },
  },
};
</script>
<style scoped>
.work-ticket-list {
  /* padding: 20px;
  background-color: #f5f7fa; */
  height: 100vh;
  overflow: auto;
}

.header {
  margin-bottom: 30px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  /* 吸顶 */
  position: absolute;
  width: 101.5%;
  left: -20px;
  top: 0;
  z-index: 100;
}

.back-btn {
  background-color: #fff;
  color: #000;
  font-weight: 600;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 10px 20px;
}

.back-btn:hover {
  background-color: rgba(46, 50, 56, 0.05);
}

.ticket-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 50px;
  margin-bottom: 60px;
}

.ticket-card {
  border-radius: 8px;
  border-bottom: 1px solid #ebeef5;
  transition: all 0.3s ease;
}

/* .ticket-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
} */
.card-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-left {
}

.card-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.ticket-title {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.card-content {
  padding: 8px 0;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  line-height: 1.6;
}

.label {
  color: #606266;
  font-weight: 500;
  min-width: 100px;
  text-align: right;
  margin-right: 8px;
}

.value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}

.footer-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  gap: 12px;
  z-index: 1000;
}

.detail-btn {
  background-color: #409eff;
  border-color: #409eff;
}

.detail-btn:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.confirm-btn {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .work-ticket-list {
    padding: 15px;
  }

  .ticket-list {
    gap: 12px;
  }

  .footer-actions {
    bottom: 15px;
    right: 15px;
    left: 15px;
    justify-content: center;
  }

  .info-item {
    flex-direction: column;
    margin-bottom: 12px;
  }

  .label {
    text-align: left;
    margin-bottom: 4px;
    min-width: auto;
    font-weight: bold;
  }
}

@media (max-width: 480px) {
  .work-ticket-list {
    padding: 10px;
  }

  .footer-actions {
    flex-direction: column;
    bottom: 10px;
    right: 10px;
    left: 10px;
  }

  .ticket-title {
    font-size: 15px;
  }
}
</style>
