module.exports = {
  projectName: '质安', // 项目名称
  prod: {  // 生产环境
    name: '质安-**********',
    script: 'npm run build:prod', // 打包命令
    host: '**********', // 服务器地址
    port: 22, // ssh port，一般默认22
    username: 'root', // 登录服务器用户名
    password: process.env.DEPLOY_PROD_PASSWORD, // 从环境变量读取密码
    distPath: 'dist',  // 本地打包目录
    webDir: '/www/wwwroot/qsms',  // 服务器部署路径
    bakDir: '/www/wwwroot/qsms_backup', // 备份路径
    isRemoveRemoteFile: true, // 是否删除远程文件,
  },
  
} 