import request from '@/utils/request'

// 查询质量检查台账列表
export function listZjQualityInspectionInfo(query) {
  return request({
    url: '/inspection/zjQualityInspectionInfo/list',
    method: 'get',
    params: query
  })
}

// 查询质量检查台账详细
export function getZjQualityInspectionInfo(id) {
  return request({
    url: '/inspection/zjQualityInspectionInfo/' + id,
    method: 'get'
  })
}

// 新增质量检查台账
export function addZjQualityInspectionInfo(data) {
  return request({
    url: '/inspection/zjQualityInspectionInfo',
    method: 'post',
    data: data
  })
}

// 修改质量检查台账
export function updateZjQualityInspectionInfo(data) {
  return request({
    url: '/inspection/zjQualityInspectionInfo',
    method: 'put',
    data: data
  })
}

// 删除质量检查台账
export function delZjQualityInspectionInfo(id) {
  return request({
    url: '/inspection/zjQualityInspectionInfo/' + id,
    method: 'delete'
  })
}
