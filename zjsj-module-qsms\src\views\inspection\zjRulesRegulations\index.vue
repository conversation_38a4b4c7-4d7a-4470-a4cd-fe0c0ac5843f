<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" @tab-click="handleClick">
      <el-tab-pane label="制度文档管理" name="first">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="120px"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入关键字"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="版本号" prop="regulationVersion">
            <el-input
              v-model="queryParams.regulationVersion"
              placeholder="请输入版本号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item label="类型" prop="regulationType">
            <el-select style="width: 100%">
              <el-option label="全部" value="0"></el-option>
              <el-option label="规章" value="1"></el-option>
              <el-option label="文件" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间" prop="effectDate">
            <el-date-picker
              clearable
              v-model="queryParams.effectDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjRulesRegulations:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjRulesRegulations:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjRulesRegulations:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjRulesRegulations:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjRulesRegulationsList"
          @selection-change="handleSelectionChange"
          height="calc(100vh - 250px)"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="规章名称" align="center" prop="regulationName" />
      <el-table-column label="版本号" align="center" prop="regulationVersion" />
      <el-table-column label="生效日期" align="center" prop="effectDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.effectDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="规章内容" align="center" prop="regulationContent" /> -->
          <el-table-column
            label="制度名称"
            align="center"
            prop="regulationName"
          />
          <el-table-column
            label="制度编号"
            align="center"
            prop="regulationNumber"
          />
          <el-table-column
            label="适用范围"
            align="center"
            prop="scopeApplication"
          />
          <el-table-column label="发布部门" align="center" prop="deptId" />
          <el-table-column label="发布日期" align="center" prop="releaseDate">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.releaseDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="附件状态"
            align="center"
            prop="regulationStatus"
          />
          <el-table-column
            label="有效期"
            align="center"
            prop="regulationValidity"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.regulationValidity, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column
          label="开展情况描述"
          align="center"
          prop="developmentSituationDes"
        />
        <el-table-column
          label="上传文件url"
          align="center"
          prop="developmentSituationUrl"
        />
        <el-table-column
          label="上传时间"
          align="center"
          prop="uploadTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.uploadTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column> -->
          <el-table-column label="附件" prop="attachmentUrl">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleSafetyTrainingRecords(scope.row.attachmentUrl)"
                >查看</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >新增</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >附件</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:remove']"
                >废止</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="单位规章管理" name="second">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="120px"
        >
          <el-form-item label="关键字" prop="keyword">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入关键字"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="版本号" prop="regulationVersion">
            <el-input
              v-model="queryParams.regulationVersion"
              placeholder="请输入版本号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item label="类型" prop="regulationType">
            <el-select style="width: 100%">
              <el-option label="全部" value="0"></el-option>
              <el-option label="规章" value="1"></el-option>
              <el-option label="文件" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间" prop="effectDate">
            <el-date-picker
              clearable
              v-model="queryParams.effectDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjRulesRegulations:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjRulesRegulations:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjRulesRegulations:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjRulesRegulations:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjRulesRegulationsList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="制度名称"
            align="center"
            prop="regulationName"
          />
          <el-table-column
            label="制度编号"
            align="center"
            prop="regulationNumber"
          />
          <el-table-column label="分类" align="center" prop="regulationType" />
          <el-table-column label="发布部门" align="center" prop="deptId" />
          <el-table-column label="发布日期" align="center" prop="releaseDate">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.releaseDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" />
          <el-table-column
            label="有效期"
            align="center"
            prop="regulationValidity"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.regulationValidity, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column>

          <!-- <el-table-column
          label="开展情况描述"
          align="center"
          prop="developmentSituationDes"
        />
        <el-table-column
          label="上传文件url"
          align="center"
          prop="developmentSituationUrl"
        />
        <el-table-column
          label="上传时间"
          align="center"
          prop="uploadTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.uploadTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column> -->
          <!-- <el-table-column label="附件" prop="attachmentUrl"></el-table-column> -->
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >新增</el-button
              > -->
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >附件</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjSafetyActivityRecords:remove']"
                >废止</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加或修改规章制度对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <div v-if="activeTab == 'first'">
          <el-form-item label="制度名称" prop="regulationName">
            <el-input
              v-model="form.regulationName"
              placeholder="请输入规章名称"
            />
          </el-form-item>
          <el-form-item label="制度编号" prop="regulationNumber">
            <el-input
              v-model="form.regulationNumber"
              placeholder="请输入制度编号"
            />
          </el-form-item>
          <el-form-item label="适用范围" prop="scopeApplication">
            <el-input
              v-model="form.scopeApplication"
              placeholder="请输入适用范围"
            />
          </el-form-item>
          <el-form-item label="发布部门" prop="deptId">
            <el-input v-model="form.deptId" placeholder="请输入发布部门" />
          </el-form-item>
          <el-form-item label="发布日期" prop="releaseDate">
            <el-input v-model="form.releaseDate" placeholder="请输入发布部门" />
          </el-form-item>

          <el-form-item label="有效期" prop="effectDate">
            <el-date-picker
              clearable
              v-model="form.effectDate"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择有效期"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="上传附件" prop="uploadFile">
            <file-upload
              v-model="form.uploadFile"
              :action="uploadUrl"
              :show-file-list="true"
              :auto-upload="true"
            ></file-upload>
          </el-form-item>
        </div>
        <div v-if="activeTab == 'second'">
          <el-form-item label="制度名称" prop="regulationName">
            <el-input
              v-model="form.regulationName"
              placeholder="请输入制度名称"
            />
          </el-form-item>
          <el-form-item label="制度编号" prop="regulationNumber">
            <el-input
              v-model="form.regulationNumber"
              placeholder="请输入制度编号"
            />
          </el-form-item>
          <el-form-item label="分类" prop="regulationType">
            <el-select style="width: 100%">
              <el-option label="行业规章" value="1"></el-option>
              <el-option label="行业标准" value="2"></el-option>
              <el-option label="行业规范" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发布部门" prop="deptId">
            <el-input v-model="form.deptId" placeholder="请输入发布部门" />
          </el-form-item>
          <el-form-item label="有效期" prop="effectDate">
            <el-date-picker
              clearable
              v-model="form.effectDate"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择有效期"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="上传附件" prop="uploadFile">
            <file-upload
              v-model="form.uploadFile"
              :action="uploadUrl"
              :show-file-list="true"
              :auto-upload="true"
            ></file-upload>
          </el-form-item>
        </div>

        <!-- <el-form-item label="规章内容">
          <editor v-model="form.regulationContent" :min-height="192" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjRulesRegulations,
  getZjRulesRegulations,
  delZjRulesRegulations,
  addZjRulesRegulations,
  updateZjRulesRegulations,
} from "@/api/inspection/zjRulesRegulations";

export default {
  name: "ZjRulesRegulations",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 规章制度表格数据
      zjRulesRegulationsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        regulationName: null,
        regulationVersion: null,
        effectDate: null,
        regulationContent: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      activeTab: "first",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查看 */
    async handleSafetyTrainingRecords(value) {
      // if (value) {
      //   window.open(this.baseUrl + value);
      // } else {
      //   this.$message.warning("该记录没有附件");
      // }
      if (!value) {
        this.$message.warning("该记录没有附件");
        return;
      }
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const response = await fetch(fileUrl);
          const buffer = await response.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    /** 查询规章制度列表 */
    getList() {
      this.loading = true;
      listZjRulesRegulations(this.queryParams).then((response) => {
        this.zjRulesRegulationsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        regulationName: null,
        regulationVersion: null,
        effectDate: null,
        regulationContent: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加规章制度";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjRulesRegulations(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改规章制度";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjRulesRegulations(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjRulesRegulations(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除规章制度编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjRulesRegulations(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjRulesRegulations/export",
        {
          ...this.queryParams,
        },
        `zjRulesRegulations_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
