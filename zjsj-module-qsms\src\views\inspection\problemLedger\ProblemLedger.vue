<template>
  <div class="problem-ledger">
    <zjHazardInvestigation></zjHazardInvestigation>
  </div>
</template>

<script>
import zjHazardInvestigation from "./components/zjHazardInvestigation/index.vue";
export default {
  name: "ProblemLedger",
  components: { zjHazardInvestigation },
  data() {
    return {};
  },
  created() {},
  methods: {},
};
</script>

<style scoped>
.problem-ledger {
  padding: 20px;
}
</style>
