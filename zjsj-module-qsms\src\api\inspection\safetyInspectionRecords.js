import request from '@/utils/request'

// 查询隐患排查记录列表
export function listSafetyInspectionRecords(query) {
  return request({
    url: '/inspection/safetyInspectionRecords/list',
    method: 'get',
    params: query
  })
}

// 查询隐患排查记录详细
export function getSafetyInspectionRecords(id) {
  return request({
    url: '/inspection/safetyInspectionRecords/' + id,
    method: 'get'
  })
}

// 新增隐患排查记录
export function addSafetyInspectionRecords(data) {
  return request({
    url: '/inspection/safetyInspectionRecords',
    method: 'post',
    data: data
  })
}

// 修改隐患排查记录
export function updateSafetyInspectionRecords(data) {
  return request({
    url: '/inspection/safetyInspectionRecords',
    method: 'put',
    data: data
  })
}

// 删除隐患排查记录
export function delSafetyInspectionRecords(id) {
  return request({
    url: '/inspection/safetyInspectionRecords/' + id,
    method: 'delete'
  })
}
