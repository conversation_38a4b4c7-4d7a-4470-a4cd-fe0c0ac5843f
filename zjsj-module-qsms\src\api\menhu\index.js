import request from "@/utils/request";

// 门户页面根据id查询新闻详情
export function getZjNewsInfoPortal(id) {
  return request({
    url: `/inspection/zjNewsInfo/getInfo/${id}`,
    method: "get",
  });
}

// 质量管理
export function getQualityAssurance(query) {
  return request({
    url: "/api/getQualityAssurance",
    method: "get",
    params: query,
  });
}
// 安全管理总览
export function getSecurityOverview(query) {
  return request({
    url: "/api/getSecurityManagement",
    method: "get",
    params: query,
  });
}
// 创优奖项
export function getCompetition(query) {
  return request({
    url: "/api/getAwardsInfoStatistics",
    method: "get",
    params: query,
  });
}
// Kpi数据
export function getKpiData(query) {
  return request({
    url: "/api/kpiIndicators",
    method: "get",
    params: query,
  });
}
// 质量
export function getQuality(query) {
  return request({
    url: "/api/kpiIndicatorszl",
    method: "get",
    params: query,
  });
}
// 安全
export function getSecurity(query) {
  return request({
    url: "/api/kpiIndicatorsaq",
    method: "get",
    params: query,
  });
}
