<template>
    <div class="media-preview">
        <!-- 媒体缩略图列表 -->
        <div class="media-list">
            <div 
                v-for="(item, index) in mediaList" 
                :key="index"
                class="media-thumbnail" 
                @click="handlePreview(index)"
            >
                <!-- 图片缩略图 -->
                <img 
                    v-if="isImage(item)" 
                    :src="item.url || item" 
                    :alt="item.alt || ''" 
                    class="media-img"
                >
                <!-- 视频缩略图 -->
                <video 
                    v-else-if="isVideo(item)" 
                    :src="item.url || item" 
                    class="media-video"
                >
                    您的浏览器不支持视频播放
                </video>
            </div>
        </div>

        <!-- 预览弹窗 -->
        <el-dialog
            :visible.sync="dialogVisible"
            append-to-body
            :modal="true"
            custom-class="media-preview-dialog"
            :show-close="true"
            :close-on-click-modal="true"
            :close-on-press-escape="true"
            width="80%"
        >
            <!-- 轮播图组件 -->
            <el-carousel 
                v-if="dialogVisible"
                :initial-index="currentIndex"
                height="80vh"
                indicator-position="outside"
                arrow="always"
                @change="handleSlideChange"
            >
                <el-carousel-item v-for="(item, index) in mediaList" :key="index">
                    <!-- 图片预览 -->
                    <img 
                        v-if="isImage(item)" 
                        :src="item.url || item" 
                        :alt="item.alt || ''" 
                        class="preview-img"
                    >
                    <!-- 视频预览 -->
                    <video 
                        v-else-if="isVideo(item)" 
                        :src="item.url || item" 
                        controls 
                        class="preview-video"
                        ref="videoPlayer"
                    >
                        您的浏览器不支持视频播放
                    </video>
                </el-carousel-item>
            </el-carousel>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'MediaPreview',
    props: {
        // 媒体列表
        mediaList: {
            type: Array,
            required: true,
            default: () => []
        },
        // 缩略图宽度
        width: {
            type: [String, Number],
            default: '100px'
        },
        // 缩略图高度
        height: {
            type: [String, Number],
            default: '100px'
        }
    },
    data() {
        return {
            dialogVisible: false,
            currentIndex: 0
        }
    },
    methods: {
        // 判断是否为图片
        isImage(item) {
            const url = item.url || item;
            return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
        },
        // 判断是否为视频
        isVideo(item) {
            const url = item.url || item;
            return /\.(mp4|webm|ogg)$/i.test(url);
        },
        // 处理预览点击
        handlePreview(index) {
            this.currentIndex = index;
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.handleMediaPlay();
            });
        },
        // 处理轮播切换
        handleSlideChange(index) {
            this.currentIndex = index;
            this.handleMediaPlay();
        },
        // 处理媒体播放
        handleMediaPlay() {
            // 暂停所有视频
            if (this.$refs.videoPlayer) {
                const videos = Array.isArray(this.$refs.videoPlayer) 
                    ? this.$refs.videoPlayer 
                    : [this.$refs.videoPlayer];
                    
                videos.forEach(video => {
                    if (video) {
                        video.pause();
                    }
                });
            }
            
            // 播放当前视频
            const currentItem = this.mediaList[this.currentIndex];
            if (this.isVideo(currentItem)) {
                const currentVideo = Array.isArray(this.$refs.videoPlayer) 
                    ? this.$refs.videoPlayer[this.currentIndex]
                    : this.$refs.videoPlayer;
                    
                if (currentVideo) {
                    currentVideo.play();
                }
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (!newVal) {
                // 关闭弹窗时暂停所有视频
                if (this.$refs.videoPlayer) {
                    const videos = Array.isArray(this.$refs.videoPlayer) 
                        ? this.$refs.videoPlayer 
                        : [this.$refs.videoPlayer];
                        
                    videos.forEach(video => {
                        if (video) {
                            video.pause();
                        }
                    });
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.media-preview {
    display: inline-block;

    .media-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .media-thumbnail {
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                transform: scale(1.02);
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }

            .media-img,
            .media-video {
                width: 120px;
                height: 120px;
                object-fit: cover;
            }
        }
    }
}

.media-preview-dialog {
    :deep(.el-dialog__header) {
        padding: 0;
    }

    :deep(.el-dialog__body) {
        padding: 0;
        background: rgba(0, 0, 0, 0.7);
    }

    :deep(.el-carousel__container) {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .preview-img,
    .preview-video {
        max-width: 100%;
        max-height: 80vh;
        object-fit: contain;
    }
}
</style> 