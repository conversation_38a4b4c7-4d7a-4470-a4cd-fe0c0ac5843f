<template>
  <div class="evaluation-tab">
    <el-table v-loading="loading" :data="evaluationList" border style="width: 100%">
      <el-table-column label="评价名称" align="center" prop="evaluationName" />
      <el-table-column label="评价模板" align="center" prop="evaluationTemplateId">
        <template slot-scope="scope">
          <span>{{ scope.row.evaluationTemplateName || '未设置' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评价方式" align="center" prop="evaluationMethod">
        <template slot-scope="scope">
          <dict-tag 
            :options="dict.type.contractore_valuation_method" 
            :value="scope.row.evaluationMethod"
          />
          <!-- 兜底显示：如果字典转换失败，显示名称字段 -->
          <span v-if="!scope.row.evaluationMethod && scope.row.evaluationMethodName">
            {{ scope.row.evaluationMethodName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="评价时间" align="center" prop="createTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下次评价时间" align="center" prop="createTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评价结论" align="center" prop="evaluationConclusion">
        <template slot-scope="scope">
          <el-tag :type="getEvaluationConclusionType(scope.row.evaluationConclusion)">
            {{ getEvaluationConclusionText(scope.row.evaluationConclusion) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="评价人员" align="center" prop="createBy" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listZjContractorEvaluation } from "@/api/contractor/zjContractorEvaluation";

export default {
  name: "EvaluationTab",
  dicts: ["contractore_valuation_method"],
  props: {
    contractorId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      evaluationList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorId: null
      }
    };
  },
  watch: {
    contractorId: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.contractorId = newVal;
          this.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询评价记录列表 */
    getList() {
      if (!this.contractorId) return;
      this.loading = true;
      listZjContractorEvaluation(this.queryParams).then(response => {
        this.evaluationList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.evaluationList = [];
        this.total = 0;
      });
    },

    /** 获取评价结论的标签类型 */
    getEvaluationConclusionType(conclusion) {
      const conclusionStr = String(conclusion || '').trim();
      switch (conclusionStr) {
        case '1':
          return 'success';
        case '2':
          return 'danger';
        default:
          return 'info';
      }
    },
    
    /** 获取评价结论的显示文本 */
    getEvaluationConclusionText(conclusion) {
      const conclusionStr = String(conclusion || '').trim();
      switch (conclusionStr) {
        case '1':
          return '合格';
        case '2':
          return '不合格';
        default:
          return conclusionStr || '未设置';
      }
    }
  }
};
</script>

<style scoped>
.evaluation-tab {
  padding: 20px;
}
</style>
