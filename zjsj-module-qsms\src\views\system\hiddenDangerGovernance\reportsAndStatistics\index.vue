<template>
    <div class="safetyManagement">
      <el-row style="display: flex; width: 100%; height: 100%">
        <el-col :span="6" style="display: flex; flex-direction: column; height: 100%; overflow-y: auto">
          <div class="card_li">
            <!-- 项目周检 -->
            <div class="cardBox">
              <div class="cardBoxTitle">
                <div class="cardTopTitle">
                  <span>项目周检</span>
                </div>
                <div><span class="top_btn" @click="handleOpen('1013001')">详细记录</span><span class="top_btn"
                        @click="handleOpen1('1013001')">隐患记录</span></div>
              </div>
              <div class="cardBoxContent">
                <div class="card_content">
                  <div class="card_tabs">
                    <div class="tabsBox">
                      <div class="tabs_li" :class="{ active: tabsIndex === 1 }" @click.stop="changeTab(1)">当月</div>
                      <div class="tabs_li" :class="{ active: tabsIndex === 2 }" @click.stop="changeTab(2)">累计</div>
                    </div>
                  </div>
                  <div class="card_center">
                    <div><img src="@/assets/safety/weekWrite.png" alt="" /></div>
                    <div class="card_center_r">
                      <div class="data_li">
                        <div class="data_li_top">检查次数</div>
                        <div class="data_li_btm"><span class="btm_value">{{ weeklyData.checkNum }}</span><span class="btm_unit">次</span></div>
                      </div>
                      <div class="data_li">
                        <div class="data_li_top">隐患数量</div>
                        <div class="data_li_btm"><span class="btm_value">{{ weeklyData.dangerNum }}</span><span class="btm_unit">个</span></div>
                      </div>
                      <div class="data_li">
                        <div class="data_li_top">整改率</div>
                        <div class="data_li_btm"><span class="btm_value" style="color: #06B33D;">{{ weeklyData.rectifyRate }}</span><span class="btm_unit">%</span></div>
                      </div>
                    </div>
                  </div>
                  <div class="card_btm">
                    <div class="card_btm_l">
                      <div class="li_name">已整改</div>
                      <div class="card_btm_l_btm"><span class="li_value" style="color: #06B33D;">{{ weeklyData.rectifiedNum }}</span><span class="li_unit">次</span></div>
                    </div>
                    <div class="card_btm_c"><img src="@/assets/safety/weekAlarm.png" alt="" /></div>
                    <div class="card_btm_r">
                      <div class="li_name">未整改</div>
                      <div class="card_btm_r_ctr">
                        <span class="li_value" style="color: #0974FF;">{{ weeklyData.unrectifiedNum }}</span><span class="li_unit">次</span>
                      </div>
                      <div class="card_btm_r_btm">
                        <span class="li_unit">超期未整改</span><span style="color: #FF5709;font-size: 20px;margin-left: 4px;">{{ weeklyData.timeoutUnrectifiedNum }}</span><span class="li_unit">次</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card_li">
            <!-- 检查频次 -->
            <div class="cardBox">
              <div class="cardBoxTitle">
                <div class="cardTopTitle">
                  <span>检查频次</span>
                </div>
              </div>
              <div class="cardBoxContent">
                <div class="card_content">
                  <div class="date">
                    <el-date-picker v-model="selectedYear" type="year" value-format="yyyy" placeholder="选择年"
                        style="width: 100px" @change="changeYear" />
                  </div>
                  <div class="echarts" ref="inspectChart"></div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12" style="display: flex; flex-direction: column; height: 100%; overflow-y: auto">
          <div class="card_li card_center">
            <!-- 检查中心 -->
            <div class="check_center">
              <div class="center_img"><img src="@/assets/safety/gridCenter.png" alt="" /></div>
              <div v-for="(item, index) in centerData" :key="index" class="img_li" :class="['img_li' + index]">
                <div class="li_top"><span class="li_top_value">{{ item.value }}</span><span class="li_top_unit">{{ item.unit }}</span></div>
                <div class="li_center">{{ item.name }}</div>
                <div class="li_btm"><img :src="item.imgName" alt="" /></div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6" style="display: flex; flex-direction: column; height: 100%; overflow-y: auto">
          <div class="card_li">
            <!-- 隐患分类分析 -->
            <div class="cardBox">
              <div class="cardBoxTitle">
                <div class="cardTopTitle">
                  <span>隐患分类分析</span>
                </div>
              </div>
              <div class="cardBoxContent">
                <div class="card_content">
                  <div class="echarts" ref="dangerTypeChart"></div>
                  <!-- <img class="pie" src="@/assets/generalize/pieBackground.png" alt="" /> -->
                </div>
              </div>
            </div>
          </div>
          <div class="card_li">
            <!-- 隐患区域分析 -->
            <div class="cardBox">
              <div class="cardBoxTitle">
                <div class="cardTopTitle">
                  <span>隐患区域分析</span>
                </div>
              </div>
              <div class="cardBoxContent">
                <div class="card_content">
                  <div class="echarts" ref="dangerAreaChart"></div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </template>
  
  <script>
  import * as echarts from "echarts";
  import gridImg1 from '@/assets/safety/gridImg1.png'
  import gridImg2 from '@/assets/safety/gridImg2.png'
  import gridImg3 from '@/assets/safety/gridImg3.png'
  import gridImg4 from '@/assets/safety/gridImg4.png'
  
  export default {
    name: 'SafetyLogStatic',
    data() {
      return {
        // Tab切换
        tabsIndex: 1,
        
        // 选择年份
        selectedYear: '2024',
        
        // 项目周检数据
        weeklyData: {
          checkNum: 12,
          dangerNum: 8,
          rectifyRate: '87.50',
          rectifiedNum: 7,
          unrectifiedNum: 1,
          timeoutUnrectifiedNum: 0
        },
        
        // 检查中心数据
        centerData: [
          { name: '隐患数量', value: 8, unit: '个', imgName: gridImg1 },
          { name: '项目周检', value: 12, unit: '个', imgName: gridImg2 },
          { name: '已整改', value: 7, unit: '个', imgName: gridImg3 },
          { name: '整改率', value: '87.50', unit: '%', imgName: gridImg4 },
        ],
        
        // 检查频次模拟数据
        inspectFrequencyData: [
          { month: '01', checkNum: 5 },
          { month: '02', checkNum: 8 },
          { month: '03', checkNum: 12 },
          { month: '04', checkNum: 6 },
          { month: '05', checkNum: 9 },
          { month: '06', checkNum: 15 },
          { month: '07', checkNum: 18 },
          { month: '08', checkNum: 22 },
          { month: '09', checkNum: 16 },
          { month: '10', checkNum: 12 },
          { month: '11', checkNum: 8 },
          { month: '12', checkNum: 5 }
        ],
        
        // 隐患分类模拟数据
        dangerTypeData: [
          { name: '高空作业', value: 25, color: '#08D97E' },
          { name: '临时用电', value: 18, color: '#3CC3DF' },
          { name: '机械设备', value: 12, color: '#FF928A' }
        ],
        
        // 隐患区域模拟数据
        dangerAreaData: [
          { areaName: '施工区域A', dangerNum: 15 },
          { areaName: '施工区域B', dangerNum: 12 },
          { areaName: '施工区域C', dangerNum: 8 },
          { areaName: '施工区域D', dangerNum: 20 },
          { areaName: '施工区域E', dangerNum: 6 }
        ],
        
        // 图表实例
        inspectChartInstance: null,
        dangerTypeChartInstance: null,
        dangerAreaChartInstance: null
      }
    },
    
    mounted() {
      var self = this;
      this.$nextTick(function() {
        self.initInspectChart();
        self.initDangerTypeChart();
        self.initDangerAreaChart();
      });
    },
    
    methods: {
      // 事件处理
      changeTab: function(index) {
        this.tabsIndex = index;
        if (index === 1) {
          // 当月数据
          this.weeklyData = {
            checkNum: 12,
            dangerNum: 8,
            rectifyRate: '87.50',
            rectifiedNum: 7,
            unrectifiedNum: 1,
            timeoutUnrectifiedNum: 0
          };
        } else {
          // 累计数据
          this.weeklyData = {
            checkNum: 48,
            dangerNum: 32,
            rectifyRate: '84.38',
            rectifiedNum: 27,
            unrectifiedNum: 5,
            timeoutUnrectifiedNum: 2
          };
        }
        this.updateCenterData();
      },
      
      changeYear: function() {
        this.initInspectChart();
      },
      
      handleOpen: function(type) {
        console.log('打开详细记录', type);
      },
      
      handleOpen1: function(type) {
        console.log('打开隐患记录', type);
      },
      
      // 更新中心数据
      updateCenterData: function() {
        this.centerData = [
          { name: '隐患数量', value: this.weeklyData.dangerNum, unit: '个', imgName: gridImg1 },
          { name: '项目周检', value: this.weeklyData.checkNum, unit: '个', imgName: gridImg2 },
          { name: '已整改', value: this.weeklyData.rectifiedNum, unit: '个', imgName: gridImg3 },
          { name: '整改率', value: this.weeklyData.rectifyRate, unit: '%', imgName: gridImg4 },
        ];
      },
      
      // 初始化检查频次图表
      initInspectChart: function() {
        if (!this.$refs.inspectChart) return;
        
        if (this.inspectChartInstance) {
          this.inspectChartInstance.dispose();
        }
        
        this.inspectChartInstance = echarts.init(this.$refs.inspectChart);
        var xData = this.inspectFrequencyData.map(function(item) { return item.month; });
        var yData = this.inspectFrequencyData.map(function(item) { return item.checkNum; });
        
        var options = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params) {
              var param = params[0];
              return param.name + '月<br/>' +
                     '<div class="flex jc-space-between">' +
                     '<span>' + param.marker + param.seriesName + ' : &nbsp;&nbsp;</span>' +
                     '<span>' + param.value + '&nbsp;次</span>' +
                     '</div>';
            }
          },
          legend: {
            show: false,
          },
          grid: {
            left: "2%",
            right: "2%",
            top: "10%",
            bottom: "0%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            axisTick: {
              show: false,
            },
            data: xData,
            axisLabel: {
              fontSize: 12,
            },
          },
          yAxis: {
            type: "value",
            splitLine: {
              lineStyle: {
                type: "dashed",
              },
            },
            axisLabel: {
              fontSize: 12,
            },
          },
          series: [
            {
              name: '检查次数',
              z: 2,
              type: 'pictorialBar',
              symbolPosition: 'end',
              data: yData,
              symbol: 'diamond',
              symbolOffset: [0, '-50%'],
              symbolSize: [29, 10],
              itemStyle: {
                borderColor: '#0974FF',
                color: '#0974FF'
              },
            },
            {
              z: 1,
              type: 'bar',
              barWidth: 30,
              barGap: '-50%',
              data: yData,
              itemStyle: {
                color: {
                  type: 'linear',
                  x: 0, x2: 1, y: 0, y2: 0,
                  colorStops: [
                    { offset: 0, color: 'rgb(157, 199, 255, .7)' },
                    { offset: 0.5, color: 'rgb(157, 199, 255, .7)' },
                    { offset: 0.5, color: 'rgb(9, 116, 255, .7)' },
                    { offset: 1, color: 'rgb(9, 116, 255, .7)' }
                  ]
                }
              },
              showBackground: true,
              backgroundStyle: {
                color: 'rgba(214,219,237,0.4)'
              }
            }
          ]
        };
        
        this.inspectChartInstance.setOption(options);
      },
      
      // 初始化隐患分类图表
      initDangerTypeChart: function() {
        if (!this.$refs.dangerTypeChart) return;
        
        if (this.dangerTypeChartInstance) {
          this.dangerTypeChartInstance.dispose();
        }
        
        this.dangerTypeChartInstance = echarts.init(this.$refs.dangerTypeChart);
        var totalAmount = this.dangerTypeData.reduce(function(acc, item) { return acc + item.value; }, 0);
        var seriesData = this.dangerTypeData.map(function(item) {
          return {
            name: item.name,
            value: item.value
          };
        });
        var self = this;
        
        var options = {
          color: ['#08D97E', '#3CC3DF', '#FF928A'],
          title: [
            {
              text: '隐患总数',
              left: 'center',
              top: '40%',
              textStyle: {
                fontSize: 16,
                color: '#101419'
              }
            },
            {
              text: totalAmount.toString(),
              right: '49%',
              top: '30%',
              textStyle: {
                fontSize: 24,
                fontWeight: 'bold',
                color: '#33373B'
              }
            },
            {
              text: '个',
              left: '51%',
              top: '32%',
              textStyle: {
                fontSize: 14,
                color: '#999B9D'
              }
            }
          ],
          tooltip: {
            trigger: 'item',
            confine: true,
            formatter: function (params) {
              return '<div>' +
                     '<span>' + params.marker + params.name + '&nbsp;&nbsp;</span>' +
                     '<span style="color: ' + params.color + '; font-weight: 600">' +
                     params.value + '&nbsp;&nbsp;</span>' +
                     '<span>' + parseFloat(params.percent).toFixed(2) + '%</span>' +
                     '</div>';
            }
          },
          legend: {
            icon: 'circle',
            itemGap: 36,
            itemWidth: 8,
            left: 'center',
            bottom: '0',
            data: this.dangerTypeData.map(function(item) { return item.name; }),
            textStyle: {
              fontSize: 16
            },
            formatter: function(name) {
              var item = self.dangerTypeData.find(function(d) { return d.name === name; });
              var percent = item ? ((item.value / totalAmount) * 100).toFixed(2) : '0.00';
              return name + '\n\n  ' + percent + '%';
            }
          },
          series: [
            {
              name: '隐患分类',
              type: 'pie',
              radius: ['38%', '52%'],
              center: ['50%', '40%'],
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              },
              data: seriesData
            }
          ]
        };
        
        this.dangerTypeChartInstance.setOption(options);
      },
      
      // 初始化隐患区域图表
      initDangerAreaChart: function() {
        if (!this.$refs.dangerAreaChart) return;
        
        if (this.dangerAreaChartInstance) {
          this.dangerAreaChartInstance.dispose();
        }
        
        this.dangerAreaChartInstance = echarts.init(this.$refs.dangerAreaChart);
        var xData = this.dangerAreaData.map(function(item) { return item.areaName; });
        var yData = this.dangerAreaData.map(function(item) { return item.dangerNum; });
        var maxValue = Math.max.apply(Math, yData);
        var maxData = yData.map(function() { return maxValue; });
        
        var options = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params) {
              var param = params[0];
              return param.name + '<br/>' +
                     '<div class="flex jc-space-between">' +
                     '<span>' + param.marker + param.seriesName + ' : &nbsp;&nbsp;</span>' +
                     '<span>' + param.value + '&nbsp;个</span>' +
                     '</div>';
            }
          },
          legend: {
            show: false,
          },
          grid: {
            left: "2%",
            right: "2%",
            top: "10%",
            bottom: "0%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            axisTick: {
              show: false,
            },
            data: xData,
            axisLabel: {
              fontSize: 12,
            },
          },
          yAxis: {
            type: "value",
            splitLine: {
              lineStyle: {
                type: "dashed",
              },
            },
            axisLabel: {
              fontSize: 12,
            },
          },
          series: [
            {
              name: "隐患数量",
              type: "pictorialBar",
              stack: "数量",
              barMaxWidth: 60,
              itemStyle: {
                normal: {
                  borderWidth: 2,
                  borderColor: '#2d858a',
                  color: {
                    type: "linear",
                    x: 0, x2: 1, y: 0, y2: 0,
                    colorStops: [
                      { offset: 0, color: '#ACA1FF' },
                      { offset: 0.5, color: '#ACA1FF' },
                      { offset: 0.5, color: '#8979FF' },
                      { offset: 1, color: '#8979FF' }
                    ],
                    globalCoord: false,
                  },
                },
              },
              symbol: "path://M48 0 L49 17 C52 72 68 125 96 173 H0 C25 125 41 73 46 19 L48 0Z",
              data: yData,
              showBackground: true,
              backgroundStyle: {
                color: 'rgba(214,219,237,0.4)'
              },
              z: 10
            },
            {
              name: "hill",
              type: "bar",
              barWidth: '20%',
              symbol: "path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z",
              itemStyle: {
                normal: {
                  color: 'rgba(214,219,237,0.4)'
                }
              },
              data: maxData,
              z: 9
            }
          ]
        };
        
        this.dangerAreaChartInstance.setOption(options);
      }
    },
    
    beforeDestroy: function() {
      // 销毁图表实例
      if (this.inspectChartInstance) {
        this.inspectChartInstance.dispose();
      }
      if (this.dangerTypeChartInstance) {
        this.dangerTypeChartInstance.dispose();
      }
      if (this.dangerAreaChartInstance) {
        this.dangerAreaChartInstance.dispose();
      }
    }
  }
  </script>
  
  <style scoped lang="scss">
  .safetyManagement {
    width: 100%;
    height: 100%;
    display: flex;
    padding: 0 20px 20px 20px;
    box-sizing: border-box;
  
    .card_li {
      flex: 1;
      height: 2px;
      min-height: 300px;
      margin-bottom: 10px;
  
      &:last-child {
        margin-bottom: 0;
      }
    }
  
    .card_center {
      min-height: 600px;
      margin: 0 10px;
    }
  }
  
  // CardBox样式
  .cardBox {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    box-shadow: 0px 2px 6px 0px rgba(0, 82, 217, 0.15);
    border-radius: 4px;
  
    .cardBoxTitle {
      height: 62px;
      font-family: PingFang SC, PingFang SC;
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding: 12px 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
  
      .cardTopTitle {
        display: flex;
        align-items: center;
        line-height: 16px;
  
        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background-color: #0974FF;
          margin-right: 12px;
        }
      }
    }
  
    .cardBoxContent {
      padding: 0 16px 16px 16px;
      box-sizing: border-box;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
  }
  
  // TabBackRadius样式
  .tabsBox {
    display: flex;
    align-items: center;
    background: #F5F7F9;
    box-shadow: inset 0px -1px 1px 0px #FFFFFF, inset 0px 1px 1px 0px rgba(17, 35, 58, 0.15);
    border-radius: 23px 23px 23px 23px;
  
    .tabs_li {
      font-size: 16px;
      color: #33373B;
      padding: 2px 16px;
      box-sizing: border-box;
      cursor: pointer;
    }
  
    .active {
      color: #FFFFFF;
      background: #0974FF;
      box-shadow: 0px 1px 1px 0px rgba(10, 24, 42, 0.2);
      border-radius: 20px 20px 20px 20px;
    }
  }
  
  // 项目周检样式
  .top_btn {
    background: #F5F7F9;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #EBEBEB;
    font-size: 16px;
    color: #0974FF;
    padding: 4px 8px;
    box-sizing: border-box;
    margin-right: 8px;
    cursor: pointer;
  
    &:last-child {
      margin-right: 16px;
    }
  }
  
  .card_content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: auto;
  
    .card_tabs {
      display: flex;
      justify-content: center;
    }
  
    .card_center {
      background: linear-gradient(90deg, #FFFFFF 0%, #F5F7F9 100%), #F5F7F9;
      border-radius: 0px 0px 0px 0px;
      display: flex;
  
      .card_center_r {
        flex: 1;
        display: flex;
        justify-content: space-between;
        padding: 8px 16px;
        box-sizing: border-box;
  
        .data_li {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
  
          .data_li_top {
            font-size: 18px;
            color: #33373B;
          }
  
          .data_li_btm {
            .btm_value {
              font-weight: bold;
              font-size: 24px;
              color: #33373B;
            }
  
            .btm_unit {
              margin-left: 4px;
              font-size: 16px;
              color: #999B9D;
            }
          }
        }
      }
    }
  
    .card_btm {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
  
      .card_btm_l, .card_btm_r {
        display: flex;
        flex-direction: column;
        align-items: center;
  
        .li_name {
          font-size: 16px;
          color: #33373B;
          margin-bottom: 8px;
        }
  
        .card_btm_l_btm, .card_btm_r_ctr {
          .li_value {
            font-weight: bold;
            font-size: 20px;
          }
  
          .li_unit {
            margin-left: 4px;
            font-size: 14px;
            color: #999B9D;
          }
        }
  
        .card_btm_r_btm {
          margin-top: 4px;
          font-size: 12px;
          color: #999B9D;
        }
      }
  
      .card_btm_c {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  
    // 检查频次样式
    .date {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 10px;
    }
  
    .echarts {
      flex: 1;
      height: 250px;
      min-height: 200px;
    }
  
    // 隐患分类分析样式
    .pie {
      height: 80%;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
    }
  }
  
  // 检查中心样式
  .check_center {
    width: 100%;
    height: 100%;
    display: flex;
    overflow: auto;
    background: url('~@/assets/safety/gridBackground.png') 0% 0% / 100% 100%;
    position: relative;
  
    .center_img {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -30%);
    }
  
    .img_li {
      position: absolute;
      display: flex;
      flex-direction: column;
      justify-content: center;
  
      .li_top {
        display: flex;
        align-items: center;
        justify-content: center;
  
        .li_top_value {
          font-weight: bold;
          font-size: 28px;
          color: #0974FF;
        }
  
        .li_top_unit {
          font-size: 16px;
          color: #999B9D;
          margin-left: 4px;
        }
      }
  
      .li_center {
        display: flex;
        justify-content: center;
        font-size: 18px;
        color: #33373B;
      }
    }
  
    .img_li0 {
      left: 8%;
      top: 42%;
    }
  
    .img_li1 {
      left: 25%;
      top: 10%;
    }
  
    .img_li2 {
      right: 25%;
      top: 10%;
    }
  
    .img_li3 {
      right: 7%;
      top: 42%;
    }
  }
  </style>