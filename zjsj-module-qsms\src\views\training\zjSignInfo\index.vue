<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="学员姓名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入学员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="身份证号" prop="identityno">
        <el-input v-model="queryParams.identityno" placeholder="请输入身份证号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item label="企业名称" prop="entpname">
        <el-input
          v-model="queryParams.entpname"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="是否收费" prop="ispaid">
        <el-select v-model="queryParams.ispaid" placeholder="请选择是否收费" clearable>
          <el-option label="否" value="0" />
          <el-option label="是" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="培训计划" prop="suitename">
        <el-input v-model="queryParams.suitename" placeholder="请输入培训计划" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="培训计划编号" prop="suiteid">
        <el-input v-model="queryParams.suiteid" placeholder="请输入培训计划编号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="报名时间" prop="createdate">
        <el-date-picker v-model="queryParams.createdate" clearable type="date" value-format="yyyy-MM-dd"
          placeholder="请选择报名时间" />
      </el-form-item>
      <el-form-item label="收费时间" prop="paytime">
        <el-date-picker v-model="queryParams.paytime" clearable type="date" value-format="yyyy-MM-dd"
          placeholder="请选择收费时间" />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjSignInfo:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
    
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjSignInfo:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleBatchDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjSignInfo:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjSignInfoList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 230px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="学员姓名" width="105" align="center" prop="username" />
      <el-table-column label="身份证号" align="center" prop="identityno" />
      <el-table-column label="企业名称" align="center" prop="entpname" />
      <el-table-column label="是否收费" align="center" prop="ispaid">
        <template slot-scope="scope">
          {{
            scope.row.ispaid == 0
              ? "否"
              : scope.row.ispaid == 1
              ? "是"
              : scope.row.ispaid
          }}
        </template>
      </el-table-column>
      <el-table-column label="培训计划" align="center" prop="suitename" width="190" />
      <!-- <el-table-column label="培训计划编号" align="center" prop="suiteid" /> -->
      <el-table-column
        label="报名时间"
        align="center"
        prop="createdate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="收费时间"
        align="center"
        prop="paytime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paytime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['training:zjSignInfo:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['training:zjSignInfo:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改培训学员报名信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="学员姓名" prop="username">
          <el-input v-model="form.username" placeholder="请输入学员姓名" />
        </el-form-item>
        <el-form-item label="身份证号" prop="identityno">
          <el-input v-model="form.identityno" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="企业名称" prop="entpname">
          <el-input v-model="form.entpname" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="是否收费" prop="ispaid">
          <el-select v-model="form.ispaid" placeholder="请选择是否收费">
            <el-option label="否" value="0" />
            <el-option label="是" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="培训计划" prop="suitename">
          <el-input v-model="form.suitename" placeholder="请输入培训计划" />
        </el-form-item>
        <el-form-item label="报名时间" prop="createdate">
          <el-date-picker
            v-model="form.createdate"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择报名时间"
          />
        </el-form-item>
        <el-form-item label="收费时间" prop="paytime">
          <el-date-picker
            v-model="form.paytime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择收费时间"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjSignInfo,
  getZjSignInfo,
  delZjSignInfo,
  addZjSignInfo,
  updateZjSignInfo,
} from "@/api/training/zjSignInfo";

export default {
  name: "ZjSignInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 培训学员报名信息表格数据
      zjSignInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: null,
        identityno: null,
        entpname: null,
        ispaid: null,
        suitename: null,
        createdate: null,
        paytime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        username: [
          { required: true, message: "学员姓名不能为空", trigger: "blur" },
        ],
        identityno: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
        ],
        entpname: [
          { required: true, message: "企业名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询培训学员报名信息列表 */
    getList() {
      this.loading = true;
      listZjSignInfo(this.queryParams).then((response) => {
        this.zjSignInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        username: null,
        identityno: null,
        entpname: null,
        ispaid: null,
        suitename: null,
        createdate: null,
        paytime: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加培训学员报名信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjSignInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改培训学员报名信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjSignInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjSignInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 批量删除
    handleBatchDelete() {
      const ids = this.ids;
      this.$modal
        .confirm('是否确认删除' + this.ids.length + '条培训学员报名信息的数据项？')
        .then(function () {
          return delZjSignInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除培训学员报名信息"' + row.username + '"的数据项？')
        .then(function () {
          return delZjSignInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 构建导出参数
      let exportParams = {};

      // 如果有选中的数据，使用选中的ids导出
      if (this.ids && this.ids.length > 0) {
        exportParams.ids = this.ids;
      } else {
        // 如果没有选中数据，导出所有符合查询条件的数据（不需要分页参数）
        exportParams = {};
      }

      this.download(
        "training/zjSignInfo/export",
        exportParams,
        `zjSignInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
