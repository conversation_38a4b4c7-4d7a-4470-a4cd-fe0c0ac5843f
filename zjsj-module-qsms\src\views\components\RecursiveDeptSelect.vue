<template>
  <el-option-group v-if="dept.children && dept.children.length">
    <el-option :label="dept.deptName" :value="dept.deptId" />
    <recursive-dept-select
      v-for="child in dept.children"
      :key="child.deptId"
      :dept="child"
    />
  </el-option-group>
  <el-option v-else :label="dept.deptName" :value="dept.deptId" />
</template>

<script>
export default {
  name: "RecursiveDeptSelect",
  props: {
    dept: {
      type: Object,
      required: true,
    },
  },
};
</script>
