<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="资质信息" prop="qualificationName">
        <el-input
          v-model="queryParams.qualificationName"
          placeholder="请输入资质信息"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资质编号" prop="qualificationNumber">
        <el-input
          v-model="queryParams.qualificationNumber"
          placeholder="请输入资质编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资质有效期开始" prop="expirationBeginDate">
        <el-date-picker
          clearable
          v-model="queryParams.expirationBeginDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择资质有效期开始"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="资质有效期结束" prop="expirationEndDate">
        <el-date-picker
          clearable
          v-model="queryParams.expirationEndDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择资质有效期结束"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="资质等级" prop="qualificationLevel">
        <el-input
          v-model="queryParams.qualificationLevel"
          placeholder="请输入资质等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="附件" prop="attachmentUrl">
        <el-input
          v-model="queryParams.attachmentUrl"
          placeholder="请输入附件"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="承包商id" prop="contractorId">
        <el-input
          v-model="queryParams.contractorId"
          placeholder="请输入承包商id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['contractor:zjContractorQualificationInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['contractor:zjContractorQualificationInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['contractor:zjContractorQualificationInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['contractor:zjContractorQualificationInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjContractorQualificationInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column
        label="资质信息"
        align="center"
        prop="qualificationName"
      />
      <el-table-column
        label="资质编号"
        align="center"
        prop="qualificationNumber"
      />
      <el-table-column
        label="资质有效期开始"
        align="center"
        prop="expirationBeginDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.expirationBeginDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="资质有效期结束"
        align="center"
        prop="expirationEndDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.expirationEndDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="资质等级"
        align="center"
        prop="qualificationLevel"
      />
      <el-table-column
        label="资质类型 1-安全生产许可证 2-安全生产标准化证书3-其他"
        align="center"
        prop="qualificationType"
      />
      <el-table-column label="附件" align="center" prop="attachmentUrl" />
      <el-table-column label="承包商id" align="center" prop="contractorId" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['contractor:zjContractorQualificationInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['contractor:zjContractorQualificationInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改承包商资质信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="资质信息" prop="qualificationName">
          <el-input
            v-model="form.qualificationName"
            placeholder="请输入资质信息"
          />
        </el-form-item>
        <el-form-item label="资质编号" prop="qualificationNumber">
          <el-input
            v-model="form.qualificationNumber"
            placeholder="请输入资质编号"
          />
        </el-form-item>
        <el-form-item label="资质有效期开始" prop="expirationBeginDate">
          <el-date-picker
            clearable
            v-model="form.expirationBeginDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择资质有效期开始"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="资质有效期结束" prop="expirationEndDate">
          <el-date-picker
            clearable
            v-model="form.expirationEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择资质有效期结束"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="资质等级" prop="qualificationLevel">
          <el-input
            v-model="form.qualificationLevel"
            placeholder="请输入资质等级"
          />
        </el-form-item>
        <el-form-item label="附件" prop="attachmentUrl">
          <el-input v-model="form.attachmentUrl" placeholder="请输入附件" />
        </el-form-item>
        <el-form-item label="承包商id" prop="contractorId">
          <el-input v-model="form.contractorId" placeholder="请输入承包商id" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjContractorQualificationInfo,
  getZjContractorQualificationInfo,
  delZjContractorQualificationInfo,
  addZjContractorQualificationInfo,
  updateZjContractorQualificationInfo,
} from "@/api/contractor/zjContractorQualificationInfo";

export default {
  name: "ZjContractorQualificationInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 承包商资质信息表格数据
      zjContractorQualificationInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        qualificationName: null,
        qualificationNumber: null,
        expirationBeginDate: null,
        expirationEndDate: null,
        qualificationLevel: null,
        qualificationType: null,
        attachmentUrl: null,
        contractorId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询承包商资质信息列表 */
    getList() {
      this.loading = true;
      listZjContractorQualificationInfo(this.queryParams).then((response) => {
        this.zjContractorQualificationInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        qualificationName: null,
        qualificationNumber: null,
        expirationBeginDate: null,
        expirationEndDate: null,
        qualificationLevel: null,
        qualificationType: null,
        attachmentUrl: null,
        contractorId: null,
        createTime: null,
        createBy: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加承包商资质信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjContractorQualificationInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改承包商资质信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjContractorQualificationInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjContractorQualificationInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除承包商资质信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjContractorQualificationInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "contractor/zjContractorQualificationInfo/export",
        {
          ...this.queryParams,
        },
        `zjContractorQualificationInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
