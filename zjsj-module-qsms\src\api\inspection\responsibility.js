import request from "@/utils/request";

// 查询安全生产责任制列表
export function listResponsibility(query) {
  return request({
    url: "/inspection/responsibility/list",
    method: "get",
    params: query,
  });
}

// 查询安全生产责任制详细
export function getResponsibility(id) {
  return request({
    url: "/inspection/responsibility/" + id,
    method: "get",
  });
}

// 新增安全生产责任制
export function addResponsibility(data) {
  return request({
    url: "/inspection/responsibility",
    method: "post",
    data: data,
  });
}

// 修改安全生产责任制
export function updateResponsibility(data) {
  return request({
    url: "/inspection/responsibility",
    method: "put",
    data: data,
  });
}

// 删除安全生产责任制
export function delResponsibility(id) {
  return request({
    url: "/inspection/responsibility/" + id,
    method: "delete",
  });
}
// 责任公司 inspection/responsibility/getSubjectNamelist
export function getSubjectNamelist() {
  return request({
    url: "/inspection/responsibility/getSubjectNamelist",
    method: "get",
  });
}

// 责任年度  inspection/responsibility/getResponsibilityYearlist
export function getResponsibilityYearlist() {
  return request({
    url: "/inspection/responsibility/getResponsibilityYearlist",
    method: "get",
  });
}
