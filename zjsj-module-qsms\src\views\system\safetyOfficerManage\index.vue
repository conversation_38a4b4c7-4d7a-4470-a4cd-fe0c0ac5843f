<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="用户" prop="userId">
        <el-select
          v-model="queryParams.userId"
          placeholder="请选择用户"
          style="width: 100%"
          filterable
          clearable
          :filter-method="handleFilter"
          @visible-change="handleVisibleChange"
          :disabled="isCheck"
        >
          <el-option
            v-for="item in userList"
            :key="item.userId"
            :label="item.nickName"
            :value="item.userId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="负责的项目" prop="projectName">
        <selectPeopleTree
          v-model="queryParams.projectName"
          :people-list="projectList"
          placeholder="请选择所在项目"
          @change="handleSearchProjectChange"
        />
      </el-form-item>
      <!-- <el-form-item label="任命日期" prop="appointmentDate">
        <el-date-picker
          clearable
          v-model="queryParams.appointmentDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择任命日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="任命人ID (关联用户表)" prop="appointorId">
        <el-input
          v-model="queryParams.appointorId"
          placeholder="请输入任命人ID (关联用户表)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:officers:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:officers:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:officers:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="officersList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="用户" align="center" prop="nickName" />
      <el-table-column label="负责的项目" align="center" prop="projectName" />
      <!-- <el-table-column
        label="具体职责描述"
        align="center"
        prop="responsibility"
      /> -->
      <el-table-column
        label="任命日期"
        align="center"
        prop="appointmentDate"
        width="180"
      >
      </el-table-column>
      <el-table-column
        label="任职状态"
        align="center"
        prop="status"
        :formatter="formatStatus"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:officers:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:officers:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目安全员对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="140px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-form-item label="用户" prop="userId">
          <el-select
            v-model="form.userId"
            placeholder="请选择用户"
            style="width: 100%"
            filterable
            clearable
            :filter-method="handleAddFilter"
            @visible-change="handleAddVisibleChange"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in addUserList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负责的项目" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择所在项目"
            @change="handleAddSearchProjectChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="具体职责描述" prop="responsibility">
          <el-input
            v-model="form.responsibility"
            type="textarea"
            placeholder="请输入具体职责描述"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="任命日期" prop="appointmentDate">
          <el-date-picker
            clearable
            v-model="form.appointmentDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择任命日期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="任职状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择用户"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="任命人ID (关联用户表)" prop="appointorId">
          <el-input
            v-model="form.appointorId"
            placeholder="请输入任命人ID (关联用户表)"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listOfficers,
  getOfficers,
  delOfficers,
  addOfficers,
  updateOfficers,
} from "@/api/system/safetyOfficerManage/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { getFileOrignalName } from "@/utils/common.js";
import { listUser } from "@/api/system/user";
import { querytree } from "@/api/system/info";
export default {
  name: "Officers",
  components: {
    selectPeopleTree,
  },
  data() {
    return {
      isCheck: false,
      selectedValue: this.value,
      allOptions: [], // 备份所有选项
      isSearching: false, // 是否正在搜索
      isAddSearching: false, // 是否正在搜索
      showCount: 500, // 初始默认显示的条数
      userList: [],
      addUserList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      projectList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目安全员表格数据
      officersList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        projectId: null,
        projectName: null,
        responsibility: null,
        appointmentDate: null,
        status: null,
        appointorId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userId: [
          {
            required: true,
            message: "用户",
            trigger: "blur",
          },
        ],
        projectName: [
          {
            required: true,
            message: "负责的项目",
            trigger: "blur",
          },
        ],
        appointmentDate: [
          { required: true, message: "任命日期不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "任职状态不能为空", trigger: "change" },
        ],
        appointorId: [
          {
            required: true,
            message: "任命人不能为空",
            trigger: "blur",
          },
        ],
      },
      statusOptions: [
        {
          label: "已卸任",
          value: 0,
        },
        {
          label: "任职中",
          value: 1,
        },
      ],
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    this.getUserList();
  },
  methods: {
    formatStatus(row, column, value) {
      const option = this.statusOptions.find((item) => item.value == value);
      return option ? option.label : value;
    },
    handleFilter(query) {
      this.isSearching = true;
      if (!query) {
        this.userList = this.allOptions.slice(0, this.showCount);
      } else {
        const lowerQuery = query.toString().toLowerCase();
        this.userList = this.allOptions.filter((item) =>
          item.nickName.toLowerCase().includes(lowerQuery)
        );
      }
    },
    handleVisibleChange(visible) {
      if (!visible) {
        this.isSearching = false;
        this.userList = this.allOptions.slice(0, this.showCount);
      }
    },
    handleAddFilter(query) {
      this.isAddSearching = true;
      if (!query) {
        this.addUserList = this.allOptions.slice(0, this.showCount);
      } else {
        const lowerQuery = query.toString().toLowerCase();
        this.addUserList = this.allOptions.filter((item) =>
          item.nickName.toLowerCase().includes(lowerQuery)
        );
      }
    },
    handleAddVisibleChange(visible) {
      if (!visible) {
        this.isAddSearching = false;
        this.addUserList = this.allOptions.slice(0, this.showCount);
      }
    },
    getUserList() {
      listUser({
        pageNum: 1,
        pageSize: 9999999,
      }).then((response) => {
        let tempUserList = response.rows;
        this.allOptions = tempUserList.map((item) => {
          return {
            userId: item.userId,
            userName: item.userName,
            nickName: item.nickName,
          };
        });
        this.userList = this.allOptions.slice(0, this.showCount);
        this.addUserList = this.allOptions.slice(0, this.showCount);
      });
    },
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    handleSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectId = selectedItem.id;
        this.queryParams.projectName = selectedItem.label;
      } else {
        this.queryParams.projectId = null;
        this.queryParams.projectName = null;
      }
    },
    handleAddSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectId = selectedItem.id;
        this.form.projectName = selectedItem.label;
      } else {
        this.form.projectId = null;
        this.form.projectName = null;
      }
    },
    /** 查询项目安全员列表 */
    getList() {
      this.loading = true;
      listOfficers(this.queryParams).then((response) => {
        this.officersList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        userName: null,
        projectId: null,
        projectName: null,
        responsibility: null,
        appointmentDate: null,
        status: null,
        appointorId: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.projectId = null;
      this.queryParams.userId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加项目安全员";
    },
    /** 修改按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getOfficers(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "修改项目安全员";
      });
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getOfficers(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改项目安全员";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateOfficers(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOfficers(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除项目安全员编号为"' + ids + '"的数据项？')
        .then(function () {
          return delOfficers(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/officers/export",
        {
          ...this.queryParams,
        },
        `安全员管理.xlsx`
      );
    },
  },
};
</script>
