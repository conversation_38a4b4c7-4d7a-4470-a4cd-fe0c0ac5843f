<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item label="演练名称" prop="drillName">
        <el-input v-model="queryParams.drillName" placeholder="请输入演练名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="演练地点" prop="planLocation">
        <el-input v-model="queryParams.planLocation" placeholder="请输入演练地点" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="计划组织部门" prop="planOrganizer" label-width="120px">
        <el-input v-model="queryParams.planOrganizer" placeholder="请输入计划组织部门" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="计划开始时间" prop="planStartTime">
        <el-date-picker
          clearable
          v-model="queryParams.planStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划结束时间" prop="planEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.planEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="演练地点" prop="planLocation">
        <el-input
          v-model="queryParams.planLocation"
          placeholder="请输入演练地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划参与人数" prop="planParticipants">
        <el-input
          v-model="queryParams.planParticipants"
          placeholder="请输入计划参与人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="演练负责人" prop="planLeader">
        <el-input
          v-model="queryParams.planLeader"
          placeholder="请输入演练负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实际开始时间" prop="actualStartTime">
        <el-date-picker
          clearable
          v-model="queryParams.actualStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择实际开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="实际结束时间" prop="actualEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.actualEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择实际结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="实际参与人数" prop="actualParticipants">
        <el-input
          v-model="queryParams.actualParticipants"
          placeholder="请输入实际参与人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估人" prop="evaluator">
        <el-input
          v-model="queryParams.evaluator"
          placeholder="请输入评估人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估时间" prop="evaluationTime">
        <el-date-picker
          clearable
          v-model="queryParams.evaluationTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择评估时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:management:add']" type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:management:edit']" type="success" plain icon="el-icon-edit" size="mini"
          :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:management:remove']" type="danger" plain icon="el-icon-delete" size="mini"
          :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:management:export']" type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport">{{ ids.length > 0 ? '导出选中' : '导出' }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="managementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="演练编号" align="center" prop="drillCode" show-overflow-tooltip />
      <el-table-column label="演练名称" align="center" prop="drillName" show-overflow-tooltip />
      <!-- ：消防/地震/化学品泄漏/设备故障/其他 -->
      <el-table-column label="演练类型" align="center" prop="drillType" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getDictLabel(dict.type.yjjy_sglx, scope.row.drillType) || scope.row.drillType || '-' }}</span>
        </template>
      </el-table-column>
      <!-- 公司级/部门级/项目级/区域联合 -->
      <!-- <el-table-column label="演练级别" align="center" prop="drillLevel">
        <template slot-scope="scope">
          <span>{{ getDictLabel(dict.type.yljb, scope.row.drillLevel) || scope.row.drillLevel || '-' }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="项目名称" align="center" prop="projectName" show-overflow-tooltip />
      <el-table-column label="演练日期" align="center" prop="planStartTime" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.planStartTime ? parseTime(scope.row.planStartTime, "{y}-{m}-{d}") : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="演练地点" align="center" prop="planLocation" show-overflow-tooltip />
      <el-table-column label="参与人员" align="center" prop="participantsName" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.participantsName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划组织部门" align="center" prop="planOrganizer" show-overflow-tooltip />
      <el-table-column label="物资信息" align="center" prop="materialinformationUrl" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.materialinformationUrl">
            <el-button size="mini" type="text" icon="el-icon-folder-opened"
              @click="handleMaterialInfoPreview(scope.row)">查看附件</el-button>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="计划开始时间"
        align="center"
        prop="planStartTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planStartTime, "{y}-{m}-{d}") }}</span>
        </template>
</el-table-column>
<el-table-column label="计划结束时间" align="center" prop="planEndTime" width="180">
  <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planEndTime, "{y}-{m}-{d}") }}</span>
        </template>
</el-table-column>
<el-table-column label="演练地点" align="center" prop="planLocation" />
<el-table-column label="计划参与人数" align="center" prop="planParticipants" />
<el-table-column label="计划组织部门" align="center" prop="planOrganizer" />
<el-table-column label="演练负责人" align="center" prop="planLeader" />
<el-table-column label="演练计划内容" align="center" prop="planContent" />
<el-table-column label="计划审批状态" align="center" prop="planApprovalStatus" />
<el-table-column label="审批意见" align="center" prop="approvalOpinion" />
<el-table-column label="实际开始时间" align="center" prop="actualStartTime" width="180">
  <template slot-scope="scope"></template>
          <span>{{ parseTime(scope.row.actualStartTime, "{y}-{m}-{d}") }}</span>
        </template>
</el-table-column>
<el-table-column label="实际结束时间" align="center" prop="actualEndTime" width="180">
  <template slot-scope="scope">
          <span>{{ parseTime(scope.row.actualEndTime, "{y}-{m}-{d}") }}</span>
        </template>
</el-table-column>
<el-table-column label="实际参与人数" align="center" prop="actualParticipants" />
<el-table-column label="演练实施过程记录" align="center" prop="drillProcess" />
<el-table-column label="演练状态" align="center" prop="drillStatus" />
<el-table-column label="演练照片URL" align="center" prop="photoUrls" />
<el-table-column label="演练视频URL" align="center" prop="videoUrls" />
<el-table-column label="相关文档URL" align="center" prop="documentUrls" />
<el-table-column label="评估人" align="center" prop="evaluator" />
<el-table-column label="评估时间" align="center" prop="evaluationTime" width="180">
  <template slot-scope="scope">
          <span>{{ parseTime(scope.row.evaluationTime, "{y}-{m}-{d}") }}</span>
        </template>
</el-table-column>
<el-table-column label="演练效果" align="center" prop="drillEffect" />
<el-table-column label="发现的问题" align="center" prop="problemsFound" />
<el-table-column label="改进措施" align="center" prop="improvementMeasures" />
<el-table-column label="评估总结报告" align="center" prop="evaluationSummary" />
<el-table-column label="其他备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" fixed="right" width="200" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-hasPermi="['system:management:view']" size="mini" type="text" icon="el-icon-view"
            @click="handleView(scope.row)">查看</el-button>
          <el-button v-hasPermi="['system:management:edit']" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['system:management:remove']" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改应急演练管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="演练名称" prop="drillName">
              <el-input v-model="form.drillName" placeholder="请输入演练名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="演练地点" prop="planLocation">
              <el-input v-model="form.planLocation" placeholder="请输入演练地点" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <selectPeopleTree v-model="form.projectName" :people-list="projectList" placeholder="请选择项目名称"
                @change="handleProjectChange" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="演练类型" prop="drillType">
              <el-select v-model="form.drillType" placeholder="请选择演练类型" clearable style="width: 100%"
                @change="handleDrillTypeChange">
                <el-option v-for="dict in dict.type.yjjy_sglx" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="参与人员" prop="participants">
              <el-select ref="participantsSelect" v-model="participantsDisplay" placeholder="请选择参与人员所在部门" clearable
                style="width: 100%" popper-class="org-tree-select-dropdown" @clear="clearParticipants">
                <el-option :value="participantsDisplay" :label="participantsDisplay"
                  style="height: auto; padding: 0; border: none;">
                  <div class="tree-select-wrapper">
                    <org-tree :type="'1'" @nodeClick="handleParticipantsNodeClick" />
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划组织部门" prop="planOrganizer">
              <el-select ref="deptSelect" v-model="form.planOrganizer" placeholder="请选择计划组织部门" clearable
                style="width: 100%" popper-class="org-tree-select-dropdown">
                <el-option :value="form.planOrganizer" :label="form.planOrganizer"
                  style="height: auto; padding: 0; border: none;">
                  <div class="tree-select-wrapper">
                    <org-tree :type="'1'" @nodeClick="handleDeptNodeClick" />
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="演练级别" prop="drillLevel">
              <el-select v-model="form.drillLevel" placeholder="请选择演练级别" style="width: 100%">
                <el-option v-for="dict in dict.type.yljb" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="演练日期" prop="planStartTime">
              <el-date-picker v-model="form.planStartTime" type="datetime" placeholder="请选择演练日期"
                value-format="yyyy-MM-dd HH:mm:ss" format="yyyy-MM-dd HH:mm:ss" style="width: 100%" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 物资装备展示区域 -->
        <el-form-item v-if="emergencyFacilities && emergencyFacilities.length > 0" label="可用物资装备">
          <div class="facilities-container">
            <el-tag v-for="(facility, index) in emergencyFacilities" :key="index" style="margin: 5px;" type="info">
              {{ facility.materialName }} × {{ facility.materialNumber }}
            </el-tag>
          </div>
        </el-form-item>


        <!-- 文件上传区域 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="演练照片" prop="photoUrls">
              <file-upload v-model="form.photoUrls" :file-type="photoFileType" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="演练视频" prop="videoUrls">
              <file-upload v-model="form.videoUrls" :file-type="videoFileType" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="相关文档" prop="documentUrls">
          <file-upload v-model="form.documentUrls" :file-type="documentFileType" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog :title="viewTitle" :visible.sync="viewOpen" width="1000px" append-to-body custom-class="detail-dialog">
      <div v-if="viewData" class="detail-content">
        <!-- 基本信息部分 -->
        <div class="independent-section">
          <h3 class="independent-title">演练信息</h3>
          <!-- 左右两列布局 -->
          <div class="record-columns">
            <!-- 左列 -->
            <div class="left-column">
              <div class="field-row highlighted-field">
                <span class="field-label">演练名称:</span>
                <span class="field-value">{{ viewData.drillName || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">演练地点:</span>
                <span class="field-value">{{ viewData.planLocation || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">项目名称:</span>
                <span class="field-value">{{ viewData.projectName || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">演练类型:</span>
                <span class="field-value">{{ getDictLabel(dict.type.yjjy_sglx, viewData.drillType) || viewData.drillType
                  ||
                  '-' }}</span>
              </div>
            </div>

            <!-- 右列 -->
            <div class="right-column">
              <div class="field-row highlighted-field">
                <span class="field-label">演练日期:</span>
                <span class="field-value">{{ viewData.planStartTime ? parseTime(viewData.planStartTime, "{y}-{m}-{d}") :
                  '-'
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">参与人员:</span>
                <span class="field-value">{{ viewData.participantsName || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">计划组织部门:</span>
                <span class="field-value">{{ viewData.planOrganizer || '-' }}</span>
              </div>

              <!-- <div class="field-row">
                <span class="field-label">演练级别:</span>
                <span class="field-value">{{ getDictLabel(dict.type.yljb, viewData.drillLevel) || viewData.drillLevel ||
                  '-'
                }}</span>
              </div> -->
            </div>
          </div>

          <!-- 可用物资装备 -->
          <div v-if="viewData.materialQuipment" class="field-row full-width">
            <span class="field-label">可用物资装备:</span>
            <div class="field-value">
              <div class="facilities-display">
                <el-tag v-for="(item, index) in getMaterialEquipmentList(viewData.materialQuipment)" :key="index"
                  style="margin: 2px 5px 2px 0;" type="info">
                  {{ item }}
                </el-tag>
              </div>
            </div>
          </div>


          <!-- 相关附件 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关附件:</span>
            </div>
            <div class="photo-container">
              <!-- 演练照片 -->
              <div v-if="viewData.photoUrls" class="attachment-group">
                <div class="attachment-title">演练照片:</div>
                <div class="photo-grid">
                  <div v-for="(photo, index) in getPhotoList(viewData.photoUrls)" :key="'photo-' + index"
                    class="photo-item">
                    <img :src="getImageUrl(photo)" alt="演练照片" @click="previewImage(getImageUrl(photo))">
                  </div>
                </div>
              </div>

              <!-- 演练视频 -->
              <div v-if="viewData.videoUrls" class="attachment-group">
                <div class="attachment-title">演练视频:</div>
                <div class="video-list">
                  <div v-for="(video, index) in getVideoList(viewData.videoUrls)" :key="'video-' + index"
                    class="video-item">
                    <span class="attachment-item" @click="handleVideoPreview(video)">
                      <i class="el-icon-video-camera" />
                      演练视频{{ index + 1 }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 相关文档 -->
              <div v-if="viewData.documentUrls" class="attachment-group">
                <div class="attachment-title">相关文档:</div>
                <div class="document-list">
                  <div v-for="(doc, index) in getDocumentList(viewData.documentUrls)" :key="'doc-' + index"
                    class="document-item">
                    <span class="attachment-item" @click="handleDocumentPreview(doc)">
                      <i class="el-icon-document" />
                      {{ getFileName(doc) || `相关文档${index + 1}` }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 无附件时显示 -->
              <div v-if="!viewData.photoUrls && !viewData.videoUrls && !viewData.documentUrls"
                class="photo-placeholder">
                <img
                  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                  alt="暂无附件">
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {
  listManagement,
  getManagement,
  delManagement,
  addManagement,
  updateManagement
} from '@/api/system/emergencyDrillManage/index'
import selectPeopleTree from '@/views/components/selectPeopleTree.vue'
import OrgTree from '@/views/components/orgTree.vue'
import { querytree } from '@/api/system/info'
import { getDicts } from '@/api/system/dict/data'
import { listZjEmployeeInfo } from '@/api/inspection/zjEmployeeInfo'
import request from '@/utils/request'

export default {
  name: 'Management',
  dicts: ['yjjy_sglx', 'yljb'],
  components: {
    selectPeopleTree,
    OrgTree
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应急演练管理表格数据
      managementList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查看弹窗相关
      viewOpen: false,
      viewTitle: '查看详情',
      // 查看详情数据
      viewData: {},
      // 基础URL
      baseUrl: process.env.VUE_APP_BASE_API,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        drillName: null,
        drillType: null,
        // drillLevel: null,
        planOrganizer: null,
        planLocation: null,
        planStartTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        drillName: [
          { required: true, message: '演练名称不能为空', trigger: 'blur' }
        ],
        planLocation: [
          { required: true, message: '演练地点不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'change' }
        ],
        drillType: [
          { required: true, message: '演练类型不能为空', trigger: 'change' }
        ]
        // drillLevel: [
        //   { required: true, message: '演练级别不能为空', trigger: 'change' }
        // ]
      },
      // 项目列表
      projectList: [],
      // 用户列表
      userList: [],
      // 物资装备列表
      emergencyFacilities: [],
      // 参与人员显示文本
      participantsDisplay: '',
      // 当前选择的参与人员部门ID
      selectedDeptId: null,
      // 当前选择的参与人员部门名称
      selectedDeptName: null,
      // 文件类型限制
      photoFileType: ['png', 'jpg', 'jpeg', 'gif', 'bmp'],
      videoFileType: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
      documentFileType: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
    }
  },
  created() {
    this.getList()
    this.getProjectList()
    this.getUserList()
  },
  mounted() {
    // 如果字典数据为空，手动重新加载
    this.$nextTick(() => {
      if (!this.dict.type.yjjy_sglx || this.dict.type.yjjy_sglx.length === 0) {
        this.dict.reloadDict('yjjy_sglx').catch(() => {
          // 如果重新加载失败，手动获取字典数据
          this.manualLoadDict()
        })
      }
      if (!this.dict.type.yljb || this.dict.type.yljb.length === 0) {
        this.dict.reloadDict('yljb').catch(() => {
          // 如果重新加载失败，手动获取字典数据
          this.manualLoadYljbDict()
        })
      }
    })
  },
  methods: {
    /** 获取字典标签 */
    getDictLabel(dictOptions, value) {
      if (!dictOptions || !Array.isArray(dictOptions) || !value) {
        return ''
      }
      const dict = dictOptions.find(item => item.value === value || item.value === String(value))
      return dict ? dict.label : ''
    },
    /** 手动加载字典数据 */
    manualLoadDict() {
      // 直接调用字典 API 获取数据
      getDicts('yjjy_sglx').then(response => {
        if (response.data) {
          // 手动设置字典数据
          this.$set(this.dict.type, 'yjjy_sglx', response.data.map(item => ({
            label: item.dictLabel,
            value: item.dictValue
          })))
        }
      }).catch(() => {
        // 设置默认的事故类型选项
        this.$set(this.dict.type, 'yjjy_sglx', [
          { label: '消防事故', value: '1' },
          { label: '地震', value: '2' },
          { label: '化学品泄漏', value: '3' },
          { label: '设备故障', value: '4' },
          { label: '其他', value: '5' }
        ])
      })
    },
    /** 手动加载演练级别字典数据 */
    manualLoadYljbDict() {
      // 直接调用字典 API 获取数据
      getDicts('yljb').then(response => {
        if (response.data) {
          // 手动设置字典数据
          this.$set(this.dict.type, 'yljb', response.data.map(item => ({
            label: item.dictLabel,
            value: item.dictValue
          })))
        }
      }).catch(() => {
        // 设置默认的演练级别选项
        this.$set(this.dict.type, 'yljb', [
          { label: '项目级', value: '1' },
          { label: '公司级', value: '2' },
          { label: '部门级', value: '3' },
          { label: '区域联合', value: '4' }
        ])
      })
    },
    /** 查询应急演练管理列表 */
    getList() {
      this.loading = true
      listManagement(this.queryParams).then((response) => {
        this.managementList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        drillCode: null,
        drillName: null,
        planLocation: null,
        projectName: null,
        projectId: null,
        participants: [],
        participantsId: null,
        participantsName: null,
        planOrganizer: null,
        drillType: null,
        // drillLevel: null,
        planStartTime: null,
        materialQuipment: null,
        photoUrls: null,
        videoUrls: null,
        documentUrls: null
      }
      this.emergencyFacilities = []
      this.participantsDisplay = ''
      this.selectedDeptId = null
      this.selectedDeptName = null
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      const id = row.id
      getManagement(id).then((response) => {
        this.viewData = response.data
        this.viewOpen = true
        this.viewTitle = `查看详情 - ${this.viewData.drillName || '应急演练'}`
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 设置新增时的默认演练编号
      this.open = true
      this.title = '添加应急演练'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getManagement(id).then((response) => {
        this.form = response.data

        // 处理参与人员数据
        if (this.form.participantsId && this.form.participantsName) {
          // participantsId现在存储部门ID，participantsName存储部门名称
          this.selectedDeptId = this.form.participantsId
          this.selectedDeptName = this.form.participantsName

          // 根据部门ID重新获取人员列表
          this.getParticipantsByDept(this.form.participantsId, this.form.participantsName)
        } else if (this.form.participants && typeof this.form.participants === 'string') {
          // 兼容旧数据：如果只有participants字符串，转换为字符串数组
          this.form.participants = this.form.participants.split(',').map(item => item.trim())
        } else if (!this.form.participants) {
          this.form.participants = []
        }

        // 如果有演练类型，自动加载相关数据
        if (this.form.drillType) {
          if (this.form.projectId) {
            this.getEmergencyFacilities()
          }
        }

        // 更新参与人员显示文本
        this.updateParticipantsDisplay()

        this.open = true
        this.title = '修改应急演练管理'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // 处理参与人员数据
          const formData = { ...this.form }
          if (Array.isArray(formData.participants) && formData.participants.length > 0 && this.selectedDeptId && this.selectedDeptName) {
            // participantsId存储部门ID，participantsName存储部门名称
            formData.participantsId = this.selectedDeptId
            formData.participantsName = this.selectedDeptName
          } else {
            formData.participantsId = ''
            formData.participantsName = ''
          }

          // 移除participants字段，后端不需要此参数
          delete formData.participants

          // 处理物资装备字段：拼接成materialName×materialNumber格式，多个用逗号分隔
          if (this.emergencyFacilities && this.emergencyFacilities.length > 0) {
            formData.materialQuipment = this.emergencyFacilities
              .map(facility => `${facility.materialName}×${facility.materialNumber}`)
              .join(',')
          } else {
            formData.materialQuipment = ''
          }


          if (this.form.id != null) {
            updateManagement(formData).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addManagement(formData).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm(
          '是否确认删除应急演练管理编号为"' +
          ids +
          '"的数据项？'
        )
        .then(() => {
          return delManagement(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 如果有选中记录，导出选中的；否则导出全部
      if (this.ids.length > 0) {
        // 导出选中记录
        this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条记录？').then(() => {
          this.download(
            'system/drillManagement/export',
            {
              ids: this.ids.join(',')
            },
            `management_selected_${new Date().getTime()}.xlsx`
          )
        })
      } else {
        // 导出全部记录（根据查询条件，但不包含分页参数）
        const exportParams = { ...this.queryParams }
        // 移除分页参数，确保导出全部数据
        delete exportParams.pageNum
        delete exportParams.pageSize

        this.download(
          'system/drillManagement/export',
          exportParams,
          `management_${new Date().getTime()}.xlsx`
        )
      }
    },
    /** 获取项目列表 */
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data
        }
      })
    },
    /** 获取用户列表 */
    getUserList() {
      // 模拟用户数据，实际应该调用相应的API
      this.userList = [
        { id: 1, name: '张三' },
        { id: 2, name: '李四' },
        { id: 3, name: '王五' },
        { id: 4, name: '赵六' },
        { id: 5, name: '陈七' }
      ]
    },
    /** 处理参与人员部门选择 */
    handleParticipantsNodeClick(nodeData) {
      if (nodeData && nodeData.label && nodeData.id) {
        this.selectedDeptId = nodeData.id
        this.selectedDeptName = nodeData.label
        // 获取该部门的所有人员
        this.getParticipantsByDept(nodeData.id, nodeData.label)
        // 选择后关闭下拉框
        this.$nextTick(() => {
          if (this.$refs.participantsSelect) {
            this.$refs.participantsSelect.blur()
          }
        })
      }
    },
    /** 根据部门ID获取参与人员 */
    getParticipantsByDept(deptId, deptName) {
      const queryParams = {
        deptId: deptId,
        pageNum: 1,
        pageSize: 1000 // 获取所有人员
      }

      listZjEmployeeInfo(queryParams).then(response => {
        if (response.rows && response.rows.length > 0) {
          // 将获取到的人员转换为包含id和name的对象数组
          this.form.participants = response.rows.map(person => ({
            id: person.id,
            name: person.employeeName
          }))
          // 更新显示文本
          this.updateParticipantsDisplay()
          // 设置选择器显示的文本为部门名称
          this.participantsDisplay = `${deptName}(${response.rows.length}人)`
        } else {
          this.$modal.msgWarning(`${deptName}部门暂无人员`)
          this.form.participants = []
          this.participantsDisplay = ''
        }
      }).catch(error => {
        console.error('获取部门人员失败:', error)
        this.$modal.msgError('获取部门人员失败')
        this.form.participants = []
        this.participantsDisplay = ''
      })
    },
    /** 清空参与人员 */
    clearParticipants() {
      this.form.participants = []
      this.participantsDisplay = ''
      this.selectedDeptId = null
      this.selectedDeptName = null
    },
    /** 更新参与人员显示文本 */
    updateParticipantsDisplay() {
      if (this.form.participants && this.form.participants.length > 0) {
        let displayNames = []

        // 处理不同数据格式
        if (typeof this.form.participants[0] === 'object' && this.form.participants[0].name) {
          // 对象数组格式，提取姓名
          displayNames = this.form.participants.map(p => p.name)
        } else {
          // 字符串数组格式
          displayNames = this.form.participants
        }

        if (displayNames.length <= 3) {
          this.participantsDisplay = displayNames.join('、')
        } else {
          this.participantsDisplay = `${displayNames.slice(0, 3).join('、')}等${displayNames.length}人`
        }
      } else {
        this.participantsDisplay = ''
      }
    },
    /** 处理组织部门选择 */
    handleDeptNodeClick(nodeData) {
      if (nodeData && nodeData.label) {
        this.form.planOrganizer = nodeData.label
        // 选择后关闭下拉框
        this.$nextTick(() => {
          if (this.$refs.deptSelect) {
            this.$refs.deptSelect.blur()
          }
        })
      }
    },
    /** 项目选择变化处理 */
    handleProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === 'general-project') {
        this.form.projectName = selectedItem.label
        this.form.projectId = selectedItem.id
        // 如果演练类型也已选择，获取物资装备
        this.getEmergencyFacilities()
      } else {
        this.form.projectName = null
        this.form.projectId = null
        this.emergencyFacilities = []
      }
    },
    /** 演练类型变化处理 */
    handleDrillTypeChange(value) {
      // 如果项目也已选择，获取物资装备
      this.getEmergencyFacilities()
    },
    /** 获取物资装备列表 */
    getEmergencyFacilities() {
      if (this.form.projectId && this.form.drillType) {
        // 调用您提供的接口获取物资装备
        request.get(`/inspection/zjEmergencyFacilities/zjEmergencyFacilitieslist`, {
          params: {
            accidentType: this.form.drillType,
            projectId: this.form.projectId
          }
        }).then((response) => {
          if (response.code === 200) {
            this.emergencyFacilities = response.data || []
          }
        }).catch(() => {
          this.emergencyFacilities = []
        })
      } else {
        this.emergencyFacilities = []
      }
    },
    /** 解析物资装备列表 */
    getMaterialEquipmentList(materialQuipment) {
      if (!materialQuipment) return []
      return materialQuipment.split(',').filter(item => item.trim())
    },
    /** 解析照片列表 */
    getPhotoList(photoUrls) {
      if (!photoUrls) return []
      return photoUrls.split(',').filter(item => item.trim())
    },
    /** 解析视频列表 */
    getVideoList(videoUrls) {
      if (!videoUrls) return []
      return videoUrls.split(',').filter(item => item.trim())
    },
    /** 解析文档列表 */
    getDocumentList(documentUrls) {
      if (!documentUrls) return []
      return documentUrls.split(',').filter(item => item.trim())
    },
    /** 获取图片URL */
    getImageUrl(imageUrl) {
      if (!imageUrl) return ''
      // 如果是相对路径，需要拼接服务器地址
      if (imageUrl.startsWith('/')) {
        return `${process.env.VUE_APP_BASE_API}${imageUrl}`
      }
      return imageUrl
    },
    /** 预览图片 */
    previewImage(imageUrl) {
      if (!imageUrl) return
      // 使用element-ui的图片预览功能
      this.$alert(`<img src="${imageUrl}" style="width: 100%; max-width: 500px;" alt="预览图片">`, '图片预览', {
        dangerouslyUseHTMLString: true,
        customClass: 'image-preview-dialog',
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: '关闭'
      })
    },
    /** 文档预览 */
    async handleDocumentPreview(docUrl) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + docUrl
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith('.txt')) {
          const res = await fetch(fileUrl)
          const buffer = await res.arrayBuffer()

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder('utf-8')
          const text = decoder.decode(buffer)

          // 在弹窗中显示文本内容
          this.$alert(text, '文件内容', {
            customClass: 'txt-preview-dialog',
            showConfirmButton: false,
            closeOnClickModal: true,
            cancelButtonText: '关闭'
          })
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl)
        }
      } catch (error) {
        this.$message.error('文件打开失败: ' + error.message)
      }
    },
    /** 视频预览 */
    handleVideoPreview(videoUrl) {
      try {
        // 获取视频完整URL
        const fileUrl = this.baseUrl + videoUrl
        // 直接打开视频文件
        window.open(fileUrl)
      } catch (error) {
        this.$message.error('视频打开失败: ' + error.message)
      }
    },
    /** 获取文件名 */
    getFileName(url) {
      if (!url) return ''
      const parts = url.split('/')
      return parts[parts.length - 1] || ''
    },
    /** 物资信息附件预览 */
    handleMaterialInfoPreview(row) {
      if (!row.materialinformationUrl) {
        this.$message.warning('暂无物资信息附件')
        return
      }

      // 处理多个附件的情况，以逗号分隔
      const attachmentUrls = row.materialinformationUrl.split(',').filter(url => url.trim())

      if (attachmentUrls.length === 0) {
        this.$message.warning('暂无物资信息附件')
        return
      }

      if (attachmentUrls.length === 1) {
        // 单个附件直接预览
        this.handleDocumentPreview(attachmentUrls[0])
      } else {
        // 多个附件显示选择列表
        this.showMaterialInfoList(attachmentUrls)
      }
    },
    /** 显示物资信息附件列表 */
    showMaterialInfoList(attachmentUrls) {
      const attachmentList = attachmentUrls.map((url, index) => {
        const fileName = this.getFileName(url) || `物资信息附件${index + 1}`
        return `<div style="margin: 8px 0; cursor: pointer; color: #409eff;" onclick="window.handleMaterialAttachmentClick('${url}')">${fileName}</div>`
      }).join('')

      // 注册全局点击处理函数
      window.handleMaterialAttachmentClick = (url) => {
        this.handleDocumentPreview(url)
      }

      this.$alert(attachmentList, '物资信息附件列表', {
        dangerouslyUseHTMLString: true,
        customClass: 'material-info-dialog',
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: '关闭'
      }).finally(() => {
        // 清理全局函数
        delete window.handleMaterialAttachmentClick
      })
    }
  }
}
</script>

<style scoped>
.facilities-container {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background-color: #f9f9f9;
}


/* 树形下拉选择器样式 */
.tree-select-wrapper {
  width: 300px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

::v-deep .org-tree-select-dropdown .el-select-dropdown__item {
  padding: 0 !important;
  height: auto !important;
  line-height: normal !important;
}

::v-deep .org-tree-select-dropdown .el-select-dropdown__item:hover {
  background-color: transparent !important;
}

/* 参与人员选择器样式 */
.participants-selector {
  display: flex;
  align-items: center;
  width: 100%;
}

.participants-selector .el-input {
  flex: 1;
}

.participants-selector .el-button {
  white-space: nowrap;
}

/* 详情弹窗样式 */
:deep(.detail-dialog .el-dialog__header) {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e6e6e6;
  padding: 15px 20px;
}

:deep(.detail-dialog .el-dialog__title) {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

:deep(.detail-dialog .el-dialog__body) {
  padding: 0px 20px !important;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-content {
  padding: 0 20px;
}

.detail-content .independent-section {
  margin-bottom: 10px;
}

.detail-content .independent-section:last-child {
  margin-bottom: 0;
}

.detail-content .independent-title {
  margin: 0 0 20px 0;
  padding: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 20px;
}

.detail-content .independent-title:after {
  content: '';
  flex: 1;
  height: 2px;
  border-top: 2px dashed #409eff;
}

/* 左右两列布局 */
.detail-content .record-columns {
  display: flex;
  gap: 40px;
}

.detail-content .left-column,
.detail-content .right-column {
  flex: 1;
}

.detail-content .field-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.detail-content .field-row.full-width {
  width: 100%;
}

.detail-content .field-row:last-child {
  margin-bottom: 0;
}

.detail-content .field-row.highlighted-field {
  background-color: #ebecf0;
  padding: 8px 0;
  margin-bottom: 0;
  margin-left: -20px;
  margin-right: -20px;
  padding-left: 20px;
  padding-right: 20px;
}

/* 非高亮字段与高亮字段之间的间距 */
.detail-content .field-row:not(.highlighted-field)+.field-row.highlighted-field,
.detail-content .field-row.highlighted-field+.field-row:not(.highlighted-field) {
  margin-top: 16px;
}

.detail-content .field-label {
  min-width: 100px;
  font-weight: 400;
  color: #666;
  margin-right: 10px;
  white-space: nowrap;
  font-size: 14px;
  line-height: 1.5;
}

.detail-content .field-value {
  color: #333;
  word-break: break-all;
  line-height: 1.5;
  font-size: 14px;
  flex: 1;
}

.detail-content .field-value.status-tag {
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
}

/* 整改记录部分样式 */
.detail-content .rectification-info {
  display: flex;
  gap: 0;
  margin-bottom: 16px;
  background-color: #ebecf0;
  margin-left: -20px;
  margin-right: -20px;
  padding: 8px 20px;
}

.detail-content .rectification-info .field-row {
  margin-bottom: 0;
  white-space: nowrap;
  flex: 1;
  margin-left: 0;
  margin-right: 0;
  padding: 0 20px;
  background-color: transparent;
}

.detail-content .rectification-info .field-row:first-child {
  padding-left: 0;
}

.detail-content .rectification-info .field-row:last-child {
  padding-right: 0;
}

/* 照片相关样式 */
.detail-content .photo-section {
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.detail-content .photo-container {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-content .attachment-group .attachment-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-content .photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.detail-content .video-list,
.detail-content .document-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-content .photo-item,
.detail-content .photo-placeholder {
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  background: #fafafa;
}

.detail-content .photo-item:hover,
.detail-content .photo-placeholder:hover {
  transform: scale(1.02);
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.detail-content .photo-item img,
.detail-content .photo-placeholder img {
  width: 120px;
  height: 90px;
  object-fit: cover;
  display: block;
}

.detail-content .photo-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: 1px dashed #d1d5db;
  width: 120px;
  height: 90px;
}

.detail-content .photo-placeholder:hover {
  transform: none;
  border-color: #d1d5db;
  box-shadow: none;
}

.detail-content .photo-placeholder img {
  opacity: 0.3;
  background: transparent;
}

.detail-content .video-item,
.detail-content .document-item {
  padding: 5px 0;
}

.detail-content .facilities-display {
  margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content .record-columns {
    flex-direction: column;
    gap: 20px;
  }

  .detail-content .rectification-info {
    flex-direction: column;
    gap: 10px;
  }
}

/* 图片预览弹窗样式 */
:deep(.image-preview-dialog .el-message-box__content) {
  text-align: center;
}

:deep(.image-preview-dialog .el-message-box__message) {
  margin: 0;
}

/* 附件样式 */
.attachment-item {
  cursor: pointer;
  color: #409eff;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s;
}

.attachment-item:hover {
  text-decoration: underline;
  color: #66b1ff;
}

.attachment-item i {
  font-size: 14px;
}

/* TXT文件预览弹窗样式 */
:deep(.txt-preview-dialog) {
  max-width: 800px;
}

:deep(.txt-preview-dialog .el-message-box__content) {
  text-align: left;
  max-height: 500px;
  overflow-y: auto;
}

:deep(.txt-preview-dialog .el-message-box__message) {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* 物资信息附件列表弹窗样式 */
:deep(.material-info-dialog) {
  max-width: 600px;
}

:deep(.material-info-dialog .el-message-box__content) {
  text-align: left;
  max-height: 400px;
  overflow-y: auto;
}

:deep(.material-info-dialog .el-message-box__message) {
  margin: 0;
}

:deep(.material-info-dialog .el-message-box__message div) {
  padding: 10px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  margin: 8px 0;
  transition: all 0.2s;
  background-color: #fafafa;
}

:deep(.material-info-dialog .el-message-box__message div:hover) {
  background-color: #ecf5ff;
  border-color: #409eff;
  transform: translateX(5px);
}
</style>
