import request from '@/utils/request'

// 查询应急预案管理列表
export function listPlanManagement(query) {
  return request({
    url: '/inspection/planManagement/list',
    method: 'get',
    params: query
  })
}

// 查询应急预案管理详细
export function getPlanManagement(id) {
  return request({
    url: '/inspection/planManagement/' + id,
    method: 'get'
  })
}

// 新增应急预案管理
export function addPlanManagement(data) {
  return request({
    url: '/inspection/planManagement',
    method: 'post',
    data: data
  })
}

// 修改应急预案管理
export function updatePlanManagement(data) {
  return request({
    url: '/inspection/planManagement',
    method: 'put',
    data: data
  })
}

// 删除应急预案管理
export function delPlanManagement(id) {
  return request({
    url: '/inspection/planManagement/' + id,
    method: 'delete'
  })
}
