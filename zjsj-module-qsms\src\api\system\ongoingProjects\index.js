import request from '@/utils/request'

// 查询在建项目一览（汇总展示在建项目基本信息与状态）列表
export function listProjects(query) {
  return request({
    url: '/system/projects/list',
    method: 'get',
    params: query
  })
}

// 查询在建项目一览（汇总展示在建项目基本信息与状态）详细
export function getProjects(id) {
  return request({
    url: '/system/projects/' + id,
    method: 'get'
  })
}

// 新增在建项目一览（汇总展示在建项目基本信息与状态）
export function addProjects(data) {
  return request({
    url: '/system/projects',
    method: 'post',
    data: data
  })
}

// 修改在建项目一览（汇总展示在建项目基本信息与状态）
export function updateProjects(data) {
  return request({
    url: '/system/projects',
    method: 'put',
    data: data
  })
}

// 删除在建项目一览（汇总展示在建项目基本信息与状态）
export function delProjects(id) {
  return request({
    url: '/system/projects/' + id,
    method: 'delete'
  })
}
