<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="公司/部门" prop="subjectName">
        <!-- <el-input
          v-model="queryParams.subjectName"
          placeholder="请输入公司/部门"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <selectPeopleTree
          ref="chargePersonName"
          v-model="queryParams.subjectName"
          :people-list="companyList"
          placeholder="请搜索或选择公司/部门"
          @change="handleQueryChange"
        />
        <!-- <el-select
          v-model="queryParams.subjectName"
          placeholder="请选择公司/部门"
          clearable
          filterable
        >
          <el-option
            v-for="item in subjectNameList"
            :key="item.subjectName"
            :label="item.subjectName"
            :value="item.subjectName"
          />
        </el-select> -->
      </el-form-item>
      <el-form-item label="责任年度" prop="responsibilityYear">
        <!-- <el-input
          v-model="queryParams.responsibilityYear"
          placeholder="请输入责任年度"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <template slot="append">年份</template>
</el-input> -->
        <el-select
          v-model="queryParams.responsibilityYear"
          placeholder="请选择责任年度"
          clearable
          filterable
        >
          <el-option
            v-for="item in responsibilityYearList"
            :key="item.responsibilityYear"
            :label="item.responsibilityYear"
            :value="item.responsibilityYear"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:responsibility:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:responsibility:edit']"
          >修改</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:responsibility:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:responsibility:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="responsibilityList"
      height="calc(100vh - 230px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="50" label="序号"> </el-table-column>
      <el-table-column label="公司/部门" align="center" prop="subjectName" />
      <el-table-column
        label="责任年度"
        align="center"
        prop="responsibilityYear"
        width="120"
      />
      <el-table-column label="负责人" align="center" prop="head" width="120" />

      <el-table-column
        label="签订时间"
        align="center"
        prop="executionDate"
        width="120"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.executionDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="安全生产责任书"
        align="center"
        prop="contractFileUrl"
      >
        <template slot-scope="scope">
          <!-- <file-preview
            :src="scope.row.contractFileUrl"
            :width="50"
            :height="50"
          /> -->
          <!-- <el-button
            v-if="scope.row.contractFileUrl"
            size="mini"
            type="text"
            @click="handleAttach(scope.row.contractFileUrl)"
            >查看</el-button
          > -->
          <div v-if="scope.row.contractFileUrl" class="contract-file">
            <div
              v-for="(item, index) in scope.row.contractFileUrl.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
              <!-- {{ item.split("/").pop() }} -->
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <!-- 查看 -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >

          <el-button
            v-hasPermi="['inspection:responsibility:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['inspection:responsibility:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全生产目标责任书对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="140px"
        :class="showButton == true ? '' : 'view-mode'"
      >
        <el-form-item label="公司/部门" prop="subjectName">
          <!-- <el-input v-model="form.subjectName" placeholder="请输入公司/部门" /> -->
          <!-- <el-select
            v-model="form.subjectName"
            placeholder="请选择公司/部门"
            @change="handleQuery"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in subjectNameList"
              :key="item.subjectName"
              :label="item.subjectName"
              :value="item.subjectName"
            />
          </el-select> -->
          <selectPeopleTree
            ref="chargePersonName"
            v-model="form.subjectName"
            :people-list="companyList"
            placeholder="请搜索或选择公司/部门"
            @change="handleChange"
            :disabled="!showButton"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="head">
          <el-input
            v-model="form.head"
            placeholder="请输入负责人"
            :disabled="!showButton"
          />
        </el-form-item>
        <el-form-item label="责任年度" prop="responsibilityYear">
          <!-- <el-input
            v-model="form.responsibilityYear"
            placeholder="请输入责任年度"
          >
            <template slot="append">年份</template>
          </el-input> -->
          <el-date-picker
            v-model="form.responsibilityYear"
            clearable
            type="year"
            value-format="yyyy"
            placeholder="请选择签订时间"
            style="width: 100%"
            :disabled="!showButton"
          />
          <!-- <el-select
            v-model="form.responsibilityYear"
            placeholder="请选择责任年度"
            @change="handleQuery"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in responsibilityYearList"
              :key="item.responsibilityYear"
              :label="item.responsibilityYear"
              :value="item.responsibilityYear"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="签订时间" prop="executionDate">
          <el-date-picker
            v-model="form.executionDate"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择签订时间"
            style="width: 100%"
            :disabled="!showButton"
          />
        </el-form-item>
        <el-form-item label="安全生产责任书" prop="contractFileUrl">
          <file-upload
            v-model="form.contractFileUrl"
            :file-type="['pdf', 'docx', 'png', 'jpg', 'doc', 'xlsx', 'xls']"
            :disabled="!showButton"
          />
        </el-form-item>
      </el-form>
      <div v-show="showButton" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog
      v-model="attachmentDialogVisible"
      :attachment-list="attachmentList"
    />
  </div>
</template>

<script>
import {
  listResponsibility,
  getResponsibility,
  delResponsibility,
  addResponsibility,
  updateResponsibility,
  getSubjectNamelist,
  getResponsibilityYearlist,
} from "@/api/inspection/responsibility";
import { getEnterpriseInfo } from "@/api/system/info";
import { getFileOrignalName } from "@/utils/common.js";

import AttachmentDialog from "@/views/components/attchmentDialog.vue";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";

export default {
  name: "Responsibility",
  components: {
    AttachmentDialog,
    selectPeopleTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全生产目标责任书表格数据
      responsibilityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      showButton: true,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subjectName: null,
        contractFileUrl: null,
        hazardReporting: null,
        inspectionCycle: null,
        jobCategory: null,
        responsibilityYear: null,
        executionDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        subjectName: [
          { required: true, message: "请输入公司/部门", trigger: "change" },
        ],
        head: [{ required: true, message: "请输入负责人", trigger: "change" }],

        contractFileUrl: [
          {
            required: true,
            message: "请上传安全生产责任书",
            trigger: "change",
          },
        ],

        responsibilityYear: [
          { required: true, message: "请选择责任年度", trigger: "change" },
        ],
        executionDate: [
          { required: true, message: "请选择签订时间", trigger: "blur" },
        ],
      },
      attachmentDialogVisible: false,
      attachmentList: [],
      subjectNameList: [],
      responsibilityYearList: [],
      companyList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    this.getList();
    this.getSubjectNamelist();
    this.getResponsibilityYearlist();
    this.getCompanyList();
  },
  methods: {
    getFileOrignalName,
    // 公司/部门
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    handleChange(selectedItem) {
      if (selectedItem) {
        // this.form.subjectId = selectedItem.id;
        this.form.subjectName = selectedItem.label;
      } else {
        // this.form.subjectId = null;
        this.form.subjectName = null;
      }
    },
    handleQueryChange(selectedItem) {
      if (selectedItem) {
        this.queryParams.subjectName = selectedItem.label;
      } else {
        this.queryParams.subjectName = null;
      }
    },
    filterSubjectName(value) {
      // console.log(value, "111");
      this.queryParams.subjectName = value;
      // 关闭下拉框
      this.$nextTick(() => {
        this.$refs.subjectSelect.blur();
      });
      this.getList();
    },

    // 公司/部门
    async getSubjectNamelist() {
      getSubjectNamelist().then((res) => {
        this.subjectNameList = res.data;
      });
    },
    // 责任年度
    async getResponsibilityYearlist() {
      getResponsibilityYearlist().then((res) => {
        this.responsibilityYearList = res.data;
      });
    },

    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    /** 查询安全生产目标责任书列表 */
    getList() {
      this.loading = true;
      listResponsibility(this.queryParams).then((res) => {
        this.responsibilityList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        head: null,
        subjectName: null,
        contractFileUrl: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        responsibilityYear: null,
        executionDate: null,
      };
      this.resetForm("form");
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 查看
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getResponsibility(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "查看安全生产目标责任书";
        this.showButton = false;
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全生产目标责任书";
      this.showButton = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getResponsibility(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改安全生产目标责任书";
        this.showButton = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateResponsibility(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getResponsibilityYearlist();
              this.getSubjectNamelist();
            });
          } else {
            addResponsibility(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getResponsibilityYearlist();
              this.getSubjectNamelist();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全生产目标责任书编号为"' + ids + '"的数据项？')
        .then(function () {
          return delResponsibility(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/responsibility/export",
        {
          ...this.queryParams,
        },
        `responsibility_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped lang="scss">
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>
<style scoped>
::v-deep .view-mode .el-form-item__label::before {
  content: none !important;
}

::v-deep .view-mode .el-form-item__label {
  color: #606266;
  font-weight: normal;
}
::v-deep .view-mode .el-input.is-disabled .el-input__inner {
  /* background-color: transparent !important; */
  /* border-color: transparent !important; */
  color: #2c2d2e;
  cursor: not-allowed;
}
/* ::v-deep .view-mode .el-input__prefix {
  display: none !important;
} */
</style>
