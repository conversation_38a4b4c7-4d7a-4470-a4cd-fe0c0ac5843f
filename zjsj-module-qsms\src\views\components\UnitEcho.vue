<template>
  <div class="unit-echo">
    <div>
      {{ getDeptName(unit) }}
    </div>
    <!-- 若无单位数据，显示提示信息 -->
    <!-- <div v-else class="no-unit">-</div> -->
  </div>
</template>

<script>
import { listInfo, getOrgTree } from "@/api/system/info";
export default {
  name: "UnitEcho",
  props: {
    // 接收单位数组，数组元素包含 label 属性
    unit: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      deptList: [],
      deptParams: {
        enterpriseName: undefined,
        status: undefined,
      },
    };
  },
  mounted() {
    this.getDeptList();
  },
  methods: {
    /** 查询部门列表 */
    getDeptList() {
      getOrgTree(this.deptParams).then((response) => {
        this.deptList = this.handleTree(response.rows, "id");
        this.loading = false;
      });
    },
    // 添加扁平化部门的方法
    flattenDept(children) {
      let result = [];
      children.forEach((child) => {
        result.push(child);
        if (child.children && child.children.length) {
          result = result.concat(this.flattenDept(child.children));
        }
      });
      return result;
    },
    getDeptName(deptId) {
      console.log(deptId, "DEPT");
      const dept = this.flattenDept(this.deptList).find(
        (item) => item.id == deptId
      );
      return dept ? dept.name : "";
    },
  },
};
</script>

<style scoped>
.no-unit {
  color: #999;
  font-size: 14px;
}
</style>
