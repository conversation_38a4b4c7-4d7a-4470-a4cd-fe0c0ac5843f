<template>
  <div class="app-container">
    <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租户ID" prop="tenantId">
        <el-input
          v-model="queryParams.tenantId"
          placeholder="请输入租户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目ID" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="删除状态 0 未删除 1 已删除" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入删除状态 0 未删除 1 已删除"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="危大类别ID" prop="typeId">
        <el-input
          v-model="queryParams.typeId"
          placeholder="请输入危大类别ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管控要点序号" prop="index">
        <el-input
          v-model="queryParams.index"
          placeholder="请输入管控要点序号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="管控类别" prop="controlTypeName">
        <el-input
          v-model="queryParams.controlTypeName"
          placeholder="请输入管控类别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="引用危大字典库对应的管控要点ID" prop="libId">
        <el-input
          v-model="queryParams.libId"
          placeholder="请输入引用危大字典库对应的管控要点ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联的危大工程ID" prop="dangerProjectId">
        <el-input
          v-model="queryParams.dangerProjectId"
          placeholder="请输入关联的危大工程ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否完成 0 否  1 是" prop="finish">
        <el-input
          v-model="queryParams.finish"
          placeholder="请输入是否完成 0 否  1 是"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form> -->

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjControlPointsProject:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjControlPointsProject:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjControlPointsProject:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjControlPointsProject:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row> -->
    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
      >重置</el-button
    >
    <el-table
      v-loading="loading"
      :data="zjControlPointsProjectList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 200px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column align="center" label="序号">
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="主键ID" align="center" prop="id" />
      <el-table-column label="项目编码" align="center" prop="projectCode" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="租户ID" align="center" prop="tenantId" />
      <el-table-column label="项目ID" align="center" prop="projectId" />
      <el-table-column
        label="删除状态 0 未删除 1 已删除"
        align="center"
        prop="deleted"
      />
      <el-table-column label="危大类别ID" align="center" prop="typeId" />
      <el-table-column label="管控要点" align="center" prop="index" />
      <el-table-column label="管控类别" align="center" prop="controlTypeName" />
      <el-table-column label="知晓内容" align="center" prop="content" />
      <el-table-column
        label="状态为是 对应的中文说明"
        align="center"
        prop="yesStatus"
      />
      <el-table-column
        label="状态为否 对应的中文说明"
        align="center"
        prop="noStatus"
      />
      <el-table-column
        label="引用危大字典库对应的管控要点ID"
        align="center"
        prop="libId"
      />
      <el-table-column
        label="关联的危大工程ID"
        align="center"
        prop="dangerProjectId"
      />
      <el-table-column
        label="是否完成 0 否  1 是"
        align="center"
        prop="finish"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjControlPointsProject:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjControlPointsProject:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改同步危大工程项目层管控要点记录对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="租户ID" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户ID" />
        </el-form-item>
        <el-form-item label="项目ID" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目ID" />
        </el-form-item>
        <el-form-item label="删除状态 0 未删除 1 已删除" prop="deleted">
          <el-input
            v-model="form.deleted"
            placeholder="请输入删除状态 0 未删除 1 已删除"
          />
        </el-form-item>
        <el-form-item label="危大类别ID" prop="typeId">
          <el-input v-model="form.typeId" placeholder="请输入危大类别ID" />
        </el-form-item>
        <el-form-item label="管控要点序号" prop="index">
          <el-input v-model="form.index" placeholder="请输入管控要点序号" />
        </el-form-item>
        <el-form-item label="管控类别" prop="controlTypeName">
          <el-input
            v-model="form.controlTypeName"
            placeholder="请输入管控类别"
          />
        </el-form-item>
        <el-form-item label="知晓内容">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item label="引用危大字典库对应的管控要点ID" prop="libId">
          <el-input
            v-model="form.libId"
            placeholder="请输入引用危大字典库对应的管控要点ID"
          />
        </el-form-item>
        <el-form-item label="关联的危大工程ID" prop="dangerProjectId">
          <el-input
            v-model="form.dangerProjectId"
            placeholder="请输入关联的危大工程ID"
          />
        </el-form-item>
        <el-form-item label="是否完成 0 否  1 是" prop="finish">
          <el-input
            v-model="form.finish"
            placeholder="请输入是否完成 0 否  1 是"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import {
  listZjControlPointsProject,
  getZjControlPointsProject,
  delZjControlPointsProject,
  addZjControlPointsProject,
  updateZjControlPointsProject,
} from "@/api/inspection/zjControlPointsProject";

export default {
  name: "ZjControlPointsProject",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 同步危大工程项目层管控要点记录表格数据
      zjControlPointsProjectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: null,
        projectName: null,
        tenantId: null,
        projectId: null,
        deleted: null,
        typeId: null,
        index: null,
        controlTypeName: null,
        content: null,
        yesStatus: null,
        noStatus: null,
        libId: null,
        dangerProjectId: null,
        finish: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询同步危大工程项目层管控要点记录列表 */
    getList() {
      this.loading = true;
      listZjControlPointsProject(this.queryParams).then((response) => {
        this.zjControlPointsProjectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectCode: null,
        projectName: null,
        tenantId: null,
        projectId: null,
        deleted: null,
        createTime: null,
        updateTime: null,
        typeId: null,
        index: null,
        controlTypeName: null,
        content: null,
        yesStatus: null,
        noStatus: null,
        libId: null,
        dangerProjectId: null,
        finish: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加同步危大工程项目层管控要点记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjControlPointsProject(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改同步危大工程项目层管控要点记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjControlPointsProject(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjControlPointsProject(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除同步危大工程项目层管控要点记录编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delZjControlPointsProject(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjControlPointsProject/export",
        {
          ...this.queryParams,
        },
        `zjControlPointsProject_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
