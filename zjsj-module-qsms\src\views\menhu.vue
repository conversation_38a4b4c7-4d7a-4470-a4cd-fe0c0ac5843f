<template>
  <div class="menhu-container">
    <Navbar :showProject="showProject" />
    <div class="dashboard-grid">
      <!-- 轮播图 -->
      <div class="swiper">
        <!-- <el-carousel trigger="click" height="100%">
       
          <el-carousel-item v-for="(item, index) in swiperList" :key="index"> -->
        <img :src="banner" alt="" class="swiper-image" />
        <!-- </el-carousel-item>
        </el-carousel> -->
      </div>
      <!-- 栅格gridrow -->
      <div class="grid-container">
        <!-- 第一行-->
        <el-row :gutter="10" style="margin-bottom: 10px; height: 320px">
          <el-col :span="8">
            <div class="swiper-yaowen">
              <el-carousel trigger="click" height="100%">
                <!-- 使用动态绑定图片路径 -->
                <el-carousel-item v-for="(item, index) in imgList" :key="index">
                  <img
                    :src="`${baseUrl}${item.coverImg}`"
                    alt=""
                    class="swiper-image"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
          </el-col>

          <el-col :span="8">
            <el-card class="news-card" :style="{ height: '100%' }">
              <div slot="header" class="clearfix">
                <img
                  src="@/assets/images/menhu/yaowen.png"
                  style="width: 20px; height: 20px; margin-right: 5px"
                  alt=""
                  srcset=""
                />
                <span class="news-title">公司要闻</span>
                <span style="cursor: pointer" @click="gotoMore('news')">
                  更多</span
                >
              </div>

              <div class="news-list">
                <div
                  class="news-item"
                  v-for="(item, index) in newsList"
                  :key="index"
                  @click="gotoDetail(item.id)"
                >
                  <div class="news-content">
                    <span class="news-bullet"></span>
                    {{ item.title }}
                  </div>
                  <div class="news-date">{{ item.publishTime }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="news-card" :style="{ height: '100%' }">
              <div slot="header" class="clearfix">
                <img
                  src="@/assets/images/menhu/gonggao.png"
                  style="width: 20px; height: 20px; margin-right: 5px"
                  alt=""
                  srcset=""
                />
                <span class="news-title">通知公告</span>
                <span style="cursor: pointer" @click="gotoMore('notice')">
                  更多</span
                >
              </div>

              <div class="news-list">
                <div
                  class="news-item"
                  v-for="(item, index) in noticeList"
                  :key="index"
                  @click="gotoDetail(item.id)"
                >
                  <div class="news-content">
                    <span class="news-bullet"></span>
                    {{ item.title }}
                  </div>
                  <div class="news-date">{{ item.publishTime }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <!-- 第二行 -->
        <el-row :gutter="10" style="margin-bottom: 10px; height: 320px">
          <el-col :span="8">
            <el-card class="news-card" :style="{ height: '100%' }">
              <div slot="header" class="clearfix">
                <img
                  src="@/assets/images/menhu/EHS.png"
                  style="width: 20px; height: 20px; margin-right: 5px"
                  alt=""
                  srcset=""
                />
                <span class="news-title">EHS 动态</span>
                <span style="cursor: pointer" @click="gotoMore('ehs')">
                  更多</span
                >
              </div>

              <div class="news-list">
                <div
                  class="news-item"
                  v-for="(item, index) in ehsList"
                  :key="index"
                  @click="gotoDetail(item.id)"
                >
                  <div class="news-content">
                    <span class="news-bullet"></span>
                    {{ item.title }}
                  </div>
                  <div class="news-date">{{ item.publishTime }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="news-card" :style="{ height: '100%' }">
              <div slot="header" class="clearfix">
                <img
                  src="@/assets/images/menhu/falvfagui.png"
                  style="width: 20px; height: 20px; margin-right: 5px"
                  alt=""
                  srcset=""
                />
                <span class="news-title">法律法规</span>
                <span style="cursor: pointer" @click="gotoMore('law')">
                  更多</span
                >
              </div>

              <div class="news-list">
                <div
                  class="news-item"
                  v-for="(item, index) in lawList"
                  :key="index"
                  @click="gotoDetail(item.id)"
                >
                  <div class="news-content">
                    <span class="news-bullet"></span>
                    {{ item.title }}
                  </div>
                  <div class="news-date">{{ item.publishTime }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="news-card video-card" :style="{ height: '100%' }">
              <div slot="header" class="clearfix">
                <img
                  src="@/assets/images/menhu/anquanshipin.png"
                  style="width: 20px; height: 20px; margin-right: 5px"
                  alt=""
                  srcset=""
                />
                <span class="news-title">安全视频</span>
                <span style="cursor: pointer" @click="gotoMore('video')">
                  更多</span
                >
              </div>

              <div class="video-content" @click="gotoMore('video')">
                <!-- <div v-if="isLoadingVideoList" class="loading-text">
                  加载中...
                </div> -->
                <video
                  v-if="videoList && videoList.attachmentUrl"
                  :src="`${baseUrl}${videoList.attachmentUrl}`"
                  alt="安全视频"
                  style="height: 100%; width: 100%; object-fit: cover"
                  muted
                  loop
                  autoplay
                  controls
                ></video>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <!-- 第三行 -->
        <el-row :gutter="10" style="margin-bottom: 10px; height: 224px">
          <el-col :span="24">
            <el-card class="quality-card">
              <div slot="header" class="card-header">
                <div class="card-tool">
                  <img
                    src="@/assets/images/menhu/anquanshipin.png"
                    style="width: 20px; height: 20px; margin-right: 5px"
                    alt=""
                    srcset=""
                  />
                  <span class="card-title">质量管理</span>
                </div>
                <div class="date-filter">
                  <el-select
                    v-model="selectedDate"
                    placeholder="选择日期"
                    @change="handleDateChange"
                    style="width: 100px"
                  >
                    <el-option
                      v-for="item in dateOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
              <div class="quality-content">
                <div class="quality-stats">
                  <div class="stat-item">
                    <img
                      class="stat-bg"
                      src="@/assets/images/menhu/zhiliangbg.png"
                      alt=""
                    />
                    <div class="stat-content">
                      <div class="stat-label">
                        <img
                          class="stat-icon"
                          src="@/assets/images/menhu/zhiliang.png"
                          alt=""
                        />检查总数
                      </div>
                      <div class="stat-value">
                        {{ qaList.yhzs }} <span class="unit">个</span>
                      </div>
                      <div class="stat-detail">
                        <div>
                          已完成数量<span style="margin: 0 6px">{{
                            qaList.ywcsl
                          }}</span
                          >个
                        </div>
                        <div>
                          可完成数量<span style="margin: 0 6px">{{
                            qaList.kwcsl
                          }}</span
                          >个
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="stat-item">
                    <img
                      class="stat-bg"
                      src="@/assets/images/menhu/zhiliangbg.png"
                      alt=""
                    />
                    <div class="stat-content">
                      <div class="stat-label">
                        <img
                          class="stat-icon"
                          src="@/assets/images/menhu/zhiliang.png"
                          alt=""
                        />问题总数
                      </div>
                      <div class="stat-value">
                        {{ qaList.ycxzgtgl }} <span class="unit">个</span>
                      </div>
                      <div class="stat-detail">
                        <div>
                          质量检查问题<span style="margin: 0 6px">{{
                            qaList.ycxzgtg
                          }}</span
                          >个
                        </div>
                        <div>
                          安全隐患问题<span
                            style="margin: 0 6px; color: #e85164"
                            >{{ qaList.ycxzgwtg }}</span
                          >个
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="stat-item">
                    <img
                      class="stat-bg"
                      src="@/assets/images/menhu/zhiliangbg.png"
                      alt=""
                    />
                    <div class="stat-content">
                      <div class="stat-label">
                        <img
                          class="stat-icon"
                          src="@/assets/images/menhu/zhiliang.png"
                          alt=""
                        />按时整改率
                      </div>
                      <div class="stat-value">
                        {{ qaList.aszgl * 100 }} <span class="unit">%</span>
                      </div>
                      <div class="stat-detail">
                        <div>
                          按时整改<span style="margin: 0 6px">{{
                            qaList.aszg
                          }}</span
                          >个
                        </div>
                        <div>
                          未按时整改<span
                            style="margin: 0 6px; color: #e85164"
                            >{{ qaList.waszg }}</span
                          >个
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <!-- 第四行 -->
        <el-row :gutter="10" style="margin-bottom: 10px; height: 330px">
          <el-col :span="8">
            <el-card class="overview-card">
              <div slot="header" class="card-header">
                <div class="card-tool">
                  <img
                    src="@/assets/images/menhu/anquanguanli.png"
                    style="width: 20px; height: 20px; margin-right: 5px"
                    alt=""
                    srcset=""
                  />
                  <span class="card-title">安全管理总览</span>
                </div>
              </div>
              <div class="overview-grid">
                <div class="overview-item">
                  <img
                    class="overview-icon"
                    src="@/assets/images/menhu/xms.png"
                    alt="项目"
                  />
                  <div class="overview-content">
                    <div class="overview-label">在建项目数</div>
                    <div class="overview-value">
                      {{ securityOverviewList.xmzs
                      }}<span class="unit">个</span>
                    </div>
                  </div>
                </div>
                <div class="overview-item">
                  <img
                    class="overview-icon"
                    src="@/assets/images/menhu/gcs.png"
                    alt="项目"
                  />

                  <div class="overview-content">
                    <div class="overview-label">危大工程数</div>
                    <div class="overview-value">
                      {{ securityOverviewList.wdgcs }}
                      <span class="unit">个</span>
                    </div>
                  </div>
                </div>
                <div class="overview-item">
                  <img
                    class="overview-icon"
                    src="@/assets/images/menhu/jcs.png"
                    alt="项目"
                  />

                  <div class="overview-content">
                    <div class="overview-label">安全检查数</div>
                    <div class="overview-value">
                      {{ securityOverviewList.aqjcs }}
                      <span class="unit">个</span>
                    </div>
                  </div>
                </div>
                <div class="overview-item">
                  <img
                    class="overview-icon"
                    src="@/assets/images/menhu/zgl.png"
                    alt="项目"
                  />

                  <div class="overview-content">
                    <div class="overview-label">质量检查数</div>
                    <div class="overview-value">
                      {{ securityOverviewList.zljcs }}
                      <span class="unit">个</span>
                    </div>
                  </div>
                </div>
                <div class="overview-item">
                  <img
                    class="overview-icon"
                    src="@/assets/images/menhu/flfg.png"
                    alt="项目"
                  />

                  <div class="overview-content">
                    <div class="overview-label">法律法规</div>
                    <div class="overview-value">
                      {{ securityOverviewList.flfg
                      }}<span class="unit">条</span>
                    </div>
                  </div>
                </div>
                <div class="overview-item">
                  <img
                    class="overview-icon"
                    src="@/assets/images/menhu/pxs.png"
                    alt="项目"
                  />

                  <div class="overview-content">
                    <div class="overview-label">教育培训数</div>
                    <div class="overview-value">
                      {{ securityOverviewList.aqjypx }}
                      <span class="unit">次</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <!-- 安全教育 -->
            <el-card class="sidebar-card">
              <div slot="header" class="card-header">
                <div class="card-tool">
                  <img
                    src="@/assets/images/menhu/anquanjiaoyu.png"
                    style="width: 20px; height: 20px; margin-right: 5px"
                    alt=""
                    srcset=""
                  />
                  <span class="card-title">安全教育</span>
                </div>
              </div>
              <div class="education-content">
                <div class="education-charts">
                  <div class="edu-chart">
                    <div
                      id="examChart"
                      style="width: 150px; height: 150px"
                    ></div>
                    <div class="chart-label">月度考试合格率</div>
                  </div>
                  <div class="edu-chart">
                    <div
                      id="courseChart"
                      style="width: 150px; height: 150px"
                    ></div>
                    <div class="chart-label">课程完成率</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="awards-card">
              <div slot="header" class="card-header">
                <div class="card-tool">
                  <img
                    src="@/assets/images/menhu/chuangyou.png"
                    style="width: 20px; height: 20px; margin-right: 5px"
                    alt=""
                    srcset=""
                  />
                  <span class="card-title">创优的奖项</span>
                </div>
              </div>
              <div class="awards-list">
                <div
                  class="award-item"
                  v-for="(item, index) in competitionList"
                  :key="index"
                  @click="gotoMore('awards', item.awardsType)"
                >
                  <div class="award-content">
                    <div class="award-title">{{ item.name }}</div>
                    <div class="award-count">
                      {{ item.value }} <span class="award-unit">个</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <!-- 第五行 -->
        <el-row :gutter="10" style="margin-bottom: 10px; height: 414px">
          <el-col :span="24">
            <el-card class="kpi-card">
              <div slot="header" class="card-header">
                <span class="card-title">安全管理KPI指标</span>
                <div class="card-tabs">
                  <el-button
                    type="text"
                    :class="{ active: activeTab === 'rectify' }"
                    @click="activeTab = 'rectify'"
                    >质量问题</el-button
                  >
                  <el-button
                    type="text"
                    :class="{ active: activeTab === 'hidden' }"
                    @click="activeTab = 'hidden'"
                    >安全隐患</el-button
                  >

                  <!-- <el-button
                    type="text"
                    :class="{ active: activeTab === 'training' }"
                    @click="activeTab = 'training'"
                    >培训次数</el-button
                  > -->
                </div>
                <div class="header-controls">
                  <div class="time-tabs">
                    <el-button
                      type="text"
                      :class="{ active: timeRange === 'day' }"
                      @click="changeTimeRange('day')"
                      >日</el-button
                    >
                    <el-button
                      type="text"
                      :class="{ active: timeRange === 'month' }"
                      @click="changeTimeRange('month')"
                      >月</el-button
                    >
                    <el-button
                      type="text"
                      :class="{ active: timeRange === 'year' }"
                      @click="changeTimeRange('year')"
                      >年</el-button
                    >
                  </div>
                </div>
              </div>
              <div class="kpi-chart-container">
                <div id="kpiChart" style="width: 100%; height: 350px"></div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <!-- 底部 footer -->
      <footer class="footer">
        <!-- <div class="footer-links">
          <span>友情链接:</span>
          <a href="#" target="_blank">综合项目管理系统</a>
          <a href="#" target="_blank">造价云</a>
          <a href="#" target="_blank">企业BI数据决策系统</a>
          <a href="#" target="_blank">质量管理系统</a>
          <a href="#" target="_blank">安全管理系统应用</a>
        </div> -->
      </footer>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import * as echarts from "echarts";
import swiper01 from "@/assets/images/menhu/swiper01.jpg";
import swiper02 from "@/assets/images/menhu/swiper02.jpg";
import banner from "@/assets/images/menhu/banner.png";
// import yaowen01 from "@/assets/images/menhu/yaowen01.png";
import { listZjNewsInfoPortal } from "@/api/inspection/zjNewsInfo";
import {
  getQualityAssurance,
  getSecurityOverview,
  getCompetition,
  getSecurity,
  getQuality,
} from "@/api/menhu/index";
import Navbar from "@/views/components/navBar.vue";

export default {
  name: "Menhu",
  components: {
    Navbar,
  },
  data() {
    return {
      showProject: true,
      // 判断是否有权限跳转到后台
      isAdmin: false,
      banner: banner,
      swiperList: [
        {
          src: swiper01,
          title: "安全综合门户",
        },
        {
          src: swiper02,
          title: "安全视频",
        },
      ],
      imgList: [],

      activeTab: "rectify",
      timeRange: "day",
      chartInstance: null,
      examChartInstance: null,
      courseChartInstance: null,
      generalChartInstance: null,
      hiddenChartInstance: null,
      stopChartInstance: null,
      newsList: [],
      // 通知公告数据
      noticeList: [],
      // EHS 动态数据
      ehsList: [],
      // 法律法规数据
      lawList: [],
      // 行业动态
      industryList: [],
      // 安全视频
      videoList: [],
      isLoadingVideoList: true,
      // 质量管理
      qaList: [],
      // 安全管理总览
      securityOverviewList: [],
      // 创优奖项
      competitionList: [],
      kpiData: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      // 日期选择
      selectedDate: "1",
      dateOptions: [],
    };
  },
  created() {
    const now = new Date();
    this.dateOptions = [
      {
        value: "1",
        label: `${now.getMonth() + 1}月`,
      },
      {
        value: "2",
        label: `${now.getFullYear()}年`,
      },
      // 更多日期选项
    ];
    this.getList();
  },
  mounted() {
    // 权限判断
    // console.log(this.$store, "store");
    const permissions = this.$store.getters.permissions;
    const roles = this.$store.getters.roles;
    this.isAdmin =
      (Array.isArray(permissions) && permissions.includes("system:pcadmin")) ||
      roles.includes("admin");
    this.initEducationCharts();
    this.initAnalysisCharts();

    this.getQaList();
    this.getSecurityOverviewList();
    this.getCompetitionList();
    this.getKpiData(1);
  },
  watch: {
    activeTab() {
      this.getKpiData(1);
      this.updateChart();
    },
    timeRange() {
      this.updateChart();
    },
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
    if (this.examChartInstance) {
      this.examChartInstance.dispose();
    }
    if (this.courseChartInstance) {
      this.courseChartInstance.dispose();
    }
    if (this.generalChartInstance) {
      this.generalChartInstance.dispose();
    }
    if (this.hiddenChartInstance) {
      this.hiddenChartInstance.dispose();
    }
    if (this.stopChartInstance) {
      this.stopChartInstance.dispose();
    }
  },
  computed: {
    ...mapGetters(["avatar"]),
    userName() {
      return this.$store.state.user.name || "管理员";
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
  },
  methods: {
    handleDateChange(value) {
      // console.log(value);
      // 质量管理日期选择
      this.selectedDate = value;
      this.getQaList();
    },
    changeTimeRange(type) {
      this.timeRange = type;

      switch (type) {
        case "day":
          this.getKpiData(1);
          break;
        case "month":
          this.getKpiData(2);
          break;
        case "year":
          this.getKpiData(3);
          break;

        default:
          break;
      }
    },
    // kpi数据管理
    async getKpiData(type) {
      console.log("this.timeRange", this.timeRange);
      if (this.timeRange == "day") {
        type = 1;
      } else if (this.timeRange == "month") {
        type = 2;
      } else {
        type = 3;
      }
      const params = {
        type: type,
      };
      if (this.activeTab == "rectify") {
        getQuality(params).then((res) => {
          if (res.code == 200) {
            this.kpiData = res.data;
            this.initChart();
          }
        });
      } else if (this.activeTab == "hidden") {
        getSecurity(params).then((res) => {
          if (res.code == 200) {
            this.kpiData = res.data;
            this.initChart();
          }
        });
      }
    },
    // 质量管理
    async getQaList() {
      const params = {
        timeType: this.selectedDate,
      };
      getQualityAssurance(params).then((res) => {
        if (res.code == 200) {
          this.qaList = res.data;
        }
      });
    },
    // 安全管理总览
    async getSecurityOverviewList() {
      getSecurityOverview().then((res) => {
        if (res.code == 200) {
          this.securityOverviewList = res.data;
        }
      });
    },
    // 创优奖项
    async getCompetitionList() {
      getCompetition().then((res) => {
        if (res.code == 200) {
          this.competitionList = res.data;
        }
      });
    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/qsms/login";
          });
        })
        .catch(() => {});
    },

    // 列表
    gotoMore(type, awardsType) {
      this.$router.push({
        path: "/articles",
        query: {
          type,
          awardsType,
        },
      });
    },

    // 详情
    gotoDetail(id) {
      this.$router.push({
        path: "/articleDetail",
        query: {
          id,
        },
      });
    },
    async getList() {
      this.loading = true;
      this.isLoadingVideoList = true;
      // <!-- 1-公司要闻2-通知公告3-法律法规4-行业动态 5-EHS动态 6-安全视频 -->
      const params = {
        pageNum: 1,
        pageSize: 5,
      };

      try {
        const [newsRes, noticeRes, lawRes, ehsRes, videoRes, imgRes] =
          await Promise.all([
            listZjNewsInfoPortal({ ...params, newType: "1" }),
            listZjNewsInfoPortal({ ...params, newType: "2" }),
            listZjNewsInfoPortal({ ...params, newType: "3" }),
            listZjNewsInfoPortal({ ...params, newType: "5" }),
            listZjNewsInfoPortal({ ...params, newType: "6" }),
            listZjNewsInfoPortal({
              pageNum: "1",
              pageSize: "4",
              isCarousel: "1",
            }),
          ]);

        // 处理响应数据
        this.newsList = newsRes.rows;
        this.noticeList = noticeRes.rows;
        this.lawList = lawRes.rows;
        this.ehsList = ehsRes.rows;
        this.videoList = videoRes.rows[0];
        this.imgList = imgRes.rows;
        console.log("imgList", this.imgList);
        console.log(this.videoList, "videoList");
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
      } finally {
        this.loading = false;
        this.isLoadingVideoList = false;
      }

      // listZjNewsInfoPortal(params).then((res) => {
      //   // this.zjNewsInfoList = res.rows;
      //   const data = res.data;
      //   this.newsList = data.filter((item) => item.newType == "1").slice(0, 5);
      //   // console.log(this.newsList, "newsList");
      //   this.noticeList = data
      //     .filter((item) => item.newType == "2")
      //     .slice(0, 5);
      //   this.lawList = data.filter((item) => item.newType == "3").slice(0, 5);
      //   this.industryList = data
      //     .filter((item) => item.newType == "4")
      //     .slice(0, 5);
      //   this.ehsList = data.filter((item) => item.newType == "5").slice(0, 5);
      //   this.videoList = data
      //     .filter((item) => item.newType == "6")
      //     .slice(0, 1)[0];
      //   console.log(this.videoList, "videoList");

      //   this.imgList = data
      //     .filter((item) => {
      //       if (item.isCarousel == "1") {
      //         return item;
      //       }
      //     })
      //     .slice(0, 4);
      //   this.total = res.total;
      //   this.loading = false;
      //   this.isLoadingVideoList = false;
      // });
    },
    // getCurrentData() {
    //   return this.kpiData[this.activeTab][this.timeRange];
    // },
    initChart() {
      this.$nextTick(() => {
        const chartDom = document.getElementById("kpiChart");
        if (chartDom) {
          this.chartInstance = echarts.init(chartDom);
          this.updateChart();
          // 监听窗口大小变化
          window.addEventListener("resize", () => {
            if (this.chartInstance) {
              this.chartInstance.resize();
            }
          });
        }
      });
    },
    updateChart() {
      if (!this.chartInstance) return;

      // const data = this.getCurrentData();
      const data = this.kpiData;
      const dates = data.map((item) => item.date);
      const seriousData = data.map((item) => item.serious);
      const generalData = data.map((item) => item.general);
      const commonData = data.map((item) => item.common);
      // 合并三个数组
      const allData = [...seriousData, ...generalData, ...commonData];
      console.log("allData", allData);
      // 找出最大值
      const maxValue = Math.max(...allData);
      // 为最大值添加一定余量，例如向上取整到最近的 5 的倍数
      const yAxisMax = Math.ceil(maxValue / 5) * 5;

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            let result = params[0].axisValue + "<br/>";
            params.forEach((param) => {
              result +=
                param.marker + param.seriesName + "：" + param.value + "<br/>";
            });
            return result;
          },
        },
        legend: {
          data: ["严重", "一般", "普通"],
          bottom: 10,
          itemGap: 30,
          textStyle: {
            fontSize: 14,
            color: "#333",
          },
        },
        grid: {
          left: "2%",
          right: "2%",
          top: "2%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: dates,
          axisLine: {
            lineStyle: {
              color: "#E4E7ED",
            },
          },
          axisLabel: {
            color: "#666",
            fontSize: 12,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          max: yAxisMax + yAxisMax / 5 || 5,
          min: 0,
          interval: yAxisMax > 0 ? yAxisMax / 5 : 1,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#666",
            fontSize: 12,
          },
          splitLine: {
            lineStyle: {
              color: "#F0F0F0",
              type: "solid",
            },
          },
        },
        series: [
          {
            name: "严重",
            type: "bar",
            data: seriousData,
            barWidth: 20,
            itemStyle: {
              color: "#FF8C42",
              borderRadius: [2, 2, 0, 0],
            },
            emphasis: {
              itemStyle: {
                color: "#FF7A28",
              },
            },
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return params.value === 0 ? "" : params.value; // 值为0时返回空字符串，否则显示值
              },
              color: "#333",
              fontSize: 12,
            },
          },
          {
            name: "一般",
            type: "bar",
            data: generalData,
            barWidth: 20,
            itemStyle: {
              color: "#4A90E2",
              borderRadius: [2, 2, 0, 0],
            },
            emphasis: {
              itemStyle: {
                color: "#357ABD",
              },
            },
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return params.value === 0 ? "" : params.value; // 值为0时返回空字符串，否则显示值
              },
              color: "#333",
              fontSize: 12,
            },
          },
          {
            name: "普通",
            type: "bar",
            data: commonData,
            barWidth: 20,
            itemStyle: {
              color: "#4AE268FF",
              borderRadius: [2, 2, 0, 0],
            },
            emphasis: {
              itemStyle: {
                color: "#4AE268FF",
              },
            },
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return params.value === 0 ? "" : params.value; // 值为0时返回空字符串，否则显示值
              },
              color: "#333",
              fontSize: 12,
            },
          },
        ],
      };

      this.chartInstance.setOption(option);
    },
    initEducationCharts() {
      this.$nextTick(() => {
        this.initExamChart();
        this.initCourseChart();
      });
    },
    initExamChart() {
      const chartDom = document.getElementById("examChart");
      if (chartDom) {
        this.examChartInstance = echarts.init(chartDom);
        const option = this.createPieChartOption(0, "0%");
        this.examChartInstance.setOption(option);
      }
    },
    initCourseChart() {
      const chartDom = document.getElementById("courseChart");
      if (chartDom) {
        this.courseChartInstance = echarts.init(chartDom);
        const option = this.createPieChartOption(0, "0%");
        this.courseChartInstance.setOption(option);
      }
    },
    createPieChartOption(percentage, displayText) {
      return {
        series: [
          {
            type: "pie",
            radius: ["60%", "80%"],
            center: ["50%", "50%"],
            data: [
              {
                value: percentage,
                name: "完成",
                itemStyle: {
                  color: "rgba(38,86,245,0.8)",
                },
              },
              {
                value: 100 - percentage,
                name: "未完成",
                itemStyle: {
                  color: "rgba(255,150,89,0.8)",
                },
              },
            ],
            labelLine: {
              show: false,
            },
            label: {
              show: true,
              position: "center",
              fontSize: 18,
              fontWeight: "bold",
              color: "#333",
              formatter: displayText,
            },
            emphasis: {
              scale: false,
            },
            silent: true,
          },
        ],
      };
    },
    initAnalysisCharts() {
      this.$nextTick(() => {
        this.initGeneralChart();
        this.initHiddenChart();
        this.initStopChart();
      });
    },
    initGeneralChart() {
      const chartDom = document.getElementById("generalChart");
      if (chartDom) {
        this.generalChartInstance = echarts.init(chartDom);
        const option = this.createAnalysisChartOption(12, "#4A90E2");
        this.generalChartInstance.setOption(option);
      }
    },
    initHiddenChart() {
      const chartDom = document.getElementById("hiddenChart");
      if (chartDom) {
        this.hiddenChartInstance = echarts.init(chartDom);
        const option = this.createAnalysisChartOption(38, "#FF8C42");
        this.hiddenChartInstance.setOption(option);
      }
    },
    initStopChart() {
      const chartDom = document.getElementById("stopChart");
      if (chartDom) {
        this.stopChartInstance = echarts.init(chartDom);
        const option = this.createAnalysisChartOption(50, "#FF4757");
        this.stopChartInstance.setOption(option);
      }
    },
    createAnalysisChartOption(percentage, color) {
      // 创建背景刻度数据（40个均匀间隔）
      const backgroundData = [];
      for (let i = 0; i < 40; i++) {
        backgroundData.push({
          value: 0.8,
          name: "bg" + i,
          itemStyle: {
            color: "#D7DEE6",
            borderWidth: 1,
            borderColor: "transparent",
          },
        });
        backgroundData.push({
          value: 0.2,
          name: "gap" + i,
          itemStyle: {
            color: "transparent",
          },
        });
      }

      // 创建进度数据（有间隔的进度条）
      const progressData = [];
      const totalSegments = 40; // 总段数
      const activeSegments = Math.round((percentage / 100) * totalSegments); // 激活的段数

      for (let i = 0; i < totalSegments; i++) {
        if (i < activeSegments) {
          // 激活的进度段
          progressData.push({
            value: 0.8,
            name: "progress" + i,
            itemStyle: {
              color: color,
            },
          });
        } else {
          // 未激活的进度段
          progressData.push({
            value: 0.8,
            name: "inactive" + i,
            itemStyle: {
              color: "transparent",
            },
          });
        }
        // 添加间隔
        progressData.push({
          value: 0.4,
          name: "pgap" + i,
          itemStyle: {
            color: "transparent",
          },
        });
      }

      return {
        series: [
          // 背景刻度系列
          {
            type: "pie",
            radius: ["70%", "90%"],
            center: ["50%", "50%"],
            startAngle: 90,
            data: backgroundData,
            labelLine: {
              show: false,
            },
            label: {
              show: false,
            },
            emphasis: {
              scale: false,
            },
            silent: true,
          },
          // 进度系列（有间隔）
          {
            type: "pie",
            radius: ["70%", "90%"],
            center: ["50%", "50%"],
            startAngle: 90,
            data: progressData,
            labelLine: {
              show: false,
            },
            label: {
              show: true,
              position: "center",
              fontSize: 16,
              fontWeight: "bold",
              color: "#333",
              formatter: percentage + "%",
            },
            emphasis: {
              scale: false,
            },
            silent: true,
          },
        ],
      };
    },
  },
};
</script>
<style scoped lang="scss">
.menhu-container {
  height: calc(100vh - 60px);
  background: #f5f7fa;
  overflow-y: auto;
}
.top-navbar {
  height: 60px;
  // background: linear-gradient(135deg, #4A90E2 0%, #5BA3F5 100%);
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  position: relative;

  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(35, 69, 103, 0.15);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #ebebeb;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  //   opacity: 0.9;
  //   z-index: -1;
  // }

  .nav-container {
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 20px;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-right: 100px;

    .logo-img {
      height: 85px;
      width: auto;
    }

    .system-title {
      // width: 270px;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.9);
    }
    .title-img {
      height: 25px;
      margin-top: 8px;
      margin-left: -35px;
    }
  }

  .nav-buttons {
    flex-grow: 1;
    display: flex;
    justify-content: flex-end;
    // gap: 100px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    padding: 4px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .nav-btn {
      width: 120px;
      padding: 8px 24px;
      border: none;
      background: transparent;
      font-weight: normal;
      font-size: 16px;
      color: #33373b;
      cursor: pointer;
      border-radius: 20px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      font-family: "MiSans-Regular";

      // &::before {
      //   content: '';
      //   position: absolute;
      //   top: 0;
      //   left: -100%;
      //   width: 100%;
      //   height: 100%;
      //   background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      //   transition: left 0.5s;
      // }

      &.active {
        background: #0974ff;
        border-radius: 38px 38px 38px 38px;
        color: #ffffff;
      }
      &:hover {
        background: rgba(0, 0, 0, 0.1);
        // font-family: "MiSans-Semibold";
      }
    }
  }

  .user-section {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-left: auto;

    .right-menu-item {
      color: #333;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }

    .avatar-container {
      .avatar-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 20px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 2px solid rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;

          &:hover {
            border-color: #333;
          }
        }

        .user-name {
          color: #333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  // 响应式处理
  @media (max-width: 768px) {
    .nav-container {
      padding: 0 16px;
    }

    .logo-section .system-title {
      font-size: 16px;
    }

    .nav-buttons .nav-btn {
      padding: 6px 16px;
      font-size: 12px;
    }
    .logo-img {
      height: 64px !important;
    }
    .title-img {
      height: 18px !important;
    }
  }
}
.dashboard-grid {
  display: flex;
  flex-direction: column;
  // gap: 20px;
  width: 100%;
  margin: 0 auto;
  .swiper {
    width: 100%;
    height: 45vh;
    .swiper-image {
      width: 100%;
      height: 100%; // 确保图片高度填充容器
      object-fit: cover; // 保证图片比例，覆盖容器
    }
    .el-carousel--horizontal {
      height: 45vh;
    }
    .el-carousel__item {
      color: #475669;
      font-size: 14px;
      opacity: 0.75;
      line-height: 150px;
      margin: 0;
    }
  }
}

/* 栅格容器样式 */
.grid-container {
  padding: 0 10%;
  margin-top: 10px;
  .swiper-yaowen {
    height: 320px;
    .swiper-image {
      width: 100%;
      height: 100%; // 确保图片高度填充容器
      object-fit: cover; // 保证图片比例，覆盖容器
    }
    .el-carousel--horizontal {
      height: 100%;
    }
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-weight: bold;
      font-size: 16px;
      color: #333;
    }

    .more-btn {
      font-weight: 400;
      font-size: 14px;
      color: #394d6f;
      line-height: 12px;
    }
    .card-tool {
      display: flex;
      align-items: center;
    }
  }

  .news-card {
    height: 100%;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    .clearfix {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .news-title {
      font-size: 18px;
      font-weight: bold;
      flex: 1;
    }

    .news-list {
      padding: 0;
      height: 240px;
      cursor: pointer;
    }

    .news-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 0;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
    }

    .news-item:last-child {
      border-bottom: none;
    }

    .news-content {
      flex: 1;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .news-bullet {
      display: inline-block;
      width: 6px; // 小圆点的宽度
      height: 6px; // 小圆点的高度
      border-radius: 50%; // 将元素设置为圆形
      background-color: #909399; // 灰色背景，与日期颜色一致
      margin-right: 8px;
    }

    .news-date {
      color: #909399;
      font-size: 13px;
    }
  }
  .video-card {
    height: 322px !important;
    .video-content {
      height: 274px;
    }
    ::v-deep .el-card__body {
      padding: 0px !important;
    }
  }

  // 质量管理
  .quality-card {
    padding-bottom: 6px;
    ::v-deep .el-input__inner {
      background: #ebf5ff;
      border: none;
    }

    .quality-stats {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      // gap: 15px;

      .stat-item {
        position: relative;
        width: 32%;
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, rgba(#2656f5, 0.1) 0%, #ffffff 54%);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #f5f5f6;
        padding: 20px;
        box-sizing: border-box;

        .stat-bg {
          position: absolute;
          top: 13px;
          right: 1px;
        }

        .stat-icon {
          font-size: 30px;
          margin-right: 15px;
        }

        .stat-content {
          width: 100%;

          .stat-label {
            margin-bottom: 5px;
            display: flex;
            align-items: center;

            font-weight: 500;
            font-size: 16px;
            color: #232428;
          }

          .stat-value {
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 32px;
            color: #2656f5;

            .unit {
              font-weight: 400;
              font-size: 12px;
              color: #232428;
            }
          }

          .stat-detail {
            width: 100%;
            font-weight: 400;
            font-size: 12px;
            color: #686c7f;
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }
  // 安全管理总览
  .overview-card {
    height: 100%;
    .overview-grid {
      // display: grid;
      // grid-template-columns: repeat(4, 1fr);
      // gap: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      // height: 100%;
      height: 245px;
      overflow-y: auto;
    }

    .overview-item {
      width: 166px;
      height: 69px;
      display: flex;
      align-items: center;
      padding: 10px;
      border-radius: 10px;
      background: linear-gradient(
          180deg,
          rgba(20, 121, 252, 0.1) 0%,
          rgba(20, 121, 252, 0) 100%
        ),
        #ffffff;

      .overview-icon {
        width: 44px;
        height: 44px;
        margin-right: 14px;
      }

      .overview-content {
        .overview-label {
          font-size: 16px;
          color: #262b48;
          margin-bottom: 4px;
          // white-space: nowrap;
        }

        .overview-value {
          font-weight: bold;
          font-size: 24px;
          color: #101623;

          .unit {
            font-weight: 400;
            font-size: 14px;
            color: #444444;
          }
        }
      }
    }
  }
  // 安全教育
  .education-content {
    .education-charts {
      display: flex;
      justify-content: space-around;
      align-items: center;
      height: 100%;
      padding: 33.5px 0;

      .edu-chart {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        position: relative;

        #examChart,
        #courseChart {
          background: url("../assets/images/menhu/pie-bg.png") no-repeat center
            center / 80% 80%;
        }

        .chart-label {
          font-size: 14px;
          color: #666;
          margin-top: 10px;
          font-weight: 500;
        }
      }
    }
  }
  // 创优奖项
  .awards-card {
    .awards-list {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      height: 245px;
      // 隐藏滚动条，但保留滚动功能
      overflow: hidden;
      overflow-y: auto;
      // 隐藏滚动条轨道
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;
      // 鼠标悬浮时显示滚动条
      &:hover {
        scrollbar-color: #d3d3d3 #f5f5f5;
      }

      // 针对 Chrome、Safari 和 Edge 浏览器
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: transparent;
        border-radius: 3px;
      }

      &:hover::-webkit-scrollbar-thumb {
        background-color: #d3d3d3;
      }

      .award-item {
        width: 100%;
        height: 45px;
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        background: url("../assets/images/menhu/award-bg.png") no-repeat center
          center / 100% 100%;
        // margin-bottom: 15px;
        cursor: pointer;

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        .award-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .award-title {
            margin-left: 83px;
            font-weight: 500;
            font-size: 16px;
            color: #181e2c;
          }

          .award-count {
            font-weight: bold;
            font-size: 24px;
            color: #2656f5;
          }

          .award-unit {
            font-weight: 400;
            font-size: 12px;
            color: #686c7f;
            margin-right: 15px;
          }
        }
      }
    }
  }
  .kpi-card .el-card__body {
    padding: 10px;
  }

  // KPI卡片
  .kpi-card {
    .card-header {
      .card-tabs {
        display: flex;
        gap: 20px;

        .el-button {
          padding: 8px 0;
          font-size: 14px;
          border: none;
          border-radius: 0;
          background: transparent;
          color: #666;
          position: relative;
          margin: 0;

          &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: #1479fc;
            transform: scaleX(0);
            transition: transform 0.3s ease;
          }

          &.active {
            color: #1479fc;
            font-weight: 500;

            &::after {
              transform: scaleX(1);
            }
          }

          &:hover:not(.active) {
            color: #1479fc;
          }

          &:focus {
            outline: none;
            box-shadow: none;
          }
        }
      }
      .header-controls {
        display: flex;
        gap: 20px;
        align-items: center;
      }

      .time-tabs {
        display: flex;

        .el-button {
          padding: 6px 16px;
          font-size: 14px;
          border-radius: 0;
          border: 1px solid #e4e7ed;
          color: #606266;
          margin: 0;
          position: relative;

          &:first-child {
            border-radius: 4px 0 0 4px;
          }

          &:last-child {
            border-radius: 0 4px 4px 0;
          }

          &:not(:first-child) {
            // border-left: none;
          }

          &.active {
            color: #1479fc;
            background: #f0f7ff;
            border-color: #1479fc;
            z-index: 1;
          }

          &:hover:not(.active) {
            color: #1479fc;
            border-color: #1479fc;
            z-index: 1;
          }
        }
      }
    }

    .kpi-chart-container {
      height: 360px;
    }
  }
  ::v-deep .el-card__header {
    background: #ebf5ff;
  }
}

/* 底部 footer 样式 */
.footer {
  text-align: center;
  padding: 20px;
  background-color: #ebf5ff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  .footer-links {
    display: flex;
    justify-content: space-around; // 让链接均匀分布
    max-width: 800px; // 限制链接区域宽度
    margin: 0 auto; // 水平居中

    a {
      color: #333;
      text-decoration: none;
      transition: color 0.3s;

      &:hover {
        color: #1479fc; // 鼠标悬停时改变颜色
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-grid {
    flex-direction: column;
  }

  .right-sidebar {
    width: 100%;
  }

  .overview-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: 1fr !important;
  }

  .quality-stats {
    flex-direction: column !important;
  }
}
</style>
