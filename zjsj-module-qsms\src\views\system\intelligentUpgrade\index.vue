<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="所属项目" prop="projectName">
        <selectPeopleTree
          v-model="queryParams.projectName"
          :people-list="projectList"
          placeholder="请选择所属项目"
          @change="handleSearchProjectChange"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="所属公司" prop="companyName">
        <selectPeopleTree
          ref="chargePersonName"
          v-model="queryParams.companyName"
          :people-list="companyList"
          placeholder="请搜索或选择所属公司"
          @change="handleSearchCompanyChange"
        />
      </el-form-item>
      <el-form-item label="升级项目名称" prop="upgradeName">
        <el-input
          v-model="queryParams.upgradeName"
          placeholder="请输入升级项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="升级类别" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="请选择升级类别"
          style="width: 100%"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.dictValue"
            :label="item.dictLabel"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          style="width: 100%"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:smartUpgradeProjects:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:smartUpgradeProjects:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:smartUpgradeProjects:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="smartUpgradeProjectsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="所属项目" align="center" prop="projectName" />
      <el-table-column label="所属公司" align="center" prop="companyName" />
      <el-table-column label="升级项目名称" align="center" prop="upgradeName" />
      <el-table-column label="升级类别" align="center" prop="category">
        <template slot-scope="scope">
          <span>{{
            getDictLabel(categoryOptions, scope.row.category) ||
            scope.row.category ||
            "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        prop="status"
        :formatter="formatStatus"
      />
      <el-table-column label="附件" align="center" prop="smartUpgradeProjects">
        <template slot-scope="scope">
          <div v-if="scope.row.smartUpgradeProjects" class="contract-file">
            <div
              v-for="(item, index) in scope.row.smartUpgradeProjects.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="280"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:smartUpgradeProjects:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:smartUpgradeProjects:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改智能化升级项目对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="160px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-form-item label="所属项目" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择所属项目"
            @change="handleFormSearchProjectChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="所属公司" prop="companyName">
          <selectPeopleTree
            v-model="form.companyName"
            :people-list="companyList"
            placeholder="请选择所属公司"
            @change="handleFormSearchCompanyChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="升级项目名称" prop="upgradeName">
          <el-input
            v-model="form.upgradeName"
            placeholder="请输入升级项目名称"
            :disabled="isCheck"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="升级类别" prop="category">
          <el-select
            v-model="form.category"
            placeholder="请选择升级类别"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请理由/现状分析" prop="applicationReason">
          <el-input
            v-model="form.applicationReason"
            type="textarea"
            placeholder="请输入内容"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="技术方案与实施内容" prop="technicalSolution">
          <el-input
            v-model="form.technicalSolution"
            type="textarea"
            placeholder="请输入内容"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="申报预算(万元)" prop="plannedBudget">
          <el-input
            v-model="form.plannedBudget"
            placeholder="请输入申报预算"
            :disabled="isCheck"
            @input="handlePlannedBudgetInput"
            @blur="handlePlannedBudgetBlur"
          />
        </el-form-item>
        <el-form-item label="批复预算(万元)" prop="approvedBudget">
          <el-input
            v-model="form.approvedBudget"
            placeholder="请输入批复预算"
            @input="handleApprovedBudgetInput"
            @blur="handleApprovedBudgetBlur"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="计划开始日期" prop="plannedStartDate">
          <el-date-picker
            clearable
            v-model="form.plannedStartDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划开始日期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划完成日期" prop="plannedEndDate">
          <el-date-picker
            clearable
            v-model="form.plannedEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划完成日期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际开始日期" prop="actualStartDate">
          <el-date-picker
            clearable
            v-model="form.actualStartDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择实际开始日期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际完成日期" prop="actualEndDate">
          <el-date-picker
            clearable
            v-model="form.actualEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择实际完成日期"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="预期达成的目标" prop="kpiIndicators">
          <el-input
            v-model="form.kpiIndicators"
            type="textarea"
            placeholder="请输入内容"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="实际达成情况" prop="actualKpiResults">
          <el-input
            v-model="form.actualKpiResults"
            type="textarea"
            placeholder="请输入内容"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择状态"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="附件" prop="smartUpgradeProjects">
          <file-upload
            v-model="form.smartUpgradeProjects"
            :disabled="isCheck"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSmartUpgradeProjects,
  getSmartUpgradeProjects,
  delSmartUpgradeProjects,
  addSmartUpgradeProjects,
  updateSmartUpgradeProjects,
} from "@/api/system/intelligentUpgrade/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { getDicts } from "@/api/system/dict/data";
import { getFileOrignalName } from "@/utils/common.js";
import { querytree } from "@/api/system/info";
import { getEnterpriseInfo } from "@/api/system/info";
export default {
  name: "SmartUpgradeProjects",
  components: {
    selectPeopleTree,
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      statusOptions: [
        {
          label: "草稿",
          value: 1,
        },
        {
          label: "已提交(待审批)",
          value: 2,
        },
        {
          label: "已批准",
          value: 3,
        },
        {
          label: "实施中",
          value: 4,
        },
        {
          label: "已完成",
          value: 5,
        },
        {
          label: "已验收",
          value: 6,
        },
      ],
      isCheck: false,
      categoryOptions: [],
      projectList: [],
      companyList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 智能化升级项目表格数据
      smartUpgradeProjectsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        projectName: null,
        companyId: null,
        companyName: null,
        upgradeName: null,
        category: null,
        applicationReason: null,
        technicalSolution: null,
        plannedBudget: null,
        approvedBudget: null,
        plannedStartDate: null,
        plannedEndDate: null,
        actualStartDate: null,
        actualEndDate: null,
        kpiIndicators: null,
        actualKpiResults: null,
        status: null,
        smartUpgradeProjects: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          {
            required: true,
            message: "所属项目不能为空",
            trigger: "blur",
          },
        ],
        companyName: [
          {
            required: true,
            message: "所属公司不能为空",
            trigger: "blur",
          },
        ],
        upgradeName: [
          { required: true, message: "升级项目名称不能为空", trigger: "blur" },
        ],
        category: [
          { required: true, message: "升级类别不能为空", trigger: "blur" },
        ],
        applicationReason: [
          {
            required: true,
            message: "申请理由/现状分析不能为空",
            trigger: "blur",
          },
        ],
        technicalSolution: [
          {
            required: true,
            message: "技术方案与实施内容不能为空",
            trigger: "blur",
          },
        ],
        plannedBudget: [
          { required: true, message: "申报预算不能为空", trigger: "blur" },
        ],
        plannedStartDate: [
          { required: true, message: "计划开始日期不能为空", trigger: "blur" },
        ],
        plannedEndDate: [
          { required: true, message: "计划完成日期不能为空", trigger: "blur" },
        ],
        kpiIndicators: [
          {
            required: true,
            message: "预期达成的目标不能为空",
            trigger: "blur",
          },
        ],
        status: [
          {
            required: true,
            message: "状态不能为空",
            trigger: "change",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    this.loadDict();
    this.getCompanyList();
  },
  methods: {
    handlePlannedBudgetInput(value) {
      let val = value.replace(/[^\d.]/g, "");
      val = val.replace(/\.{2,}/g, ".");
      val = val.replace(/^\./g, "");
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      if (!val || val === ".") {
        val = "0";
      }
      this.form.plannedBudget = val;
    },
    handlePlannedBudgetBlur(value) {
      let val = value.replace(/[^\d.]/g, "");
      if (val && val[val.length - 1] === ".") {
        val = "0";
        this.form.plannedBudget = val;
      }
    },
    handleApprovedBudgetInput(value) {
      let val = value.replace(/[^\d.]/g, "");
      val = val.replace(/\.{2,}/g, ".");
      val = val.replace(/^\./g, "");
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      if (!val || val === ".") {
        val = "0";
      }
      this.form.approvedBudget = val;
    },
    handleApprovedBudgetBlur(value) {
      let val = value.replace(/[^\d.]/g, "");
      if (val && val[val.length - 1] === ".") {
        val = "0";
        this.form.approvedBudget = val;
      }
    },
    formatStatus(row, column, value) {
      const option = this.statusOptions.find((item) => item.value == value);
      return option ? option.label : value;
    },
    /** 获取字典标签 */
    getDictLabel(dictOptions, value) {
      if (!dictOptions || !Array.isArray(dictOptions) || !value) {
        return "";
      }
      const dict = dictOptions.find(
        (item) => item.dictValue === value || item.dictValue === String(value)
      );
      return dict ? dict.dictLabel : "";
    },
    loadDict() {
      getDicts("upgrade_category").then((response) => {
        this.categoryOptions = response.data;
      });
    },
    handleSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectId = selectedItem.id;
        this.queryParams.projectName = selectedItem.label;
      } else {
        this.queryParams.projectId = null;
      }
    },
    handleSearchCompanyChange(selectedItem) {
      if (selectedItem) {
        this.queryParams.companyName = selectedItem.label;
        this.queryParams.companyId = selectedItem.id;
      } else {
        this.queryParams.companyName = null;
        this.queryParams.companyId = null;
      }
    },
    handleFormSearchProjectChange(selectedItem) {
      // console.log("选中项目", selectedItem);
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectId = selectedItem.id;
        this.form.projectName = selectedItem.label;
      } else {
        this.form.projectId = null;
        this.form.projectName = null;
      }
    },
    handleFormSearchCompanyChange(selectedItem) {
      // console.log("选中公司", selectedItem);
      if (selectedItem) {
        this.form.companyId = selectedItem.id;
        this.form.companyName = selectedItem.label;
      } else {
        this.form.companyId = null;
        this.form.companyName = null;
      }
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    getFileOrignalName,
    /** 查询智能化升级项目列表 */
    getList() {
      this.loading = true;
      listSmartUpgradeProjects(this.queryParams).then((response) => {
        this.smartUpgradeProjectsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        projectName: null,
        companyId: null,
        companyName: null,
        upgradeName: null,
        category: null,
        applicationReason: null,
        technicalSolution: null,
        plannedBudget: null,
        approvedBudget: null,
        plannedStartDate: null,
        plannedEndDate: null,
        actualStartDate: null,
        actualEndDate: null,
        kpiIndicators: null,
        actualKpiResults: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        smartUpgradeProjects: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.companyId = null;
      this.queryParams.projectId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加智能化升级项目";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSmartUpgradeProjects(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改智能化升级项目";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getSmartUpgradeProjects(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "查看智能化升级项目";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSmartUpgradeProjects(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSmartUpgradeProjects(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除智能化升级项目编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSmartUpgradeProjects(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/smartUpgradeProjects/export",
        {
          ...this.queryParams,
        },
        `智能化升级.xlsx`
      );
    },
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>

