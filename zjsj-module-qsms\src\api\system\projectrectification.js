import request from '@/utils/request'

// 查询隐患统计列表
export function statListProjectrectification(query) {
  return request({
    url: '/pc/projectrectification/queryHazardStatisticsList',
    method: 'get',
    params: query
  })
}

// 查询隐患列表
export function listProjectrectification(query) {
  return request({
    url: '/pc/projectrectification/list',
    method: 'get',
    params: query
  })
}

// 查询隐患详细
export function getProjectrectification(projectrectificationid) {
  return request({
    url: '/system/projectrectification/' + projectrectificationid,
    method: 'get'
  })
}

// 新增隐患
export function addProjectrectification(data) {
  return request({
    url: '/system/projectrectification',
    method: 'post',
    data: data
  })
}

// 修改隐患
export function updateProjectrectification(data) {
  return request({
    url: '/pc/projectrectification',
    method: 'put',
    data: data
  })
}

// 删除隐患
export function delProjectrectification(projectrectificationid) {
  return request({
    url: '/pc/projectrectification/' + projectrectificationid,
    method: 'delete'
  })
}


// 设为典型
export function setTypical(data) {
  return request({
    url: '/pc/projectrectification/updateIsTypical',
    method: 'post',
    data: data
  })
}