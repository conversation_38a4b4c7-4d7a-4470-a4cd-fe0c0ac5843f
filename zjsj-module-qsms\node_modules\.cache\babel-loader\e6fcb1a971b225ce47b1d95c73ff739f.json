{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\api\\inspection\\zjEmployeeCasualtyAccidents.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\api\\inspection\\zjEmployeeCasualtyAccidents.js", "mtime": 1757491066000}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1757382153351}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listZjEmployeeCasualtyAccidents", "query", "request", "url", "method", "params", "getZjEmployeeCasualtyAccidents", "id", "addZjEmployeeCasualtyAccidents", "data", "updateZjEmployeeCasualtyAccidents", "delZjEmployeeCasualtyAccidents"], "sources": ["C:/Users/<USER>/Desktop/zhian/zjsj-module-qsms/src/api/inspection/zjEmployeeCasualtyAccidents.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询职工伤亡事故列表\r\nexport function listZjEmployeeCasualtyAccidents(query) {\r\n  return request({\r\n    url: '/inspection/zjEmployeeCasualtyAccidents/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询职工伤亡事故详细\r\nexport function getZjEmployeeCasualtyAccidents(id) {\r\n  return request({\r\n    url: '/inspection/zjEmployeeCasualtyAccidents/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增职工伤亡事故\r\nexport function addZjEmployeeCasualtyAccidents(data) {\r\n  return request({\r\n    url: '/inspection/zjEmployeeCasualtyAccidents',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改职工伤亡事故\r\nexport function updateZjEmployeeCasualtyAccidents(data) {\r\n  return request({\r\n    url: '/inspection/zjEmployeeCasualtyAccidents',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除职工伤亡事故\r\nexport function delZjEmployeeCasualtyAccidents(id) {\r\n  return request({\r\n    url: '/inspection/zjEmployeeCasualtyAccidents/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,+BAA+BA,CAACC,KAAK,EAAE;EACrD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,8BAA8BA,CAACC,EAAE,EAAE;EACjD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0CAA0C,GAAGI,EAAE;IACpDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,8BAA8BA,CAACC,IAAI,EAAE;EACnD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iCAAiCA,CAACD,IAAI,EAAE;EACtD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,8BAA8BA,CAACJ,EAAE,EAAE;EACjD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0CAA0C,GAAGI,EAAE;IACpDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}