<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="所属分公司" prop="companyName">
        <selectComponyTree
            
          v-model="queryParams.companyName"
          :people-list="companyList"
          placeholder="请搜索或选择所属分公司"
          @change="handleCompanyChange"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          placeholder="选择年份"
          value-format="yyyy"
          size="small"
          style="width: 120px"
        />
      </el-form-item>
      <el-form-item label="开始月份" prop="start">
        <el-select
          v-model="queryParams.start"
          placeholder="选择开始月份"
          size="small"
          style="width: 120px"
        >
          <el-option
            v-for="month in 12"
            :key="month"
            :label="month + '月'"
            :value="month.toString()"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          @click="handlePrint"
          >打印</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      height="calc(100vh - 230px)"
      border
      stripe
      :span-method="spanMethod"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.key"
        :prop="column.key"
        :label="column.name"
        :width="column.width || 'auto'"
        :min-width="column.minWidth || 120"
        align="center"
        show-overflow-tooltip
      />
    </el-table>
  </div>
</template>

<script>
import { queryConstructionProjectView } from "@/api/report/index";
import selectComponyTree from "@/views/components/selectComponyTree.vue";
import { getEnterpriseInfo } from "@/api/system/info";
// 在建项目报表
export default {
  name: "ConstructionProjectReport",
  components: {
    selectComponyTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格列配置
      columns: [],
      // 表格数据
      dataList: [],
      companyList: [],
      // 查询参数
      queryParams: {
        year: "2025", // 年份
        start: "1", // 开始月份
        company: "", // 所属分公司
        companyName: "", // 所属分公司
      },
    };
  },
  computed: {
    /** 处理数据行 */
    tableData() {
      if (!this.dataList || this.dataList.length === 0) {
        return [];
      }
      return this.dataList.map((item, index) => ({
        ...item,
      }));
    },
  },
  created() {
    this.getList();
    this.getCompanyList();
  },
  methods: {
    handleCompanyChange(selectedItem) {
      if (selectedItem) {
        this.queryParams.companyName = selectedItem.label;
        this.queryParams.company = selectedItem.id;
      } else {
        this.queryParams.companyName = null;
        this.queryParams.company = null;
      }
      this.$forceUpdate();
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },

    /** 查询在建项目报表数据 */
    getList() {
      this.loading = true;
      queryConstructionProjectView(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.columns = res.data.columns || [];
            this.dataList = res.data.dataList || [];
            this.total = this.dataList.length;
          } else {
            this.$message.error(res.msg || "查询失败");
            this.columns = [];
            this.dataList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.columns = [];
          this.dataList = [];
          this.total = 0;
        });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        year: "2025",
        start: "1",
        company: "",
        companyName: "",
      };
      this.getList();
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "report/constructionProject/export",
        {
          ...this.queryParams,
        },
        `${this.queryParams.year}年${this.queryParams.start}月在建项目一览表.xlsx`
      );
    },

    /** 打印按钮操作 */
    handlePrint() {
      if (this.tableData.length === 0) {
        this.$message.warning("暂无数据可打印");
        return;
      }

      // 创建打印内容
      const printContent = this.generatePrintContent();

      // 创建新窗口进行打印
      const printWindow = window.open("", "_blank");
      printWindow.document.write(printContent);
      printWindow.document.close();

      // 使用setTimeout确保内容完全加载后再打印
      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        // 打印对话框关闭后关闭窗口
        printWindow.onafterprint = function () {
          printWindow.close();
        };
        // 如果用户取消打印，也关闭窗口
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.close();
          }
        }, 1000);
      }, 100);
    },

    /** 生成打印内容 */
    generatePrintContent() {
      const year = this.queryParams.year;
      const startMonth = this.queryParams.start;

      // 生成表格
      const printTable = this.generatePrintTable();

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${year}年${startMonth}月在建项目一览表</title>
          <style>
            body {
              font-family: "Microsoft YaHei", Arial, sans-serif;
              margin: 20px;
              font-size: 12px;
            }
            .print-header {
              text-align: center;
              margin-bottom: 20px;
            }
            .print-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .print-info {
              font-size: 14px;
              margin-bottom: 20px;
            }
            .print-table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
            }
            .print-table th,
            .print-table td {
              border: 1px solid #000;
              padding: 8px;
              text-align: center;
            }
            .print-table th {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            .print-footer {
              margin-top: 20px;
              text-align: right;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .print-header { page-break-after: avoid; }
              .print-table { page-break-inside: auto; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <div class="print-title">${year}年${startMonth}月在建项目一览表</div>
           
          </div>

          ${printTable}

          <div class="print-footer">
            共 ${this.tableData.length} 条记录
          </div>
        </body>
        </html>
      `;
    },

    /** 合并单元格的方法 */
    spanMethod({ row, column, rowIndex, columnIndex }) {
      const columnKey = this.columns[columnIndex]?.key;
      const currentValue = row[columnKey];

      // 如果当前行的index字段为空或null或者NaN，则合并当前行的所有列
      if (
        columnKey === "index" &&
        (currentValue === "" || currentValue === null || isNaN(currentValue))
      ) {
        return [1, Object.keys(row).length];
      }

      // 默认不合并
      return [1, 1];
    },

    /** 生成打印表格 */
    generatePrintTable() {
      if (!this.dataList || this.dataList.length === 0) {
        return "<p>暂无数据</p>";
      }

      let tableHtml = '<table class="print-table">';

      // 生成表头
      tableHtml += "<thead><tr>";
      this.columns.forEach((column) => {
        tableHtml += `<th>${column.name}</th>`;
      });
      tableHtml += "</tr></thead>";

      // 生成数据行
      tableHtml += "<tbody>";
      this.tableData.forEach((row, index) => {
        if (row.index === "" || row.index === null || isNaN(row.index)) {
          tableHtml += "<tr >";
          let value = row["index"],
            i = 0;
          while (!value && i < this.columns.length) {
            value = row[this.columns[i].key];
            i++;
          }

          tableHtml += `<td colspan="${Object.keys(row).length}">${value}</td>`;
        } else {
          tableHtml += "<tr>";
          this.columns.forEach((column) => {
            const value = row[column.key] || "";
            tableHtml += `<td>${value}</td>`;
          });
        }

        tableHtml += "</tr>";
      });
      tableHtml += "</tbody></table>";

      return tableHtml;
    },
  },
};
</script>
<style scoped lang="scss">
// 在建项目报表样式
</style>
