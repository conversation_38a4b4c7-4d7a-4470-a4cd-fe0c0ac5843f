import request from '@/utils/request'

// 查询应急演练管理（计划、过程、评估及多媒体记录）列表
export function listManagement(query) {
  return request({
    url: '/system/drillManagement/list',
    method: 'get',
    params: query
  })
}

// 查询应急演练管理（计划、过程、评估及多媒体记录）详细
export function getManagement(id) {
  return request({
    url: '/system/drillManagement/' + id,
    method: 'get'
  })
}

// 新增应急演练管理（计划、过程、评估及多媒体记录）
export function addManagement(data) {
  return request({
    url: '/system/drillManagement',
    method: 'post',
    data: data
  })
}

// 修改应急演练管理（计划、过程、评估及多媒体记录）
export function updateManagement(data) {
  return request({
    url: '/system/drillManagement',
    method: 'put',
    data: data
  })
}

// 删除应急演练管理（计划、过程、评估及多媒体记录）
export function delManagement(id) {
  return request({
    url: '/system/drillManagement/' + id,
    method: 'delete'
  })
}
