import request from '@/utils/request'

// 查询建造师信息列表
export function listInfo(query) {
  return request({
    url: '/system/constructorInfo/list',
    method: 'get',
    params: query
  })
}

// 查询建造师信息详细
export function getInfo(id) {
  return request({
    url: '/system/constructorInfo/' + id,
    method: 'get'
  })
}

// 新增建造师信息
export function addInfo(data) {
  return request({
    url: '/system/constructorInfo',
    method: 'post',
    data: data
  })
}

// 修改建造师信息
export function updateInfo(data) {
  return request({
    url: '/system/constructorInfo',
    method: 'put',
    data: data
  })
}

// 删除建造师信息
export function delInfo(id) {
  return request({
    url: '/system/constructorInfo/' + id,
    method: 'delete'
  })
}
