import request from '@/utils/request'

// 查询安全考核汇总（汇总各单位安全考核结果）列表
export function listSummary(query) {
  return request({
    url: '/system/summary/list',
    method: 'get',
    params: query
  })
}

// 查询安全考核汇总（汇总各单位安全考核结果）详细
export function getSummary(id) {
  return request({
    url: '/system/summary/' + id,
    method: 'get'
  })
}

// 新增安全考核汇总（汇总各单位安全考核结果）
export function addSummary(data) {
  return request({
    url: '/system/summary',
    method: 'post',
    data: data
  })
}

// 修改安全考核汇总（汇总各单位安全考核结果）
export function updateSummary(data) {
  return request({
    url: '/system/summary',
    method: 'put',
    data: data
  })
}

// 删除安全考核汇总（汇总各单位安全考核结果）
export function delSummary(id) {
  return request({
    url: '/system/summary/' + id,
    method: 'delete'
  })
}
