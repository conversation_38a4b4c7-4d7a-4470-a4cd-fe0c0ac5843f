<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="120px"
    >
      <el-form-item label="预算年度" prop="budgetYear">
        <el-date-picker
          v-model="queryParams.budgetYear"
          clearable
          type="year"
          value-format="yyyy"
          placeholder="请选择预算年度"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:investment:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:investment:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:investment:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="investmentList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column
        label="预算年度"
        align="center"
        prop="budgetYear"
        width="100"
      />
      <el-table-column label="所属公司" align="center" prop="company" />
      <el-table-column
        label="年度预算金额(万元)"
        align="center"
        prop="annualBudgetAmount"
      />
      <el-table-column
        label="实际投入金额(万元)"
        align="center"
        prop="actualInputAmount"
      />
      <el-table-column
        label="剩余预算金额(万元)"
        align="center"
        prop="remainingBudgetAmount"
      />
      <el-table-column
        label="投入说明"
        align="center"
        prop="inputDescription"
      />
      <el-table-column label="凭证附件" align="center" prop="voucherAttachment">
        <template slot-scope="scope">
          <div v-if="scope.row.voucherAttachment" class="contract-file">
            <div
              v-for="(item, index) in scope.row.voucherAttachment.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
              <!-- {{ item.split("/").pop() }} -->
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            v-hasPermi="['system:investment:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['system:investment:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或年度计划对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="预算年度" prop="budgetYear">
          <el-date-picker
            v-model="form.budgetYear"
            clearable
            type="year"
            value-format="yyyy"
            placeholder="请选择预算年度"
            style="width: 100%"
            :disabled="isCheck"
          />
        </el-form-item>
        <!-- <el-form-item  orm-item label="责任部门" prop="responsibleDepartment">
          <el-input
            v-model="form.responsibleDepartment"
            placeholder="请输入责任部门"
          />
        </el-form-item> -->
        <el-form-item label="所属公司" prop="company">
          <selectPeopleTree
            ref="chargePersonName"
            v-model="form.company"
            :people-list="companyList"
            placeholder="请搜索或选择所属公司"
            @change="handleChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="年度预算金额(万元)" prop="annualBudgetAmount">
          <el-input
            v-model="form.annualBudgetAmount"
            placeholder="请输入年度预算金额(万元)"
            @input="handleInput"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="实际投入金额(万元)" prop="actualInputAmount">
          <el-input
            v-model="form.actualInputAmount"
            placeholder="请输入实际投入金额(万元)"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item
          label="剩余预算金额，自动计算"
          prop="remainingBudgetAmount"
        >
          <el-input
            v-model="form.remainingBudgetAmount"
            placeholder="请输入剩余预算金额"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="投入说明" prop="inputDescription">
          <el-input
            v-model="form.inputDescription"
            type="textarea"
            :rows="5"
            placeholder="请输入投入说明"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="凭证附件" prop="voucherAttachment">
          <file-upload
            v-model="form.voucherAttachment"
            :file-type="['pdf', 'docx', 'png', 'jpg', 'doc', 'xls', 'xlsx']"
            :disabled="isCheck"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="isCheck"
          >确 定</el-button
        >
        <el-button @click="cancel" :disabled="isCheck">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInvestment,
  getInvestment,
  delInvestment,
  addInvestment,
  updateInvestment,
} from "@/api/system/annualPlanTable/index";
import { getEnterpriseInfo } from "@/api/system/info";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { getFileOrignalName } from "@/utils/common.js";
export default {
  name: "Investment",
  components: {
    AttachmentDialog,
    selectPeopleTree,
  },
  data() {
    return {
      isCheck: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      companyList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全投入年度计划表格数据
      investmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        budgetYear: null,
        // responsibleDepartment: null,
      },
      // 表单参数
      form: {
        responsibleDepartment: "default",
      },
      // 表单校验
      rules: {
        budgetYear: [
          { required: true, message: "请选择预算年度", trigger: "change" },
        ],
        company: [
          { required: true, message: "请选择所属公司", trigger: "change" },
        ],
        annualBudgetAmount: [
          { required: true, message: "请输入年度预算金额", trigger: "blur" },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    this.getList();
    this.getCompanyList();
  },
  methods: {
    getFileOrignalName,
    /** 查询安全投入年度计划列表 */
    getList() {
      this.loading = true;
      listInvestment(this.queryParams).then((response) => {
        this.investmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleInput(value) {
      // 清除除数字和小数点外的所有字符
      let val = value.replace(/[^\d.]/g, "");
      // 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");
      // 确保小数点不在开头
      val = val.replace(/^\./g, "");
      // 只保留两位小数
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      // 更新输入值
      this.form.annualBudgetAmount = val;
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        budgetYear: null,
        responsibleDepartment: "default",
        company: null,
        annualBudgetAmount: null,
        actualInputAmount: null,
        remainingBudgetAmount: null,
        inputDescription: null,
        voucherAttachment: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加安全投入年度计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInvestment(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改安全投入年度计划";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getInvestment(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "修改安全投入年度计划";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateInvestment(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInvestment(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全投入年度计划编号为"' + ids + '"的数据项？')
        .then(function () {
          return delInvestment(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/investment/export",
        {
          ...this.queryParams,
        },
        `investment_${new Date().getTime()}.xlsx`
      );
    },
    handleChange(selectedItem) {
      if (selectedItem) {
        this.form.company = selectedItem.label;
      } else {
        this.form.company = null;
      }
    },
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.el-textarea {
  width: 100%;

  :deep(.el-textarea__inner) {
    font-family: inherit;
    color: red !important;
  }
}
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}

// ::v-deep.el-textarea__inner {
//   display: block;
//   resize: vertical;
//   padding: 5px 15px;
//   line-height: 1.5;
//   -webkit-box-sizing: border-box;
//   box-sizing: border-box;
//   width: 100%;
//   font-size: inherit;
//   font-weight: 550 !important;
//   color: #606266 !important;
//   background-color: #ffffff;
//   background-image: none;
//   border: 1px solid #dcdfe6;
//   border-radius: 4px;
//   -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
//   transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
// }
</style>

<style>
.el-textarea__inner {
  font-family: inherit !important;
}
</style>
