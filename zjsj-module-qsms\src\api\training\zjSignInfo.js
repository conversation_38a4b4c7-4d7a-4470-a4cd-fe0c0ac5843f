import request from '@/utils/request'

// 查询培训学员报名信息列表
export function listZjSignInfo(query) {
  return request({
    url: '/training/zjSignInfo/list',
    method: 'get',
    params: query
  })
}

// 查询培训学员报名信息详细
export function getZjSignInfo(id) {
  return request({
    url: '/training/zjSignInfo/' + id,
    method: 'get'
  })
}

// 新增培训学员报名信息
export function addZjSignInfo(data) {
  return request({
    url: '/training/zjSignInfo',
    method: 'post',
    data: data
  })
}

// 修改培训学员报名信息
export function updateZjSignInfo(data) {
  return request({
    url: '/training/zjSignInfo',
    method: 'put',
    data: data
  })
}

// 删除培训学员报名信息
export function delZjSignInfo(id) {
  return request({
    url: '/training/zjSignInfo/' + id,
    method: 'delete'
  })
}

// 导出培训学员报名信息
export function exportZjSignInfo(query) {
  return request({
    url: '/training/zjSignInfo/export',
    method: 'get',
    params: query
  })
}
