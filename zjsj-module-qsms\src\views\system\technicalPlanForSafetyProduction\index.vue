<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="方案编号" prop="planCode">
        <el-input
          v-model="queryParams.planCode"
          placeholder="请输入方案编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="方案名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入方案名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="所属项目" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入所属项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制人" prop="compiler">
        <el-input
          v-model="queryParams.compiler"
          placeholder="请输入编制人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制部门" prop="compileDept">
        <el-input
          v-model="queryParams.compileDept"
          placeholder="请输入编制部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制日期" prop="compileTime">
        <el-date-picker
          clearable
          v-model="queryParams.compileTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择编制日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="方案版本号" prop="version">
        <el-input
          v-model="queryParams.version"
          placeholder="请输入方案版本号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级审核人" prop="reviewer1">
        <el-input
          v-model="queryParams.reviewer1"
          placeholder="请输入一级审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="一级审核时间" prop="reviewer1Time">
        <el-date-picker
          clearable
          v-model="queryParams.reviewer1Time"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择一级审核时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="二级审核人" prop="reviewer2">
        <el-input
          v-model="queryParams.reviewer2"
          placeholder="请输入二级审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二级审核时间" prop="reviewer2Time">
        <el-date-picker
          clearable
          v-model="queryParams.reviewer2Time"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择二级审核时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="最终审批人" prop="approver">
        <el-input
          v-model="queryParams.approver"
          placeholder="请输入最终审批人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最终审批时间" prop="approveTime">
        <el-date-picker
          clearable
          v-model="queryParams.approveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择最终审批时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="生效日期" prop="effectiveDate">
        <el-date-picker
          clearable
          v-model="queryParams.effectiveDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择生效日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="失效日期" prop="expireDate">
        <el-date-picker
          clearable
          v-model="queryParams.expireDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择失效日期"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:approval:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:approval:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:approval:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:approval:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="approvalList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="方案编号" align="center" prop="planCode" />
      <el-table-column label="方案名称" align="center" prop="planName" />
      <el-table-column label="所属项目" align="center" prop="projectName" />
      <!-- <el-table-column
        label="技术类型：基础工程/结构工程/起重吊装/拆除工程/消防工程/其他"
        align="center"
        prop="techType"
      />
      <el-table-column
        label="风险等级：一般/重大/极危"
        align="center"
        prop="riskLevel"
      /> -->
      <el-table-column label="编制人" align="center" prop="compiler" />
      <el-table-column label="编制部门" align="center" prop="compileDept" />
      <el-table-column
        label="编制日期"
        align="center"
        prop="compileTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.compileTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="方案版本号" align="center" prop="version" />
      <el-table-column label="方案适用范围" align="center" prop="planScope" />
      <el-table-column label="核心技术内容" align="center" prop="coreContent" />
      <el-table-column
        label="方案文件地址"
        align="center"
        prop="attachmentUrl"
      />
      <!-- <el-table-column
        label="审批状态：草稿/待提交/审核中/修改中/已批准/已驳回"
        align="center"
        prop="approvalStatus"
      />
      <el-table-column label="一级审核人" align="center" prop="reviewer1" />
      <el-table-column
        label="一级审核时间"
        align="center"
        prop="reviewer1Time"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reviewer1Time, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="一级审核意见"
        align="center"
        prop="reviewer1Opinion"
      />
      <el-table-column label="二级审核人" align="center" prop="reviewer2" />
      <el-table-column
        label="二级审核时间"
        align="center"
        prop="reviewer2Time"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reviewer2Time, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="二级审核意见"
        align="center"
        prop="reviewer2Opinion"
      />
      <el-table-column label="最终审批人" align="center" prop="approver" />
      <el-table-column
        label="最终审批时间"
        align="center"
        prop="approveTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approveTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="最终审批意见"
        align="center"
        prop="approveOpinion"
      />
      <el-table-column label="驳回原因" align="center" prop="rejectReason" />
      <el-table-column
        label="生效日期"
        align="center"
        prop="effectiveDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.effectiveDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="失效日期"
        align="center"
        prop="expireDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expireDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="实施状态"
        align="center"
        prop="implementationStatus"
      />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:approval:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:approval:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全生产技术方案审批对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="方案编号" prop="planCode">
          <el-input v-model="form.planCode" placeholder="请输入方案编号" />
        </el-form-item>
        <el-form-item label="方案名称" prop="planName">
          <el-input v-model="form.planName" placeholder="请输入方案名称" />
        </el-form-item>
        <el-form-item label="所属项目" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入所属项目" />
        </el-form-item>
        <el-form-item label="编制人" prop="compiler">
          <el-input v-model="form.compiler" placeholder="请输入编制人" />
        </el-form-item>
        <el-form-item label="编制部门" prop="compileDept">
          <el-input v-model="form.compileDept" placeholder="请输入编制部门" />
        </el-form-item>
        <el-form-item label="编制日期" prop="compileTime">
          <el-date-picker
            clearable
            v-model="form.compileTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择编制日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="方案版本号" prop="version">
          <el-input v-model="form.version" placeholder="请输入方案版本号" />
        </el-form-item>
        <el-form-item label="方案适用范围" prop="planScope">
          <el-input
            v-model="form.planScope"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="核心技术内容">
          <editor v-model="form.coreContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="方案文件地址" prop="attachmentUrl">
          <el-input
            v-model="form.attachmentUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="一级审核人" prop="reviewer1">
          <el-input v-model="form.reviewer1" placeholder="请输入一级审核人" />
        </el-form-item>
        <el-form-item label="一级审核时间" prop="reviewer1Time">
          <el-date-picker
            clearable
            v-model="form.reviewer1Time"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择一级审核时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="一级审核意见" prop="reviewer1Opinion">
          <el-input
            v-model="form.reviewer1Opinion"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="二级审核人" prop="reviewer2">
          <el-input v-model="form.reviewer2" placeholder="请输入二级审核人" />
        </el-form-item>
        <el-form-item label="二级审核时间" prop="reviewer2Time">
          <el-date-picker
            clearable
            v-model="form.reviewer2Time"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择二级审核时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="二级审核意见" prop="reviewer2Opinion">
          <el-input
            v-model="form.reviewer2Opinion"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="最终审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="请输入最终审批人" />
        </el-form-item>
        <el-form-item label="最终审批时间" prop="approveTime">
          <el-date-picker
            clearable
            v-model="form.approveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择最终审批时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最终审批意见" prop="approveOpinion">
          <el-input
            v-model="form.approveOpinion"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="驳回原因" prop="rejectReason">
          <el-input
            v-model="form.rejectReason"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="生效日期" prop="effectiveDate">
          <el-date-picker
            clearable
            v-model="form.effectiveDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择生效日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="失效日期" prop="expireDate">
          <el-date-picker
            clearable
            v-model="form.expireDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择失效日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listApproval,
  getApproval,
  delApproval,
  addApproval,
  updateApproval,
} from "@/api/system/technicalPlanForSafetyProduction/index";

export default {
  name: "Approval",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全生产技术方案审批表格数据
      approvalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planCode: null,
        planName: null,
        projectName: null,
        techType: null,
        riskLevel: null,
        compiler: null,
        compileDept: null,
        compileTime: null,
        version: null,
        planScope: null,
        coreContent: null,
        attachmentUrl: null,
        approvalStatus: null,
        reviewer1: null,
        reviewer1Time: null,
        reviewer1Opinion: null,
        reviewer2: null,
        reviewer2Time: null,
        reviewer2Opinion: null,
        approver: null,
        approveTime: null,
        approveOpinion: null,
        rejectReason: null,
        effectiveDate: null,
        expireDate: null,
        implementationStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        planCode: [
          { required: true, message: "方案编号不能为空", trigger: "blur" },
        ],
        planName: [
          { required: true, message: "方案名称不能为空", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "所属项目不能为空", trigger: "blur" },
        ],
        techType: [
          {
            required: true,
            message:
              "技术类型：基础工程/结构工程/起重吊装/拆除工程/消防工程/其他不能为空",
            trigger: "change",
          },
        ],
        riskLevel: [
          {
            required: true,
            message: "风险等级：一般/重大/极危不能为空",
            trigger: "blur",
          },
        ],
        compiler: [
          { required: true, message: "编制人不能为空", trigger: "blur" },
        ],
        compileDept: [
          { required: true, message: "编制部门不能为空", trigger: "blur" },
        ],
        compileTime: [
          { required: true, message: "编制日期不能为空", trigger: "blur" },
        ],
        planScope: [
          { required: true, message: "方案适用范围不能为空", trigger: "blur" },
        ],
        coreContent: [
          { required: true, message: "核心技术内容不能为空", trigger: "blur" },
        ],
        attachmentUrl: [
          { required: true, message: "方案文件地址不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全生产技术方案审批列表 */
    getList() {
      this.loading = true;
      listApproval(this.queryParams).then((response) => {
        this.approvalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        planCode: null,
        planName: null,
        projectName: null,
        techType: null,
        riskLevel: null,
        compiler: null,
        compileDept: null,
        compileTime: null,
        version: null,
        planScope: null,
        coreContent: null,
        attachmentUrl: null,
        approvalStatus: null,
        reviewer1: null,
        reviewer1Time: null,
        reviewer1Opinion: null,
        reviewer2: null,
        reviewer2Time: null,
        reviewer2Opinion: null,
        approver: null,
        approveTime: null,
        approveOpinion: null,
        rejectReason: null,
        effectiveDate: null,
        expireDate: null,
        implementationStatus: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全生产技术方案审批";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getApproval(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全生产技术方案审批";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateApproval(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addApproval(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除安全生产技术方案审批编号为"' + ids + '"的数据项？'
        )
        .then(function () {
          return delApproval(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/approval/export",
        {
          ...this.queryParams,
        },
        `approval_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
