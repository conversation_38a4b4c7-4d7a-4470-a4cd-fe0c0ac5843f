<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="80px">
      <el-form-item label="奖项名称" prop="awardsName">
        <el-input v-model="queryParams.awardsName" placeholder="请输入奖项名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="附件" prop="awardsUrl">
        <el-input
          v-model="queryParams.awardsUrl"
          placeholder="请输入附件"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="数量" prop="awardsNumber">
        <el-input
          v-model="queryParams.awardsNumber"
          placeholder="请输入数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- 获奖类别 -->
      <el-form-item label="获奖类别" prop="awardsType">
        <el-select v-model="queryParams.awardsType" placeholder="请选择获奖类别" clearable @change="handleQuery">
          <el-option v-for="item in zjAwardsTypeList" :key="item.dictValue" :label="item.dictLabel"
            :value="item.dictValue" />
        </el-select>
      </el-form-item>

      <el-form-item label="获奖项目" prop="awardWinningProjects">
        <el-input v-model="queryParams.awardWinningProjects" placeholder="请输入获奖项目" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="获奖单位" prop="winningUnits">
        <el-input v-model="queryParams.winningUnits" placeholder="请输入获奖单位" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="获奖时间" prop="awardsTime">
        <el-date-picker v-model="queryParams.awardsTime" clearable type="date" value-format="yyyy-MM-dd"
          placeholder="请选择获奖时间" />
      </el-form-item>
      <el-form-item label="颁奖文号" prop="awardNumber">
        <el-input v-model="queryParams.awardNumber" placeholder="请输入颁奖文号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="颁奖单位" prop="awardingUnit">
        <el-input v-model="queryParams.awardingUnit" placeholder="请输入颁奖单位" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="备注" prop="awardDesc">
        <el-input
          v-model="queryParams.awardDesc"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjAwardsInfo:add']" type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjAwardsInfo:edit']" type="success" plain icon="el-icon-edit" size="mini"
          :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjAwardsInfo:remove']" type="danger" plain icon="el-icon-delete" size="mini"
          :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjAwardsInfo:export']" type="warning" plain icon="el-icon-download"
          size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="zjAwardsInfoList" height="calc(100vh - 300px)"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="奖项名称" align="center" prop="awardsName" width="150" show-overflow-tooltip />

      <!-- <el-table-column label="数量" align="center" prop="awardsNumber" /> -->
      <el-table-column label="奖项类别" align="center" prop="awardsType" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ getZjAwardsTypeLabel(scope.row.awardsType) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="获奖项目" align="center" prop="awardWinningProjects" width="180" show-overflow-tooltip />
      <el-table-column label="获奖单位" align="center" prop="winningUnits" width="150" show-overflow-tooltip />
      <el-table-column label="获奖时间" align="center" prop="awardsTime" width="150" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.awardsTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column label="颁奖文号" align="center" prop="awardNumber" width="150" show-overflow-tooltip />
      <el-table-column label="颁奖单位" align="center" prop="awardingUnit" width="180" show-overflow-tooltip />
      <el-table-column label="附件" align="center" prop="awardsUrl" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button v-if="scope.row.awardsUrl" size="mini" type="text"
            @click="handleViewAttachment(scope.row.awardsUrl)">查看</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="awardDesc" width="150" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
        <template slot-scope="scope">
          <el-button v-hasPermi="['inspection:zjAwardsInfo:edit']" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['inspection:zjAwardsInfo:remove']" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改创优的奖项对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="奖项名称" prop="awardsName">
          <el-input v-model="form.awardsName" placeholder="请输入奖项名称" />
        </el-form-item>

        <!-- <el-form-item label="数量" prop="awardsNumber">
          <el-input v-model="form.awardsNumber" placeholder="请输入数量" />
        </el-form-item> -->
        <!-- 奖项类别 -->
        <el-form-item label="奖项类别" prop="awardsType">
          <el-select v-model="form.awardsType" placeholder="请选择奖项类别" style="width: 100%">
            <el-option v-for="item in zjAwardsTypeList" :key="item.dictValue" :label="item.dictLabel"
              :value="item.dictValue" />
          </el-select>
        </el-form-item>

        <el-form-item label="获奖项目" prop="awardWinningProjects">
          <el-input v-model="form.awardWinningProjects" placeholder="请输入获奖项目" />
        </el-form-item>

        <el-form-item label="获奖单位" prop="winningUnits">
          <el-input v-model="form.winningUnits" placeholder="请输入获奖单位" />
        </el-form-item>
        <el-form-item label="获奖时间" prop="awardsTime">
          <el-date-picker v-model="form.awardsTime" clearable type="date" value-format="yyyy-MM-dd"
            placeholder="请选择获奖时间" style="width: 100%" />
        </el-form-item>
        <el-form-item label="颁奖文号" prop="awardNumber">
          <el-input v-model="form.awardNumber" placeholder="请输入颁奖文号" />
        </el-form-item>
        <el-form-item label="颁奖单位" prop="awardingUnit">
          <el-input v-model="form.awardingUnit" placeholder="请输入颁奖单位" />
        </el-form-item>
        <el-form-item label="附件" prop="awardsUrl">
          <file-upload v-model="form.awardsUrl" :file-type="fileType" />
        </el-form-item>
        <el-form-item label="备注" prop="awardDesc">
          <el-input v-model="form.awardDesc" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog v-model="attachmentDialogVisible" :attachment-list="attachmentList" />
  </div>
</template>

<script>
import {
  listZjAwardsInfo,
  getZjAwardsInfo,
  delZjAwardsInfo,
  addZjAwardsInfo,
  updateZjAwardsInfo,
  getZjAwardsType
} from '@/api/inspection/zjAwardsInfo'
import AttachmentDialog from '@/views/components/attchmentDialog.vue'

export default {
  name: 'ZjAwardsInfo',
  components: {
    AttachmentDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 创优的奖项表格数据
      zjAwardsInfoList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        awardsName: null,
        awardsUrl: null,
        awardsNumber: null,
        awardsType: null,
        awardsTime: null,
        awardWinningProjects: null,
        winningUnits: null,
        awardNumber: null,
        awardingUnit: null,
        awardDesc: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        awardsName: [
          { required: true, message: '奖项名称不能为空', trigger: 'blur' }
        ],
        awardsType: [
          { required: true, message: '奖项类别不能为空', trigger: 'change' }
        ],
        awardWinningProjects: [
          { required: true, message: '获奖项目不能为空', trigger: 'blur' }
        ],
        winningUnits: [
          { required: true, message: '获奖单位不能为空', trigger: 'blur' }
        ],
        awardsTime: [
          { required: true, message: '获奖时间不能为空', trigger: 'change' }
        ],
        awardNumber: [
          { required: true, message: '颁奖文号不能为空', trigger: 'blur' }
        ],
        awardingUnit: [
          { required: true, message: '颁奖单位不能为空', trigger: 'blur' }
        ],
        awardsUrl: [
          { required: true, message: '附件不能为空', trigger: 'change' }
        ],
        awardDesc: [
          { required: true, message: '备注不能为空', trigger: 'blur' }
        ]
      },
      attachmentDialogVisible: false,
      attachmentList: [],
      zjAwardsTypeList: [],
      fileType: ['png', 'jpg', 'jpeg', 'doc', 'xls', 'pdf']
    }
  },
  created() {
    this.getList()
    this.getZjAwardsType()
  },
  methods: {
    getZjAwardsType() {
      const params = {
        dictType: 'zj_awards_type'
      }
      getZjAwardsType(params).then((response) => {
        this.zjAwardsTypeList = response.rows
      })
    },
    handleViewAttachment(value) {
      this.attachmentDialogVisible = true
      this.attachmentList = value.split(',')
    },
    getZjAwardsTypeLabel(value) {
      const item = this.zjAwardsTypeList.find(
        (item) => item.dictValue === value
      )
      return item ? item.dictLabel : ''
    },
    /** 查询创优的奖项列表 */
    getList() {
      this.loading = true
      listZjAwardsInfo(this.queryParams).then((response) => {
        this.zjAwardsInfoList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        awardsName: null,
        awardsUrl: null,
        awardsNumber: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        awardsType: null,
        awardsTime: null,
        awardWinningProjects: null,
        winningUnits: null,
        awardNumber: null,
        awardingUnit: null,
        awardDesc: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加创优的奖项'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getZjAwardsInfo(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改创优的奖项'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjAwardsInfo(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addZjAwardsInfo(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除创优的奖项编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjAwardsInfo(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'inspection/zjAwardsInfo/export',
        {
          ...this.queryParams
        },
        `zjAwardsInfo_${new Date().getTime()}.xlsx`
      )
    }
  }
}
</script>
