<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="考核编号" prop="assessmentNo">
        <el-input
          v-model="queryParams.assessmentNo"
          placeholder="请输入考核编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核周期" prop="assessmentPeriod">
        <el-input
          v-model="queryParams.assessmentPeriod"
          placeholder="请输入考核周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核范围" prop="assessmentScope">
        <el-input
          v-model="queryParams.assessmentScope"
          placeholder="请输入考核范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="考核对象ID" prop="targetId">
        <el-input
          v-model="queryParams.targetId"
          placeholder="请输入考核对象ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="考核对象名称" prop="targetName">
        <el-input
          v-model="queryParams.targetName"
          placeholder="请输入考核对象名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="考核对象负责人" prop="responsiblePerson">
        <el-input
          v-model="queryParams.responsiblePerson"
          placeholder="请输入考核对象负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全管理制度落实得分" prop="safetyManagementScore">
        <el-input
          v-model="queryParams.safetyManagementScore"
          placeholder="请输入安全管理制度落实得分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隐患排查治理得分" prop="hiddenDangerScore">
        <el-input
          v-model="queryParams.hiddenDangerScore"
          placeholder="请输入隐患排查治理得分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="教育培训得分" prop="trainingEducationScore">
        <el-input
          v-model="queryParams.trainingEducationScore"
          placeholder="请输入教育培训得分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备安全得分" prop="equipmentSafetyScore">
        <el-input
          v-model="queryParams.equipmentSafetyScore"
          placeholder="请输入设备安全得分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="应急响应得分" prop="emergencyResponseScore">
        <el-input
          v-model="queryParams.emergencyResponseScore"
          placeholder="请输入应急响应得分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核总分" prop="totalScore">
        <el-input
          v-model="queryParams.totalScore"
          placeholder="请输入考核总分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="在同批次考核中的排名" prop="ranking">
        <el-input
          v-model="queryParams.ranking"
          placeholder="请输入在同批次考核中的排名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="奖励金额" prop="bonusAmount">
        <el-input
          v-model="queryParams.bonusAmount"
          placeholder="请输入奖励金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处罚措施" prop="penaltyMeasure">
        <el-input
          v-model="queryParams.penaltyMeasure"
          placeholder="请输入处罚措施"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考核开始时间" prop="assessmentStartTime">
        <el-date-picker
          clearable
          v-model="queryParams.assessmentStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择考核开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="考核结束时间" prop="assessmentEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.assessmentEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择考核结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="整改期限" prop="rectificationDeadline">
        <el-date-picker
          clearable
          v-model="queryParams.rectificationDeadline"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择整改期限"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="复核人" prop="reviewer">
        <el-input
          v-model="queryParams.reviewer"
          placeholder="请输入复核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="复核时间" prop="reviewTime">
        <el-date-picker
          clearable
          v-model="queryParams.reviewTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择复核时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:summary:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:summary:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:summary:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:summary:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="summaryList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="考核编号" align="center" prop="assessmentNo" />
      <el-table-column
        label="考核周期"
        align="center"
        prop="assessmentPeriod"
      />
      <!-- <el-table-column
        label="考核类型：月度/季度/年度/专项"
        align="center"
        prop="assessmentType"
      /> -->
      <el-table-column label="考核范围" align="center" prop="assessmentScope" />
      <!-- <el-table-column
        label="考核对象类型：公司/部门/项目/班组"
        align="center"
        prop="targetType"
      /> -->
      <!-- <el-table-column label="考核对象ID" align="center" prop="targetId" /> -->
      <el-table-column label="考核对象名称" align="center" prop="targetName" />
      <el-table-column
        label="考核对象负责人"
        align="center"
        prop="responsiblePerson"
      />
      <!-- <el-table-column
        label="安全管理制度落实得分"
        align="center"
        prop="safetyManagementScore"
      />
      <el-table-column
        label="隐患排查治理得分"
        align="center"
        prop="hiddenDangerScore"
      /> -->
      <el-table-column
        label="教育培训得分"
        align="center"
        prop="trainingEducationScore"
      />
      <el-table-column
        label="设备安全得分"
        align="center"
        prop="equipmentSafetyScore"
      />
      <el-table-column
        label="应急响应得分"
        align="center"
        prop="emergencyResponseScore"
      />
      <el-table-column label="考核总分" align="center" prop="totalScore" />
      <!-- 优秀/良好/合格/不合格 -->
      <el-table-column
        label="考核结果"
        align="center"
        prop="assessmentResult"
      />
      <el-table-column
        label="在同批次考核中的排名"
        align="center"
        prop="ranking"
      />
      <!-- <el-table-column label="奖励金额" align="center" prop="bonusAmount" />
      <el-table-column label="处罚措施" align="center" prop="penaltyMeasure" />
      <el-table-column
        label="考核组成员"
        align="center"
        prop="assessmentTeam"
      />
      <el-table-column
        label="考核开始时间"
        align="center"
        prop="assessmentStartTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.assessmentStartTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="考核结束时间"
        align="center"
        prop="assessmentEndTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.assessmentEndTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="考核报告文件地址"
        align="center"
        prop="assessmentReportUrl"
      />
      <el-table-column label="主要优势" align="center" prop="keyAdvantages" />
      <el-table-column
        label="需改进项"
        align="center"
        prop="improvementItems"
      />
      <el-table-column
        label="整改期限"
        align="center"
        prop="rectificationDeadline"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.rectificationDeadline, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="考核状态：草稿/进行中/已完成/已复核"
        align="center"
        prop="assessmentStatus"
      />
      <el-table-column label="复核人" align="center" prop="reviewer" />
      <el-table-column
        label="复核时间"
        align="center"
        prop="reviewTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.reviewTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="其他说明" align="center" prop="remark" /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:summary:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:summary:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全考核汇总（汇总各单位安全考核结果）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="考核编号" prop="assessmentNo">
          <el-input v-model="form.assessmentNo" placeholder="请输入考核编号" />
        </el-form-item>
        <el-form-item label="考核周期" prop="assessmentPeriod">
          <el-input
            v-model="form.assessmentPeriod"
            placeholder="请输入考核周期"
          />
        </el-form-item>
        <el-form-item label="考核范围" prop="assessmentScope">
          <el-input
            v-model="form.assessmentScope"
            placeholder="请输入考核范围"
          />
        </el-form-item>
        <el-form-item label="考核对象ID" prop="targetId">
          <el-input v-model="form.targetId" placeholder="请输入考核对象ID" />
        </el-form-item>
        <el-form-item label="考核对象名称" prop="targetName">
          <el-input
            v-model="form.targetName"
            placeholder="请输入考核对象名称"
          />
        </el-form-item>
        <el-form-item label="考核对象负责人" prop="responsiblePerson">
          <el-input
            v-model="form.responsiblePerson"
            placeholder="请输入考核对象负责人"
          />
        </el-form-item>
        <el-form-item label="安全管理制度落实得分" prop="safetyManagementScore">
          <el-input
            v-model="form.safetyManagementScore"
            placeholder="请输入安全管理制度落实得分"
          />
        </el-form-item>
        <el-form-item label="隐患排查治理得分" prop="hiddenDangerScore">
          <el-input
            v-model="form.hiddenDangerScore"
            placeholder="请输入隐患排查治理得分"
          />
        </el-form-item>
        <el-form-item label="教育培训得分" prop="trainingEducationScore">
          <el-input
            v-model="form.trainingEducationScore"
            placeholder="请输入教育培训得分"
          />
        </el-form-item>
        <el-form-item label="设备安全得分" prop="equipmentSafetyScore">
          <el-input
            v-model="form.equipmentSafetyScore"
            placeholder="请输入设备安全得分"
          />
        </el-form-item>
        <el-form-item label="应急响应得分" prop="emergencyResponseScore">
          <el-input
            v-model="form.emergencyResponseScore"
            placeholder="请输入应急响应得分"
          />
        </el-form-item>
        <el-form-item label="考核总分" prop="totalScore">
          <el-input v-model="form.totalScore" placeholder="请输入考核总分" />
        </el-form-item>
        <el-form-item label="在同批次考核中的排名" prop="ranking">
          <el-input
            v-model="form.ranking"
            placeholder="请输入在同批次考核中的排名"
          />
        </el-form-item>
        <el-form-item label="奖励金额" prop="bonusAmount">
          <el-input v-model="form.bonusAmount" placeholder="请输入奖励金额" />
        </el-form-item>
        <el-form-item label="处罚措施" prop="penaltyMeasure">
          <el-input
            v-model="form.penaltyMeasure"
            placeholder="请输入处罚措施"
          />
        </el-form-item>
        <el-form-item label="考核组成员" prop="assessmentTeam">
          <el-input
            v-model="form.assessmentTeam"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="考核开始时间" prop="assessmentStartTime">
          <el-date-picker
            clearable
            v-model="form.assessmentStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择考核开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="考核结束时间" prop="assessmentEndTime">
          <el-date-picker
            clearable
            v-model="form.assessmentEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择考核结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="考核报告文件地址" prop="assessmentReportUrl">
          <el-input
            v-model="form.assessmentReportUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="主要优势" prop="keyAdvantages">
          <el-input
            v-model="form.keyAdvantages"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="需改进项" prop="improvementItems">
          <el-input
            v-model="form.improvementItems"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="整改期限" prop="rectificationDeadline">
          <el-date-picker
            clearable
            v-model="form.rectificationDeadline"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择整改期限"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="复核人" prop="reviewer">
          <el-input v-model="form.reviewer" placeholder="请输入复核人" />
        </el-form-item>
        <el-form-item label="复核时间" prop="reviewTime">
          <el-date-picker
            clearable
            v-model="form.reviewTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择复核时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="其他说明" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSummary,
  getSummary,
  delSummary,
  addSummary,
  updateSummary,
} from "@/api/system/safetyAssessment/index";

export default {
  name: "Summary",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全考核汇总（汇总各单位安全考核结果）表格数据
      summaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assessmentNo: null,
        assessmentPeriod: null,
        assessmentType: null,
        assessmentScope: null,
        targetType: null,
        targetId: null,
        targetName: null,
        responsiblePerson: null,
        safetyManagementScore: null,
        hiddenDangerScore: null,
        trainingEducationScore: null,
        equipmentSafetyScore: null,
        emergencyResponseScore: null,
        totalScore: null,
        assessmentResult: null,
        ranking: null,
        bonusAmount: null,
        penaltyMeasure: null,
        assessmentTeam: null,
        assessmentStartTime: null,
        assessmentEndTime: null,
        assessmentReportUrl: null,
        keyAdvantages: null,
        improvementItems: null,
        rectificationDeadline: null,
        assessmentStatus: null,
        reviewer: null,
        reviewTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        assessmentNo: [
          { required: true, message: "考核编号不能为空", trigger: "blur" },
        ],
        assessmentPeriod: [
          { required: true, message: "考核周期不能为空", trigger: "blur" },
        ],
        assessmentType: [
          {
            required: true,
            message: "考核类型：月度/季度/年度/专项不能为空",
            trigger: "change",
          },
        ],
        assessmentScope: [
          { required: true, message: "考核范围不能为空", trigger: "blur" },
        ],
        targetType: [
          {
            required: true,
            message: "考核对象类型：公司/部门/项目/班组不能为空",
            trigger: "change",
          },
        ],
        targetId: [
          { required: true, message: "考核对象ID不能为空", trigger: "blur" },
        ],
        targetName: [
          { required: true, message: "考核对象名称不能为空", trigger: "blur" },
        ],
        assessmentStartTime: [
          { required: true, message: "考核开始时间不能为空", trigger: "blur" },
        ],
        assessmentEndTime: [
          { required: true, message: "考核结束时间不能为空", trigger: "blur" },
        ],
        improvementItems: [
          { required: true, message: "需改进项不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全考核汇总（汇总各单位安全考核结果）列表 */
    getList() {
      this.loading = true;
      listSummary(this.queryParams).then((response) => {
        this.summaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        assessmentNo: null,
        assessmentPeriod: null,
        assessmentType: null,
        assessmentScope: null,
        targetType: null,
        targetId: null,
        targetName: null,
        responsiblePerson: null,
        safetyManagementScore: null,
        hiddenDangerScore: null,
        trainingEducationScore: null,
        equipmentSafetyScore: null,
        emergencyResponseScore: null,
        totalScore: null,
        assessmentResult: null,
        ranking: null,
        bonusAmount: null,
        penaltyMeasure: null,
        assessmentTeam: null,
        assessmentStartTime: null,
        assessmentEndTime: null,
        assessmentReportUrl: null,
        keyAdvantages: null,
        improvementItems: null,
        rectificationDeadline: null,
        assessmentStatus: null,
        reviewer: null,
        reviewTime: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加安全考核汇总（汇总各单位安全考核结果）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSummary(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全考核汇总（汇总各单位安全考核结果）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSummary(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSummary(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除安全考核汇总（汇总各单位安全考核结果）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delSummary(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/summary/export",
        {
          ...this.queryParams,
        },
        `summary_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
