import request from '@/utils/request'

// 查询危大工程方案审批管理列表
export function listApproval(query) {
  return request({
    url: '/system/engineeringApproval/list',
    method: 'get',
    params: query
  })
}

// 查询危大工程方案审批管理详细
export function getApproval(id) {
  return request({
    url: '/system/engineeringApproval/' + id,
    method: 'get'
  })
}

// 新增危大工程方案审批管理
export function addApproval(data) {
  return request({
    url: '/system/engineeringApproval',
    method: 'post',
    data: data
  })
}

// 修改危大工程方案审批管理
export function updateApproval(data) {
  return request({
    url: '/system/engineeringApproval',
    method: 'put',
    data: data
  })
}

// 删除危大工程方案审批管理
export function delApproval(id) {
  return request({
    url: '/system/engineeringApproval/' + id,
    method: 'delete'
  })
}
