<template>
    <div class="app-container">
        <el-row>
            <el-col :span="24" :xs="24">
                <el-card body-style="padding: 12px">
                    <div class="top">
                        <div class="top-left">项目地图</div>
                        <div class="top-right">
                            <el-date-picker style="margin-right: 20px;" v-model="timeRange" type="daterange" range-separator="至"
                                start-placeholder="开始日期" end-placeholder="结束日期">
                            </el-date-picker>
                            <el-button type="primary" icon="el-icon-download" size="small">搜索</el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-card class="box-card">
                    <iframe :src="iframeUrl" scrolling="auto" frameborder="0" class="trend-container2"
                        id="iframe"></iframe>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>

export default {
    data() {
        return {
            iframeUrl: 'http://127.0.0.1:5501/index.html',
            timeRange: []
        };
    },
    created() {

    },
    methods: {
    }
};
</script>
<style lang="scss" scoped>
.app-container {
    width: 100%;
    height: calc(100vh - 100px);

}

.top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .top-left {
        font-weight: bold;
    }

    .top-right {
        display: flex;
    }
}

.el-row {
    margin-bottom: 10px;

    &:last-child {
        margin-bottom: 0;
    }
}

.box-card {
    position: relative;
    height: calc(100vh - 162px);
    font-size: 14px;

    .trend-container2 {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}
</style>