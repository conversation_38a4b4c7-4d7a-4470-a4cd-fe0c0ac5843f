<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="标题名称" prop="titleName">
        <el-input
          v-model="queryParams.titleName"
          placeholder="请输入标题名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报告编号" prop="reportNumber">
        <el-input
          v-model="queryParams.reportNumber"
          placeholder="请输入报告编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="对应法规" prop="correspondingRegulations">
        <el-input
          v-model="queryParams.correspondingRegulations"
          placeholder="请输入对应法规"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评估日期" prop="valuationDate">
        <el-date-picker
          clearable
          v-model="queryParams.valuationDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择评估日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="评估结论" prop="evaluateConclusion">
        <el-input
          v-model="queryParams.evaluateConclusion"
          placeholder="请输入评估结论"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传人" prop="uploaderName">
        <el-input
          v-model="queryParams.uploaderName"
          placeholder="请输入上传人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="附件" prop="attachmentUrl">
        <el-input
          v-model="queryParams.attachmentUrl"
          placeholder="请输入附件"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjComplianceEvaluationReport:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjComplianceEvaluationReport:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjComplianceEvaluationReport:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjComplianceEvaluationReport:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjComplianceEvaluationReportList"
      @selection-change="handleSelectionChange"
      height="calc(100vh-250pc)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="标题名称" align="center" prop="titleName" />
      <el-table-column label="报告编号" align="center" prop="reportNumber" />
      <el-table-column
        label="对应法规"
        align="center"
        prop="correspondingRegulations"
      />
      <el-table-column label="部门" align="center" prop="department" />
      <el-table-column
        label="评估日期"
        align="center"
        prop="valuationDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.valuationDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="评估结论"
        align="center"
        prop="evaluateConclusion"
      />
      <el-table-column label="上传人" align="center" prop="uploaderName" />
      <el-table-column label="附件" align="center" prop="attachmentUrl" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjComplianceEvaluationReport:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjComplianceEvaluationReport:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改符合性评价报告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题名称" prop="titleName">
          <el-input v-model="form.titleName" placeholder="请输入标题名称" />
        </el-form-item>
        <el-form-item label="报告编号" prop="reportNumber">
          <el-input v-model="form.reportNumber" placeholder="请输入报告编号" />
        </el-form-item>
        <el-form-item label="对应法规" prop="correspondingRegulations">
          <el-input
            v-model="form.correspondingRegulations"
            placeholder="请输入对应法规"
          />
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="form.department" placeholder="请输入部门" />
        </el-form-item>
        <el-form-item label="评估日期" prop="valuationDate">
          <el-date-picker
            clearable
            v-model="form.valuationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择评估日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="评估结论" prop="evaluateConclusion">
          <el-input
            v-model="form.evaluateConclusion"
            placeholder="请输入评估结论"
          />
        </el-form-item>
        <el-form-item label="上传人" prop="uploaderName">
          <el-input v-model="form.uploaderName" placeholder="请输入上传人" />
        </el-form-item>
        <el-form-item label="附件" prop="attachmentUrl">
          <el-input v-model="form.attachmentUrl" placeholder="请输入附件" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjComplianceEvaluationReport,
  getZjComplianceEvaluationReport,
  delZjComplianceEvaluationReport,
  addZjComplianceEvaluationReport,
  updateZjComplianceEvaluationReport,
} from "@/api/inspection/zjComplianceEvaluationReport";

export default {
  name: "ZjComplianceEvaluationReport",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 符合性评价报告表格数据
      zjComplianceEvaluationReportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        titleName: null,
        reportNumber: null,
        correspondingRegulations: null,
        department: null,
        valuationDate: null,
        evaluateConclusion: null,
        uploaderName: null,
        attachmentUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询符合性评价报告列表 */
    getList() {
      this.loading = true;
      listZjComplianceEvaluationReport(this.queryParams).then((response) => {
        this.zjComplianceEvaluationReportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        titleName: null,
        reportNumber: null,
        correspondingRegulations: null,
        department: null,
        valuationDate: null,
        evaluateConclusion: null,
        uploaderName: null,
        attachmentUrl: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加符合性评价报告";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjComplianceEvaluationReport(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改符合性评价报告";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjComplianceEvaluationReport(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjComplianceEvaluationReport(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除符合性评价报告编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjComplianceEvaluationReport(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjComplianceEvaluationReport/export",
        {
          ...this.queryParams,
        },
        `zjComplianceEvaluationReport_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
