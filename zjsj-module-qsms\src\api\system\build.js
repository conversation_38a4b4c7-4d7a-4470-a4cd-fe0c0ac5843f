import request from '@/utils/request'

// 查询建筑信息列表
export function buildingListInfo(query) {
  return request({
    url: '/pc/building/list',
    method: 'get',
    params: query
  })
}

// selectAjBuildingInfoByBuildingId
// 查询建筑信息详细
export function getInfo(buildingId) {
  return request({
    url: '/pc/building/selectAjBuildingInfoByBuildingId',
    method: 'get',
    params: {
      buildingId
    }
  })
}

// 新增建筑信息
export function addInfo(data) {
  return request({
    url: '/pc/building',
    method: 'post',
    data: data
  })
}

// 修改建筑信息
export function updateInfo(data) {
  return request({
    url: '/pc/building',
    method: 'put',
    data: data
  })
}

// 删除建筑信息
export function delInfo(buildingId) {
  return request({
    url: '/system/info/' + buildingId,
    method: 'delete'
  })
}



export function checkBuildingEnterprise(query) {
  return request({
    url: '/api/checkBuildingEnterprise',
    method: 'get',
    params: query
  })
}

export function deleteBuildingEnterprise(query) {
  return request({
    url: '/api/deleteBuildingEnterprise',
    method: 'get',
    params: query
  })
}

export function bindUnitBuild(data) {
  return request({
    url: '/api/buildingEnterprise/add',
    method: 'post',
    data: data
  })
}

// 建筑消防列表
export function getBuildXFList() {
  return request({
    url: '/api/type/aj_fire_protection_facilities',
    method: 'get'
  })
}