<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <!-- <el-form-item label="项目名称" prop="projectName">
        <selectPeopleTree v-model="queryParams.projectName" :people-list="projectList" placeholder="请选择项目名称"
          @change="handleProjectChange" />
      </el-form-item> -->
      <el-form-item label="公司" prop="company">
        <el-input v-model="queryParams.company" placeholder="请输入负责检查的公司/部门" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="检查人员" prop="inspectors">
        <el-input v-model="queryParams.inspectors" placeholder="请输入检查人员" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="检查日期" prop="inspectionDate">
        <el-date-picker v-model="queryParams.inspectionDate" clearable type="date" value-format="yyyy-MM-dd"
          placeholder="请选择检查日期" />
      </el-form-item>
      <!-- <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="是否需要整改 (0: 否, 1: 是)" prop="needRectification">
        <el-input
          v-model="queryParams.needRectification"
          placeholder="请输入是否需要整改 (0: 否, 1: 是)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="问题等级 ：1-一般 2-紧急 3-非常紧急" prop="problemLevel">
        <el-input
          v-model="queryParams.problemLevel"
          placeholder="请输入问题等级 ：1-一般 2-紧急 3-非常紧急"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="涉及单位" prop="relatedUnit">
        <el-input
          v-model="queryParams.relatedUnit"
          placeholder="请输入涉及单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="要求完成日期" prop="requiredCompletionDate">
        <el-date-picker clearable
          v-model="queryParams.requiredCompletionDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择要求完成日期">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="整改人" prop="rectificationBy">
        <el-input v-model="queryParams.rectificationBy" placeholder="请输入整改人" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="整改人id" prop="rectificationId">
        <el-input
          v-model="queryParams.rectificationId"
          placeholder="请输入整改人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="整改时间" prop="rectificationTime">
        <el-date-picker v-model="queryParams.rectificationTime" clearable type="date" value-format="yyyy-MM-dd"
          placeholder="请选择整改时间" />
      </el-form-item>
      <!-- <el-form-item label="创建人姓名" prop="createdBy">
        <el-input
          v-model="queryParams.createdBy"
          placeholder="请输入创建人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="创建时间" prop="createdTime">
        <el-date-picker clearable
          v-model="queryParams.createdTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="修改时间" prop="updatedTime">
        <el-date-picker clearable
          v-model="queryParams.updatedTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择修改时间">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="修改人姓名" prop="updatedBy">
        <el-input
          v-model="queryParams.updatedBy"
          placeholder="请输入修改人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:safetyInspectionRecords:add']" type="primary" plain icon="el-icon-plus"
          size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:safetyInspectionRecords:edit']" type="success" plain icon="el-icon-edit"
          size="mini" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:safetyInspectionRecords:remove']" type="danger" plain icon="el-icon-delete"
          size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:safetyInspectionRecords:export']" type="warning" plain
          icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="safetyInspectionRecordsList" style="width: 100%" :scroll-x="true"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="项目id" align="center" prop="projectId" /> -->
      <el-table-column label="公司" align="center" prop="company" width="120" />
      <el-table-column label="检查日期" align="center" prop="inspectionDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.inspectionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- 质量：1-安全 2-文明施工3-专项检查4-日常检查5-第三方检查 -->
      <el-table-column label="检查类别" align="center" prop="inspectionType" width="100" />
      <el-table-column label="检查人" align="center" prop="inspectors" width="120" />
      <el-table-column label="标题" align="center" prop="title" width="150" />
      <el-table-column label="检查说明" align="center" prop="inspectionNotes" width="300" show-overflow-tooltip />
      <el-table-column label="隐患照片" align="center" prop="inspectionNotesUrls" width="120" />
      <!-- (0: 否, 1: 是) -->
      <el-table-column label="是否整改" align="center" prop="needRectification" width="100" />
      <!-- ：1-一般 2-紧急 3-非常紧急 -->
      <el-table-column label="问题等级" align="center" prop="problemLevel" width="100" />
      <!-- <el-table-column label="整改要求" align="center" prop="rectificationRequirements" />
      <el-table-column label="涉及单位" align="center" prop="relatedUnit" />
      <el-table-column label="要求完成日期" align="center" prop="requiredCompletionDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requiredCompletionDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="整改结果" align="center" prop="rectificationResult" /> -->
      <el-table-column label="整改人" align="center" prop="rectificationBy" width="120" />
      <!-- <el-table-column label="整改人id" align="center" prop="rectificationId" /> -->
      <el-table-column label="整改时间" align="center" prop="rectificationTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.rectificationTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="整改现场照片" align="center" prop="rectificationPhotoUrls" />
      <el-table-column label="创建人姓名" align="center" prop="createdBy" />
      <el-table-column label="创建时间" align="center" prop="createdTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" prop="updatedTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updatedTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人姓名" align="center" prop="updatedBy" /> -->
      <el-table-column label="操作" align="center" width="140" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-hasPermi="['inspection:safetyInspectionRecords:edit']" size="mini" type="text"
            icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['inspection:safetyInspectionRecords:remove']" size="mini" type="text"
            icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改隐患排查记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <!-- 左列 - 基本信息和检查信息 -->
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <selectPeopleTree v-model="form.projectName" :people-list="projectList" placeholder="请选择项目名称"
                @change="handleFormProjectChange" />
            </el-form-item>
            <el-form-item label="公司" prop="company">
              <el-input v-model="form.company" placeholder="请输入负责检查的公司/部门" />
            </el-form-item>
            <el-form-item label="检查日期" prop="inspectionDate">
              <el-date-picker v-model="form.inspectionDate" clearable type="date" value-format="yyyy-MM-dd"
                placeholder="请选择检查日期" style="width: 100%" />
            </el-form-item>
            <el-form-item label="检查类别" prop="inspectionType">
              <el-select v-model="form.inspectionType" placeholder="请选择检查类别" style="width: 100%">
                <el-option label="安全" value="1" />
                <el-option label="文明施工" value="2" />
                <el-option label="专项检查" value="3" />
                <el-option label="日常检查" value="4" />
                <el-option label="第三方检查" value="5" />
              </el-select>
            </el-form-item>
            <el-form-item label="检查人员" prop="inspectors">
              <el-input v-model="form.inspectors" type="textarea" placeholder="检查人员姓名，多个用逗号分隔" :rows="3" />
            </el-form-item>
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item label="检查说明" prop="inspectionNotes">
              <el-input v-model="form.inspectionNotes" type="textarea" placeholder="请输入检查说明" :rows="3" />
            </el-form-item>
            <el-form-item label="隐患照片" prop="inspectionNotesUrls">
              <el-input v-model="form.inspectionNotesUrls" type="textarea" placeholder="请输入隐患照片URLs" :rows="2" />
            </el-form-item>
            <el-form-item label="是否需要整改" prop="needRectification">
              <el-select v-model="form.needRectification" placeholder="请选择" style="width: 100%">
                <el-option label="否" :value="0" />
                <el-option label="是" :value="1" />
              </el-select>
            </el-form-item>
            <el-form-item label="问题等级" prop="problemLevel">
              <el-select v-model="form.problemLevel" placeholder="请选择问题等级" style="width: 100%">
                <el-option label="一般" :value="1" />
                <el-option label="紧急" :value="2" />
                <el-option label="非常紧急" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="涉及单位" prop="relatedUnit">
              <el-input v-model="form.relatedUnit" placeholder="请输入涉及单位" />
            </el-form-item>
            <el-form-item label="要求完成日期" prop="requiredCompletionDate">
              <el-date-picker v-model="form.requiredCompletionDate" clearable type="date" value-format="yyyy-MM-dd"
                placeholder="请选择要求完成日期" style="width: 100%" />
            </el-form-item>
          </el-col>

          <!-- 右列 - 整改信息和系统信息 -->
          <el-col :span="12">
            <el-form-item label="整改要求" prop="rectificationRequirements">
              <el-input v-model="form.rectificationRequirements" type="textarea" placeholder="请输入整改要求" :rows="3" />
            </el-form-item>
            <el-form-item label="整改结果" prop="rectificationResult">
              <el-input v-model="form.rectificationResult" type="textarea" placeholder="请输入整改结果" :rows="3" />
            </el-form-item>
            <el-form-item label="整改人" prop="rectificationBy">
              <selectPeopleTree v-model="form.rectificationBy" :people-list="peopleList" placeholder="请选择整改人"
                @change="handleRectificationPersonChange" />
            </el-form-item>
            <el-form-item label="整改时间" prop="rectificationTime">
              <el-date-picker v-model="form.rectificationTime" clearable type="date" value-format="yyyy-MM-dd"
                placeholder="请选择整改时间" style="width: 100%" />
            </el-form-item>
            <el-form-item label="整改现场照片" prop="rectificationPhotoUrls">
              <el-input v-model="form.rectificationPhotoUrls" type="textarea" placeholder="请输入整改现场照片URLs" :rows="2" />
            </el-form-item>
            <el-form-item label="创建人姓名" prop="createdBy">
              <el-input v-model="form.createdBy" placeholder="请输入创建人姓名" />
            </el-form-item>
            <el-form-item label="创建时间" prop="createdTime">
              <el-date-picker v-model="form.createdTime" clearable type="date" value-format="yyyy-MM-dd"
                placeholder="请选择创建时间" style="width: 100%" />
            </el-form-item>
            <el-form-item label="修改时间" prop="updatedTime">
              <el-date-picker v-model="form.updatedTime" clearable type="date" value-format="yyyy-MM-dd"
                placeholder="请选择修改时间" style="width: 100%" />
            </el-form-item>
            <el-form-item label="修改人姓名" prop="updatedBy">
              <el-input v-model="form.updatedBy" placeholder="请输入修改人姓名" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSafetyInspectionRecords, getSafetyInspectionRecords, delSafetyInspectionRecords, addSafetyInspectionRecords, updateSafetyInspectionRecords } from '@/api/inspection/safetyInspectionRecords'
import selectPeopleTree from '@/views/components/selectPeopleTree.vue'
import { querytree, listPeople } from '@/api/system/info'

export default {
  name: 'SafetyInspectionRecords',
  components: {
    selectPeopleTree
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患排查记录表格数据
      safetyInspectionRecordsList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        projectName: null,
        company: null,
        inspectionDate: null,
        inspectionType: null,
        inspectors: null,
        title: null,
        inspectionNotes: null,
        inspectionNotesUrls: null,
        needRectification: null,
        problemLevel: null,
        rectificationRequirements: null,
        relatedUnit: null,
        requiredCompletionDate: null,
        rectificationResult: null,
        rectificationBy: null,
        rectificationId: null,
        rectificationTime: null,
        rectificationPhotoUrls: null,
        createdBy: null,
        createdTime: null,
        updatedTime: null,
        updatedBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'change' }
        ],
        company: [
          { required: true, message: '负责检查的公司/部门不能为空', trigger: 'blur' }
        ],
        inspectionDate: [
          { required: true, message: '检查日期不能为空', trigger: 'blur' }
        ],
        inspectionType: [
          { required: true, message: '检查类别质量：1-安全 2-文明施工3-专项检查4-日常检查5-第三方检查不能为空', trigger: 'change' }
        ],
        inspectors: [
          { required: true, message: '检查人员检查人员姓名，多个用逗号分隔不能为空', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        inspectionNotes: [
          { required: true, message: '检查说明不能为空', trigger: 'blur' }
        ]
      },
      // 项目列表
      projectList: [],
      // 人员列表
      peopleList: []
    }
  },
  created() {
    this.getList()
    this.getProjectList()
    this.getPeopleList()
  },
  methods: {
    /** 查询隐患排查记录列表 */
    getList() {
      this.loading = true
      listSafetyInspectionRecords(this.queryParams).then(response => {
        this.safetyInspectionRecordsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        projectName: null,
        company: null,
        inspectionDate: null,
        inspectionType: null,
        inspectors: null,
        title: null,
        inspectionNotes: null,
        inspectionNotesUrls: null,
        needRectification: null,
        problemLevel: null,
        rectificationRequirements: null,
        relatedUnit: null,
        requiredCompletionDate: null,
        rectificationResult: null,
        rectificationBy: null,
        rectificationId: null,
        rectificationTime: null,
        rectificationPhotoUrls: null,
        createdBy: null,
        createdTime: null,
        updatedTime: null,
        updatedBy: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加隐患排查记录'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getSafetyInspectionRecords(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改隐患排查记录'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSafetyInspectionRecords(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addSafetyInspectionRecords(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除隐患排查记录编号为"' + ids + '"的数据项？').then(function () {
        return delSafetyInspectionRecords(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('inspection/safetyInspectionRecords/export', {
        ...this.queryParams
      }, `safetyInspectionRecords_${new Date().getTime()}.xlsx`)
    },
    /** 获取项目列表 */
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data
        }
      })
    },
    /** 项目选择变化处理（查询表单） */
    handleProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === 'general-project') {
        this.queryParams.projectName = selectedItem.label
        this.queryParams.projectId = selectedItem.id
      } else {
        this.queryParams.projectName = null
        this.queryParams.projectId = null
      }
    },
    /** 表单项目选择变化处理 */
    handleFormProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === 'general-project') {
        this.form.projectName = selectedItem.label
        this.form.projectId = selectedItem.id
      } else {
        this.form.projectName = null
        this.form.projectId = null
      }
    },
    /** 获取人员列表 */
    getPeopleList() {
      listPeople().then((response) => {
        if (response.code === 200) {
          this.peopleList = response.data
        }
      })
    },
    /** 整改人选择变化处理 */
    handleRectificationPersonChange(selectedItem) {
      if (selectedItem) {
        this.form.rectificationBy = selectedItem.label
        this.form.rectificationId = selectedItem.id
      } else {
        this.form.rectificationBy = null
        this.form.rectificationId = null
      }
    }
  }
}
</script>
