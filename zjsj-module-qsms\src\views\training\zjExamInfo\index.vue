<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="姓名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入姓名"
          clearable
          style="width: 290px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作单位" prop="entpname">
        <el-input
          v-model="queryParams.entpname"
          placeholder="请输入企业名称"
          clearable
          style="width: 290px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- <el-form-item label="得分" prop="score">
        <el-input
          v-model="queryParams.score"
          placeholder="请输入得分"
          clearable
          @keyup.enter.native="handleQuery"
          style="width: 330px"
        />
      </el-form-item> -->
      <el-form-item label="是否合格" prop="examsatus">
        <el-select
          v-model="queryParams.examsatus"
          placeholder="请选择是否合格"
          style="width: 290px"
          clearable
        >
          <el-option label="合格" value="合格" />
          <el-option label="不合格" value="不合格" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjExamInfo:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
     
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjExamInfo:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleBatchDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['training:zjExamInfo:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 表格横向滚动容器 -->
    <div class="table-scroll-container">
      <el-table
        v-loading="loading"
        :data="zjExamInfoList"
        height="calc(100vh - 270px)"
        class="scroll-table"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="65" align="center" />
        <el-table-column
          label="试卷名称"
          align="center"
          prop="papertitle"
          
          show-overflow-tooltip
        />
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="姓名" align="center" width="105" prop="username" />
        <el-table-column
          label="身份证号"
          align="center"
          prop="identityno"
          width="180"
          show-overflow-tooltip
        />
        <el-table-column
          label="工作单位"
          align="center"
          prop="entpname"
          
          show-overflow-tooltip
        />
        <el-table-column
          label="参考时间"
          align="center"
          prop="startquiztime"
          width="160"
          show-overflow-tooltip
        />
        <el-table-column
          label="交卷时间"
          align="center"
          prop="gettime"
          width="160"
          show-overflow-tooltip
        />

        <el-table-column label="得分" align="center" width="105" prop="score" />
        <el-table-column label="是否合格" align="center" width="105" prop="examsatus" />
        <el-table-column
          label="操作"
          fixed="right"
          width="150"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              v-hasPermi="['training:zjExamInfo:edit']"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              v-hasPermi="['training:zjExamInfo:remove']"
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div v-if="zjExamInfoList.length > 0" class="pagination-wrapper">
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改考试信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="username">
          <el-input v-model="form.username" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="身份证号" prop="identityno">
          <el-input v-model="form.identityno" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="工作单位" prop="entpname">
          <el-input v-model="form.entpname" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="试卷名称" prop="papertitle">
          <el-input v-model="form.papertitle" placeholder="请输入试卷名称" />
          <!-- <el-select
            v-model="form.papertitle"
            placeholder="请选择试卷名称"
            style="width: 100%"
          >
            <el-option
              v-for="item in paperNameList"
              :key="item.papertitle"
              :label="item.papertitle"
              :value="item.papertitle"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item label="参考时间" prop="startquiztime">
          <el-date-picker
            v-model="form.startquiztime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
            placeholder="请选择参考时间"
          />
        </el-form-item>
        <el-form-item label="交卷时间" prop="gettime">
          <el-date-picker
            v-model="form.gettime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
            placeholder="请选择交卷时间"
          />
        </el-form-item>
        <el-form-item label="得分" prop="score">
          <el-input v-model="form.score" placeholder="请输入得分" />
        </el-form-item>
        <el-form-item label="是否合格" prop="examsatus">
          <el-input v-model="form.examsatus" placeholder="请输入是否合格" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjExamInfo,
  getZjExamInfo,
  delZjExamInfo,
  addZjExamInfo,
  updateZjExamInfo,
  getPaperNameList,
} from "@/api/training/zjExamInfo";

export default {
  name: "ZjExamInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 考试信息表格数据
      zjExamInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: null,
        entpname: null,
        score: null,
        examsatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        username: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        identityno: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
        ],
        entpname: [
          { required: true, message: "工作单位不能为空", trigger: "blur" },
        ],
        papertitle: [
          { required: true, message: "试卷名称不能为空", trigger: "blur" },
        ],
        startquiztime: [
          { required: true, message: "参考时间不能为空", trigger: "change" },
        ],
        gettime: [
          { required: true, message: "交卷时间不能为空", trigger: "change" },
        ],
        score: [{ required: true, message: "得分不能为空", trigger: "blur" }],
        examsatus: [
          { required: true, message: "是否合格不能为空", trigger: "blur" },
        ],
      },
      paperNameList: [],
    };
  },
  created() {
    this.getList();
    this.getPaperNameList();
  },
  methods: {
    // 试卷名称
    getPaperNameList() {
      getPaperNameList().then((response) => {
        this.paperNameList = response.data;
      });
    },

    /** 查询考试信息列表 */
    getList() {
      this.loading = true;
      listZjExamInfo(this.queryParams).then((response) => {
        this.zjExamInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        username: null,
        identityno: null,
        entpname: null,
        papertitle: null,
        score: null,
        examsatus: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      console.log(this.queryParams);

      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");

      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加考试信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjExamInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改考试信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjExamInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjExamInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 批量删除
    handleBatchDelete() {
      const ids = this.ids;
      this.$modal
        .confirm('是否确认删除' + this.ids.length + '条考试信息的数据项？')
        .then(function () {
          return delZjExamInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除考试信息"' + row.username + '"的数据项？')
        .then(function () {
          return delZjExamInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "training/zjExamInfo/export",
        {
          ...this.queryParams,
        },
        `zjExamInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style scoped lang="scss">
// 表格横向滚动容器样式
.table-scroll-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  // 美化滚动条
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // 确保表格能够横向滚动
  ::v-deep .el-table {
    min-width: 100%;

    .el-table__body-wrapper {
      overflow-x: visible;
    }

    // 固定列阴影效果
    .el-table__fixed-right {
      box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// 表格单元格样式优化
::v-deep .el-table {
  .el-table__cell {
    padding: 12px 8px;

    .cell {
      word-break: break-word;
      white-space: normal;
    }
  }

  // 优化tooltip显示
  .el-tooltip {
    max-width: 300px;
  }
}

/* 分页样式 - 固定在底部 */
.pagination-wrapper {
  margin-top: 0;
  text-align: center;
  padding: 16px 0;
  background: #fff;
  position: sticky;
  bottom: 0;
  z-index: 10;
  border-top: 1px solid #ebeef5;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

/* 分页组件响应式样式 */
.pagination-wrapper ::v-deep .el-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
  margin-right: 16px;
  order: 1;
}

.pagination-wrapper ::v-deep .el-pagination .btn-prev {
  order: 2;
}

.pagination-wrapper ::v-deep .el-pagination .el-pager {
  order: 3;
}

.pagination-wrapper ::v-deep .el-pagination .btn-next {
  order: 4;
}

// 响应式设计
@media (max-width: 1200px) {
  .table-scroll-container {
    ::v-deep .el-table {
      .el-table-column--selection {
        width: 50px !important;
      }
    }
  }
}

/* 中等屏幕适配 (平板) */
@media (max-width: 1024px) and (min-width: 769px) {
  .pagination-wrapper {
    padding: 10px 0 15px;
    border-top: 1px solid #ebeef5;
    background: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
    font-size: 13px;
  }

  .pagination-wrapper ::v-deep .el-pagination .btn-prev,
  .pagination-wrapper ::v-deep .el-pagination .btn-next {
    padding: 0 10px;
    min-width: 36px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pager li {
    min-width: 36px;
    height: 36px;
    line-height: 36px;
    font-size: 13px;
  }
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .pagination-wrapper {
    margin-bottom: 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    border-top: 2px solid #ebeef5;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  }

  .pagination-wrapper ::v-deep .el-pagination {
    flex-direction: row;
    justify-content: center;
    gap: 4px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {
    font-size: 12px;
    margin-right: 8px;
  }

  .pagination-wrapper ::v-deep .el-pagination .btn-prev,
  .pagination-wrapper ::v-deep .el-pagination .btn-next {
    padding: 0 8px;
    min-width: 32px;
  }

  .pagination-wrapper ::v-deep .el-pagination .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
  }

  /* 为固定分页器预留底部空间 */
  .app-container {
    padding-bottom: 80px !important;
  }
}
</style>
