import request from '@/utils/request'

// 查询绩效评定列表
export function listZjPerformanceEvaluation(query) {
  return request({
    url: '/inspection/zjPerformanceEvaluation/list',
    method: 'get',
    params: query
  })
}

// 查询绩效评定详细
export function getZjPerformanceEvaluation(id) {
  return request({
    url: '/inspection/zjPerformanceEvaluation/' + id,
    method: 'get'
  })
}

// 新增绩效评定
export function addZjPerformanceEvaluation(data) {
  return request({
    url: '/inspection/zjPerformanceEvaluation',
    method: 'post',
    data: data
  })
}

// 修改绩效评定
export function updateZjPerformanceEvaluation(data) {
  return request({
    url: '/inspection/zjPerformanceEvaluation',
    method: 'put',
    data: data
  })
}

// 删除绩效评定
export function delZjPerformanceEvaluation(id) {
  return request({
    url: '/inspection/zjPerformanceEvaluation/' + id,
    method: 'delete'
  })
}
