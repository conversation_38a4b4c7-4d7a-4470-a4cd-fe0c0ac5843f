<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="活动名称" prop="activityName">
        <el-input
          style="width: 250px"
          v-model="queryParams.activityName"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="主办部门" prop="organizer">
        <el-input
          style="width: 250px"
          v-model="queryParams.organizer"
          placeholder="请输入主办部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          style="width: 250px"
        >
          <el-option label="已完成" value="1" />
          <el-option label="未完成" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="计划开始时间" prop="planStartTime">
        <el-date-picker
          clearable
          v-model="queryParams.planStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划开始时间"
          style="width: 250px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划结束时间" prop="planEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.planEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划结束时间"
          style="width: 250px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="提醒时间" prop="remindTime">
        <el-date-picker
          clearable
          v-model="queryParams.remindTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择提醒时间"
          style="width: 250px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-date"
          size="mini"
          @click="handleSecdule"
          v-hasPermi="['inspection:zjSafetyActivityPlan:add']"
          >查看计划</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjSafetyActivityPlan:add']"
          >新增计划</el-button
        >
      </el-col> -->

      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjSafetyActivityPlan:edit']"
          >修改</el-button
        >
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjSafetyActivityPlan:remove']"
          >删除</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjSafetyActivityPlan:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <div style="display: flex; flex-wrap: wrap; gap: 10px">
      <el-card class="box-card firstCardStyle" @click.native="handleAdd">
        <i class="el-icon-plus"></i>
        <div>新建安全计划</div>
      </el-card>
      <el-card
        class="box-card cardStyle"
        v-for="(item, index) in zjSafetyActivityPlanList"
        :key="index"
        @dblclick.native="handlePreview(item)"
      >
        <div class="title">
          <span class="w-1/2 truncate">{{ item.activityName }}</span>
          <div class="tool flex gap-2 items-center">
            <div v-show="item.showTool">
              <el-button
                size="mini"
                type="text"
                @click.stop="handleUpdate(item)"
                v-hasPermi="['inspection:zjSafetyActivityPlan:edit']"
                >编辑</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click.stop="handleDelete(item)"
                v-hasPermi="['inspection:zjSafetyActivityPlan:remove']"
                >删除</el-button
              >
            </div>
            <i
              class="el-icon-more cursor-pointer"
              @click="handleContextMenu(item)"
            ></i>
          </div>
        </div>
        <div class="text">
          <span class="label"> 活动时间</span>
          {{ item.planStartTime }}至{{ item.planEndTime }}
        </div>
        <div class="text">
          <span class="label">内容</span>
          <el-tooltip
            class="item"
            effect="dark"
            :content="item.activityContent"
            :dangerouslyUseHTMLString="true"
            placement="top"
          >
            <span class="content">{{ item.activityContent }}</span>
          </el-tooltip>
        </div>
        <div class="text">
          <span class="label">负责人</span>{{ item.responder }}
        </div>
        <div class="text">
          <span class="label">状态</span>
          <div class="flex justify-between" style="flex-grow: 1">
            <span>{{ item.statusName }}未开始</span>
            <el-switch v-model="item.status"> </el-switch>
          </div>
        </div>
      </el-card>
      <el-dialog :visible.sync="previewVisible" width="500px" title="活动详情">
        <el-form ref="detailForm" :model="detailForm" label-width="120px">
          <el-form-item label="活动名称" prop="activityName">
            <span>{{ detailForm.activityName }}</span>
          </el-form-item>
          <el-form-item label="主办部门" prop="organizer">
            <span>{{ detailForm.organizer }}</span>
          </el-form-item>
          <el-form-item label="负责人" prop="responder">
            <span>{{ detailForm.responder }}</span>
          </el-form-item>
          <el-form-item label="活动描述">
            <span>{{ detailForm.activityContent }}</span>
          </el-form-item>
          <el-form-item label="计划开始时间" prop="planStartTime">
            <span>{{ detailForm.planStartTime }}</span>
          </el-form-item>
          <el-form-item label="计划结束时间" prop="planEndTime">
            <span>{{ detailForm.planEndTime }}</span>
          </el-form-item>
          <el-form-item label="提醒时间" prop="remindTime">
            <span>{{ detailForm.remindTime }}</span>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>

    <!-- <el-table v-loading="loading" :data="zjSafetyActivityPlanList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="活动名称" align="center" prop="activityName" />
      <el-table-column label="类型(1-培训/2-演练/3-宣传等)" align="center" prop="activityType" />
      <el-table-column label="主办部门" align="center" prop="organizer" />
      <el-table-column label="活动描述" align="center" prop="activityContent" />
      <el-table-column label="计划开始时间" align="center" prop="planStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划结束时间" align="center" prop="planEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提醒时间" align="center" prop="remindTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.remindTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjSafetyActivityPlan:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjSafetyActivityPlan:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table> -->

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 查看计划 -->
    <el-dialog
      :title="title"
      :visible.sync="secduleVisible"
      width="800px"
      append-to-body
    >
      <el-calendar v-model="currentDate">
        <template #dateCell="{ date, data }">
          <div class="calendar-day">
            <div class="day-number">
              {{ data.day.split("-").slice(2).join("-") }}
            </div>
            <div class="schedules">
              <div
                v-for="(schedule, index) in getSchedulesForDate(date)"
                :key="index"
                class="schedule-item"
                :style="{
                  backgroundColor: `${schedule.color}20`,
                }"
              >
                {{ schedule.activityName }}
              </div>
            </div>
          </div>
        </template>
      </el-calendar>
    </el-dialog>
    <!-- 添加或修改安全活动计划对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="活动名称" prop="activityName">
          <el-input v-model="form.activityName" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="主办部门" prop="organizer">
          <el-input v-model="form.organizer" placeholder="请输入主办部门" />
        </el-form-item>
        <el-form-item label="负责人" prop="responder">
          <el-input v-model="form.responder" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="活动描述">
          <editor v-model="form.activityContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="计划开始时间" prop="planStartTime">
          <el-date-picker
            clearable
            v-model="form.planStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划开始时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划结束时间" prop="planEndTime">
          <el-date-picker
            clearable
            v-model="form.planEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划结束时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="提醒时间" prop="remindTime">
          <el-date-picker
            clearable
            v-model="form.remindTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择提醒时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjSafetyActivityPlan,
  getZjSafetyActivityPlan,
  delZjSafetyActivityPlan,
  addZjSafetyActivityPlan,
  updateZjSafetyActivityPlan,
} from "@/api/inspection/zjSafetyActivityPlan";

export default {
  name: "ZjSafetyActivityPlan",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全活动计划表格数据
      zjSafetyActivityPlanList: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityName: null,
        activityType: null,
        organizer: null,
        activityContent: null,
        planStartTime: null,
        planEndTime: null,
        remindTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      previewVisible: false,
      detailForm: {},
      secduleVisible: false,
      currentDate: new Date(),
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getSchedulesForDate(date) {
      const dateStr = this.formatDate(date);
      // console.log(dateStr, "1111");
      const hasEvent = this.zjSafetyActivityPlanList.filter((item) => {
        const startDate = new Date(item.planStartTime);
        const endDate = new Date(item.planEndTime);
        const currentDate = new Date(dateStr);
        return currentDate >= startDate && currentDate <= endDate;
      });
      if (hasEvent.length > 0) {
        const cell = document.querySelector(`[data-date="${dateStr}"]`);
        if (cell) {
          cell.classList.add("has-event");
        }
      }
      return this.zjSafetyActivityPlanList.filter((item) => {
        const startDate = new Date(item.planStartTime);
        const endDate = new Date(item.planEndTime);
        const currentDate = new Date(dateStr);
        return currentDate >= startDate && currentDate <= endDate;
      });
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    handleSecdule() {
      this.secduleVisible = true;
    },
    handlePreview(item) {
      this.zjSafetyActivityPlanList.forEach((i) => {
        this.$set(i, "showTool", false);
      });
      this.previewVisible = true;
      this.detailForm = item;
      // 这里可以加载详情数据
    },
    handleContextMenu(item) {
      // 关闭其他所有卡片的菜单
      this.zjSafetyActivityPlanList.forEach((i) => {
        if (i !== item) this.$set(i, "showTool", false);
      });
      // 切换当前卡片的菜单状态
      this.$set(item, "showTool", !item.showTool);
      this.$forceUpdate(); //强制更新试图
    },
    /** 查询安全活动计划列表 */
    getList() {
      this.loading = true;
      listZjSafetyActivityPlan(this.queryParams).then((response) => {
        // this.zjSafetyActivityPlanList = response.rows;
        const colors = [
          "#67c23a",
          "#409eff",
          "#e6a23c",
          "#f56c6c",
          "#909399",
          "#9a60b4",
          "#ff85c0",
        ];
        this.zjSafetyActivityPlanList = response.rows.map((item, index) => ({
          ...item,
          activityContent: item.activityContent.replace(/<[^>]+>/g, ""),
          showTool: false, // 初始化为 false
          color: colors[index % colors.length],
        }));
        this.total = response.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        activityName: null,
        activityType: null,
        organizer: null,
        activityContent: null,
        planStartTime: null,
        planEndTime: null,
        remindTime: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      console.log("Add button clicked");
      this.reset();
      this.open = true;
      this.title = "添加安全活动计划";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjSafetyActivityPlan(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改安全活动计划";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjSafetyActivityPlan(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjSafetyActivityPlan(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全活动计划编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjSafetyActivityPlan(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjSafetyActivityPlan/export",
        {
          ...this.queryParams,
        },
        `zjSafetyActivityPlan_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}
.box-card:hover {
  border: 2px solid #409eff;
}
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  color: #1e2426;
  font-weight: 500;
  margin-bottom: 10px;
  height: 22px;
}
.text {
  font-size: 14px;
  color: #646678;
  display: flex;
  margin: 5px 0;
  .label {
    width: 64px;
  }
  .content {
    /* 自定义样式 */
    line-height: 1.5;
    width: 157px;
    margin: 0 !important;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
}
.cardStyle {
  width: 265px;
  height: 178px;
  border-radius: 4px;
  border: 2px solid #ebeef5;
  cursor: pointer;
}
.firstCardStyle {
  width: 265px;
  height: 178px;
  border-radius: 4px;
  color: #91919f;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

::v-deep .firstCardStyle .el-card__body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.calendar-day {
  position: relative;
  height: 100%;
}

.schedule-item {
  margin: 2px 0;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-calendar-day {
  padding: 0 !important;
  height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-calendar-table .el-calendar-day {
  position: relative;
}

::v-deep .el-calendar-table .el-calendar-day.is-selected {
  background-color: transparent;
}

/* 添加连续背景色效果 */
::v-deep .el-calendar-table .el-calendar-day.has-event {
  background-color: rgba(103, 194, 58, 0.1); /* 使用活动颜色+透明度 */
}
</style>
