<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">安全巡查计划</div>
            <div class="top-right">
              <el-button type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增任务</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card class="box-card">
          <el-form ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="80px">
            <!-- <el-form-item label="所属街镇" prop="belongStreet">
                            <el-select v-model="queryParams.belongStreet" placeholder="选择所属街镇" clearable style="width: 150px">
                                <el-option v-for="dict in dict.type.pz_street" :key="dict.value" :label="dict.label"
                                :value="dict.value" />
                            </el-select>
                        </el-form-item> -->
            <el-form-item label="任务类型" prop="inspectType">
              <el-select v-model="queryParams.inspectType" placeholder="选择任务类型" clearable style="width: 150px">
                <el-option v-for="dict in dict.type.inspect_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="检查人员" prop="inspector">
                            <el-select v-model="queryParams.inspector" placeholder="全部" clearable style="width: 150px">
                                <el-option label="全部" value="" />
                                <el-option v-for="item in inspectorOptions" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item> -->
            <!-- <el-form-item label="检查时间" prop="inspectTime">
                            <el-date-picker
                                v-model="queryParams.inspectTime"
                                type="date"
                                placeholder="选择日期"
                                style="width: 150px">
                            </el-date-picker>
                        </el-form-item> -->
            <!-- <el-form-item label="状态" prop="status">
                            <el-select v-model="queryParams.status" placeholder="全部" clearable style="width: 150px">
                                <el-option label="全部" value="" />
                                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="检查周期" prop="checkPeriod">
                            <el-select v-model="queryParams.checkPeriod" placeholder="全部" clearable style="width: 150px">
                                <el-option label="全部" value="" />
                                <el-option v-for="item in periodOptions" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="工程类型" prop="projectType">
                            <el-select v-model="queryParams.projectType" placeholder="全部" clearable style="width: 150px">
                                <el-option label="全部" value="" />
                                <el-option v-for="item in projectTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="有无问题" prop="hasProblem">
                            <el-select v-model="queryParams.hasProblem" placeholder="全部" clearable style="width: 150px">
                                <el-option label="全部" value="" />
                                <el-option v-for="item in problemOptions" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="任务单号" prop="taskNo">
                            <el-input v-model="queryParams.taskNo" placeholder="请输入任务单号" clearable style="width: 200px" />
                        </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-table v-loading="loading" :data="list">
            <el-table-column label="任务单号" align="center" prop="taskNumber" width="180" />
            <el-table-column label="任务来源" align="center" prop="taskSource">
              <template slot-scope="scope">
                <span v-if="scope.row.taskSource === '1'">区</span>
                <span v-else-if="scope.row.taskSource === '2'">街道</span>
                <span v-else>未知</span>
                <!-- 可选：处理其他情况 -->
              </template>
            </el-table-column>
            <el-table-column label="任务类型" align="center" prop="inspectType">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.inspect_type" :value="scope.row.inspectType" />
              </template>
            </el-table-column>
            <el-table-column label="检查日期" align="center" prop="inspectTime" width="100" />
            <el-table-column label="所属街镇" align="center" prop="belongStreet" />
            <el-table-column label="检查工程数量" align="center" prop="jcgcsl" width="100" />
            <el-table-column label="已检数量" align="center" prop="yjcsl" />
            <el-table-column label="未检查" align="center" prop="wjcsl" />
            <el-table-column label="问题数量" align="center" prop="fxwts" />
            <el-table-column label="任务状态" align="center" prop="taskStatus">
              <template slot-scope="scope">
                <el-tag :type="scope.row.taskStatus == '2' ? 'success' : 'warning'">
                  <dict-tag :options="dict.type.task_status" :value="scope.row.taskStatus" />
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="280" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="handleCheck(scope.row)">查看</el-button>
                <!-- <el-button v-if="scope.row.taskStatus == '0'" size="mini" type="success" @click="handleSend(scope.row)">发送</el-button> -->
                <!-- <el-button
                  size="mini"
                  type="warning"
                  @click="handleEdit(scope.row)"
                  >编辑</el-button
                > -->
                <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import store from '@/store'
import {
  getJianChaTaskList,
  sendJianChaTask,
  deleteJianChaTask
} from '@/api/inspection/jiancha'
export default {
  name: 'Checkdeinspectrecords',
  dicts: ['pz_street', 'inspect_type', 'task_status'],
  data() {
    return {
      // 编辑表单内容
      editForm: {},
      // 详情表单内容
      detailForm: {},
      // 显示详情表单
      showDetailForm: false,
      // 显示编辑表单
      showEditForm: false,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 5,
      // 项目检查表格数据
      list: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        belongStreet: '',
        // inspectTime: '',
        // status: '',
        inspectType: ''
        // projectType: '',
        // hasProblem: '',
        // taskNo: ''
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      roles: []
    }
  },
  created() {
    this.getList()
    this.roles = store.getters.roles
  },
  // computed: {
  //   belongStreet() {
  //     return this.$store.getter.belongStreet
  //   }
  // },
  methods: {
    getList() {
      this.loading = true
      getJianChaTaskList(this.queryParams).then((response) => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      console.log(this.queryParams)
      this.queryParams.pageNum = 1
      this.queryParams.belongStreet = store.getters.belongStreet
      this.getList()
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = (d.getMonth() + 1).toString().padStart(2, '0')
      const day = d.getDate().toString().padStart(2, '0')
      return `${year}.${month}.${day}`
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        belongStreet: '',
        // inspectTime: '',
        // status: '',
        inspectType: ''
        // projectType: '',
        // hasProblem: '',
        // taskNo: ''
      }
      // this.list = [...this.originalList];
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.inspectrecordsid1)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({ path: '/yinhuan/jiancha/addTask' })
    },
    /** 修改按钮操作 */
    handleUpdate(row) { },
    // 新增操作方法
    handleCheck(row) {
      this.$router.push({
        path: '/yinhuan/jiancha/taskList',
        query: { id: row.id }
      })
    },
    // 点击发送按钮
    handleSend(row) {
      this.$confirm('是否确认发送该任务？', '确认发送', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        message: `任务单号：${row.taskNumber}`,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '发送中...'

            sendJianChaTask({
              id: row.id,
              taskStatus: 1
            }).then((res) => {
              // 更新状态
              row.sendStatus = '已发送'

              // 提示成功
              this.$message({
                type: 'success',
                message: '发送成功'
              })

              this.getList()
              done()
              instance.confirmButtonLoading = false
            })
          } else {
            done()
          }
        }
      }).catch(() => { })
    },
    handleEdit(row) {
      console.log('编辑', row)
      this.$router.push({
        path: '/wangge/safeCheck/addTask',
        query: { id: row.id }
      })
    },
    handleDelete(row) {
      this.$confirm('是否确认删除该任务？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        message: `任务单号：${row.taskNumber}`,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '删除中...'

            deleteJianChaTask(row.id).then((res) => {
              this.$message({
                type: 'success',
                message: '删除成功'
              })
              // 删除成功后刷新表格
              this.getList()
              done()
              instance.confirmButtonLoading = false
            }).catch((error) => {
              console.error('删除失败:', error)
              this.$message({
                type: 'error',
                message: '删除失败'
              })
              done()
              instance.confirmButtonLoading = false
            })
          } else {
            done()
          }
        }
      }).catch(() => { })
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  // overflow-y: auto;
  height: calc(100vh - 100px);

  .el-dialog__header {
    padding: 12px;
    border-bottom: 1px solid #ebebeb;
  }

  .detail-content {
    width: 100%;
    height: 70vh;
    overflow-y: auto;
    color: #333;

    .detail-item {
      .detail-top {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebebeb;

        .state {
          font-size: 12px;
          line-height: 20px;
          padding: 2px 8px;
          border-radius: 2px;
          margin-right: 12px;
        }

        .name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .edit {
          margin-left: auto;
          margin-right: 12px;
        }
      }

      .detail-center {
        display: flex;

        .detail-center-left {
          .detail-center-item {
            display: flex;
            line-height: 24px;
            margin: 12px 0;

            .detail-center-item-left {
              width: 70px;
            }

            .detail-center-item-right {
              flex: 1;
            }
          }
        }

        .detail-center-right {
          flex: 1;
          display: flex;
          margin: 12px 0;

          .el-image {
            margin-left: 12px;
            width: 156px !important;
            height: 156px !important;
          }
        }
      }
    }

    .detail-edit-content {
      width: 60%;
      margin: 0 auto;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  height: calc(100vh - 180px);
  overflow-y: auto;
  font-size: 14px;
}
</style>
