import request from "@/utils/request";

// 查询隐患问题库列表
export function listHazard(query) {
  return request({
    url: "/inspection/hazard/list",
    method: "get",
    params: query,
  });
}

// 查询隐患问题库详细
export function getHazard(hazardId) {
  return request({
    url: "/inspection/hazard/" + hazardId,
    method: "get",
  });
}

// 新增隐患问题库
export function addHazard(data) {
  return request({
    url: "/inspection/hazard",
    method: "post",
    data: data,
  });
}

// 修改隐患问题库
export function updateHazard(data) {
  return request({
    url: "/inspection/hazard",
    method: "put",
    data: data,
  });
}

// 删除隐患问题库
export function delHazard(hazardId) {
  return request({
    url: "/inspection/hazard/" + hazardId,
    method: "delete",
  });
}
// 隐患类别管理 树结构
export function treeHazard(params) {
  return request({
    url: "/inspection/hazard/tree",
    method: "get",
    params,
  });
}
// 一级树结构
export function treeHazardFirst(params) {
  return request({
    url: "/inspection/hazard/hazardList",
    method: "get",
    params,
  });
}
// 隐患类别
export function getHazardHazardCategory() {
  return request({
    url: "/inspection/hazard/hazardtree",
    method: "get",
  });
}
