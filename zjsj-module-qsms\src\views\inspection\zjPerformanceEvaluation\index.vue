<template>
  <div class="app-container">
    <el-row>
      <el-col :span="3">
        <!-- <div class="title mb-2">组织架构</div>
        <el-tree
          :data="data"
          :props="defaultProps"
          @node-click="handleNodeClick"
        ></el-tree> -->
        <org-tree :type="'1'" @node-click="handleOrgTreeNodeClick" />
      </el-col>
      <el-col :span="21">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="100px"
        >
          <el-form-item label="单位名称" prop="unitName">
            <el-input
              v-model="queryParams.unitName"
              placeholder="请输入单位名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="评定周期" prop="evaluationCycle">
            <el-input
              v-model="queryParams.evaluationCycle"
              placeholder="请输入评定周期"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="标准化得分" prop="standardScore">
            <el-input
              v-model="queryParams.standardScore"
              placeholder="请输入标准化得分"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="指标完成度" prop="indicatorCompletionRate">
            <el-input
              v-model="queryParams.indicatorCompletionRate"
              placeholder="请输入指标完成度"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="综合评定等级" prop="comprehensiveEvaluation">
            <el-input
              v-model="queryParams.comprehensiveEvaluation"
              placeholder="请输入综合评定等级"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="评定人" prop="assessorName">
            <el-input
              v-model="queryParams.assessorName"
              placeholder="请输入评定人"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="评定时间" prop="evaluationTime">
            <el-date-picker
              clearable
              v-model="queryParams.evaluationTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择评定时间"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="附件上传" prop="fjUrl">
            <el-input
              v-model="queryParams.fjUrl"
              placeholder="请输入附件上传"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['inspection:zjPerformanceEvaluation:add']"
              >新增评定</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['inspection:zjPerformanceEvaluation:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['inspection:zjPerformanceEvaluation:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['inspection:zjPerformanceEvaluation:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjPerformanceEvaluationList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
          <el-table-column label="单位名称" align="center" prop="unitName">
            <template slot-scope="scope">
              {{ getDeptName(scope.row.unitName) }}
            </template>
          </el-table-column>
          <el-table-column
            label="评定周期"
            align="center"
            prop="evaluationCycle"
          />
          <el-table-column
            label="标准化得分"
            align="center"
            prop="standardScore"
          />
          <el-table-column
            label="指标完成度"
            align="center"
            prop="indicatorCompletionRate"
          />
          <el-table-column
            label="综合评定等级"
            align="center"
            prop="comprehensiveEvaluation"
          />
          <el-table-column label="评定人" align="center" prop="assessorName" />
          <el-table-column
            label="评定时间"
            align="center"
            prop="evaluationTime"
            width="180"
          >
            <template slot-scope="scope">
              <span>{{
                parseTime(scope.row.evaluationTime, "{y}-{m}-{d}")
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="附件上传" align="center" prop="fjUrl">
            <template slot-scope="scope">
              <el-button
                v-if="!!scope.row.fjUrl"
                size="mini"
                type="text"
                @click="handleSafetyTrainingRecords(scope.row.fjUrl)"
                >查看</el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['inspection:zjPerformanceEvaluation:edit']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['inspection:zjPerformanceEvaluation:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <!-- 添加或修改绩效评定对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="单位名称" prop="unitName">
          <!-- <el-input v-model="form.unitName" placeholder="请输入单位名称" /> -->
          <el-select v-model="form.unitName" style="width: 100%">
            <template>
              <div v-for="group in deptList" :key="group.id">
                <el-option
                  :label="group.enterpriseName"
                  :value="group.id"
                  style="padding-left: 20px"
                />
                <template v-if="group.children && group.children.length">
                  <el-option
                    v-for="child in renderDeptOptions(group.children)"
                    :key="child.id"
                    :label="child.enterpriseName"
                    :value="child.id"
                    :style="{ paddingLeft: child.level * 20 + 'px' }"
                  />
                </template>
              </div>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="评定周期" prop="evaluationCycle">
          <el-input
            v-model="form.evaluationCycle"
            placeholder="请输入评定周期"
          />
        </el-form-item>
        <el-form-item label="标准化得分" prop="standardScore">
          <el-input
            v-model="form.standardScore"
            placeholder="请输入标准化得分"
          />
        </el-form-item>
        <el-form-item label="指标完成度" prop="indicatorCompletionRate">
          <el-input
            v-model="form.indicatorCompletionRate"
            placeholder="请输入指标完成度"
          />
        </el-form-item>
        <el-form-item label="综合评定等级" prop="comprehensiveEvaluation">
          <el-input
            v-model="form.comprehensiveEvaluation"
            placeholder="请输入综合评定等级"
          />
        </el-form-item>
        <el-form-item label="评定人" prop="assessorName">
          <el-input v-model="form.assessorName" placeholder="请输入评定人" />
        </el-form-item>
        <el-form-item label="评定时间" prop="evaluationTime">
          <el-date-picker
            clearable
            v-model="form.evaluationTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择评定时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="附件上传" prop="fjUrl">
          <!-- <el-input v-model="form.fjUrl" placeholder="请输入附件上传" /> -->
          <file-upload v-model="fjUrl"> </file-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjPerformanceEvaluation,
  getZjPerformanceEvaluation,
  delZjPerformanceEvaluation,
  addZjPerformanceEvaluation,
  updateZjPerformanceEvaluation,
} from "@/api/inspection/zjPerformanceEvaluation";
import { listInfo } from "@/api/system/info";
import OrgTree from "@/views/components/orgTree.vue";

export default {
  name: "ZjPerformanceEvaluation",
  components: {
    OrgTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 绩效评定表格数据
      zjPerformanceEvaluationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        unitName: null,
        evaluationCycle: null,
        standardScore: null,
        indicatorCompletionRate: null,
        comprehensiveEvaluation: null,
        assessorName: null,
        evaluationTime: null,
        fjUrl: null,
        deptId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      deptList: [],
      deptParams: {
        enterpriseName: undefined,
        status: undefined,
      },
    };
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    async handleSafetyTrainingRecords(value) {
      // if (value) {
      //   window.open(this.baseUrl + value);
      // } else {
      //   this.$message.warning("该记录没有附件");
      // }
      if (!value) {
        this.$message.warning("该记录没有附件");
        return;
      }
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const response = await fetch(fileUrl);
          const buffer = await response.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8"); // 也可以尝试 'gb2312' 或 'utf-8'
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    handleOrgTreeNodeClick(nodeData) {
      // 在这里处理接收到的子组件数据
      // console.log("接收到子组件数据:", nodeData);
      // 可以根据nodeData更新查询参数或其他状态
      this.queryParams.deptId = nodeData.id; // 假设nodeData中有id字段
      this.handleQuery(); // 触发查询
    },
    getDeptName(deptId) {
      const dept = this.flattenDept(this.deptList).find(
        (item) => item.id == deptId
      );

      return dept ? dept.enterpriseName : "";
    },
    /** 查询部门列表 */
    getDeptList() {
      listInfo(this.deptParams).then((response) => {
        this.deptList = this.handleTree(response.data, "id");
        this.loading = false;
      });
    },
    renderDeptOptions(children, level = 1) {
      let result = [];
      children.forEach((child) => {
        result.push({
          ...child,
          level,
          enterpriseName: " ".repeat(level * 2) + child.enterpriseName,
        });
        if (child.children && child.children.length) {
          result = result.concat(
            this.renderDeptOptions(child.children, level + 1)
          );
        }
      });
      return result;
    },
    flattenDept(children) {
      let result = [];
      children.forEach((child) => {
        result.push(child);
        if (child.children && child.children.length) {
          result = result.concat(this.flattenDept(child.children));
        }
      });
      return result;
    },
    /** 查询绩效评定列表 */
    getList() {
      this.loading = true;
      listZjPerformanceEvaluation(this.queryParams).then((response) => {
        this.zjPerformanceEvaluationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        unitName: null,
        evaluationCycle: null,
        standardScore: null,
        indicatorCompletionRate: null,
        comprehensiveEvaluation: null,
        assessorName: null,
        evaluationTime: null,
        fjUrl: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加绩效评定";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjPerformanceEvaluation(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改绩效评定";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjPerformanceEvaluation(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjPerformanceEvaluation(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除绩效评定编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjPerformanceEvaluation(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjPerformanceEvaluation/export",
        {
          ...this.queryParams,
        },
        `zjPerformanceEvaluation_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
