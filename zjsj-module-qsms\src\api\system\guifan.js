import request from '@/utils/request'

// 查询项目检查列表
export function getExamList(query) {
  return request({
    url: '/api/exam',
    method: 'get',
    params: query
  })
}


// 大类
// /pc/inspec/hazard/getCategoryType?examid=3
export function getCategoryType(query) {
  return request({
    url: '/pc/inspec/hazard/getCategoryType',
    method: 'get',
    params: query
  })
}

// 小类
// /pc/inspec/hazard/getsubclassInfo?hazardid=477
export function getsubclassInfo(query) {
  return request({
    url: '/pc/inspec/hazard/getsubclassInfo',
    method: 'get',
    params: query
  })
}

// 规范
// /pc/inspec/hazard/getSpecificationInfo?categoryId=374&subclassId=382
export function getSpecificationInfo(query) {
  return request({
    url: '/pc/inspec/hazard/getSpecificationInfo',
    method: 'get',
    params: query
  })
}

// 添加 修改 排序
// /pc/inspec/hazard
export function updateList(data) {
  return request({
    url: '/pc/inspec/hazard',
    method: 'post',
    data
  })
}

// 修改大小类名称
export function editCategoryName(data) {
  return request({
    url: '/pc/inspec/hazard',
    method: 'put',
    data
  })
}

// 删除
export function deleteCategory(id) {
  return request({
    url: '/pc/inspec/hazard/' + id,
    method: 'delete',
  })
}

// 修改顺序
export function editOrderNum(data) {
  return request({
    url: '/pc/inspec/hazard/editOrderNum',
    method: 'put',
    data
  })
}

// 添加 规范详情
// /pc/specificationInfo/
export function addGuif(data) {
  return request({
    url: '/pc/specificationInfo',
    method: 'post',
    data
  })
}

// 修改 规范详情
// /pc/specificationInfo/
export function editGuif(data) {
  return request({
    url: '/pc/specificationInfo',
    method: 'put',
    data
  })
}

// 删除 规范详情
// /pc/specificationInfo/
export function deleteGuif(id) {
  return request({
    url: '/pc/specificationInfo/' + id,
    method: 'delete',
  })
}