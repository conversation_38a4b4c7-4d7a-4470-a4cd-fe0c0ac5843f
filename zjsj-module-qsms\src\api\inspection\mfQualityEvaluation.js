import request from '@/utils/request'

// 查询工程质量评价列表
export function listMfQualityEvaluation(query) {
  return request({
    url: '/inspect/mfQualityEvaluation/list',
    method: 'get',
    params: query
  })
}

// 查询工程质量评价详细
export function getMfQualityEvaluation(id) {
  return request({
    url: '/inspect/mfQualityEvaluation/' + id,
    method: 'get'
  })
}

// 新增工程质量评价
export function addMfQualityEvaluation(data) {
  return request({
    url: '/inspect/mfQualityEvaluation',
    method: 'post',
    data: data
  })
}

// 修改工程质量评价
export function updateMfQualityEvaluation(data) {
  return request({
    url: '/inspect/mfQualityEvaluation',
    method: 'put',
    data: data
  })
}

// 删除工程质量评价
export function delMfQualityEvaluation(id) {
  return request({
    url: '/inspect/mfQualityEvaluation/' + id,
    method: 'delete'
  })
}
