<template>
  <div class="top-navbar">
    <div class="nav-container">
      <!-- 左侧系统标题 -->
      <div class="logo-section">
        <img src="@/assets/images/menhu/logo.png" alt="logo" class="logo-img">
        <span class="system-title">
          <img src="@/assets/images/menhu/navTitle.png" class="title-img" alt=""></span>
      </div>
      <!-- 中间模式切换按钮 -->
      <!-- <div class="nav-buttons"> -->
      <!-- <button v-if="showProject" class="nav-btn" @click="switchMode('project')">
          项目看板
        </button> -->
      <!-- </div> -->
      <div class="nav-buttons">
        <button v-if="isAdmin" class="nav-btn" @click="switchMode('admin')">后台管理</button>
        <span class="nav-btn" @click="goBack">一部一屏</span>
      </div>
      <!-- 右侧用户信息区域 -->
      <div class="user-section">
        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <!-- <img :src="avatar" class="user-avatar" /> -->
            <span class="user-name">{{ userName }}</span>
          </div>
          <el-dropdown-menu slot="dropdown">
            <!-- <el-dropdown-item @click.native="setting = true">
                  <span>布局设置</span>
                </el-dropdown-item> -->
            <el-dropdown-item divided @click.native="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import swiper01 from '@/assets/images/menhu/swiper01.jpg'
import swiper02 from '@/assets/images/menhu/swiper02.jpg'

export default {
  props: {
    showProject: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      swiperList: [
        { src: swiper01, title: '中江集团' },
        { src: swiper02, title: '中江集团' }
      ],
      breadTitle: '',
      articleDetail: {
        title: '',
        content: '',
        publishTime: ''
      },
      isVideoType: false,
      attachments: [],
      videoExtensions: ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv'],
      isAdmin: false,
      baseUrl: process.env.VUE_APP_BASE_API
    }
  },
  computed: {
    ...mapGetters(['avatar']),
    userName() {
      return this.$store.state.user.name || '管理员'
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  mounted() {
    const permissions = this.$store.getters.permissions
    const roles = this.$store.getters.roles
    this.isAdmin =
      (Array.isArray(permissions) && permissions.includes('system:pcadmin')) ||
      roles.includes('admin')
  },
  methods: {
    goBack() {
      window.location.href = 'http://192.168.13.68:8099/#/auto-detail'
    },

    switchMode(type) {
      if (type == 'project') {
        // 大屏
        window.open('http://192.168.13.68:8098/#/project/index', '_blank')
      } else if (type == 'admin') {
        // 后台首页
        window.open('/qsms/index', '_blank')
      }
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/qsms/login'
          })
        })
        .catch(() => { })
    },

    goHome() {
      this.$router.push('/menhu')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-navbar {
  height: 60px;
  // background: linear-gradient(135deg, #4A90E2 0%, #5BA3F5 100%);
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  position: relative;

  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(35, 69, 103, 0.15);
  border-radius: 0px 0px 0px 0px;
  border: 1px solid #ebebeb;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  //   opacity: 0.9;
  //   z-index: -1;
  // }

  .nav-container {
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 20px;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-right: 100px;

    .logo-img {
      height: 85px;
      width: auto;
    }

    .system-title {
      // width: 270px;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.9);
    }

    .title-img {
      height: 25px;
      margin-top: 8px;
      margin-left: -35px;
    }
  }

  .nav-buttons {
    flex-grow: 1;
    display: flex;
    justify-content: flex-end;
    // gap: 100px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    padding: 4px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .nav-btn {
      width: 120px;
      padding: 8px 24px;
      border: none;
      background: transparent;
      font-weight: normal;
      font-size: 16px;
      color: #33373b;
      cursor: pointer;
      border-radius: 20px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      font-family: "MiSans-Regular";

      // &::before {
      //   content: '';
      //   position: absolute;
      //   top: 0;
      //   left: -100%;
      //   width: 100%;
      //   height: 100%;
      //   background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      //   transition: left 0.5s;
      // }

      &.active {
        background: #0974ff;
        border-radius: 38px 38px 38px 38px;
        color: #ffffff;
      }

      &:hover {
        background: rgba(0, 0, 0, 0.1);
        // font-family: "MiSans-Semibold";
      }
    }
  }

  .user-section {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-left: auto;

    .right-menu-item {
      color: #333;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }

    .avatar-container {
      .avatar-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        padding: 6px 12px;
        border-radius: 20px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 2px solid rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;

          &:hover {
            border-color: #333;
          }
        }

        .user-name {
          color: #333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .back-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
    background: rgba(64, 158, 255, 0.1);
    color: #409eff;
    margin-left: 16px;

    .back-icon {
      font-size: 14px;
    }
  }

  // 响应式处理
  @media (max-width: 768px) {
    .nav-container {
      padding: 0 16px;
    }

    .logo-section .system-title {
      font-size: 16px;
    }

    .nav-buttons .nav-btn {
      padding: 6px 16px;
      font-size: 12px;
    }
  }
}
</style>
