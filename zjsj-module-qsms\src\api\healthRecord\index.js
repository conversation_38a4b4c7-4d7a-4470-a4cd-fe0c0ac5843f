import request from '@/utils/request'

// 查询员工健康状况登记单列表
export function healthList(query) {
  return request({
    url: '/system/form/list',
    method: 'get',
    params: query
  })
}

// 获取员工健康状况登记单详细信息
export function getHealthDetail(id) {
  return request({
    url: '/system/form/' + id,
    method: 'get'
  })
}

// 新增员工健康状况登记单
export function addHealth(data) {
  return request({
    url: '/system/form',
    method: 'post',
    data:data
  })
}

// 修改员工健康状况登记单
export function updateHealth(data) {
  return request({
    url: '/system/form',
    method: 'put',
    data: data
  })
}


//删除员工健康状况登记单
export function delHealth(ids) {
  return request({
    url: '/system/form/' + ids,
    method: 'delete'
  })
}

//导出员工健康状况登记单
export function exportHealth(params) {
  return request({
    url: '/system/form/export',
    method: 'post'
  })
}

