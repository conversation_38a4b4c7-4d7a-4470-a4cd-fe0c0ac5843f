import request from '@/utils/request'

// 查询智能技术字典列表
export function listSmartTechDictionary(query) {
  return request({
    url: '/system/smartTechDictionary/list',
    method: 'get',
    params: query
  })
}

// 查询智能技术字典详细
export function getSmartTechDictionary(id) {
  return request({
    url: '/system/smartTechDictionary/' + id,
    method: 'get'
  })
}

// 新增智能技术字典
export function addSmartTechDictionary(data) {
  return request({
    url: '/system/smartTechDictionary',
    method: 'post',
    data: data
  })
}

// 修改智能技术字典
export function updateSmartTechDictionary(data) {
  return request({
    url: '/system/smartTechDictionary',
    method: 'put',
    data: data
  })
}

// 删除智能技术字典
export function delSmartTechDictionary(id) {
  return request({
    url: '/system/smartTechDictionary/' + id,
    method: 'delete'
  })
}
