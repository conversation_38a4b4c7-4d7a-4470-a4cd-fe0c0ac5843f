{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjEmployeeCasualtyAccidents\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjEmployeeCasualtyAccidents\\index.vue", "mtime": 1757497628586}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0WmpFbXBsb3llZUNhc3VhbHR5QWNjaWRlbnRzLCBnZXRaakVtcGxveWVlQ2FzdWFsdHlBY2NpZGVudHMsIGRlbFpqRW1wbG95ZWVDYXN1YWx0eUFjY2lkZW50cywgYWRkWmpFbXBsb3llZUNhc3VhbHR5QWNjaWRlbnRzLCB1cGRhdGVaakVtcGxveWVlQ2FzdWFsdHlBY2NpZGVudHMgfSBmcm9tICJAL2FwaS9pbnNwZWN0aW9uL3pqRW1wbG95ZWVDYXN1YWx0eUFjY2lkZW50cyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlpqRW1wbG95ZWVDYXN1YWx0eUFjY2lkZW50cyIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDogYzlt6XkvKTkuqHkuovmlYXooajmoLzmlbDmja4NCiAgICAgIHpqRW1wbG95ZWVDYXN1YWx0eUFjY2lkZW50c0xpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGNvbXBhbnlJZDogbnVsbCwNCiAgICAgICAgY29tcGFueU5hbWU6IG51bGwsDQogICAgICAgIHByb2plY3ROYW1lOiBudWxsLA0KICAgICAgICBwcm9qZWN0SWQ6IG51bGwsDQogICAgICAgIGFjY2lkZW50RGF0ZTogbnVsbCwNCiAgICAgICAgYWNjaWRlbnRzTnVtOiBudWxsLA0KICAgICAgICBjYXN1YWx0aWVzVG90YWxOdW06IG51bGwsDQogICAgICAgIHNlcmlvdXNJbmp1cnlUb3RhbE51bTogbnVsbCwNCiAgICAgICAgbWlub3JJbmp1cnlUb3RhbE51bTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6IGM5bel5Lyk5Lqh5LqL5pWF5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0WmpFbXBsb3llZUNhc3VhbHR5QWNjaWRlbnRzKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnpqRW1wbG95ZWVDYXN1YWx0eUFjY2lkZW50c0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgY29tcGFueUlkOiBudWxsLA0KICAgICAgICBjb21wYW55TmFtZTogbnVsbCwNCiAgICAgICAgcHJvamVjdE5hbWU6IG51bGwsDQogICAgICAgIHByb2plY3RJZDogbnVsbCwNCiAgICAgICAgYWNjaWRlbnREYXRlOiBudWxsLA0KICAgICAgICBhY2NpZGVudHNOdW06IG51bGwsDQogICAgICAgIGNhc3VhbHRpZXNUb3RhbE51bTogbnVsbCwNCiAgICAgICAgc2VyaW91c0luanVyeVRvdGFsTnVtOiBudWxsLA0KICAgICAgICBtaW5vckluanVyeVRvdGFsTnVtOiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6IGM5bel5Lyk5Lqh5LqL5pWFIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0WmpFbXBsb3llZUNhc3VhbHR5QWNjaWRlbnRzKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnogYzlt6XkvKTkuqHkuovmlYUiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlWmpFbXBsb3llZUNhc3VhbHR5QWNjaWRlbnRzKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkWmpFbXBsb3llZUNhc3VhbHR5QWNjaWRlbnRzKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGxldCBpZHM7DQogICAgICBpZiAocm93ICYmIHJvdy5pZCkgew0KICAgICAgICAvLyDljZXooYzliKDpmaQNCiAgICAgICAgaWRzID0gW3Jvdy5pZF07DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpJrooYzliKDpmaQNCiAgICAgICAgaWRzID0gdGhpcy5pZHM7DQogICAgICB9DQoNCiAgICAgIGlmICghaWRzIHx8IChBcnJheS5pc0FycmF5KGlkcykgJiYgaWRzLmxlbmd0aCA9PT0gMCkpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqeimgeWIoOmZpOeahOaVsOaNriIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaQnICsgaWRzLmxlbmd0aCArICfmnaHogYzlt6XkvKTkuqHkuovmlYXmlbDmja7pobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgICByZXR1cm4gZGVsWmpFbXBsb3llZUNhc3VhbHR5QWNjaWRlbnRzKGlkcyk7DQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgLy8g5qC55o2u6YCJ5Lit54q25oCB5Yaz5a6a5a+85Ye65YaF5a655ZKM5Y+C5pWwDQogICAgICBjb25zdCBoYXNTZWxlY3Rpb24gPSB0aGlzLmlkcy5sZW5ndGggPiAwOw0KICAgICAgY29uc3QgY29uZmlybU1lc3NhZ2UgPSBoYXNTZWxlY3Rpb24NCiAgICAgICAgPyBg5piv5ZCm56Gu6K6k5a+85Ye66YCJ5Lit55qEJHt0aGlzLmlkcy5sZW5ndGh95p2h6IGM5bel5Lyk5Lqh5LqL5pWF5pWw5o2u6aG5P2ANCiAgICAgICAgOiAi5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ6IGM5bel5Lyk5Lqh5LqL5pWF5pWw5o2u6aG5PyI7DQoNCiAgICAgIC8vIOWHhuWkh+WvvOWHuuWPguaVsA0KICAgICAgY29uc3QgZXhwb3J0UGFyYW1zID0gaGFzU2VsZWN0aW9uDQogICAgICAgID8geyBpZHM6IHRoaXMuaWRzLmpvaW4oIiwiKSB9IC8vIOmAieS4reS6huihjO+8jOWPquS8oGlkc+WPguaVsA0KICAgICAgICA6IHsgLi4udGhpcy5xdWVyeVBhcmFtcyB9OyAvLyDmsqHpgInkuK3vvIzkvKDmn6Xor6Llj4LmlbANCg0KICAgICAgLy8g5aaC5p6c5a+85Ye65YWo6YOo77yM56e76Zmk5YiG6aG15Y+C5pWwDQogICAgICBpZiAoIWhhc1NlbGVjdGlvbikgew0KICAgICAgICBkZWxldGUgZXhwb3J0UGFyYW1zLnBhZ2VOdW07DQogICAgICAgIGRlbGV0ZSBleHBvcnRQYXJhbXMucGFnZVNpemU7DQogICAgICB9DQoNCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKGNvbmZpcm1NZXNzYWdlKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgICAgICdpbnNwZWN0aW9uL3pqRW1wbG95ZWVDYXN1YWx0eUFjY2lkZW50cy9leHBvcnQnLA0KICAgICAgICAgICAgZXhwb3J0UGFyYW1zLA0KICAgICAgICAgICAgYHpqRW1wbG95ZWVDYXN1YWx0eUFjY2lkZW50c18ke2hhc1NlbGVjdGlvbiA/ICdzZWxlY3RlZF8nIDogJyd9JHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICAgICApOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/zjEmployeeCasualtyAccidents", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"公司id\" prop=\"companyId\">\r\n        <el-input\r\n          v-model=\"queryParams.companyId\"\r\n          placeholder=\"请输入公司id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          v-model=\"queryParams.companyName\"\r\n          placeholder=\"请输入公司名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目id\" prop=\"projectId\">\r\n        <el-input\r\n          v-model=\"queryParams.projectId\"\r\n          placeholder=\"请输入项目id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"事故日期\" prop=\"accidentDate\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.accidentDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择事故日期\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"事故件数\" prop=\"accidentsNum\">\r\n        <el-input\r\n          v-model=\"queryParams.accidentsNum\"\r\n          placeholder=\"请输入事故件数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"死亡人数\" prop=\"casualtiesTotalNum\">\r\n        <el-input\r\n          v-model=\"queryParams.casualtiesTotalNum\"\r\n          placeholder=\"请输入死亡人数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"重伤人数\" prop=\"seriousInjuryTotalNum\">\r\n        <el-input\r\n          v-model=\"queryParams.seriousInjuryTotalNum\"\r\n          placeholder=\"请输入重伤人数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"轻伤人数\" prop=\"minorInjuryTotalNum\">\r\n        <el-input\r\n          v-model=\"queryParams.minorInjuryTotalNum\"\r\n          placeholder=\"请输入轻伤人数\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"zjEmployeeCasualtyAccidentsList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"公司\" align=\"center\" prop=\"companyName\" />\r\n      <el-table-column label=\"项目名称\" align=\"center\" prop=\"projectName\" />\r\n      <el-table-column label=\"事故日期\" align=\"center\" prop=\"accidentDate\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.accidentDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"事故件数\" align=\"center\" prop=\"accidentsNum\" />\r\n      <el-table-column label=\"伤亡人数\" align=\"center\">\r\n        <el-table-column label=\"死亡\" align=\"center\" prop=\"casualtiesTotalNum\" />\r\n        <el-table-column label=\"重伤\" align=\"center\" prop=\"seriousInjuryTotalNum\" />\r\n        <el-table-column label=\"轻伤\" align=\"center\" prop=\"minorInjuryTotalNum\" />\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjEmployeeCasualtyAccidents:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <div class=\"pagination-wrapper\">\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改职工伤亡事故对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"公司id\" prop=\"companyId\">\r\n          <el-input v-model=\"form.companyId\" placeholder=\"请输入公司id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n          <el-input v-model=\"form.companyName\" placeholder=\"请输入公司名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n          <el-input v-model=\"form.projectName\" placeholder=\"请输入项目名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目id\" prop=\"projectId\">\r\n          <el-input v-model=\"form.projectId\" placeholder=\"请输入项目id\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"事故日期\" prop=\"accidentDate\">\r\n          <el-date-picker clearable\r\n            v-model=\"form.accidentDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择事故日期\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"事故件数\" prop=\"accidentsNum\">\r\n          <el-input v-model=\"form.accidentsNum\" placeholder=\"请输入事故件数\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"死亡人数\" prop=\"casualtiesTotalNum\">\r\n          <el-input v-model=\"form.casualtiesTotalNum\" placeholder=\"请输入死亡人数\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"重伤人数\" prop=\"seriousInjuryTotalNum\">\r\n          <el-input v-model=\"form.seriousInjuryTotalNum\" placeholder=\"请输入重伤人数\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"轻伤人数\" prop=\"minorInjuryTotalNum\">\r\n          <el-input v-model=\"form.minorInjuryTotalNum\" placeholder=\"请输入轻伤人数\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listZjEmployeeCasualtyAccidents, getZjEmployeeCasualtyAccidents, delZjEmployeeCasualtyAccidents, addZjEmployeeCasualtyAccidents, updateZjEmployeeCasualtyAccidents } from \"@/api/inspection/zjEmployeeCasualtyAccidents\";\r\n\r\nexport default {\r\n  name: \"ZjEmployeeCasualtyAccidents\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 职工伤亡事故表格数据\r\n      zjEmployeeCasualtyAccidentsList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        companyId: null,\r\n        companyName: null,\r\n        projectName: null,\r\n        projectId: null,\r\n        accidentDate: null,\r\n        accidentsNum: null,\r\n        casualtiesTotalNum: null,\r\n        seriousInjuryTotalNum: null,\r\n        minorInjuryTotalNum: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询职工伤亡事故列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listZjEmployeeCasualtyAccidents(this.queryParams).then(response => {\r\n        this.zjEmployeeCasualtyAccidentsList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        companyId: null,\r\n        companyName: null,\r\n        projectName: null,\r\n        projectId: null,\r\n        accidentDate: null,\r\n        accidentsNum: null,\r\n        casualtiesTotalNum: null,\r\n        seriousInjuryTotalNum: null,\r\n        minorInjuryTotalNum: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加职工伤亡事故\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getZjEmployeeCasualtyAccidents(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改职工伤亡事故\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateZjEmployeeCasualtyAccidents(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addZjEmployeeCasualtyAccidents(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids;\r\n      if (row && row.id) {\r\n        // 单行删除\r\n        ids = [row.id];\r\n      } else {\r\n        // 多行删除\r\n        ids = this.ids;\r\n      }\r\n\r\n      if (!ids || (Array.isArray(ids) && ids.length === 0)) {\r\n        this.$modal.msgError(\"请选择要删除的数据\");\r\n        return;\r\n      }\r\n\r\n      this.$modal\r\n        .confirm('是否确认删除' + ids.length + '条职工伤亡事故数据项？')\r\n        .then(function() {\r\n          return delZjEmployeeCasualtyAccidents(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 根据选中状态决定导出内容和参数\r\n      const hasSelection = this.ids.length > 0;\r\n      const confirmMessage = hasSelection\r\n        ? `是否确认导出选中的${this.ids.length}条职工伤亡事故数据项?`\r\n        : \"是否确认导出所有职工伤亡事故数据项?\";\r\n\r\n      // 准备导出参数\r\n      const exportParams = hasSelection\r\n        ? { ids: this.ids.join(\",\") } // 选中了行，只传ids参数\r\n        : { ...this.queryParams }; // 没选中，传查询参数\r\n\r\n      // 如果导出全部，移除分页参数\r\n      if (!hasSelection) {\r\n        delete exportParams.pageNum;\r\n        delete exportParams.pageSize;\r\n      }\r\n\r\n      this.$modal\r\n        .confirm(confirmMessage)\r\n        .then(() => {\r\n          this.download(\r\n            'inspection/zjEmployeeCasualtyAccidents/export',\r\n            exportParams,\r\n            `zjEmployeeCasualtyAccidents_${hasSelection ? 'selected_' : ''}${new Date().getTime()}.xlsx`\r\n          );\r\n        })\r\n        .catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 确保分页固定在底部，参考其他页面的实现 */\r\n.app-container {\r\n  height: calc(100vh - 84px);\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px;\r\n}\r\n\r\n/* 搜索表单区域 */\r\n.app-container .el-form {\r\n  flex-shrink: 0;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n/* 工具栏区域 */\r\n.app-container .el-row.mb8 {\r\n  flex-shrink: 0;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n/* 表格区域 - 占据剩余空间 */\r\n.app-container .el-table {\r\n  flex: 1;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 分页样式 - 固定在底部 */\r\n.pagination-wrapper {\r\n  flex-shrink: 0;\r\n  text-align: center;\r\n  padding: 16px 0;\r\n  margin-top: auto;\r\n  background-color: #fff;\r\n  border-top: 1px solid #ebeef5;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n/* 分页组件响应式样式 */\r\n.pagination-wrapper ::v-deep .el-pagination {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .el-pagination__total {\r\n  margin-right: 16px;\r\n  order: 1;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .btn-prev {\r\n  order: 2;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .el-pager {\r\n  order: 3;\r\n}\r\n\r\n.pagination-wrapper ::v-deep .el-pagination .btn-next {\r\n  order: 4;\r\n}\r\n\r\n/* 中等屏幕适配 (平板) */\r\n@media (max-width: 1024px) and (min-width: 769px) {\r\n  .pagination-wrapper {\r\n    padding: 10px 0 15px;\r\n    border-top: 1px solid #ebeef5;\r\n    background: #fff;\r\n    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {\r\n    font-size: 13px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-prev,\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-next {\r\n    padding: 0 10px;\r\n    min-width: 36px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pager li {\r\n    min-width: 36px;\r\n    height: 36px;\r\n    line-height: 36px;\r\n    font-size: 13px;\r\n  }\r\n}\r\n\r\n/* 小屏幕适配 */\r\n@media (max-width: 768px) {\r\n  .app-container {\r\n    height: calc(100vh - 84px);\r\n    padding: 10px;\r\n  }\r\n\r\n  .pagination-wrapper {\r\n    margin-bottom: 0;\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n    background: #fff;\r\n    border-top: 2px solid #ebeef5;\r\n    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);\r\n    z-index: 1000;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination {\r\n    flex-direction: row;\r\n    justify-content: center;\r\n    gap: 4px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pagination__total {\r\n    font-size: 12px;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-prev,\r\n  .pagination-wrapper ::v-deep .el-pagination .btn-next {\r\n    padding: 0 8px;\r\n    min-width: 32px;\r\n  }\r\n\r\n  .pagination-wrapper ::v-deep .el-pagination .el-pager li {\r\n    min-width: 32px;\r\n    height: 32px;\r\n    line-height: 32px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  /* 为小屏幕预留底部分页空间 */\r\n  .el-table {\r\n    margin-bottom: 60px;\r\n  }\r\n}\r\n</style>\r\n"]}]}