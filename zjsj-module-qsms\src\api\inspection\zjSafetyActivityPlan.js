import request from '@/utils/request'

// 查询安全活动计划列表
export function listZjSafetyActivityPlan(query) {
  return request({
    url: '/inspection/zjSafetyActivityPlan/list',
    method: 'get',
    params: query
  })
}

// 查询安全活动计划详细
export function getZjSafetyActivityPlan(id) {
  return request({
    url: '/inspection/zjSafetyActivityPlan/' + id,
    method: 'get'
  })
}

// 新增安全活动计划
export function addZjSafetyActivityPlan(data) {
  return request({
    url: '/inspection/zjSafetyActivityPlan',
    method: 'post',
    data: data
  })
}

// 修改安全活动计划
export function updateZjSafetyActivityPlan(data) {
  return request({
    url: '/inspection/zjSafetyActivityPlan',
    method: 'put',
    data: data
  })
}

// 删除安全活动计划
export function delZjSafetyActivityPlan(id) {
  return request({
    url: '/inspection/zjSafetyActivityPlan/' + id,
    method: 'delete'
  })
}
