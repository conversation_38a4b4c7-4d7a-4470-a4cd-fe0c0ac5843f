import request from '@/utils/request'

// 查询机构信息列表
export function listZjOrgInfo(query) {
  return request({
    url: '/inspection/zjOrgInfo/list',
    method: 'get',
    params: query
  })
}

// 查询机构信息详细
export function getZjOrgInfo(id) {
  return request({
    url: '/inspection/zjOrgInfo/' + id,
    method: 'get'
  })
}

// 新增机构信息
export function addZjOrgInfo(data) {
  return request({
    url: '/inspection/zjOrgInfo',
    method: 'post',
    data: data
  })
}

// 修改机构信息
export function updateZjOrgInfo(data) {
  return request({
    url: '/inspection/zjOrgInfo',
    method: 'put',
    data: data
  })
}

// 删除机构信息
export function delZjOrgInfo(id) {
  return request({
    url: '/inspection/zjOrgInfo/' + id,
    method: 'delete'
  })
}
