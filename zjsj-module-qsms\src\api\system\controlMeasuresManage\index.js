import request from '@/utils/request'

// 查询风险控制措施管理（关联风险点与管控措施）列表
export function listMeasures(query) {
  return request({
    url: '/system/measures/list',
    method: 'get',
    params: query
  })
}

// 查询风险控制措施管理（关联风险点与管控措施）详细
export function getMeasures(id) {
  return request({
    url: '/system/measures/' + id,
    method: 'get'
  })
}

// 新增风险控制措施管理（关联风险点与管控措施）
export function addMeasures(data) {
  return request({
    url: '/system/measures',
    method: 'post',
    data: data
  })
}

// 修改风险控制措施管理（关联风险点与管控措施）
export function updateMeasures(data) {
  return request({
    url: '/system/measures',
    method: 'put',
    data: data
  })
}

// 删除风险控制措施管理（关联风险点与管控措施）
export function delMeasures(id) {
  return request({
    url: '/system/measures/' + id,
    method: 'delete'
  })
}
