{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorInfo\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorInfo\\detail.vue", "mtime": 1757491222233}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBnZXRaakNvbnRyYWN0b3JJbmZvLA0KICB1cGRhdGVaakNvbnRyYWN0b3JJbmZvLA0KfSBmcm9tICJAL2FwaS9jb250cmFjdG9yL3pqQ29udHJhY3RvckluZm8iOw0KaW1wb3J0IHsgZ2V0VXNlckluZm8gfSBmcm9tICJAL2FwaS9jb250cmFjdG9yL3pqQ29udHJhY3RvckJsYWtsaXN0IjsNCmltcG9ydCBRdWFsaWZpY2F0aW9uVGFiIGZyb20gIi4vY29tcG9uZW50cy9RdWFsaWZpY2F0aW9uVGFiLnZ1ZSI7DQppbXBvcnQgUGVyZm9ybWFuY2VUYWIgZnJvbSAiLi9jb21wb25lbnRzL1BlcmZvcm1hbmNlVGFiLnZ1ZSI7DQppbXBvcnQgV29ya1RpY2tldFRhYiBmcm9tICIuL2NvbXBvbmVudHMvV29ya1RpY2tldFRhYi52dWUiOw0KaW1wb3J0IENvbnN0cnVjdGlvblRhYiBmcm9tICIuL2NvbXBvbmVudHMvQ29uc3RydWN0aW9uVGFiLnZ1ZSI7DQppbXBvcnQgRXZhbHVhdGlvblRhYiBmcm9tICIuL2NvbXBvbmVudHMvRXZhbHVhdGlvblRhYi52dWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJaakNvbnRyYWN0b3JJbmZvRGV0YWlsIiwNCiAgZGljdHM6IFsiY29udHJhY3Rvcl9jYXRlZ29yeSIsICJzeXNfY29udHJhY3Rvcl90eXBlIl0sDQogIGNvbXBvbmVudHM6IHsNCiAgICBRdWFsaWZpY2F0aW9uVGFiLA0KICAgIFBlcmZvcm1hbmNlVGFiLA0KICAgIFdvcmtUaWNrZXRUYWIsDQogICAgQ29uc3RydWN0aW9uVGFiLA0KICAgIEV2YWx1YXRpb25UYWIsDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOaJv+WMheWVhuS/oeaBrw0KICAgICAgY29udHJhY3RvckluZm86IHt9LA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY29udHJhY3Rvck5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5om/5YyF5ZWG5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGFkbWluaXN0cmF0b3JJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnrqHnkIbkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjcmVkaXRDb2RlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi57uf5LiA56S+5Lya5L+h55So5Luj56CB5LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIiwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgICBzZXJ2aWNlVGltZVJhbmdlOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi5pyN5Yqh6LW35q2i5pe26Ze05LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRyYWN0b3JNYW5hZ2VyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJv+WMheWVhui0n+i0o+S6uuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIC8vIOeuoeeQhuS6uumAiemhuQ0KICAgICAgbWFuYWdlck9wdGlvbnM6IFtdLA0KICAgICAgLy8g5b2T5YmN5rS76LeD55qEdGFiDQogICAgICBhY3RpdmVUYWJOYW1lOiAicXVhbGlmaWNhdGlvbiIsDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zdCBpZCA9IHRoaXMuJHJvdXRlLnBhcmFtcy5pZDsNCiAgICBpZiAoaWQpIHsNCiAgICAgIHRoaXMuZ2V0Q29udHJhY3RvckRldGFpbChpZCk7DQogICAgICB0aGlzLmxvYWRNYW5hZ2VyT3B0aW9ucygpOw0KICAgIH0gZWxzZSB7DQogICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi57y65bCR5om/5YyF5ZWGSUTlj4LmlbAiKTsNCiAgICAgIHRoaXMuZ29CYWNrKCk7DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOiOt+WPluaJv+WMheWVhuivpuaDhSAqLw0KICAgIGdldENvbnRyYWN0b3JEZXRhaWwoaWQpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBnZXRaakNvbnRyYWN0b3JJbmZvKGlkKQ0KICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICB0aGlzLmNvbnRyYWN0b3JJbmZvID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6I635Y+W5om/5YyF5ZWG6K+m5oOF5aSx6LSlIik7DQogICAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog6L+U5Zue5YiX6KGo6aG1ICovDQogICAgZ29CYWNrKCkgew0KICAgICAgLy8g5LyY5YWI5L2/55So5rWP6KeI5Zmo5Y6G5Y+y6L+U5Zue77yM6YG/5YWN5YaZ5q276Lev5b6EDQogICAgICBpZiAod2luZG93Lmhpc3RvcnkubGVuZ3RoID4gMSkgew0KICAgICAgICB0aGlzLiRyb3V0ZXIuYmFjaygpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWFnOW6lTHvvJrlpoLmnpzot6/nlLFtZXRh5o+Q5L6b5LqGYWN0aXZlTWVude+8jOWImei3s+i9rOivpeiPnOWNlQ0KICAgICAgY29uc3QgYWN0aXZlTWVudSA9IHRoaXMuJHJvdXRlICYmIHRoaXMuJHJvdXRlLm1ldGEgJiYgdGhpcy4kcm91dGUubWV0YS5hY3RpdmVNZW51Ow0KICAgICAgaWYgKGFjdGl2ZU1lbnUpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnJlcGxhY2UoYWN0aXZlTWVudSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5YWc5bqVMu+8muagueaNruW9k+WJjei3r+W+hOWKqOaAgeiuoeeul+S4iuS4gOe6p+i3r+W+hA0KICAgICAgY29uc3QgcGF0aFBhcnRzID0gKHRoaXMuJHJvdXRlICYmIHRoaXMuJHJvdXRlLnBhdGggPyB0aGlzLiRyb3V0ZS5wYXRoIDogIi8iKS5zcGxpdCgiLyIpLmZpbHRlcihCb29sZWFuKTsNCiAgICAgIGlmIChwYXRoUGFydHMubGVuZ3RoID4gMSkgew0KICAgICAgICBjb25zdCBsYXN0ID0gcGF0aFBhcnRzW3BhdGhQYXJ0cy5sZW5ndGggLSAxXTsNCiAgICAgICAgY29uc3QgcHJldiA9IHBhdGhQYXJ0c1twYXRoUGFydHMubGVuZ3RoIC0gMl0gfHwgIiI7DQogICAgICAgIC8vIOiLpei3r+W+hOS7pSAvLi4uL2RldGFpbC86aWQg5oiWIC8uLi4vZWRpdC86aWQg5b2i5byP5a2Y5Zyo77yM5YiZ5Zue6YCA5Lik57qn77ybDQogICAgICAgIC8vIOWQpuWImem7mOiupOWOu+aOieacgOWQjuS4gOe6pw0KICAgICAgICBjb25zdCBpc0lkTGlrZSA9IC9eXGQrJC8udGVzdChsYXN0KSB8fCAvXlswLTlhLWZBLUYtXXs4LH0kLy50ZXN0KGxhc3QpOw0KICAgICAgICBjb25zdCBpc0FjdGlvblNlZ21lbnQgPSBbImRldGFpbCIsICJlZGl0IiwgInZpZXciXS5pbmNsdWRlcyhwcmV2KTsNCiAgICAgICAgY29uc3QgcGFyZW50UGFydHMgPSBpc0lkTGlrZSAmJiBpc0FjdGlvblNlZ21lbnQgPyBwYXRoUGFydHMuc2xpY2UoMCwgLTIpIDogcGF0aFBhcnRzLnNsaWNlKDAsIC0xKTsNCiAgICAgICAgY29uc3QgcGFyZW50UGF0aCA9ICIvIiArIHBhcmVudFBhcnRzLmpvaW4oIi8iKTsNCiAgICAgICAgdGhpcy4kcm91dGVyLnJlcGxhY2UocGFyZW50UGF0aCB8fCAiLyIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOacgOe7iOWFnOW6le+8muWbnummlumhtQ0KICAgICAgdGhpcy4kcm91dGVyLnJlcGxhY2UoIi8iKTsNCiAgICB9LA0KDQogICAgLyoqIOe8lui+keaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUVkaXQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLmZvcm0gPSB7IC4uLnRoaXMuY29udHJhY3RvckluZm8gfTsNCiAgICAgIC8vIOiuvue9ruacjeWKoeaXtumXtOiMg+WbtA0KICAgICAgaWYgKHRoaXMuZm9ybS5zZXJ2aWNlU3RhcnRUaW1lICYmIHRoaXMuZm9ybS5zZXJ2aWNlRW5kVGltZSkgew0KICAgICAgICB0aGlzLmZvcm0uc2VydmljZVRpbWVSYW5nZSA9IFsNCiAgICAgICAgICB0aGlzLmZvcm0uc2VydmljZVN0YXJ0VGltZSwNCiAgICAgICAgICB0aGlzLmZvcm0uc2VydmljZUVuZFRpbWUsDQogICAgICAgIF07DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm0uc2VydmljZVRpbWVSYW5nZSA9IG51bGw7DQogICAgICB9DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnmib/ljIXllYbkv6Hmga8iOw0KICAgIH0sDQoNCiAgICAvKiog5Yqg6L29566h55CG5Lq66YCJ6aG5ICovDQogICAgbG9hZE1hbmFnZXJPcHRpb25zKCkgew0KICAgICAgZ2V0VXNlckluZm8oMSkNCiAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwgcmVzcG9uc2Uucm93cyB8fCByZXNwb25zZSB8fCBbXTsNCiAgICAgICAgICBjb25zdCB1bmlxdWVNYW5hZ2VycyA9IG5ldyBNYXAoKTsNCiAgICAgICAgICBkYXRhLmZvckVhY2goKG1hbmFnZXIsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB2YWx1ZSA9IG1hbmFnZXIudXNlcklkIHx8IG1hbmFnZXIuaWQgfHwgYHRlbXBfJHtpbmRleH1gOw0KICAgICAgICAgICAgY29uc3QgbGFiZWwgPQ0KICAgICAgICAgICAgICBtYW5hZ2VyLm5pY2tOYW1lIHx8DQogICAgICAgICAgICAgIG1hbmFnZXIubmFtZSB8fA0KICAgICAgICAgICAgICBtYW5hZ2VyLnVzZXJOYW1lIHx8DQogICAgICAgICAgICAgIGDmnKrlkb3lkI1fJHtpbmRleH1gOw0KICAgICAgICAgICAgY29uc3Qgbmlja05hbWUgPQ0KICAgICAgICAgICAgICBtYW5hZ2VyLm5pY2tOYW1lIHx8DQogICAgICAgICAgICAgIG1hbmFnZXIubmFtZSB8fA0KICAgICAgICAgICAgICBtYW5hZ2VyLnVzZXJOYW1lIHx8DQogICAgICAgICAgICAgIGDmnKrlkb3lkI1fJHtpbmRleH1gOw0KICAgICAgICAgICAgaWYgKCF1bmlxdWVNYW5hZ2Vycy5oYXModmFsdWUpKSB7DQogICAgICAgICAgICAgIHVuaXF1ZU1hbmFnZXJzLnNldCh2YWx1ZSwgew0KICAgICAgICAgICAgICAgIHZhbHVlLA0KICAgICAgICAgICAgICAgIGxhYmVsLA0KICAgICAgICAgICAgICAgIG5pY2tOYW1lLA0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLm1hbmFnZXJPcHRpb25zID0gQXJyYXkuZnJvbSh1bmlxdWVNYW5hZ2Vycy52YWx1ZXMoKSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5bnrqHnkIbkurrpgInpobnlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5bnrqHnkIbkurrpgInpobnlpLHotKUiKTsNCiAgICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8qKiDlpITnkIbmnI3liqHml7bpl7TojIPlm7Tlj5jljJYgKi8NCiAgICBoYW5kbGVTZXJ2aWNlVGltZUNoYW5nZSh2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlICYmIHZhbHVlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICB0aGlzLmZvcm0uc2VydmljZVN0YXJ0VGltZSA9IHZhbHVlWzBdOw0KICAgICAgICB0aGlzLmZvcm0uc2VydmljZUVuZFRpbWUgPSB2YWx1ZVsxXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZm9ybS5zZXJ2aWNlU3RhcnRUaW1lID0gbnVsbDsNCiAgICAgICAgdGhpcy5mb3JtLnNlcnZpY2VFbmRUaW1lID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqIOWkhOeQhueuoeeQhuS6uuWPmOWMliAqLw0KICAgIGhhbmRsZU1hbmFnZXJDaGFuZ2UodmFsdWUpIHsNCiAgICAgIGlmICh2YWx1ZSkgew0KICAgICAgICBjb25zdCBzZWxlY3RlZE1hbmFnZXIgPSB0aGlzLm1hbmFnZXJPcHRpb25zLmZpbmQoDQogICAgICAgICAgKG1hbmFnZXIpID0+IG1hbmFnZXIudmFsdWUgPT0gdmFsdWUNCiAgICAgICAgKTsNCiAgICAgICAgaWYgKHNlbGVjdGVkTWFuYWdlcikgew0KICAgICAgICAgIHRoaXMuZm9ybS5hZG1pbmlzdHJhdG9yTmFtZSA9IHNlbGVjdGVkTWFuYWdlci5uaWNrTmFtZTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5mb3JtLmFkbWluaXN0cmF0b3JOYW1lID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCg0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgY29udHJhY3Rvck5hbWU6IG51bGwsDQogICAgICAgIGFkbWluaXN0cmF0b3JJZDogbnVsbCwNCiAgICAgICAgYWRtaW5pc3RyYXRvck5hbWU6IG51bGwsDQogICAgICAgIGNyZWRpdENvZGU6IG51bGwsDQogICAgICAgIHNlcnZpY2VTdGFydFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VFbmRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVGltZVJhbmdlOiBudWxsLA0KICAgICAgICBjb250cmFjdG9yTWFuYWdlcjogbnVsbCwNCiAgICAgICAgY29udHJhY3RvckNhdGVnb3J5OiBudWxsLA0KICAgICAgICBjb250cmFjdG9yVHlwZTogbnVsbCwNCiAgICAgICAgY29tcGFueVByb2ZpbGU6IG51bGwsDQogICAgICAgIGNoYXJnZUNvbnRhY3ROdW1iZXI6IG51bGwsDQogICAgICAgIHVuaXROYXR1cmU6IG51bGwsDQogICAgICAgIGxlZ2FsUmVwcmVzZW50YXRpdmU6IG51bGwsDQogICAgICAgIGNvbXBhbnlBZGRyZXNzOiBudWxsLA0KICAgICAgICBsZWdhbFJlcHJlc2VudGF0aXZlUGhvbmU6IG51bGwsDQogICAgICAgIGNvbXBhbnlFbWFpbDogbnVsbCwNCiAgICAgICAgYmxhY2tsaXN0U3RhdHVzOiBudWxsLA0KICAgICAgICBhY2Nlc3NTdGF0dXM6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgYmxhY2tsaXN0UmVhc29uOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bmnI3liqHml7bpl7TojIPlm7QgKi8NCiAgICBnZXRTZXJ2aWNlVGltZVJhbmdlKCkgew0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLmNvbnRyYWN0b3JJbmZvLnNlcnZpY2VTdGFydFRpbWUgJiYNCiAgICAgICAgdGhpcy5jb250cmFjdG9ySW5mby5zZXJ2aWNlRW5kVGltZQ0KICAgICAgKSB7DQogICAgICAgIHJldHVybiBbDQogICAgICAgICAgdGhpcy5jb250cmFjdG9ySW5mby5zZXJ2aWNlU3RhcnRUaW1lLA0KICAgICAgICAgIHRoaXMuY29udHJhY3RvckluZm8uc2VydmljZUVuZFRpbWUsDQogICAgICAgIF07DQogICAgICB9DQogICAgICByZXR1cm4gbnVsbDsNCiAgICB9LA0KDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHVwZGF0ZVpqQ29udHJhY3RvckluZm8odGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIC8vIOmHjeaWsOiOt+WPluivpuaDheaVsOaNrg0KICAgICAgICAgICAgdGhpcy5nZXRDb250cmFjdG9yRGV0YWlsKHRoaXMuZm9ybS5pZCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5aSE55CGdGFi54K55Ye7ICovDQogICAgaGFuZGxlVGFiQ2xpY2sodGFiKSB7DQogICAgICB0aGlzLmFjdGl2ZVRhYk5hbWUgPSB0YWIubmFtZTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/contractor/zjContractorInfo", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-arrow-left\"\r\n          @click=\"goBack\"\r\n          class=\"back-btn\"\r\n        >\r\n          返回列表\r\n        </el-button>\r\n        <div class=\"page-title\">承包商详情</div>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <!-- 保留右侧空间以保持布局平衡 -->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 详情内容 -->\r\n    <div class=\"detail-content\" v-loading=\"loading\">\r\n      <!-- 基本信息表单 -->\r\n      <div class=\"info-section\">\r\n        <div class=\"section-title\">基本信息</div>\r\n        <el-form class=\"detail-form\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第一行：承包商名称 | 管理人 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商名称\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.contractorName\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"管理人\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.administratorName\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第二行：统一社会信用代码 | 服务起止时间 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"统一社会信用代码\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.creditCode\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"服务起止时间\">\r\n                <el-date-picker\r\n                  :value=\"getServiceTimeRange()\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 100%\"\r\n                  disabled\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第三行：承包商负责人 | 承包商类别 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商负责人\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.contractorManager\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商类别\">\r\n                <el-select\r\n                  v-model=\"contractorInfo.contractorCategory\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-option label=\"准承包商\" :value=\"1\" />\r\n                  <el-option label=\"合格承包商\" :value=\"2\" />\r\n                  <el-option label=\"不合格承包商\" :value=\"3\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第四行：承包商类型 | 公司简介 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商类型\">\r\n                <el-select\r\n                  v-model=\"contractorInfo.contractorType\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.sys_contractor_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"公司简介\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.companyProfile\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n                  placeholder=\"-\"\r\n                  style=\"width: 100%\"\r\n                  disabled\r\n                >\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第五行：负责人联系电话 | 单位性质 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"负责人联系电话\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.chargeContactNumber\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"单位性质\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.unitNature\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第六行：法定代表人 | 公司地址 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"法定代表人\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.legalRepresentative\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"公司地址\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.companyAddress\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第七行：法定代表人联系电话 | 公司邮箱 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"法定代表人联系电话\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.legalRepresentativePhone\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"公司邮箱\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.companyEmail\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- Tab切换内容 -->\r\n      <div class=\"tab-section\">\r\n        <el-tabs v-model=\"activeTabName\" @tab-click=\"handleTabClick\">\r\n          <el-tab-pane label=\"资质信息\" name=\"qualification\">\r\n            <qualification-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"业绩信息\" name=\"performance\">\r\n            <performance-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"作业票\" name=\"workTicket\">\r\n            <work-ticket-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"施工信息\" name=\"construction\">\r\n            <construction-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"评价记录\" name=\"evaluation\">\r\n            <evaluation-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"1200px\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第一行：承包商名称 | 管理人 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商名称\" prop=\"contractorName\">\r\n              <el-input\r\n                v-model=\"form.contractorName\"\r\n                placeholder=\"承包商名称不能为空\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"管理人\" prop=\"administratorId\">\r\n              <el-select\r\n                v-model=\"form.administratorId\"\r\n                placeholder=\"管理人不能为空\"\r\n                style=\"width: 100%\"\r\n                @change=\"handleManagerChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"(item, index) in managerOptions\"\r\n                  :key=\"`form_manager_${item.value}_${index}`\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第二行：统一社会信用代码 | 服务起止时间 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"统一社会信用代码\" prop=\"creditCode\">\r\n              <el-input\r\n                v-model=\"form.creditCode\"\r\n                placeholder=\"统一社会信用代码不能为空\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务起止时间\" prop=\"serviceTimeRange\">\r\n              <el-date-picker\r\n                v-model=\"form.serviceTimeRange\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"handleServiceTimeChange\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第三行：承包商负责人 | 承包商类别 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商负责人\" prop=\"contractorManager\">\r\n              <el-input\r\n                v-model=\"form.contractorManager\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商类别\" prop=\"contractorCategory\">\r\n              <el-select\r\n                v-model=\"form.contractorCategory\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"(dict, index) in dict.type.contractor_category\"\r\n                  :key=\"`form_contractor_category_${dict.value}_${index}`\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第四行：承包商类型 | 公司简介（经营范围） -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商类型\" prop=\"contractorType\">\r\n              <el-select\r\n                v-model=\"form.contractorType\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"(dict, index) in dict.type.sys_contractor_type\"\r\n                  :key=\"`form_contractor_type_${dict.value}_${index}`\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公司简介\" prop=\"companyProfile\">\r\n              <el-input\r\n                v-model=\"form.companyProfile\"\r\n                type=\"textarea\"\r\n                :rows=\"4\"\r\n                placeholder=\"请输入内容\"\r\n                style=\"width: 100%\"\r\n              >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第五行：负责人联系电话 | 单位性质 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人联系电话\" prop=\"chargeContactNumber\">\r\n              <el-input\r\n                v-model=\"form.chargeContactNumber\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"单位性质\" prop=\"unitNature\">\r\n              <el-input v-model=\"form.unitNature\" placeholder=\"请输入内容\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第六行：法定代表人 | 公司地址 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"法定代表人\" prop=\"legalRepresentative\">\r\n              <el-input\r\n                v-model=\"form.legalRepresentative\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公司地址\" prop=\"companyAddress\">\r\n              <el-input\r\n                v-model=\"form.companyAddress\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第七行：法定代表人联系电话 | 公司邮箱 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item\r\n              label=\"法定代表人联系电话\"\r\n              prop=\"legalRepresentativePhone\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.legalRepresentativePhone\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公司邮箱\" prop=\"companyEmail\">\r\n              <el-input v-model=\"form.companyEmail\" placeholder=\"请输入邮箱\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getZjContractorInfo,\r\n  updateZjContractorInfo,\r\n} from \"@/api/contractor/zjContractorInfo\";\r\nimport { getUserInfo } from \"@/api/contractor/zjContractorBlaklist\";\r\nimport QualificationTab from \"./components/QualificationTab.vue\";\r\nimport PerformanceTab from \"./components/PerformanceTab.vue\";\r\nimport WorkTicketTab from \"./components/WorkTicketTab.vue\";\r\nimport ConstructionTab from \"./components/ConstructionTab.vue\";\r\nimport EvaluationTab from \"./components/EvaluationTab.vue\";\r\n\r\nexport default {\r\n  name: \"ZjContractorInfoDetail\",\r\n  dicts: [\"contractor_category\", \"sys_contractor_type\"],\r\n  components: {\r\n    QualificationTab,\r\n    PerformanceTab,\r\n    WorkTicketTab,\r\n    ConstructionTab,\r\n    EvaluationTab,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 承包商信息\r\n      contractorInfo: {},\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contractorName: [\r\n          { required: true, message: \"承包商名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        administratorId: [\r\n          { required: true, message: \"管理人不能为空\", trigger: \"change\" },\r\n        ],\r\n        creditCode: [\r\n          {\r\n            required: true,\r\n            message: \"统一社会信用代码不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        serviceTimeRange: [\r\n          {\r\n            required: true,\r\n            message: \"服务起止时间不能为空\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        contractorManager: [\r\n          { required: true, message: \"承包商负责人不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      // 管理人选项\r\n      managerOptions: [],\r\n      // 当前活跃的tab\r\n      activeTabName: \"qualification\",\r\n    };\r\n  },\r\n  created() {\r\n    const id = this.$route.params.id;\r\n    if (id) {\r\n      this.getContractorDetail(id);\r\n      this.loadManagerOptions();\r\n    } else {\r\n      this.$modal.msgError(\"缺少承包商ID参数\");\r\n      this.goBack();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 获取承包商详情 */\r\n    getContractorDetail(id) {\r\n      this.loading = true;\r\n      getZjContractorInfo(id)\r\n        .then((response) => {\r\n          this.contractorInfo = response.data;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n          this.$modal.msgError(\"获取承包商详情失败\");\r\n        });\r\n    },\r\n\r\n    /** 返回列表页 */\r\n    goBack() {\r\n      // 优先使用浏览器历史返回，避免写死路径\r\n      if (window.history.length > 1) {\r\n        this.$router.back();\r\n        return;\r\n      }\r\n\r\n      // 兜底1：如果路由meta提供了activeMenu，则跳转该菜单\r\n      const activeMenu = this.$route && this.$route.meta && this.$route.meta.activeMenu;\r\n      if (activeMenu) {\r\n        this.$router.replace(activeMenu);\r\n        return;\r\n      }\r\n\r\n      // 兜底2：根据当前路径动态计算上一级路径\r\n      const pathParts = (this.$route && this.$route.path ? this.$route.path : \"/\").split(\"/\").filter(Boolean);\r\n      if (pathParts.length > 1) {\r\n        const last = pathParts[pathParts.length - 1];\r\n        const prev = pathParts[pathParts.length - 2] || \"\";\r\n        // 若路径以 /.../detail/:id 或 /.../edit/:id 形式存在，则回退两级；\r\n        // 否则默认去掉最后一级\r\n        const isIdLike = /^\\d+$/.test(last) || /^[0-9a-fA-F-]{8,}$/.test(last);\r\n        const isActionSegment = [\"detail\", \"edit\", \"view\"].includes(prev);\r\n        const parentParts = isIdLike && isActionSegment ? pathParts.slice(0, -2) : pathParts.slice(0, -1);\r\n        const parentPath = \"/\" + parentParts.join(\"/\");\r\n        this.$router.replace(parentPath || \"/\");\r\n        return;\r\n      }\r\n\r\n      // 最终兜底：回首页\r\n      this.$router.replace(\"/\");\r\n    },\r\n\r\n    /** 编辑按钮操作 */\r\n    handleEdit() {\r\n      this.reset();\r\n      this.form = { ...this.contractorInfo };\r\n      // 设置服务时间范围\r\n      if (this.form.serviceStartTime && this.form.serviceEndTime) {\r\n        this.form.serviceTimeRange = [\r\n          this.form.serviceStartTime,\r\n          this.form.serviceEndTime,\r\n        ];\r\n      } else {\r\n        this.form.serviceTimeRange = null;\r\n      }\r\n      this.open = true;\r\n      this.title = \"修改承包商信息\";\r\n    },\r\n\r\n    /** 加载管理人选项 */\r\n    loadManagerOptions() {\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          const data = response.data || response.rows || response || [];\r\n          const uniqueManagers = new Map();\r\n          data.forEach((manager, index) => {\r\n            const value = manager.userId || manager.id || `temp_${index}`;\r\n            const label =\r\n              manager.nickName ||\r\n              manager.name ||\r\n              manager.userName ||\r\n              `未命名_${index}`;\r\n            const nickName =\r\n              manager.nickName ||\r\n              manager.name ||\r\n              manager.userName ||\r\n              `未命名_${index}`;\r\n            if (!uniqueManagers.has(value)) {\r\n              uniqueManagers.set(value, {\r\n                value,\r\n                label,\r\n                nickName,\r\n              });\r\n            }\r\n          });\r\n          this.managerOptions = Array.from(uniqueManagers.values());\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取管理人选项失败:\", error);\r\n          this.$modal.msgError(\"获取管理人选项失败\");\r\n        });\r\n    },\r\n\r\n    /** 处理服务时间范围变化 */\r\n    handleServiceTimeChange(value) {\r\n      if (value && value.length === 2) {\r\n        this.form.serviceStartTime = value[0];\r\n        this.form.serviceEndTime = value[1];\r\n      } else {\r\n        this.form.serviceStartTime = null;\r\n        this.form.serviceEndTime = null;\r\n      }\r\n    },\r\n\r\n    /** 处理管理人变化 */\r\n    handleManagerChange(value) {\r\n      if (value) {\r\n        const selectedManager = this.managerOptions.find(\r\n          (manager) => manager.value == value\r\n        );\r\n        if (selectedManager) {\r\n          this.form.administratorName = selectedManager.nickName;\r\n        }\r\n      } else {\r\n        this.form.administratorName = null;\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        contractorName: null,\r\n        administratorId: null,\r\n        administratorName: null,\r\n        creditCode: null,\r\n        serviceStartTime: null,\r\n        serviceEndTime: null,\r\n        serviceTimeRange: null,\r\n        contractorManager: null,\r\n        contractorCategory: null,\r\n        contractorType: null,\r\n        companyProfile: null,\r\n        chargeContactNumber: null,\r\n        unitNature: null,\r\n        legalRepresentative: null,\r\n        companyAddress: null,\r\n        legalRepresentativePhone: null,\r\n        companyEmail: null,\r\n        blacklistStatus: null,\r\n        accessStatus: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        blacklistReason: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 获取服务时间范围 */\r\n    getServiceTimeRange() {\r\n      if (\r\n        this.contractorInfo.serviceStartTime &&\r\n        this.contractorInfo.serviceEndTime\r\n      ) {\r\n        return [\r\n          this.contractorInfo.serviceStartTime,\r\n          this.contractorInfo.serviceEndTime,\r\n        ];\r\n      }\r\n      return null;\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          updateZjContractorInfo(this.form).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            // 重新获取详情数据\r\n            this.getContractorDetail(this.form.id);\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 处理tab点击 */\r\n    handleTabClick(tab) {\r\n      this.activeTabName = tab.name;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  background-color: rgb(247, 248, 250);\r\n  min-height: calc(100vh - 60px);\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.page-header {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n  border: 1px solid #e8e8e8;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-btn {\r\n  color: #1890ff;\r\n  padding: 0;\r\n  margin-right: 15px;\r\n  font-size: 14px;\r\n}\r\n\r\n.back-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n.page-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.detail-content {\r\n  min-height: 500px;\r\n  overflow: visible;\r\n}\r\n\r\n.detail-form {\r\n  padding: 0;\r\n}\r\n\r\n.detail-form .el-form-item {\r\n  margin-bottom: 22px;\r\n}\r\n\r\n.detail-form .el-input.is-disabled .el-input__inner,\r\n.detail-form .el-textarea.is-disabled .el-textarea__inner,\r\n.detail-form .el-select.is-disabled .el-input__inner {\r\n  background-color: #f8f9fa !important;\r\n  border-color: #e8e8e8 !important;\r\n  color: #333 !important;\r\n  cursor: default;\r\n}\r\n\r\n.detail-form .el-date-editor.is-disabled {\r\n  background-color: #f8f9fa !important;\r\n}\r\n\r\n.detail-form .el-date-editor.is-disabled .el-input__inner {\r\n  background-color: #f8f9fa !important;\r\n  border-color: #e8e8e8 !important;\r\n  color: #333 !important;\r\n}\r\n\r\n.info-section {\r\n  background-color: #fff;\r\n  padding: 24px;\r\n  border-radius: 5px;\r\n  border: 1px solid #e8e8e8;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #1890ff;\r\n  position: relative;\r\n}\r\n\r\n.section-title::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: -2px;\r\n  width: 50px;\r\n  height: 2px;\r\n  background-color: #1890ff;\r\n}\r\n\r\n.tab-section {\r\n  background-color: #fff;\r\n  border-radius: 5px;\r\n  border: 1px solid #e8e8e8;\r\n  overflow: visible;\r\n  padding: 20px 0 20px 20px;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 20px;\r\n  align-items: start;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-item.full-width {\r\n  grid-column: 1 / -1;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #666;\r\n  min-width: 120px;\r\n  margin-right: 12px;\r\n  line-height: 22px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-item span {\r\n  color: #333;\r\n  line-height: 22px;\r\n  word-break: break-all;\r\n}\r\n\r\n.textarea-content {\r\n  background-color: #f8f9fa;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border: 1px solid #e8e8e8;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  min-height: 60px;\r\n  width: 100%;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n.el-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 页面容器滚动由 AppMain 组件控制 */\r\n\r\n/* Tab内容样式 */\r\n.el-tabs__content {\r\n  overflow: visible;\r\n}\r\n\r\n.el-tab-pane {\r\n  overflow: visible;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n</style>\r\n"]}]}