<template>
  <div class="construction-tab">
    <el-table v-loading="loading" :data="constructionList" border style="width: 100%">
      <el-table-column prop="projectName" label="项目名称" align="left" width="200" />
      <el-table-column prop="projectCode" label="项目编号" align="left" width="140" />
      <el-table-column prop="constructionPhase" label="施工阶段" align="center" width="120" />
      <el-table-column prop="startDate" label="开工日期" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="endDate" label="计划完工" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="progress" label="进度" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.progress }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="projectManager" label="项目经理" align="center" width="120" />
      <el-table-column prop="qualityStatus" label="质量状态" align="center" width="120">
        <template slot-scope="scope">
          <el-tag :type="getQualityType(scope.row.qualityStatus)">
            {{ getQualityText(scope.row.qualityStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="safetyStatus" label="安全状态" align="center" width="120">
        <template slot-scope="scope">
          <el-tag :type="getSafetyType(scope.row.safetyStatus)">
            {{ getSafetyText(scope.row.safetyStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="left" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listZjProjectInfo } from "@/api/inspection/zjProjectInfo";

export default {
  name: "ConstructionTab",
  props: {
    contractorId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      constructionList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorId: null
      }
    };
  },
  watch: {
    contractorId: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.contractorId = newVal;
          this.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询施工信息列表 */
    getList() {
      if (!this.contractorId) return;
      this.loading = true;
      listZjProjectInfo(this.queryParams).then(response => {
        this.constructionList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.constructionList = [];
        this.total = 0;
      });
    },

    /** 获取质量状态类型 */
    getQualityType(status) {
      const statusMap = {
        1: 'success',   // 良好
        2: 'warning',   // 一般
        3: 'danger'     // 不合格
      };
      return statusMap[status] || 'info';
    },

    /** 获取质量状态文本 */
    getQualityText(status) {
      const statusMap = {
        1: '良好',
        2: '一般',
        3: '不合格'
      };
      return statusMap[status] || '未知';
    },

    /** 获取安全状态类型 */
    getSafetyType(status) {
      const statusMap = {
        1: 'success',   // 安全
        2: 'warning',   // 注意
        3: 'danger'     // 危险
      };
      return statusMap[status] || 'info';
    },

    /** 获取安全状态文本 */
    getSafetyText(status) {
      const statusMap = {
        1: '安全',
        2: '注意',
        3: '危险'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style scoped>
.construction-tab {
  padding: 20px;
}
</style>
