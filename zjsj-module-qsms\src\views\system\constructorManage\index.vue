<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item
        label="人员证书所在单位"
        prop="employeeCertificateBelongUnit"
      >
        <el-input
          v-model="queryParams.employeeCertificateBelongUnit"
          placeholder="请输入人员证书所在单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="证书名称" prop="certificateName">
        <el-input
          v-model="queryParams.certificateName"
          placeholder="请输入证书名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="职称" prop="professionalTitle">
        <el-input
          v-model="queryParams.professionalTitle"
          placeholder="请输入职称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:info:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:info:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:info:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="姓名" align="center" prop="name" width="140" />
      <el-table-column label="性别" align="center" prop="gender" width="120" />
      <!-- <el-table-column label="身份证号" align="center" prop="idCardNumber" /> -->
      <el-table-column
        label="联系电话"
        align="center"
        prop="contactPhone"
        width="140"
      />
      <el-table-column
        label="职称"
        align="center"
        prop="professionalTitle"
        width="120"
      />
      <el-table-column label="证书名称" align="center" prop="certificateName" />
      <el-table-column
        label="证书所属机构"
        align="center"
        prop="certificateBelongOrg"
      />
      <!-- <el-table-column label="编制机构" align="center" prop="compilationOrg" /> -->
      <!-- <el-table-column
        label="证书所属二级单位"
        align="center"
        prop="certificateBelongSecondaryUnit"
      />
      <el-table-column
        label="人员证书所在单位"
        align="center"
        prop="employeeCertificateBelongUnit"
      /> -->
      <!-- <el-table-column
        label="建造师专业"
        align="center"
        prop="constructorMajor"
      />
      <el-table-column
        label="证书编号"
        align="center"
        prop="certificateNumber"
      />
      <el-table-column
        label="注册编号"
        align="center"
        prop="registrationNumber"
      />
      -->
      <!-- <el-table-column
        label="是否有继续教育原件"
        align="center"
        prop="hasContinueEducationOriginal"
      />
      <el-table-column
        label="资格证书编号"
        align="center"
        prop="qualificationCertificateNumber"
      /> -->
      <!-- <el-table-column
        label="B类证号"
        align="center"
        prop="classBCertificateNumber"
      /> -->
      <!-- <el-table-column label="是否在职" align="center" prop="isOnJob" /> -->
      <!-- <el-table-column
        label="当前在用状态"
        align="center"
        prop="currentUseStatus"
      />
      <el-table-column
        label="当前在用工程"
        align="center"
        prop="currentUseProject"
      />
      <el-table-column
        label="当前在用地区"
        align="center"
        prop="currentUseArea"
      /> -->
      <!-- <el-table-column label="编制人" align="center" prop="compiler" /> -->
      <!-- <el-table-column label="编制日期" align="center" prop="compilationDate" /> -->
      <!-- <el-table-column label="备注" align="center" prop="remarks" /> -->
      <!-- <el-table-column
        label="职称证书编号"
        align="center"
        prop="professionalTitleCertificateNumber"
        width="200"
        show-overflow-tooltip=""
      /> -->
      <!-- <el-table-column label="人员状态" align="center" prop="employeeStatus" /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:info:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:info:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改建造师信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :max-height="dialogMaxHeight"
    >
      <div class="form-scroll-container">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="160px"
          class="scrollable-form"
          :class="isCheck ? 'view-mode' : ''"
        >
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入姓名"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="性别" prop="gender">
            <el-select
              v-model="form.gender"
              placeholder="请选择性别"
              style="width: 100%"
              :disabled="isCheck"
            >
              <el-option
                v-for="item in genderOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="是否外部员工" prop="isExternalEmployee">
          <el-input
            v-model="form.isExternalEmployee"
            placeholder="请输入是否外部员工"
          />
        </el-form-item> -->
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input
              v-model="form.contactPhone"
              placeholder="请输入联系电话"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="身份证号" prop="idCardNumber">
            <el-input
              v-model="form.idCardNumber"
              placeholder="请输入身份证号"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="职称" prop="professionalTitle">
            <el-input
              v-model="form.professionalTitle"
              placeholder="请输入职称"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item
            label="职称证书编号"
            prop="professionalTitleCertificateNumber"
          >
            <el-input
              v-model="form.professionalTitleCertificateNumber"
              placeholder="请输入职称证书编号"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="证书所属机构" prop="certificateBelongOrg">
            <el-input
              v-model="form.certificateBelongOrg"
              placeholder="请输入证书所属机构"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item
            label="证书所属二级单位"
            prop="certificateBelongSecondaryUnit"
          >
            <el-input
              v-model="form.certificateBelongSecondaryUnit"
              placeholder="请输入证书所属二级单位"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item
            label="人员证书所在单位"
            prop="employeeCertificateBelongUnit"
          >
            <el-input
              v-model="form.employeeCertificateBelongUnit"
              placeholder="请输入人员证书所在单位"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="建造师专业" prop="constructorMajor">
            <el-input
              v-model="form.constructorMajor"
              placeholder="请输入建造师专业"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="证书名称" prop="certificateName">
            <el-input
              v-model="form.certificateName"
              placeholder="请输入证书名称"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="证书编号" prop="certificateNumber">
            <el-input
              v-model="form.certificateNumber"
              placeholder="请输入证书编号"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="注册编号" prop="registrationNumber">
            <el-input
              v-model="form.registrationNumber"
              placeholder="请输入注册编号"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="编制机构" prop="compilationOrg">
            <el-input
              v-model="form.compilationOrg"
              placeholder="请输入编制机构"
              :disabled="isCheck"
            />
          </el-form-item>
          <!-- <el-form-item
          label="是否有继续教育原件"
          prop="hasContinueEducationOriginal"
        >
          <el-input
            v-model="form.hasContinueEducationOriginal"
            placeholder="请输入是否有继续教育原件"
          />
        </el-form-item> -->
          <el-form-item
            label="资格证书编号"
            prop="qualificationCertificateNumber"
          >
            <el-tooltip
              effect="dark"
              :content="form.qualificationCertificateNumber"
              placement="top"
              :disabled="
                form.qualificationCertificateNumber
                  ? form.qualificationCertificateNumber.length <= 15
                  : true
              "
            >
              <el-input
                v-model="form.qualificationCertificateNumber"
                placeholder="请输入资格证书编号"
                :disabled="isCheck"
              />
            </el-tooltip>
          </el-form-item>
          <el-form-item label="B类证号" prop="classBCertificateNumber">
            <el-tooltip
              effect="dark"
              :content="form.classBCertificateNumber"
              placement="top"
              :disabled="
                form.classBCertificateNumbe
                  ? form.classBCertificateNumber.length <= 15
                  : true
              "
            >
              <el-input
                v-model="form.classBCertificateNumber"
                placeholder="请输入B类证号"
                :disabled="isCheck"
              />
            </el-tooltip>
          </el-form-item>
          <el-form-item label="是否在职" prop="isOnJob">
            <el-select
              v-model="form.isOnJob"
              placeholder="请选择是否在职"
              style="width: 100%"
              :disabled="isCheck"
            >
              <el-option
                v-for="item in employmentStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="当前在用工程" prop="currentUseProject">
            <el-input
              v-model="form.currentUseProject"
              placeholder="请输入当前在用工程"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="当前在用地区" prop="currentUseArea">
            <el-input
              v-model="form.currentUseArea"
              placeholder="请输入当前在用地区"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="编制人" prop="compiler">
            <el-input
              v-model="form.compiler"
              placeholder="请输入编制人"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="编制日期" prop="compilationDate">
            <el-input
              v-model="form.compilationDate"
              placeholder="请输入编制日期"
              :disabled="isCheck"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="form.remarks"
              type="textarea"
              placeholder="请输入内容"
              :disabled="isCheck"
            />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer" v-if="!isCheck">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
} from "@/api/system/constructorManage/index";

export default {
  name: "Info",
  data() {
    return {
      isCheck: false,
      dialogMaxHeight: "80vh",
      genderOptions: [
        {
          label: "男",
          value: "男",
        },
        {
          label: "女",
          value: "女",
        },
      ],
      employmentStatusOptions: [
        {
          label: "是",
          value: "是",
        },
        {
          label: "否",
          value: "否",
        },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 建造师信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
        isExternalEmployee: "",
        idCardNumber: "",
        contactPhone: "",
        certificateBelongOrg: "",
        certificateBelongSecondaryUnit: "",
        employeeCertificateBelongUnit: "",
        certificateName: "",
        constructorMajor: "",
        certificateNumber: "",
        registrationNumber: "",
        compilationOrg: "",
        hasContinueEducationOriginal: "",
        qualificationCertificateNumber: "",
        classBCertificateNumber: "",
        isOnJob: "",
        currentUseStatus: "",
        currentUseProject: "",
        currentUseArea: "",
        gender: "",
        compiler: "",
        compilationDate: "",
        remarks: "",
        professionalTitle: "",
        professionalTitleCertificateNumber: "",
        employeeStatus: "",
      },
      // 表单参数
      form: {
        name: "",
        isExternalEmployee: "",
        idCardNumber: "",
        contactPhone: "",
        certificateBelongOrg: "",
        certificateBelongSecondaryUnit: "",
        employeeCertificateBelongUnit: "",
        certificateName: "",
        constructorMajor: "",
        certificateNumber: "",
        registrationNumber: "",
        compilationOrg: "",
        hasContinueEducationOriginal: "",
        qualificationCertificateNumber: "",
        classBCertificateNumber: "",
        isOnJob: "",
        currentUseStatus: "",
        currentUseProject: "",
        currentUseArea: "",
        gender: "",
        compiler: "",
        compilationDate: "",
        remarks: "",
        professionalTitle: "",
        professionalTitleCertificateNumber: "",
        employeeStatus: "",
        createBy: "",
        createTime: "",
        updateBy: "",
        updateTime: "",
      },
      // 表单校验
      rules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        gender: [
          { required: true, message: "性别不能为空", trigger: "change" },
        ],
        isOnJob: [
          { required: true, message: "是否在职不能为空", trigger: "change" },
        ],
        contactPhone: [
          {
            required: true,
            trigger: "blur",
            validator: this.validatePhone,
          },
        ],
        certificateBelongOrg: [
          { required: true, message: "证书所属机构不能为空", trigger: "blur" },
        ],
        certificateBelongSecondaryUnit: [
          {
            required: true,
            message: "证书所属二级单位不能为空",
            trigger: "blur",
          },
        ],
        employeeCertificateBelongUnit: [
          {
            required: true,
            message: "人员证书所在单位不能为空",
            trigger: "blur",
          },
        ],
        // compilationOrg: [
        //   { required: true, message: "编制机构不能为空", trigger: "blur" },
        // ],
        // employeeStatus: [
        //   { required: true, message: "人员状态不能为空", trigger: "change" },
        // ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 手机号验证函数（放在methods中）
    validatePhone(rule, value, callback) {
      const reg = /^1[3-9]\d{9}$/;
      if (!value) {
        return callback(new Error("请输入联系电话"));
      } else if (!reg.test(value)) {
        return callback(new Error("请输入正确的11位手机号"));
      } else {
        callback();
      }
    },
    // 身份证号验证函数（放在methods中）
    validateIdCard(rule, value, callback) {
      const reg = /(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!value) {
        return callback(new Error("请输入身份证号"));
      } else if (!reg.test(value)) {
        return callback(new Error("请输入正确的18位身份证号"));
      } else {
        callback();
      }
    },
    /** 查询建造师信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: "",
        isExternalEmployee: "",
        idCardNumber: "",
        contactPhone: "",
        certificateBelongOrg: "",
        certificateBelongSecondaryUnit: "",
        employeeCertificateBelongUnit: "",
        certificateName: "",
        constructorMajor: "",
        certificateNumber: "",
        registrationNumber: "",
        compilationOrg: "",
        hasContinueEducationOriginal: "",
        qualificationCertificateNumber: "",
        classBCertificateNumber: "",
        isOnJob: "",
        currentUseStatus: "",
        currentUseProject: "",
        currentUseArea: "",
        gender: "",
        compiler: "",
        compilationDate: "",
        remarks: "",
        professionalTitle: "",
        professionalTitleCertificateNumber: "",
        employeeStatus: "",
        createBy: "",
        createTime: "",
        updateBy: "",
        updateTime: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isCheck = false;
      this.open = true;
      this.title = "添加建造师信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改建造师信息";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "查看建造师信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 是否外部员工
          this.form.isExternalEmployee = "否";
          if (this.form.id != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除建造师信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/constructorInfo/export",
        {
          ...this.queryParams,
        },
        `建造师管理.xlsx`
      );
    },
  },
};
</script>
<style scoped>
/* 表单滚动容器容器样式 */
.form-scroll-container {
  /* 减去标题和底部按钮区域的高度 */
  height: calc(80vh - 120px);
  overflow: hidden;
}

/* 滚动表单样式 */
.scrollable-form {
  max-height: 100%;
  overflow-y: auto !important;
  padding-right: 10px; /* 避免滚动条遮挡内容 */
}

/* 美化滚动条（可选） */
.scrollable-form::-webkit-scrollbar {
  width: 6px;
}
.scrollable-form::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}
.scrollable-form::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}
</style>