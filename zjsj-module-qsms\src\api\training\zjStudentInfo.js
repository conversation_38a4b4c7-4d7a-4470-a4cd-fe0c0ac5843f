import request from '@/utils/request'

// 查询学员信息列表
export function listZjStudentInfo(query) {
  return request({
    url: '/training/zjStudentInfo/list',
    method: 'get',
    params: query
  })
}

// 查询学员信息详细
export function getZjStudentInfo(id) {
  return request({
    url: '/training/zjStudentInfo/' + id,
    method: 'get'
  })
}

// 新增学员信息
export function addZjStudentInfo(data) {
  return request({
    url: '/training/zjStudentInfo',
    method: 'post',
    data: data
  })
}

// 修改学员信息
export function updateZjStudentInfo(data) {
  return request({
    url: '/training/zjStudentInfo',
    method: 'put',
    data: data
  })
}

// 删除学员信息
export function delZjStudentInfo(id) {
  return request({
    url: '/training/zjStudentInfo/' + id,
    method: 'delete'
  })
}
