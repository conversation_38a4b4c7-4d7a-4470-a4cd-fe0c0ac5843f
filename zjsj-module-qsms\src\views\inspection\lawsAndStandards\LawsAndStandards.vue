<template>
  <div class="laws-and-standards">
    <zjLegalRegulationsStandards></zjLegalRegulationsStandards>
    <!-- <el-tabs v-model="activeTab" @tab-click="handleTabClick"> -->
    <!-- <el-tab-pane label="法律法规与标准" name="laws">
        <zjLegalRegulationsStandards></zjLegalRegulationsStandards>
      </el-tab-pane> -->
    <!-- <el-tab-pane label="符合性评价报告" name="standards">
        <zjComplianceEvaluationReport></zjComplianceEvaluationReport>
      </el-tab-pane> -->
    <!-- </el-tabs> -->
  </div>
</template>

<script>
import zjComplianceEvaluationReport from "./components/zjComplianceEvaluationReport/index.vue";
import zjLegalRegulationsStandards from "./components/zjLegalRegulationsStandards/index.vue";
export default {
  name: "LawsAndStandards",
  components: {
    zjComplianceEvaluationReport,
    zjLegalRegulationsStandards,
  },
  data() {
    return {
      activeTab: "laws", // 默认选中法律法规 tab
    };
  },
  methods: {
    handleTabClick(tab) {
      this.activeTab = tab.name;
    },
  },
};
</script>

<style scoped>

</style>
