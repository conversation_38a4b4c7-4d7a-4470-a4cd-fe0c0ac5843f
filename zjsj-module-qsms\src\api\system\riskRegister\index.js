import request from '@/utils/request'

// 查询项目施工风险清单及管理措施列表
export function listChecklist(query) {
  return request({
    url: '/system/checklist/list',
    method: 'get',
    params: query
  })
}

// 查询项目施工风险清单及管理措施详细
export function getChecklist(id) {
  return request({
    url: '/system/checklist/' + id,
    method: 'get'
  })
}

// 新增项目施工风险清单及管理措施
export function addChecklist(data) {
  return request({
    url: '/system/checklist',
    method: 'post',
    data: data
  })
}

// 修改项目施工风险清单及管理措施
export function updateChecklist(data) {
  return request({
    url: '/system/checklist',
    method: 'put',
    data: data
  })
}

// 删除项目施工风险清单及管理措施
export function delChecklist(id) {
  return request({
    url: '/system/checklist/' + id,
    method: 'delete'
  })
}
