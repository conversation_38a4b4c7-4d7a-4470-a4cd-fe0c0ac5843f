<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="80px"
    >
      <el-form-item label="活动名称" prop="homeworkActivityName">
        <el-input
          v-model="queryParams.homeworkActivityName"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="作业负责人" prop="chargePersonName">
        <el-input
          v-model="queryParams.chargePersonName"
          placeholder="请输入作业负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作业场所所属部门" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入作业场所所属部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="所属区域" prop="workArea">
        <el-input
          v-model="queryParams.workArea"
          placeholder="请输入所属区域"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="关联设备" prop="linkedDevice">
        <el-input
          v-model="queryParams.linkedDevice"
          placeholder="请输入关联设备"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划启动时间" prop="planBeginTime">
        <el-date-picker
          clearable
          v-model="queryParams.planBeginTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划启动时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划结束时间" prop="planEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.planEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="作业负责人id" prop="chargePersonId">
        <el-input
          v-model="queryParams.chargePersonId"
          placeholder="请输入作业负责人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邀请关注人姓名" prop="inviteFollowName">
        <el-input
          v-model="queryParams.inviteFollowName"
          placeholder="请输入邀请关注人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="邀请关注人id" prop="inviteFollowId">
        <el-input
          v-model="queryParams.inviteFollowId"
          placeholder="请输入邀请关注人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作业开始前审批人姓名" prop="homeworkStartsApprover">
        <el-input
          v-model="queryParams.homeworkStartsApprover"
          placeholder="请输入作业开始前审批人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="作业开始前审批人id" prop="homeworkStartsApproverId">
        <el-input
          v-model="queryParams.homeworkStartsApproverId"
          placeholder="请输入作业开始前审批人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="作业结束前审批人姓名"
        prop="homeworkCompletionApprover"
      >
        <el-input
          v-model="queryParams.homeworkCompletionApprover"
          placeholder="请输入作业结束前审批人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="作业结束前审批人id"
        prop="homeworkCompletionApproverId"
      >
        <el-input
          v-model="queryParams.homeworkCompletionApproverId"
          placeholder="请输入作业结束前审批人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['work:zjWorkActivities:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['work:zjWorkActivities:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['work:zjWorkActivities:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['work:zjWorkActivities:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjWorkActivitiesList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 240px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column
        label="活动名称"
        align="center"
        prop="homeworkActivityName"
      />
      <!-- ：1-临时用电安全作业 2-动火安全作业 3-设定空间安全作业 4-盲板抽堵安全作业 5-吊装安全作业 6-动土安全作业 7-断路安全作业 8-高处安全作业 99-其他 -->
      <el-table-column
        label="涉及工作类型"
        align="center"
        prop="workType"
        width="160"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ getWorkTypeLabel(scope.row.workType) }}
        </template>
      </el-table-column>

      <el-table-column
        label="作业内容"
        align="center"
        prop="homeworkContent"
        width="160"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div v-html="scope.row.homeworkContent"></div>
        </template>
      </el-table-column>
      <el-table-column
        label="已开作业票数"
        align="center"
        prop="homeworkNum"
        width="120"
      />

      <el-table-column
        label="所属区域"
        align="center"
        prop="workArea"
        width="120"
        show-overflow-tooltip
      />

      <el-table-column
        label="作业负责人"
        align="center"
        prop="chargePersonName"
        width="120"
      />
      <!-- <el-table-column
        label="作业场所所属部门"
        align="center"
        prop="deptName"
      /> -->
      <!-- <el-table-column label="关联设备" align="center" prop="linkedDevice" /> -->
      <!-- <el-table-column
        label="活动位置"
        align="center"
        prop="activityLocation"
      /> -->
      <el-table-column
        label="计划启动时间"
        align="center"
        prop="planBeginTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planBeginTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划结束时间"
        align="center"
        prop="planEndTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planEndTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="作业负责人id"
        align="center"
        prop="chargePersonId"
      /> -->
      <!-- <el-table-column
        label="邀请关注人姓名"
        align="center"
        prop="inviteFollowName"
        width="180"
      /> -->
      <!-- <el-table-column
        label="邀请关注人id"
        align="center"
        prop="inviteFollowId"
      /> -->
      <!-- <el-table-column
        label="作业开始前审批人姓名"
        align="center"
        prop="homeworkStartsApprover"
        width="180"
      /> -->
      <!-- <el-table-column
        label="作业开始前审批人id"
        align="center"
        prop="homeworkStartsApproverId"
      /> -->
      <!-- <el-table-column
        label="作业结束前审批人姓名"
        align="center"
        prop="homeworkCompletionApprover"
        width="180"
      /> -->
      <!-- <el-table-column
        label="作业结束前审批人id"
        align="center"
        prop="homeworkCompletionApproverId"
      /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['work:zjWorkActivities:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['work:zjWorkActivities:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工作活动对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-form-item label="作业活动名称" prop="homeworkActivityName">
          <el-input
            v-model="form.homeworkActivityName"
            placeholder="请输入作业活动名称"
          />
        </el-form-item>
        <el-form-item label="涉及工作类型" prop="workType">
          <el-select
            v-model="form.workType"
            placeholder="请选择涉及工作类型"
            style="width: 100%"
          >
            <el-option label="临时用电安全作业" value="lsyd" />
            <el-option label="动火安全作业" value="dh" />
            <el-option label="受限空间安全作业" value="sx" />
            <el-option label="盲板抽堵安全作业" value="mb" />
            <el-option label="吊装安全作业" value="dz" />
            <el-option label="动土安全作业" value="dt" />
            <el-option label="断路安全作业" value="dl" />
            <el-option label="高处安全作业" value="gc" />
            <el-option label="其他" value="qt" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="作业内容">
          <editor v-model="form.homeworkContent" :min-height="192" />
        </el-form-item> -->
        <el-form-item label="作业负责人" prop="chargePersonName">
          <selectPeopleTree
            v-model="form.chargePersonName"
            :peopleList="peopleList"
            placeholder="请搜索或选择作业负责人"
            @change="handleChange"
            ref="chargePersonName"
          ></selectPeopleTree>
        </el-form-item>
        <el-form-item label="作业场所所属部门" prop="deptName">
          <el-input
            v-model="form.deptName"
            placeholder="请输入作业场所所属部门"
          />
        </el-form-item>

        <!-- 作业内容 -->
        <el-form-item label="作业内容" prop="homeworkContent">
          <editor v-model="form.homeworkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="所属区域" prop="workArea">
          <!-- <el-input v-model="form.workArea" placeholder="请输入所属区域" /> -->
          <selectPeopleTree
            v-model="form.workArea"
            :peopleList="workAreaList"
            placeholder="请搜索或选择所属区域"
            @change="handleWorkAreaChange"
          ></selectPeopleTree>
        </el-form-item>
        <el-form-item label="关联设备" prop="linkedDevice">
          <el-input v-model="form.linkedDevice" placeholder="请输入关联设备" />
        </el-form-item>
        <el-form-item label="活动位置" prop="activityLocation">
          <el-input
            v-model="form.activityLocation"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="计划启动时间" prop="planBeginTime">
          <el-date-picker
            clearable
            v-model="form.planBeginTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划启动时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划结束时间" prop="planEndTime">
          <el-date-picker
            clearable
            v-model="form.planEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划结束时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="邀请关注人姓名" prop="inviteFollowName">
          <selectPeopleTree
            v-model="form.inviteFollowName"
            :peopleList="peopleList"
            placeholder="请搜索或选择邀请关注人姓名"
            @change="handleInviteFollowNameChange"
            ref="inviteFollowName"
          ></selectPeopleTree>
        </el-form-item>

        <el-form-item
          label="作业开始前审批人姓名"
          prop="homeworkStartsApprover"
        >
          <selectPeopleTree
            v-model="form.homeworkStartsApprover"
            :peopleList="peopleList"
            placeholder="请搜索或选择作业开始前审批人姓名"
            @change="handleHomeworkStartsApproverChange"
            ref="homeworkStartsApprover"
          ></selectPeopleTree>
        </el-form-item>

        <el-form-item
          label="作业结束前审批人姓名"
          prop="homeworkCompletionApprover"
        >
          <selectPeopleTree
            v-model="form.homeworkCompletionApprover"
            :peopleList="peopleList"
            placeholder="请搜索或选择作业结束前审批人姓名"
            @change="handleHomeworkCompletionApproverChange"
            ref="homeworkCompletionApprover"
          ></selectPeopleTree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjWorkActivities,
  getZjWorkActivities,
  delZjWorkActivities,
  addZjWorkActivities,
  updateZjWorkActivities,
} from "@/api/work/zjWorkActivities";
import { getWorkAreaList } from "@/api/work/zjWorkTicket";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { listPeople } from "@/api/system/info";

export default {
  name: "ZjWorkActivities",
  components: {
    selectPeopleTree,
  },

  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工作活动表格数据
      zjWorkActivitiesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        homeworkActivityName: null,
        workType: null,
        homeworkContent: null,
        chargePersonName: null,
        deptName: null,
        workArea: null,
        linkedDevice: null,
        activityLocation: null,
        planBeginTime: null,
        planEndTime: null,
        chargePersonId: null,
        inviteFollowName: null,
        inviteFollowId: null,
        homeworkStartsApprover: null,
        homeworkStartsApproverId: null,
        homeworkCompletionApprover: null,
        homeworkCompletionApproverId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      peopleList: [],
      workAreaList: [],
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    this.getPeopleList();
    this.getWorkAreaList();
  },
  methods: {
    getWorkTypeLabel(type) {
      //   <el-option label="临时用电安全作业" value="lsyd" />
      // <el-option label="动火安全作业" value="dh" />
      //       <el-option label="受限空间安全作业" value="sx" />
      //       <el-option label="盲板抽堵安全作业" value="mb" />
      //       <el-option label="吊装安全作业" value="dz" />
      //       <el-option label="动土安全作业" value="dt" />
      //       <el-option label="断路安全作业" value="dl" />
      //       <el-option label="高处安全作业" value="gc" />
      //       <el-option label="其他" value="qt" />
      if (type == "lsyd") {
        return "临时用电安全作业";
      } else if (type == "dh") {
        return "动火安全作业";
      } else if (type == "sx") {
        return "受限空间安全作业";
      } else if (type == "mb") {
        return "盲板抽堵安全作业";
      } else if (type == "dz") {
        return "吊装安全作业";
      } else if (type == "dt") {
        return "动土安全作业";
      } else if (type == "dl") {
        return "断路安全作业";
      } else if (type == "gc") {
        return "高处安全作业";
      } else if (type == "qt") {
        return "其他";
      }
    },

    // 获取作业区域
    getWorkAreaList() {
      getWorkAreaList().then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.workAreaList = res.data;
          // console.log(this.workAreaList, "workAreaList");
        }
      });
    },
    // 人员列表
    getPeopleList() {
      listPeople().then((res) => {
        // console.log(res);
        if (res.code == 200) {
          this.peopleList = res.data;
          // console.log(this.peopleList, "peopleList");
        }
      });
    },
    // 所属区域
    handleWorkAreaChange(selectedItem) {
      if (selectedItem) {
        this.form.workAreaId = selectedItem.id;
        this.form.workArea = selectedItem.label;
      } else {
        this.form.workAreaId = null;
        this.form.workArea = null;
      }
    },
    // 选择人员
    // 作业负责人姓名
    handleChange(selectedItem) {
      if (selectedItem) {
        this.form.chargePersonId = selectedItem.id;
        this.form.chargePersonName = selectedItem.label;
      } else {
        this.form.chargePersonId = null;
        this.form.chargePersonName = null;
      }
    },
    // 邀请关注人姓名
    handleInviteFollowNameChange(selectedItem) {
      if (selectedItem) {
        this.form.inviteFollowId = selectedItem.id;
        this.form.inviteFollowName = selectedItem.label;
      } else {
        this.form.inviteFollowId = null;
        this.form.inviteFollowName = null;
      }
    },
    // 作业开始前审批人姓名
    handleHomeworkStartsApproverChange(selectedItem) {
      if (selectedItem) {
        this.form.homeworkStartsApproverId = selectedItem.id;
        this.form.homeworkStartsApprover = selectedItem.label;
      } else {
        this.form.homeworkStartsApproverId = null;
        this.form.homeworkStartsApprover = null;
      }
    },
    // 作业结束前审批人姓名
    handleHomeworkCompletionApproverChange(selectedItem) {
      if (selectedItem) {
        this.form.homeworkCompletionApproverId = selectedItem.id;
        this.form.homeworkCompletionApprover = selectedItem.label;
      } else {
        this.form.homeworkCompletionApproverId = null;
        this.form.homeworkCompletionApprover = null;
      }
    },

    /** 查询工作活动列表 */
    getList() {
      this.loading = true;
      listZjWorkActivities(this.queryParams).then((res) => {
        this.zjWorkActivitiesList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        homeworkActivityName: null,
        workType: null,
        homeworkContent: null,
        chargePersonName: null,
        deptName: null,
        workArea: null,
        linkedDevice: null,
        activityLocation: null,
        planBeginTime: null,
        planEndTime: null,
        chargePersonId: null,
        inviteFollowName: null,
        inviteFollowId: null,
        homeworkStartsApprover: null,
        homeworkStartsApproverId: null,
        homeworkCompletionApprover: null,
        homeworkCompletionApproverId: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工作活动";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjWorkActivities(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改工作活动";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjWorkActivities(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjWorkActivities(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除工作活动编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjWorkActivities(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "work/zjWorkActivities/export",
        {
          ...this.queryParams,
        },
        `zjWorkActivities_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
