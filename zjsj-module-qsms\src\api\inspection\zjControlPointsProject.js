import request from '@/utils/request'

// 查询同步危大工程项目层管控要点记录列表
export function listZjControlPointsProject(query) {
  return request({
    url: '/inspection/zjControlPointsProject/list',
    method: 'get',
    params: query
  })
}

// 查询同步危大工程项目层管控要点记录详细
export function getZjControlPointsProject(id) {
  return request({
    url: '/inspection/zjControlPointsProject/' + id,
    method: 'get'
  })
}

// 新增同步危大工程项目层管控要点记录
export function addZjControlPointsProject(data) {
  return request({
    url: '/inspection/zjControlPointsProject',
    method: 'post',
    data: data
  })
}

// 修改同步危大工程项目层管控要点记录
export function updateZjControlPointsProject(data) {
  return request({
    url: '/inspection/zjControlPointsProject',
    method: 'put',
    data: data
  })
}

// 删除同步危大工程项目层管控要点记录
export function delZjControlPointsProject(id) {
  return request({
    url: '/inspection/zjControlPointsProject/' + id,
    method: 'delete'
  })
}
