<template>
  <div class="app-container">
    <el-row>
      <el-col :span="4">
        <EnterPriseOrg
          :data="orgTreeData"
          @nodeClick="handleOrgTreeNodeClick"
        />
      </el-col>
      <el-col :span="19" style="margin-left: 2px">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          v-show="showSearch"
          label-width="80px"
        >
          <el-form-item label="企业名称" prop="enterpriseName">
            <el-input
              v-model="queryParams.enterpriseName"
              placeholder="请输入企业名称"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 250px"
            />
          </el-form-item>
          <!-- <el-form-item label="所属组织机构" prop="organName">
        <el-input
          v-model="queryParams.organName"
          placeholder="请输入所属组织机构"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <el-form-item label="企业法人" prop="businessCorporation">
            <el-input
              v-model="queryParams.businessCorporation"
              placeholder="请输入企业法人"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 250px"
            />
          </el-form-item>
          <!-- <el-form-item label="分管领导" prop="responsibleLeader">
        <el-input
          v-model="queryParams.responsibleLeader"
          placeholder="请输入分管领导"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="职业人数" prop="professionalsNumber">
        <el-input
          v-model="queryParams.professionalsNumber"
          placeholder="请输入职业人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全等级" prop="securityLevel">
        <el-input
          v-model="queryParams.securityLevel"
          placeholder="请输入安全等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:info:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:info:edit']"
              >修改</el-button
            >
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:info:remove']"
              >删除</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['system:info:export']"
              >导出</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="el-icon-sort"
              size="mini"
              @click="toggleExpandAll"
              >展开/折叠</el-button
            >
          </el-col>
          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-if="refreshTable"
          v-loading="loading"
          :data="infoList"
          @selection-change="handleSelectionChange"
          height="calc(100vh - 240px)"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="企业名称"
            align="center"
            prop="enterpriseName"
          />
          <!-- <el-table-column label="主键" align="center" prop="id" /> -->
          <!-- 
          <el-table-column label="所属组织" align="center" prop="parentId">
            <template slot-scope="scope">
              {{ getOrganName(scope.row.parentId) }}
            </template>
          </el-table-column> -->
          <el-table-column
            label="企业法人"
            align="center"
            prop="businessCorporation"
          />
          <el-table-column
            label="分管领导"
            align="center"
            prop="responsibleLeader"
          />
          <el-table-column
            label="职业人数"
            align="center"
            prop="professionalsNumber"
          />
          <el-table-column
            label="安全等级"
            align="center"
            prop="securityLevel"
          />
          <el-table-column
            label="操作"
            fixed="right"
            width="150"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:info:edit']"
                >修改</el-button
              >
              <!-- <el-button
                size="mini"
                type="text"
                icon="el-icon-plus"
                @click="handleAdd(scope.row)"
                v-hasPermi="['system:dept:add']"
                >新增</el-button
              > -->
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:info:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改企业信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="企业/部门名称" prop="enterpriseName">
          <el-input v-model="form.enterpriseName" placeholder="请输入名称" />
          <!-- <image-upload v-model="form.enterpriseName" /> -->
        </el-form-item>
        <!-- <el-form-item label="所属组织" prop="organName">
          <el-input v-model="form.organName" placeholder="请输入所属组织" />
        </el-form-item> -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属组织" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="infoList"
                :normalizer="normalizer"
                placeholder="选择所属组织"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="部门名称" prop="enterpriseName">
              <el-input v-model="form.enterpriseName" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="12">
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number
                v-model="form.orderNum"
                controls-position="right"
                :min="0"
  
              />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-form-item label="企业法人" prop="businessCorporation">
          <el-input
            v-model="form.businessCorporation"
            placeholder="请输入企业法人"
          />
        </el-form-item>
        <el-form-item label="法人联系方式" prop="businessCorporationContact">
          <el-input
            v-model="form.businessCorporationContact"
            placeholder="请输入企业法人联系方式"
          />
        </el-form-item>
        <el-form-item label="分管领导" prop="responsibleLeader">
          <el-input
            v-model="form.responsibleLeader"
            placeholder="请输入分管领导"
          />
        </el-form-item>
        <el-form-item label="分管领导联系方式" prop="responsibleLeaderContact">
          <el-input
            v-model="form.responsibleLeaderContact"
            placeholder="请输入分管领导联系方式"
          />
        </el-form-item>
        <el-form-item label="职业人数" prop="professionalsNumber">
          <el-input
            v-model="form.professionalsNumber"
            placeholder="请输入职业人数"
          />
        </el-form-item>
        <el-form-item label="安全等级" prop="securityLevel">
          <el-input v-model="form.securityLevel" placeholder="请输入安全等级" />
        </el-form-item>
        <el-form-item label="资质证书" prop="qualificationCertificate">
          <image-upload v-model="form.qualificationCertificate" />
        </el-form-item>
        <el-form-item label="企业荣誉" prop="enterpriseHonor">
          <image-upload v-model="form.enterpriseHonor" />
          <el-input
            v-model="form.enterpriseHonorDesc"
            type="textarea"
            placeholder="请输入企业荣誉描述"
            :rows="5"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
  getEnterpriseInfo,
} from "@/api/system/info";
import {
  listDept,
  getDept,
  delDept,
  addDept,
  updateDept,
  listDeptExcludeChild,
} from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import EnterPriseOrg from "./components/enterPriseOrg.vue";

export default {
  name: "Info",
  components: { Treeselect, EnterPriseOrg },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        enterpriseName: null,
        organName: null,
        businessCorporation: null,
        responsibleLeader: null,
        professionalsNumber: null,
        securityLevel: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        enterpriseName: [
          { required: true, message: "请输入企业/部门名称", trigger: "blur" },
        ],
        parentId: [
          {
            validator: (rule, value, callback) => {
              if (value === this.form.id) {
                this.$modal.msgError("所属组织不能选择自己");
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      // 是否展开，默认全部展开
      isExpandAll: false,
      // 重新渲染表格状态
      refreshTable: true,
      deptOptions: [],
      orgTreeData: [],
    };
  },
  created() {
    this.getList();
    this.getEnterpriseInfo();
    // this.getDeptList();
  },
  methods: {
    /** 查询部门列表 */
    // getDeptList() {
    //   // this.loading = true;
    //   listDept(this.queryParams).then((res) => {
    //     this.deptOptions = this.handleTree(res.data, "deptId");
    //     // console.log(this.deptOptions, "deptOptions");
    //     // this.loading = false;
    //   });
    // },
    // 企业信息 组织架构
    getEnterpriseInfo() {
      // this.loading = true;

      getEnterpriseInfo().then((res) => {
        // console.log(res, "res");
        // this.orgTreeData = res.rows;
        res.data.forEach((item) => {
          this.orgTreeData.push({
            label: item.label,
            id: item.id,
            children: item.children,
          });
        });
        // this.loading = false;
      });
    },

    handleOrgTreeNodeClick(nodeData) {
      // 在这里处理接收到的子组件数据
      // console.log("接收到子组件数据:", nodeData);
      // 可以根据nodeData更新查询参数或其他状态
      // this.queryParams.enterpriseName = nodeData.label; // 假设nodeData中有id字段
      this.queryParams.id = nodeData.id; // 假设nodeData中有id字段

      // this.currentDeptId = nodeData.id;
      this.handleQuery(); // 触发查询
    },
    getOrganName(parentId) {
      if (!parentId) return "-";
      const findParent = (id, list) => {
        for (const item of list) {
          if (item.id === id) {
            return item.enterpriseName;
          }
          if (item.children && item.children.length) {
            const found = findParent(id, item.children);
            if (found) return found;
          }
        }
        return null;
      };
      const parentName = findParent(parentId, this.infoList);
      return parentName || "-";
    },
    normalizer(node) {
      console.log(node, "node");
      if (!node) return null; // 添加空节点检查

      if (node.children && !node.children.length) {
        delete node.children;
      }
      // 确保id和label有值，否则返回null
      if (!node.id || !node.enterpriseName) return null;
      return {
        id: node.id || "",
        label: node.enterpriseName || "",
        children: node.children || "",
      };
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 查询企业信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then((res) => {
        // console.log(res.data, "res.rows");
        // if (!res || !res.data) {
        //   this.infoList = [];
        //   this.loading = false;
        //   return;
        // }
        // this.infoList = this.handleTree(res.data, "id");

        // console.log(this.infoList, "转换后");
        if (res.code == 200) {
          this.infoList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        enterpriseName: null,
        organName: null,
        businessCorporation: null,
        responsibleLeader: null,
        professionalsNumber: null,
        securityLevel: null,
        createTime: null,
        createBy: null,
        updateBy: null,
        updateTime: null,
        qualificationCertificate: null,
        enterpriseHonor: null,
        enterpriseHonorDesc: null,
        businessCorporationContact: null,
        responsibleLeaderContact: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      // console.log(row, "row");
      this.reset();
      if (row != undefined) {
        this.form.parentId = row.id;
        // console.log(this.form.parentId, "parentId");
      }
      this.open = true;
      this.title = "添加企业信息";
      // console.log(this.deptOptions, "deptOptions");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInfo(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改企业信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 额外检查parentId不能等于id
          if (this.form.parentId === this.form.id) {
            this.$modal.msgError("所属组织不能选择自己");
            return;
          }
          if (this.form.id != null) {
            updateInfo(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除企业信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/info/export",
        {
          ...this.queryParams,
        },
        `info_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
