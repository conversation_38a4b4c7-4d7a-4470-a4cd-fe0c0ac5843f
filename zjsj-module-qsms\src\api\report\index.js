import request from '@/utils/request'

// 安全费用表
export function querySafetyFeeView(params) {
  return request({
    url: '/report/view/querySafetyFeeView',
    method: 'get',
    params
  })
}
// 安全月报 
export function querySafetyMonthReport(params) {
  return request({
    url: '/report/view/querySafetyMonthReport',
    method: 'get',
    params
  })
}
// 安全考核 querySafetyEvaluation
export function querySafetyEvaluation(params) {
  return request({
    url: '/report/view/querySafetyEvaluation',
    method: 'get',
    params
  })
}
// 在建项目安管人员及施工机械设备使用情况表
export function querySafetyPersonnelAndEquipmentView(params) {
  return request({
    url: '/report/view/querySafetyPersonnelAndEquipmentView',
    method: 'get',
    params
  })
}
// 带班计划报表
export function queryLeadershipReportView(params) {
  return request({
    url: '/report/view/queryLeadershipReportView',
    method: 'get',
    params
  })
}
// 在建项目报表
export function queryConstructionProjectView(params) {
  return request({
    url: '/report/view/queryConstructionProjectView',
    method: 'get',
    params
  })
}
// 事故统计报表
export function queryAccidentsStatisticView(params) {
  return request({
    url: '/report/view/queryAccidentsStatisticView',
    method: 'get',
    params
  })
}