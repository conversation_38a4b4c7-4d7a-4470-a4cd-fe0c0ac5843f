import request from '@/utils/request'

// 查询承包商资质信息列表
export function listZjContractorQualificationInfo(query) {
  return request({
    url: '/contractor/zjContractorQualificationInfo/list',
    method: 'get',
    params: query
  })
}

// 查询承包商资质信息详细
export function getZjContractorQualificationInfo(id) {
  return request({
    url: '/contractor/zjContractorQualificationInfo/' + id,
    method: 'get'
  })
}

// 新增承包商资质信息
export function addZjContractorQualificationInfo(data) {
  return request({
    url: '/contractor/zjContractorQualificationInfo',
    method: 'post',
    data: data
  })
}

// 修改承包商资质信息
export function updateZjContractorQualificationInfo(data) {
  return request({
    url: '/contractor/zjContractorQualificationInfo',
    method: 'put',
    data: data
  })
}

// 删除承包商资质信息
export function delZjContractorQualificationInfo(id) {
  return request({
    url: '/contractor/zjContractorQualificationInfo/' + id,
    method: 'delete'
  })
}
