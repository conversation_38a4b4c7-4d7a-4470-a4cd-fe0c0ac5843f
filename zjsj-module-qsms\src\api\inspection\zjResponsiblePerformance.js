import request from '@/utils/request'

// 查询负责人履职管理列表
export function listZjResponsiblePerformance(query) {
  return request({
    url: '/inspection/zjResponsiblePerformance/list',
    method: 'get',
    params: query
  })
}

// 查询负责人履职管理详细
export function getZjResponsiblePerformance(id) {
  return request({
    url: '/inspection/zjResponsiblePerformance/' + id,
    method: 'get'
  })
}

// 新增负责人履职管理
export function addZjResponsiblePerformance(data) {
  return request({
    url: '/inspection/zjResponsiblePerformance',
    method: 'post',
    data: data
  })
}

// 修改负责人履职管理
export function updateZjResponsiblePerformance(data) {
  return request({
    url: '/inspection/zjResponsiblePerformance',
    method: 'put',
    data: data
  })
}

// 删除负责人履职管理
export function delZjResponsiblePerformance(id) {
  return request({
    url: '/inspection/zjResponsiblePerformance/' + id,
    method: 'delete'
  })
}
