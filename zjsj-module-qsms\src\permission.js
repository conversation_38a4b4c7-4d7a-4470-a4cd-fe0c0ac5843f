import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken, setToken } from "@/utils/auth";
import { isRelogin } from "@/utils/request";

NProgress.configure({ showSpinner: false });

const whiteList = ["/qsms/login", "/register", "/menhu"];

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  const token = getToken();

  if (token) {
    // 已登录，但有 token 的情况
    console.log("已登录", to.path);
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    // 删除参数
    params.delete("token");
    params.delete("source");

    // 构建新URL
    const newUrl =
      url.origin +
      url.pathname +
      (params.toString() ? "?" + params.toString() : "");
    console.log(newUrl, "newUrl11");

    // 更新地址栏
    window.history.replaceState({}, document.title, newUrl);
    to.meta.title && store.dispatch("settings/setTitle", to.meta.title);
    /* has token*/
    if (to.path === "/qsms/login") {
      next({ path: "/" });
      // next({ path: "/menhu" }); // 或 '/index'
      NProgress.done();
    } else if (to.path === "/menhu" || to.path === "/index") {
      // 检查用户权限是否已加载
      if (store.getters.roles.length === 0) {
        try {
          isRelogin.show = true;
          // 1. 获取用户信息（含角色权限）
          await store.dispatch("GetInfo");

          // 2. 请求后端动态路由（关键修改！）
          const accessRoutes = await store.dispatch("GenerateRoutes");

          // 3. 动态添加路由
          router.addRoutes(accessRoutes);

          // 4. 确保路由添加完成，跳转到目标页
          next({ ...to, replace: true });
        } catch (err) {
          // 失败时清空 Token 并退回登录页
          await store.dispatch("LogOut");
          // Message.error(err.message || "获取权限失败");
          next(`/qsms/login?redirect=${to.path}`);
        } finally {
          isRelogin.show = false;
        }
      } else {
        next(); // 已有权限，正常放行
      }
    } else {
      // 检查用户权限是否已加载
      if (store.getters.roles.length === 0) {
        try {
          isRelogin.show = true;
          // 1. 获取用户信息（含角色权限）
          await store.dispatch("GetInfo");

          // 2. 请求后端动态路由（关键修改！）
          const accessRoutes = await store.dispatch("GenerateRoutes");

          // 3. 动态添加路由
          router.addRoutes(accessRoutes);

          // 4. 确保路由添加完成，跳转到目标页
          next({ ...to, replace: true });
        } catch (err) {
          // 失败时清空 Token 并退回登录页
          await store.dispatch("LogOut");
          // Message.error(err.message || "获取权限失败");
          next(`/qsms/login?redirect=${to.path}`);
        } finally {
          isRelogin.show = false;
        }
      } else {
        next(); // 已有权限，正常放行
      }
    }
  } else {
    console.log("未登录");
    // const noAuthToken = sessionStorage.getItem("noAuthToken");

    const urlParams = new URLSearchParams(window.location.search);
    const noAuthToken = urlParams.get("token");
    const source = urlParams.get("source");
    console.log(source, "source");
    // 未登录 但有免密Token
    if (noAuthToken) {
      console.log("已有noAuthToken");
      setToken(noAuthToken);
      setTimeout(() => {
        next(`/${source}` || "/menhu");
      }, 100);

      // 删除参数
      urlParams.delete("token");
      urlParams.delete("source");

      // 构建新URL
      const newUrl =
        url.origin +
        url.pathname +
        (urlParams.toString() ? "?" + urlParams.toString() : "");
      console.log(newUrl, "newUrl");
      // 更新地址栏
      window.history.replaceState({}, document.title, newUrl);
    } else if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      console.log("没有登录1234");
      next();
    } else {
      console.log("没有登录123");
      next(`/qsms/login?redirect=${encodeURIComponent(to.fullPath)}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
