{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorInfo\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorInfo\\detail.vue", "mtime": 1757491222233}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_zjContractorInfo", "require", "_zjContractorBlaklist", "_QualificationTab", "_interopRequireDefault", "_PerformanceTab", "_WorkTicketTab", "_ConstructionTab", "_EvaluationTab", "name", "dicts", "components", "QualificationTab", "PerformanceTab", "WorkTicketTab", "ConstructionTab", "EvaluationTab", "data", "loading", "contractorInfo", "title", "open", "form", "rules", "contractorName", "required", "message", "trigger", "administratorId", "creditCode", "serviceTimeRange", "contractorManager", "managerOptions", "activeTabName", "created", "id", "$route", "params", "getContractorDetail", "loadManagerOptions", "$modal", "msgError", "goBack", "methods", "_this", "getZjContractorInfo", "then", "response", "catch", "window", "history", "length", "$router", "back", "activeMenu", "meta", "replace", "pathParts", "path", "split", "filter", "Boolean", "last", "prev", "isIdLike", "test", "isActionSegment", "includes", "parentParts", "slice", "parentPath", "join", "handleEdit", "reset", "_objectSpread2", "default", "serviceStartTime", "serviceEndTime", "_this2", "getUserInfo", "rows", "uniqueManagers", "Map", "for<PERSON>ach", "manager", "index", "value", "userId", "concat", "label", "nick<PERSON><PERSON>", "userName", "has", "set", "Array", "from", "values", "error", "console", "handleServiceTimeChange", "handleManagerChange", "<PERSON><PERSON><PERSON><PERSON>", "find", "<PERSON><PERSON><PERSON>", "cancel", "contractorCategory", "contractorType", "companyProfile", "chargeContactNumber", "unitNature", "legalRepresentative", "companyAddress", "legalRepresentativePhone", "companyEmail", "blacklistStatus", "accessStatus", "createTime", "createBy", "updateTime", "updateBy", "blacklistReason", "resetForm", "getServiceTimeRange", "submitForm", "_this3", "$refs", "validate", "valid", "updateZjContractorInfo", "msgSuccess", "handleTabClick", "tab"], "sources": ["src/views/contractor/zjContractorInfo/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-left\">\r\n        <el-button\r\n          type=\"text\"\r\n          icon=\"el-icon-arrow-left\"\r\n          @click=\"goBack\"\r\n          class=\"back-btn\"\r\n        >\r\n          返回列表\r\n        </el-button>\r\n        <div class=\"page-title\">承包商详情</div>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <!-- 保留右侧空间以保持布局平衡 -->\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 详情内容 -->\r\n    <div class=\"detail-content\" v-loading=\"loading\">\r\n      <!-- 基本信息表单 -->\r\n      <div class=\"info-section\">\r\n        <div class=\"section-title\">基本信息</div>\r\n        <el-form class=\"detail-form\" label-width=\"120px\">\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第一行：承包商名称 | 管理人 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商名称\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.contractorName\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"管理人\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.administratorName\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第二行：统一社会信用代码 | 服务起止时间 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"统一社会信用代码\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.creditCode\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"服务起止时间\">\r\n                <el-date-picker\r\n                  :value=\"getServiceTimeRange()\"\r\n                  type=\"daterange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  style=\"width: 100%\"\r\n                  disabled\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第三行：承包商负责人 | 承包商类别 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商负责人\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.contractorManager\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商类别\">\r\n                <el-select\r\n                  v-model=\"contractorInfo.contractorCategory\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-option label=\"准承包商\" :value=\"1\" />\r\n                  <el-option label=\"合格承包商\" :value=\"2\" />\r\n                  <el-option label=\"不合格承包商\" :value=\"3\" />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第四行：承包商类型 | 公司简介 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"承包商类型\">\r\n                <el-select\r\n                  v-model=\"contractorInfo.contractorType\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.sys_contractor_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"公司简介\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.companyProfile\"\r\n                  type=\"textarea\"\r\n                  :rows=\"4\"\r\n                  placeholder=\"-\"\r\n                  style=\"width: 100%\"\r\n                  disabled\r\n                >\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第五行：负责人联系电话 | 单位性质 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"负责人联系电话\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.chargeContactNumber\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"单位性质\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.unitNature\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第六行：法定代表人 | 公司地址 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"法定代表人\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.legalRepresentative\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"公司地址\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.companyAddress\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"20\">\r\n            <!-- 第七行：法定代表人联系电话 | 公司邮箱 -->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"法定代表人联系电话\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.legalRepresentativePhone\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"公司邮箱\">\r\n                <el-input\r\n                  v-model=\"contractorInfo.companyEmail\"\r\n                  disabled\r\n                  placeholder=\"-\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- Tab切换内容 -->\r\n      <div class=\"tab-section\">\r\n        <el-tabs v-model=\"activeTabName\" @tab-click=\"handleTabClick\">\r\n          <el-tab-pane label=\"资质信息\" name=\"qualification\">\r\n            <qualification-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"业绩信息\" name=\"performance\">\r\n            <performance-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"作业票\" name=\"workTicket\">\r\n            <work-ticket-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"施工信息\" name=\"construction\">\r\n            <construction-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n          <el-tab-pane label=\"评价记录\" name=\"evaluation\">\r\n            <evaluation-tab :contractor-id=\"contractorInfo.id\" />\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"1200px\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第一行：承包商名称 | 管理人 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商名称\" prop=\"contractorName\">\r\n              <el-input\r\n                v-model=\"form.contractorName\"\r\n                placeholder=\"承包商名称不能为空\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"管理人\" prop=\"administratorId\">\r\n              <el-select\r\n                v-model=\"form.administratorId\"\r\n                placeholder=\"管理人不能为空\"\r\n                style=\"width: 100%\"\r\n                @change=\"handleManagerChange\"\r\n              >\r\n                <el-option\r\n                  v-for=\"(item, index) in managerOptions\"\r\n                  :key=\"`form_manager_${item.value}_${index}`\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.value\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第二行：统一社会信用代码 | 服务起止时间 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"统一社会信用代码\" prop=\"creditCode\">\r\n              <el-input\r\n                v-model=\"form.creditCode\"\r\n                placeholder=\"统一社会信用代码不能为空\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务起止时间\" prop=\"serviceTimeRange\">\r\n              <el-date-picker\r\n                v-model=\"form.serviceTimeRange\"\r\n                type=\"daterange\"\r\n                range-separator=\"至\"\r\n                start-placeholder=\"开始日期\"\r\n                end-placeholder=\"结束日期\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                @change=\"handleServiceTimeChange\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第三行：承包商负责人 | 承包商类别 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商负责人\" prop=\"contractorManager\">\r\n              <el-input\r\n                v-model=\"form.contractorManager\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商类别\" prop=\"contractorCategory\">\r\n              <el-select\r\n                v-model=\"form.contractorCategory\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"(dict, index) in dict.type.contractor_category\"\r\n                  :key=\"`form_contractor_category_${dict.value}_${index}`\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第四行：承包商类型 | 公司简介（经营范围） -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"承包商类型\" prop=\"contractorType\">\r\n              <el-select\r\n                v-model=\"form.contractorType\"\r\n                placeholder=\"请选择\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"(dict, index) in dict.type.sys_contractor_type\"\r\n                  :key=\"`form_contractor_type_${dict.value}_${index}`\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公司简介\" prop=\"companyProfile\">\r\n              <el-input\r\n                v-model=\"form.companyProfile\"\r\n                type=\"textarea\"\r\n                :rows=\"4\"\r\n                placeholder=\"请输入内容\"\r\n                style=\"width: 100%\"\r\n              >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第五行：负责人联系电话 | 单位性质 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人联系电话\" prop=\"chargeContactNumber\">\r\n              <el-input\r\n                v-model=\"form.chargeContactNumber\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"单位性质\" prop=\"unitNature\">\r\n              <el-input v-model=\"form.unitNature\" placeholder=\"请输入内容\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第六行：法定代表人 | 公司地址 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"法定代表人\" prop=\"legalRepresentative\">\r\n              <el-input\r\n                v-model=\"form.legalRepresentative\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公司地址\" prop=\"companyAddress\">\r\n              <el-input\r\n                v-model=\"form.companyAddress\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <!-- 第七行：法定代表人联系电话 | 公司邮箱 -->\r\n          <el-col :span=\"12\">\r\n            <el-form-item\r\n              label=\"法定代表人联系电话\"\r\n              prop=\"legalRepresentativePhone\"\r\n            >\r\n              <el-input\r\n                v-model=\"form.legalRepresentativePhone\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"公司邮箱\" prop=\"companyEmail\">\r\n              <el-input v-model=\"form.companyEmail\" placeholder=\"请输入邮箱\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getZjContractorInfo,\r\n  updateZjContractorInfo,\r\n} from \"@/api/contractor/zjContractorInfo\";\r\nimport { getUserInfo } from \"@/api/contractor/zjContractorBlaklist\";\r\nimport QualificationTab from \"./components/QualificationTab.vue\";\r\nimport PerformanceTab from \"./components/PerformanceTab.vue\";\r\nimport WorkTicketTab from \"./components/WorkTicketTab.vue\";\r\nimport ConstructionTab from \"./components/ConstructionTab.vue\";\r\nimport EvaluationTab from \"./components/EvaluationTab.vue\";\r\n\r\nexport default {\r\n  name: \"ZjContractorInfoDetail\",\r\n  dicts: [\"contractor_category\", \"sys_contractor_type\"],\r\n  components: {\r\n    QualificationTab,\r\n    PerformanceTab,\r\n    WorkTicketTab,\r\n    ConstructionTab,\r\n    EvaluationTab,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 承包商信息\r\n      contractorInfo: {},\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contractorName: [\r\n          { required: true, message: \"承包商名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        administratorId: [\r\n          { required: true, message: \"管理人不能为空\", trigger: \"change\" },\r\n        ],\r\n        creditCode: [\r\n          {\r\n            required: true,\r\n            message: \"统一社会信用代码不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        serviceTimeRange: [\r\n          {\r\n            required: true,\r\n            message: \"服务起止时间不能为空\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        contractorManager: [\r\n          { required: true, message: \"承包商负责人不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      // 管理人选项\r\n      managerOptions: [],\r\n      // 当前活跃的tab\r\n      activeTabName: \"qualification\",\r\n    };\r\n  },\r\n  created() {\r\n    const id = this.$route.params.id;\r\n    if (id) {\r\n      this.getContractorDetail(id);\r\n      this.loadManagerOptions();\r\n    } else {\r\n      this.$modal.msgError(\"缺少承包商ID参数\");\r\n      this.goBack();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 获取承包商详情 */\r\n    getContractorDetail(id) {\r\n      this.loading = true;\r\n      getZjContractorInfo(id)\r\n        .then((response) => {\r\n          this.contractorInfo = response.data;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n          this.$modal.msgError(\"获取承包商详情失败\");\r\n        });\r\n    },\r\n\r\n    /** 返回列表页 */\r\n    goBack() {\r\n      // 优先使用浏览器历史返回，避免写死路径\r\n      if (window.history.length > 1) {\r\n        this.$router.back();\r\n        return;\r\n      }\r\n\r\n      // 兜底1：如果路由meta提供了activeMenu，则跳转该菜单\r\n      const activeMenu = this.$route && this.$route.meta && this.$route.meta.activeMenu;\r\n      if (activeMenu) {\r\n        this.$router.replace(activeMenu);\r\n        return;\r\n      }\r\n\r\n      // 兜底2：根据当前路径动态计算上一级路径\r\n      const pathParts = (this.$route && this.$route.path ? this.$route.path : \"/\").split(\"/\").filter(Boolean);\r\n      if (pathParts.length > 1) {\r\n        const last = pathParts[pathParts.length - 1];\r\n        const prev = pathParts[pathParts.length - 2] || \"\";\r\n        // 若路径以 /.../detail/:id 或 /.../edit/:id 形式存在，则回退两级；\r\n        // 否则默认去掉最后一级\r\n        const isIdLike = /^\\d+$/.test(last) || /^[0-9a-fA-F-]{8,}$/.test(last);\r\n        const isActionSegment = [\"detail\", \"edit\", \"view\"].includes(prev);\r\n        const parentParts = isIdLike && isActionSegment ? pathParts.slice(0, -2) : pathParts.slice(0, -1);\r\n        const parentPath = \"/\" + parentParts.join(\"/\");\r\n        this.$router.replace(parentPath || \"/\");\r\n        return;\r\n      }\r\n\r\n      // 最终兜底：回首页\r\n      this.$router.replace(\"/\");\r\n    },\r\n\r\n    /** 编辑按钮操作 */\r\n    handleEdit() {\r\n      this.reset();\r\n      this.form = { ...this.contractorInfo };\r\n      // 设置服务时间范围\r\n      if (this.form.serviceStartTime && this.form.serviceEndTime) {\r\n        this.form.serviceTimeRange = [\r\n          this.form.serviceStartTime,\r\n          this.form.serviceEndTime,\r\n        ];\r\n      } else {\r\n        this.form.serviceTimeRange = null;\r\n      }\r\n      this.open = true;\r\n      this.title = \"修改承包商信息\";\r\n    },\r\n\r\n    /** 加载管理人选项 */\r\n    loadManagerOptions() {\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          const data = response.data || response.rows || response || [];\r\n          const uniqueManagers = new Map();\r\n          data.forEach((manager, index) => {\r\n            const value = manager.userId || manager.id || `temp_${index}`;\r\n            const label =\r\n              manager.nickName ||\r\n              manager.name ||\r\n              manager.userName ||\r\n              `未命名_${index}`;\r\n            const nickName =\r\n              manager.nickName ||\r\n              manager.name ||\r\n              manager.userName ||\r\n              `未命名_${index}`;\r\n            if (!uniqueManagers.has(value)) {\r\n              uniqueManagers.set(value, {\r\n                value,\r\n                label,\r\n                nickName,\r\n              });\r\n            }\r\n          });\r\n          this.managerOptions = Array.from(uniqueManagers.values());\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取管理人选项失败:\", error);\r\n          this.$modal.msgError(\"获取管理人选项失败\");\r\n        });\r\n    },\r\n\r\n    /** 处理服务时间范围变化 */\r\n    handleServiceTimeChange(value) {\r\n      if (value && value.length === 2) {\r\n        this.form.serviceStartTime = value[0];\r\n        this.form.serviceEndTime = value[1];\r\n      } else {\r\n        this.form.serviceStartTime = null;\r\n        this.form.serviceEndTime = null;\r\n      }\r\n    },\r\n\r\n    /** 处理管理人变化 */\r\n    handleManagerChange(value) {\r\n      if (value) {\r\n        const selectedManager = this.managerOptions.find(\r\n          (manager) => manager.value == value\r\n        );\r\n        if (selectedManager) {\r\n          this.form.administratorName = selectedManager.nickName;\r\n        }\r\n      } else {\r\n        this.form.administratorName = null;\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        contractorName: null,\r\n        administratorId: null,\r\n        administratorName: null,\r\n        creditCode: null,\r\n        serviceStartTime: null,\r\n        serviceEndTime: null,\r\n        serviceTimeRange: null,\r\n        contractorManager: null,\r\n        contractorCategory: null,\r\n        contractorType: null,\r\n        companyProfile: null,\r\n        chargeContactNumber: null,\r\n        unitNature: null,\r\n        legalRepresentative: null,\r\n        companyAddress: null,\r\n        legalRepresentativePhone: null,\r\n        companyEmail: null,\r\n        blacklistStatus: null,\r\n        accessStatus: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        blacklistReason: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 获取服务时间范围 */\r\n    getServiceTimeRange() {\r\n      if (\r\n        this.contractorInfo.serviceStartTime &&\r\n        this.contractorInfo.serviceEndTime\r\n      ) {\r\n        return [\r\n          this.contractorInfo.serviceStartTime,\r\n          this.contractorInfo.serviceEndTime,\r\n        ];\r\n      }\r\n      return null;\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          updateZjContractorInfo(this.form).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            // 重新获取详情数据\r\n            this.getContractorDetail(this.form.id);\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 处理tab点击 */\r\n    handleTabClick(tab) {\r\n      this.activeTabName = tab.name;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  width: 100%;\r\n  background-color: rgb(247, 248, 250);\r\n  min-height: calc(100vh - 60px);\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.page-header {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n  border: 1px solid #e8e8e8;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.back-btn {\r\n  color: #1890ff;\r\n  padding: 0;\r\n  margin-right: 15px;\r\n  font-size: 14px;\r\n}\r\n\r\n.back-btn:hover {\r\n  color: #40a9ff;\r\n}\r\n\r\n.page-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.detail-content {\r\n  min-height: 500px;\r\n  overflow: visible;\r\n}\r\n\r\n.detail-form {\r\n  padding: 0;\r\n}\r\n\r\n.detail-form .el-form-item {\r\n  margin-bottom: 22px;\r\n}\r\n\r\n.detail-form .el-input.is-disabled .el-input__inner,\r\n.detail-form .el-textarea.is-disabled .el-textarea__inner,\r\n.detail-form .el-select.is-disabled .el-input__inner {\r\n  background-color: #f8f9fa !important;\r\n  border-color: #e8e8e8 !important;\r\n  color: #333 !important;\r\n  cursor: default;\r\n}\r\n\r\n.detail-form .el-date-editor.is-disabled {\r\n  background-color: #f8f9fa !important;\r\n}\r\n\r\n.detail-form .el-date-editor.is-disabled .el-input__inner {\r\n  background-color: #f8f9fa !important;\r\n  border-color: #e8e8e8 !important;\r\n  color: #333 !important;\r\n}\r\n\r\n.info-section {\r\n  background-color: #fff;\r\n  padding: 24px;\r\n  border-radius: 5px;\r\n  border: 1px solid #e8e8e8;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 2px solid #1890ff;\r\n  position: relative;\r\n}\r\n\r\n.section-title::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 0;\r\n  bottom: -2px;\r\n  width: 50px;\r\n  height: 2px;\r\n  background-color: #1890ff;\r\n}\r\n\r\n.tab-section {\r\n  background-color: #fff;\r\n  border-radius: 5px;\r\n  border: 1px solid #e8e8e8;\r\n  overflow: visible;\r\n  padding: 20px 0 20px 20px;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 20px;\r\n  align-items: start;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.info-item.full-width {\r\n  grid-column: 1 / -1;\r\n  flex-direction: column;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #666;\r\n  min-width: 120px;\r\n  margin-right: 12px;\r\n  line-height: 22px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.info-item span {\r\n  color: #333;\r\n  line-height: 22px;\r\n  word-break: break-all;\r\n}\r\n\r\n.textarea-content {\r\n  background-color: #f8f9fa;\r\n  padding: 12px;\r\n  border-radius: 4px;\r\n  border: 1px solid #e8e8e8;\r\n  color: #333;\r\n  line-height: 1.6;\r\n  min-height: 60px;\r\n  width: 100%;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n}\r\n\r\n.el-tag {\r\n  font-weight: 500;\r\n}\r\n\r\n/* 页面容器滚动由 AppMain 组件控制 */\r\n\r\n/* Tab内容样式 */\r\n.el-tabs__content {\r\n  overflow: visible;\r\n}\r\n\r\n.el-tab-pane {\r\n  overflow: visible;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n\r\n  .header-right {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAoaA,IAAAA,iBAAA,GAAAC,OAAA;AAIA,IAAAC,qBAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,eAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,cAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,gBAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,cAAA,GAAAJ,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAQ,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA,yBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,cAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,eAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,UAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAG,gBAAA,GACA;UACAL,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAI,iBAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAK,cAAA;MACA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAF,EAAA;IACA,IAAAA,EAAA;MACA,KAAAG,mBAAA,CAAAH,EAAA;MACA,KAAAI,kBAAA;IACA;MACA,KAAAC,MAAA,CAAAC,QAAA;MACA,KAAAC,MAAA;IACA;EACA;EACAC,OAAA;IACA,cACAL,mBAAA,WAAAA,oBAAAH,EAAA;MAAA,IAAAS,KAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,qCAAA,EAAAV,EAAA,EACAW,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAzB,cAAA,GAAA4B,QAAA,CAAA9B,IAAA;QACA2B,KAAA,CAAA1B,OAAA;MACA,GACA8B,KAAA;QACAJ,KAAA,CAAA1B,OAAA;QACA0B,KAAA,CAAAJ,MAAA,CAAAC,QAAA;MACA;IACA;IAEA,YACAC,MAAA,WAAAA,OAAA;MACA;MACA,IAAAO,MAAA,CAAAC,OAAA,CAAAC,MAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,QAAAlB,MAAA,SAAAA,MAAA,CAAAmB,IAAA,SAAAnB,MAAA,CAAAmB,IAAA,CAAAD,UAAA;MACA,IAAAA,UAAA;QACA,KAAAF,OAAA,CAAAI,OAAA,CAAAF,UAAA;QACA;MACA;;MAEA;MACA,IAAAG,SAAA,SAAArB,MAAA,SAAAA,MAAA,CAAAsB,IAAA,QAAAtB,MAAA,CAAAsB,IAAA,QAAAC,KAAA,MAAAC,MAAA,CAAAC,OAAA;MACA,IAAAJ,SAAA,CAAAN,MAAA;QACA,IAAAW,IAAA,GAAAL,SAAA,CAAAA,SAAA,CAAAN,MAAA;QACA,IAAAY,IAAA,GAAAN,SAAA,CAAAA,SAAA,CAAAN,MAAA;QACA;QACA;QACA,IAAAa,QAAA,WAAAC,IAAA,CAAAH,IAAA,0BAAAG,IAAA,CAAAH,IAAA;QACA,IAAAI,eAAA,8BAAAC,QAAA,CAAAJ,IAAA;QACA,IAAAK,WAAA,GAAAJ,QAAA,IAAAE,eAAA,GAAAT,SAAA,CAAAY,KAAA,UAAAZ,SAAA,CAAAY,KAAA;QACA,IAAAC,UAAA,SAAAF,WAAA,CAAAG,IAAA;QACA,KAAAnB,OAAA,CAAAI,OAAA,CAAAc,UAAA;QACA;MACA;;MAEA;MACA,KAAAlB,OAAA,CAAAI,OAAA;IACA;IAEA,aACAgB,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA;MACA,KAAAnD,IAAA,OAAAoD,cAAA,CAAAC,OAAA,WAAAxD,cAAA;MACA;MACA,SAAAG,IAAA,CAAAsD,gBAAA,SAAAtD,IAAA,CAAAuD,cAAA;QACA,KAAAvD,IAAA,CAAAQ,gBAAA,IACA,KAAAR,IAAA,CAAAsD,gBAAA,EACA,KAAAtD,IAAA,CAAAuD,cAAA,CACA;MACA;QACA,KAAAvD,IAAA,CAAAQ,gBAAA;MACA;MACA,KAAAT,IAAA;MACA,KAAAD,KAAA;IACA;IAEA,cACAmB,kBAAA,WAAAA,mBAAA;MAAA,IAAAuC,MAAA;MACA,IAAAC,iCAAA,KACAjC,IAAA,WAAAC,QAAA;QACA,IAAA9B,IAAA,GAAA8B,QAAA,CAAA9B,IAAA,IAAA8B,QAAA,CAAAiC,IAAA,IAAAjC,QAAA;QACA,IAAAkC,cAAA,OAAAC,GAAA;QACAjE,IAAA,CAAAkE,OAAA,WAAAC,OAAA,EAAAC,KAAA;UACA,IAAAC,KAAA,GAAAF,OAAA,CAAAG,MAAA,IAAAH,OAAA,CAAAjD,EAAA,YAAAqD,MAAA,CAAAH,KAAA;UACA,IAAAI,KAAA,GACAL,OAAA,CAAAM,QAAA,IACAN,OAAA,CAAA3E,IAAA,IACA2E,OAAA,CAAAO,QAAA,0BAAAH,MAAA,CACAH,KAAA;UACA,IAAAK,QAAA,GACAN,OAAA,CAAAM,QAAA,IACAN,OAAA,CAAA3E,IAAA,IACA2E,OAAA,CAAAO,QAAA,0BAAAH,MAAA,CACAH,KAAA;UACA,KAAAJ,cAAA,CAAAW,GAAA,CAAAN,KAAA;YACAL,cAAA,CAAAY,GAAA,CAAAP,KAAA;cACAA,KAAA,EAAAA,KAAA;cACAG,KAAA,EAAAA,KAAA;cACAC,QAAA,EAAAA;YACA;UACA;QACA;QACAZ,MAAA,CAAA9C,cAAA,GAAA8D,KAAA,CAAAC,IAAA,CAAAd,cAAA,CAAAe,MAAA;MACA,GACAhD,KAAA,WAAAiD,KAAA;QACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;QACAnB,MAAA,CAAAtC,MAAA,CAAAC,QAAA;MACA;IACA;IAEA,iBACA0D,uBAAA,WAAAA,wBAAAb,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAnC,MAAA;QACA,KAAA7B,IAAA,CAAAsD,gBAAA,GAAAU,KAAA;QACA,KAAAhE,IAAA,CAAAuD,cAAA,GAAAS,KAAA;MACA;QACA,KAAAhE,IAAA,CAAAsD,gBAAA;QACA,KAAAtD,IAAA,CAAAuD,cAAA;MACA;IACA;IAEA,cACAuB,mBAAA,WAAAA,oBAAAd,KAAA;MACA,IAAAA,KAAA;QACA,IAAAe,eAAA,QAAArE,cAAA,CAAAsE,IAAA,CACA,UAAAlB,OAAA;UAAA,OAAAA,OAAA,CAAAE,KAAA,IAAAA,KAAA;QAAA,CACA;QACA,IAAAe,eAAA;UACA,KAAA/E,IAAA,CAAAiF,iBAAA,GAAAF,eAAA,CAAAX,QAAA;QACA;MACA;QACA,KAAApE,IAAA,CAAAiF,iBAAA;MACA;IACA;IAEA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAnF,IAAA;MACA,KAAAoD,KAAA;IACA;IAEA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAnD,IAAA;QACAa,EAAA;QACAX,cAAA;QACAI,eAAA;QACA2E,iBAAA;QACA1E,UAAA;QACA+C,gBAAA;QACAC,cAAA;QACA/C,gBAAA;QACAC,iBAAA;QACA0E,kBAAA;QACAC,cAAA;QACAC,cAAA;QACAC,mBAAA;QACAC,UAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,wBAAA;QACAC,YAAA;QACAC,eAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,eAAA;MACA;MACA,KAAAC,SAAA;IACA;IAEA,eACAC,mBAAA,WAAAA,oBAAA;MACA,IACA,KAAAvG,cAAA,CAAAyD,gBAAA,IACA,KAAAzD,cAAA,CAAA0D,cAAA,EACA;QACA,QACA,KAAA1D,cAAA,CAAAyD,gBAAA,EACA,KAAAzD,cAAA,CAAA0D,cAAA,CACA;MACA;MACA;IACA;IAEA,WACA8C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,wCAAA,EAAAJ,MAAA,CAAAtG,IAAA,EAAAwB,IAAA,WAAAC,QAAA;YACA6E,MAAA,CAAApF,MAAA,CAAAyF,UAAA;YACAL,MAAA,CAAAvG,IAAA;YACA;YACAuG,MAAA,CAAAtF,mBAAA,CAAAsF,MAAA,CAAAtG,IAAA,CAAAa,EAAA;UACA;QACA;MACA;IACA;IAEA,cACA+F,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAlG,aAAA,GAAAkG,GAAA,CAAA1H,IAAA;IACA;EACA;AACA", "ignoreList": []}]}