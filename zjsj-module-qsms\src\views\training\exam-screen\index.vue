<template>
  <div class="exam-screen" :class="{ hasTagsView: needTagsView }">
    <div class="fs_top">
      <div class="header_content">
        <div class="top_location">
          <el-select 
            v-model="selectedExamDate" 
            size="mini" 
            placeholder="选择考试日期" 
            @change="handleExamDateChange"
            :loading="loading"
            style="width: 220px; margin-right: 10px;"
          >
            <el-option 
              v-for="option in examDateOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </div>
        <div class="top_name"></div>
        <div id="timeShow" class="top_time">{{ currentTime }}</div>
      </div>
    </div>
    <div class="fs_main">
      <!---left--->
      <div class="fs_left">
        <div class="fs_left_bg1">
          <div class="in_title">
            <span class="in_title_icon"></span>
            <p>学习统计</p>
            <span class="in_title_arrow"></span>
          </div>
          <div class="xuexi_box">
            <div class="clearfix">
              <div class="xuexi_list">
                <p id="allnums" class="xuexi_num">{{ examNums.allnums || 0 }}<span>人</span></p>
                <div class="xuexi_title">应考人数</div>
              </div>
              <div class="xuexi_list">
                <p id="examnums" class="xuexi_num">{{ examNums.examnums || 0 }}<span>人</span></p>
                <div class="xuexi_title">参考人数</div>
              </div>
            </div>
          </div>
        </div>
        <div class="fs_left_bg1 totop10">
          <div class="in_title" style="padding: 0 20px">
            <span class="in_title_icon"></span>
            <p>得分排名</p>
            <span class="in_title_arrow"></span>
          </div>
          <div class="scoreli_title">
            <span>排名</span>姓名/（身份证号）
            <p>分数</p>
          </div>
          <div id="examScores" class="fs_box2">
            <ul class="score" style="margin-top: 0px">
              <li 
                v-for="(item, index) in examScores" 
                :key="index" 
                class="scoreli"
              >
                <span :class="getRankingClass(index + 1)">{{ index + 1 }}</span>{{ item.username || '未知' }}（{{ formatIdCard(item.identityno) }}）
                <p>{{ item.score || 0 }}分</p>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!---right--->

      <div class="fleft">
        <!-- 第一行：考试总览和得分分段分布 -->
        <div class="first-row">
          <div class="fs_center">
            <div class="fs_center_bg1 boxbg_in">
              <div class="in_title exam_overview_title">
                <span class="in_title_icon"></span>
                <p>考试总览</p>
                <span class="in_title_arrow"></span>
              </div>
              <div class="exam-overview-grid">
                <div class="fx_box exam-highest">
                  <div class="fx_icon"></div>
                  <div class="fx_content">
                    <div class="fx_value-row">
                      <h3 id="maxscore">{{ getActualMaxScore }}</h3>
                      <!-- <span class="trend-up">10%</span> -->
                    </div>
                    <h5>最高分</h5>
                  </div>
                </div>
                <div class="fx_box exam-lowest">
                  <div class="fx_icon"></div>
                  <div class="fx_content">
                    <div class="fx_value-row">
                      <h3 id="minscore">{{ getActualMinScore }}</h3>
                      <!-- <span class="trend-down">10%</span> -->
                    </div>
                    <h5>最低分</h5>
                  </div>
                </div>
                <div class="fx_circle exam-pass-rate">
                  <div class="wc">及格率</div>
                  <div id="radio" class="bili">{{ getPassRate }}<span class="percent">%</span></div>
                </div>
                <div class="fx_box exam-average">
                  <div class="fx_icon"></div>
                  <div class="fx_content">
                    <div class="fx_value-row">
                      <h3 id="avgscore">{{ Math.round(examNums.avgscore || 0) }}</h3>
                      <!-- <span class="trend-down">10%</span> -->
                    </div>
                    <h5>平均分</h5>
                  </div>
                </div>
                <div class="fx_box exam-absent">
                  <div class="fx_icon"></div>
                  <div class="fx_content">
                    <div class="fx_value-row">
                      <h3 id="noexamnums">{{ examNums.noexamnums || 0 }}</h3>
                      <!-- <span class="trend-up">10%</span> -->
                    </div>
                    <h5>未参考人数</h5>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!---right--->
          <div class="fs_right">
            <div class="fs_right_bg1">
              <div class="in_title">
                <span class="in_title_icon"></span>
                <p>得分分段分布</p>
                <span class="in_title_arrow"></span>
              </div>
              <div
                id="scoreDistributionChart"
                class="fs_box4"
                style="
                  width: 100%;
                  height: calc(100% - 50px);
                "
              ></div>
            </div>
          </div>
        </div>
        <div class="fs_center3">
          <div class="fs_center_bg3 totop5">
            <div class="in_title">
              <span class="in_title_icon"></span>
              <p>参考公司分布</p>
              <span class="in_title_arrow"></span>
            </div>
            <div
              id="companyDistributionChart"
              class="fs_box5"
              style="
                width: 100%;
                height: calc(100% - 50px);
              "
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import * as zjExamInfoApi from '@/api/training/zjExamInfo'
const { getExamDateOptions, queryExamScreenData } = zjExamInfoApi

export default {
  data() {
    return {
      currentTime: '',
      timer: null,
      scoreDistributionChart: null,
      companyDistributionChart: null,
      // 考试日期相关数据
      selectedExamDate: '', // 当前选中的考试日期
      examDateOptions: [], // 考试日期选项列表
      loading: false, // 加载状态
      // 防止重复调用的状态标志
      isLoadingScreenData: false, // 是否正在加载考试大屏数据
      isInitialized: false, // 是否已完成初始化数据加载
      // 考试大屏数据
      examScreenData: null, // 接口返回的完整数据
      examNums: {
        allnums: 0,
        examnums: 0,
        noexamnums: 0,
        minscore: 0,
        maxscore: 0,
        avgscore: 0,
        radio: 0
      },
      examAges: [], // 各组织的考试情况
      examScoreTerms: [], // 考试成绩分段
      examScores: [] // 高分榜单
    }
  },
  computed: {
    // 检查是否需要tags-view
    needTagsView() {
      return this.$store.state.settings.tagsView;
    },
    
    // 计算实际最高分
    getActualMaxScore() {
      const { minscore, maxscore } = this.examNums;
      if (minscore && maxscore && parseFloat(minscore) > parseFloat(maxscore)) {
        return minscore || 0;
      }
      return maxscore || 0;
    },
    
    // 计算实际最低分
    getActualMinScore() {
      const { minscore, maxscore } = this.examNums;
      if (minscore && maxscore && parseFloat(minscore) > parseFloat(maxscore)) {
        return maxscore || 0;
      }
      return minscore || 0;
    },
    
    // 计算及格率
    getPassRate() {
      const { examnums, radio } = this.examNums;
      if (examnums > 0) {
        return ((radio || 0) / examnums * 100).toFixed(1);
      }
      return '0.0';
    }
  },
  mounted() {
    // 动态设置根字体大小，基于屏幕宽度 - 调整为更小的字体
    const screenWidth = window.innerWidth;
    let baseFontSize;
    
    if (screenWidth <= 1200) {
      // 1200px以下 - 小屏设备
      baseFontSize = Math.max(10, Math.min(14, screenWidth / 100));
    } else if (screenWidth <= 1400) {
      // 1200-1400px - 中等屏幕
      baseFontSize = Math.max(12, Math.min(16, screenWidth / 110));
    } else if (screenWidth <= 1600) {
      // 1400-1600px - 标准屏幕
      baseFontSize = Math.max(14, Math.min(18, screenWidth / 120));
    } else if (screenWidth <= 1800) {
      // 1600-1800px - 大屏幕
      baseFontSize = Math.max(16, Math.min(20, screenWidth / 130));
    } else {
      // 1800px以上 - 超大屏幕
      baseFontSize = Math.max(18, Math.min(22, screenWidth / 140));
    }
    
    document.getElementsByTagName("html")[0].style.fontSize = `${baseFontSize}px`;
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
    
    // 初始化时间
    this.updateTime();
    
    // 每秒更新时间
    this.timer = setInterval(() => {
      this.updateTime();
    }, 1000);
    
    // 数据变化监听（仅用于调试）
    this.$watch('examNums', (newVal) => {
      console.log('👀 examNums数据已更新:', newVal);
    }, { deep: true });
    
    this.$watch('examScores', (newVal) => {
      console.log('👀 examScores数据已更新:', newVal?.length || 0, '条记录');
    }, { deep: true });
    
    // 加载考试日期选项，完成后会自动加载数据
    this.loadExamDateOptions();
    
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
    // 销毁图表实例
    if (this.scoreDistributionChart) {
      this.scoreDistributionChart.dispose();
    }
    if (this.companyDistributionChart) {
      this.companyDistributionChart.dispose();
    }
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 获取考试日期选项
    async loadExamDateOptions() {
      try {
        this.loading = true;
        console.log('开始加载考试日期选项...');
        const response = await getExamDateOptions();
        
        if (response && response.code === 200 && response.data) {
          // 将字符串数组转换为选项对象数组
          const dateOptions = response.data.map((item, index) => ({
            value: item,
            label: item
          }));
          
          // 在开头添加"全部"选项
          this.examDateOptions = [
            {
              value: '',
              label: '全部'
            },
            ...dateOptions
          ];
          
          // 设置默认选择"全部"选项
          this.selectedExamDate = '';
          
          console.log('考试日期选项加载成功:', this.examDateOptions);
          console.log('默认选择日期:', this.selectedExamDate);
          
          // 日期选项加载完成后，立即加载数据
          await this.loadExamScreenData(this.selectedExamDate);
          this.isInitialized = true;
        }
      } catch (error) {
        console.error('获取考试日期选项失败:', error);
        this.$modal.msgError('获取考试日期选项失败，请稍后重试');
        
        // 兜底：使用空字符串加载数据
        this.selectedExamDate = '';
        await this.loadExamScreenData(this.selectedExamDate);
        this.isInitialized = true;
      } finally {
        this.loading = false;
      }
    },

    // 考试日期选择变化处理
    handleExamDateChange(selectedDate) {
      console.log('=== 日期选择发生变化 ===');
      console.log('选择的考试日期:', selectedDate);
      console.log('即将调用数据接口...');
      // 用户主动选择时，重新加载数据
      this.loadExamScreenData(selectedDate);
    },


    // 获取考试大屏数据
    async loadExamScreenData(examDate) {
      // 防止重复调用
      if (this.isLoadingScreenData) {
        console.log('考试大屏数据正在加载中，跳过重复调用');
        return;
      }
      
      try {
        console.log('=== 开始获取考试大屏数据 ===');
        console.log('传入的考试日期参数:', examDate);
        
        this.isLoadingScreenData = true;
        this.loading = true;
        
        const response = await queryExamScreenData(examDate);
        
        console.log('接口响应:', response);
        
        if (response && response.code === 200 && response.data) {
          console.log('=== 接口调用成功，开始处理数据 ===');
          
          // 直接赋值数据，Vue响应式系统会自动处理更新
          this.examScreenData = response.data;
          this.examNums = response.data.examNums || {};
          this.examAges = response.data.examAges || [];
          this.examScoreTerms = response.data.examScoreTerms || [];
          this.examScores = response.data.examScores || [];
          
          console.log('数据赋值完成:');
          console.log('- examNums:', this.examNums);
          console.log('- examScores 数量:', this.examScores.length);
          console.log('- examScoreTerms 数量:', this.examScoreTerms.length);
          console.log('- examAges 数量:', this.examAges.length);
          
          // 在下一个 tick 初始化或更新图表
          this.$nextTick(() => {
            // 强制重新初始化图表以使用最新数据
            if (this.scoreDistributionChart) {
              this.scoreDistributionChart.dispose();
              this.scoreDistributionChart = null;
            }
            if (this.companyDistributionChart) {
              this.companyDistributionChart.dispose();
              this.companyDistributionChart = null;
            }
            
            // 重新初始化图表
            this.initScoreDistributionChart();
            this.initCompanyDistributionChart();
            
            // 调整图表大小
            setTimeout(() => {
              this.resizeCharts();
            }, 100);
          });
        } else {
          console.warn('考试大屏数据接口返回数据格式异常:', response);
          this.$modal.msgWarning('考试大屏数据加载失败');
        }
      } catch (error) {
        console.error('获取考试大屏数据失败:', error);
        this.$modal.msgError('获取考试大屏数据失败，请稍后重试');
      } finally {
        this.isLoadingScreenData = false;
        this.loading = false;
      }
    },

    // 更新页面显示的统计数据（现在主要用于调试和图表更新）
    updateDisplayData() {
      console.log('=== 开始更新显示数据 ===');
      console.log('examNums数据:', this.examNums);
      console.log('examScores数据:', this.examScores);
      console.log('examScoreTerms数据:', this.examScoreTerms);
      console.log('examAges数据:', this.examAges);
      
      // 检查数据是否正确赋值
      if (this.examNums && Object.keys(this.examNums).length > 0) {
        console.log('✅ examNums数据已正确赋值');
      } else {
        console.warn('⚠️ examNums数据为空或未正确赋值');
      }
      
      if (this.examScores && this.examScores.length > 0) {
        console.log('✅ examScores数据已正确赋值，共', this.examScores.length, '条记录');
      } else {
        console.warn('⚠️ examScores数据为空');
      }
      
      console.log('=== 显示数据更新完成，Vue响应式绑定会自动更新页面 ===');
    },

    updateTime() {
      const now = new Date();
      const y = now.getFullYear();
      const m = String(now.getMonth() + 1).padStart(2, '0');
      const d = String(now.getDate()).padStart(2, '0');
      const h = String(now.getHours()).padStart(2, '0');
      const mi = String(now.getMinutes()).padStart(2, '0');
      const s = String(now.getSeconds()).padStart(2, '0');
      this.currentTime = `${y}年${m}月${d}日 ${h}:${mi}:${s}`;
    },
    
    handleResize() {
      // 窗口大小变化时重新计算字体大小 - 调整为更小的字体
      const screenWidth = window.innerWidth;
      let baseFontSize;
      
      if (screenWidth <= 1200) {
        // 1200px以下 - 小屏设备
        baseFontSize = Math.max(10, Math.min(14, screenWidth / 100));
      } else if (screenWidth <= 1400) {
        // 1200-1400px - 中等屏幕
        baseFontSize = Math.max(12, Math.min(16, screenWidth / 110));
      } else if (screenWidth <= 1600) {
        // 1400-1600px - 标准屏幕
        baseFontSize = Math.max(14, Math.min(18, screenWidth / 120));
      } else if (screenWidth <= 1800) {
        // 1600-1800px - 大屏幕
        baseFontSize = Math.max(16, Math.min(20, screenWidth / 130));
      } else {
        // 1800px以上 - 超大屏幕
        baseFontSize = Math.max(18, Math.min(22, screenWidth / 140));
      }
      
      document.getElementsByTagName("html")[0].style.fontSize = `${baseFontSize}px`;
      
      // 强制重新渲染图表
      this.$nextTick(() => {
        this.resizeCharts();
      });
    },
    
    initScoreDistributionChart() {
      const chartDom = document.getElementById('scoreDistributionChart');
      if (!chartDom) return;
      
      this.scoreDistributionChart = echarts.init(chartDom);
      
      // 使用动态数据，如果没有数据则显示空数据
      let echartData = [];
      if (this.examScoreTerms && this.examScoreTerms.length > 0) {
        echartData = this.examScoreTerms.map(item => ({
          name: item.scoreType || '未知分段',
          value: parseInt(item.nums) || 0
        }));
      } else {
        // 默认显示空数据
        echartData = [
          { name: "90-100分", value: 0 },
          { name: "80-90分", value: 0 },
          { name: "70-80分", value: 0 },
          { name: "60-70分", value: 0 },
          { name: "50-60分", value: 0 },
          { name: "50分以下", value: 0 }
        ];
      }

      // 颜色配置
      let color = ['#2A8BFD', '#BAFF7F', '#00FAC1', '#00CAFF', '#FDE056', '#4ED33C', '#FF8A26', '#FF5252', '#9689FF', '#CB00FF'];
      
      // 数值格式化函数
      let formatNumber = function (num) {
        let reg = /(?=(\B)(\d{3})+$)/g;
        return num.toString().replace(reg, ',');
      }

      const option = {
        backgroundColor: 'transparent',
        color: color,
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}人 ({d}%)',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          }
        },
        series: [{
          type: 'pie',
          roseType: 'radius',
          radius: ['25%', '65%'],
          center: ['48%', '50%'],
          data: echartData,
          hoverAnimation: true,
          itemStyle: {
            borderRadius: 2,
            borderColor: '#fff',
            borderWidth: 1
          },
          labelLine: {
            normal: {
              length: 8,
              length2: 15,
              lineStyle: {
                color: '#6B7899'
              }
            }
          },
          label: {
            normal: {
              formatter: params => {
                return (
                  '{icon|●}{name|' + params.name + '}\n{value|' +
                  formatNumber(params.value) + '人}'
                );
              },
              rich: {
                icon: {
                  fontSize: 12,
                  color: 'inherit'
                },
                name: {
                  fontSize: 12,
                  padding: [0, 0, 0, 8],
                  color: '#181E2C',
                  fontWeight: 'normal'
                },
                value: {
                  fontSize: 14,
                  fontWeight: 'bold',
                  padding: [8, 0, 0, 20],
                  color: 'inherit'
                }
              }
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }]
      };
      
      this.scoreDistributionChart.setOption(option);
    },
    
    initCompanyDistributionChart() {
      const chartDom = document.getElementById('companyDistributionChart');
      if (!chartDom) {
        console.warn('companyDistributionChart 容器未找到');
        return;
      }
      // 如果容器当前不可见或没有尺寸，延迟初始化
      const { clientWidth, clientHeight } = chartDom;
      if (!clientWidth || !clientHeight) {
        // 等待布局稳定后再试
        setTimeout(() => {
          this.initCompanyDistributionChart();
        }, 200);
        return;
      }
      
      // 如果图表实例已存在，先销毁
      if (this.companyDistributionChart) {
        this.companyDistributionChart.dispose();
      }
      
      this.companyDistributionChart = echarts.init(chartDom);
      
      // 使用动态数据，如果没有数据则显示空数据
      let companyNames = [];
      let blueBarData = [];  // 参考人数
      let orangeBarData = []; // 合格人数
      let lineData = [];      // 合格率百分比
      
      if (this.examAges && this.examAges.length > 0) {
        // 过滤掉无效的组织数据并按参考人数降序排序
        const validAges = this.examAges
          .filter(item => item.ageType && item.nums && parseInt(item.nums) > 0)
          .sort((a, b) => parseInt(b.nums) - parseInt(a.nums))
          .slice(0, 16); // 限制显示前16个组织

        validAges.forEach(item => {
          const companyName = item.ageType;
          const participantCount = parseInt(item.nums) || 0;  // 参考人数
          const passedCount = parseInt(item.scoreLevel) || 0;  // 合格人数
          const passRate = parseFloat(item.ratio) || 0;  // 合格率

          // 处理组织名称，超过5个字符的截断
          const displayName = companyName.length > 5 ? companyName.substring(0, 5) + '...' : companyName;
          
          companyNames.push(displayName);
          blueBarData.push(participantCount);
          orangeBarData.push(passedCount);
          lineData.push(passRate);
        });
      } else {
        // 默认显示空数据
        companyNames = ['暂无数据'];
        blueBarData = [0];
        orangeBarData = [0];
        lineData = [0];
      }
      
      // 参考用户示例的绿色渐变配置
      const dataColor = ['#4EB2FE', '#f19051', '#F47505', '#20d450'];
      const greenGradientColors = ['#20d450', '#4EB2FE', '#88cbfe'];
      
      // 绿色渐变配置 - 使用用户示例的颜色
      const greenGradient = {
        type: 'linear',
        x: 0,
        x2: 0,
        y: 1,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: '#20d450'  // 绿色底部
          },
          {
            offset: 0.5,
            color: '#4EB2FE'  // 蓝色中间
          },
          {
            offset: 1,
            color: '#88cbfe'  // 浅蓝色顶部
          }
        ]
      };

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          formatter: function(params) {
            let result = params[0].axisValue + '<br/>';
            params.forEach(function(item) {
              if (item.seriesType === 'bar') {
                result += item.marker + item.seriesName + ': ' + item.value + '人<br/>';
              } else {
                result += item.marker + item.seriesName + ': ' + item.value + '%<br/>';
              }
            });
            return result;
          }
        },
        legend: {
          data: ['参考人数', '合格人数', '合格率'],
          bottom: 10,
          textStyle: {
            color: '#181E2C',
            fontSize: 12
          }
        },
        xAxis: [
          {
            type: 'category',
            data: companyNames,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              color: '#6B7899',
              fontSize: 11,
              interval: 0,
              rotate: 0,
              margin: 2
            },
            axisLine: {
              lineStyle: {
                color: '#E5E5E5'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '人数',
            nameTextStyle: {
              color: '#6B7899',
              fontSize: 12
            },
            min: 0,
            max: 250,
            interval: 50,
            axisLabel: {
              color: '#6B7899',
              fontSize: 11
            },
            axisLine: {
              lineStyle: {
                color: '#E5E5E5'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#E8F4FF',
                type: 'solid'
              }
            }
          },
          {
            type: 'value',
            name: '百分比',
            nameTextStyle: {
              color: '#6B7899',
              fontSize: 12
            },
            min: 0,
            max: 100,
            interval: 20,
            axisLabel: {
              formatter: '{value}%',
              color: '#6B7899',
              fontSize: 11
            },
            axisLine: {
              lineStyle: {
                color: '#E5E5E5'
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '参考人数',
            type: 'bar',
            barWidth: '25%',
            data: blueBarData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#4A90E2' },
                { offset: 1, color: '#2E5BBA' }
              ]),
              borderRadius: [2, 2, 0, 0]
            }
          },
          {
            name: '合格人数',
            type: 'bar',
            barWidth: '25%',
            data: orangeBarData,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#FF9F40' },
                { offset: 1, color: '#FF7700' }
              ]),
              borderRadius: [2, 2, 0, 0]
            }
          },
          {
            name: '合格率',
            type: 'line',
            yAxisIndex: 1,
            data: lineData,
            smooth: true,
            symbolSize: 8,
            showSymbol: true,
            lineStyle: {
              width: 3,
              color: dataColor[3]  // 使用用户示例中的绿色
            },
            itemStyle: {
              color: dataColor[3],
              borderWidth: 2,
              borderColor: '#20d450'
            },
            symbol: 'circle',
            // 添加区域填充渐变效果
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(32, 212, 80, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(32, 212, 80, 0.1)'
                  }
                ]
              }
            }
          }
        ],
        grid: {
          left: '2%',
          right: '2%',
          top: 10,
          bottom: 56,
          containLabel: true
        }
      };
      
      this.companyDistributionChart.setOption(option);
      
      // 确保图表立即渲染
      setTimeout(() => {
        if (this.companyDistributionChart) {
          this.companyDistributionChart.resize();
        }
      }, 100);
    },

    // 格式化身份证号码（隐藏中间部分）
    formatIdCard(identityno) {
      return identityno ? 
        identityno.substring(0, 6) + '**********' + identityno.substring(16) : 
        '****************';
    },

    // 获取排名样式类名
    getRankingClass(rank) {
      switch(rank) {
        case 1:
          return 'ranking-first';
        case 2:
          return 'ranking-second';
        case 3:
          return 'ranking-third';
        default:
          return '';
      }
    },

    // 更新图表数据
    updateCharts() {
      // 更新得分分段分布图表
      this.updateScoreDistributionChart();
      // 更新公司分布图表
      this.updateCompanyDistributionChart();
    },

    // 更新得分分段分布图表
    updateScoreDistributionChart() {
      if (!this.scoreDistributionChart || !this.examScoreTerms) {
        return;
      }

      // 如果没有分段数据，使用默认数据
      let echartData = [];
      if (this.examScoreTerms && this.examScoreTerms.length > 0) {
        echartData = this.examScoreTerms.map(item => ({
          name: item.scoreType || '未知分段',
          value: parseInt(item.nums) || 0
        }));
      } else {
        // 使用原有的默认数据
        echartData = [
          { name: "90-100分", value: 0 },
          { name: "80-90分", value: 0 },
          { name: "70-80分", value: 0 },
          { name: "60-70分", value: 0 },
          { name: "50-60分", value: 0 },
          { name: "50分以下", value: 0 }
        ];
      }

      this.scoreDistributionChart.setOption({
        series: [{
          data: echartData
        }]
      });
    },

    // 更新公司分布图表
    updateCompanyDistributionChart() {
      if (!this.companyDistributionChart || !this.examAges || this.examAges.length === 0) {
        return;
      }

      // 从examAges数据中提取图表数据
      const companyNames = [];
      const participantCounts = [];
      const passedCounts = [];
      const passRates = [];

      // 过滤掉无效的组织数据并按参考人数降序排序
      const validAges = this.examAges
        .filter(item => item.ageType && item.nums && parseInt(item.nums) > 0)
        .sort((a, b) => parseInt(b.nums) - parseInt(a.nums))
        .slice(0, 16); // 限制显示前16个组织

      validAges.forEach(item => {
        const companyName = item.ageType;
        const participantCount = parseInt(item.nums) || 0;  // 参考人数
        const passedCount = parseInt(item.scoreLevel) || 0;  // 合格人数
        const passRate = parseFloat(item.ratio) || 0;  // 合格率

        // 处理组织名称，超过5个字符的截断
        const displayName = companyName.length > 5 ? companyName.substring(0, 5) + '...' : companyName;
        
        companyNames.push(displayName);
        participantCounts.push(participantCount);
        passedCounts.push(passedCount);
        passRates.push(passRate);
      });

      this.companyDistributionChart.setOption({
        xAxis: [{
          data: companyNames
        }],
        series: [
          {
            name: '参考人数',
            data: participantCounts
          },
          {
            name: '合格人数',
            data: passedCounts
          },
          {
            name: '合格率',
            data: passRates
          }
        ],
        legend: {
          data: ['参考人数', '合格人数', '合格率']
        }
      });
    },

    // 添加调试方法 - 手动检查和修复数据显示
    debugDataDisplay() {
      console.log('🔧 === 手动调试数据显示 ===');
      console.log('当前时间:', new Date().toLocaleString());
      console.log('组件数据状态:');
      console.log('- selectedExamDate:', this.selectedExamDate);
      console.log('- examDateOptions:', this.examDateOptions?.length || 0, '个选项');
      console.log('- examNums:', JSON.stringify(this.examNums));
      console.log('- examScores:', this.examScores?.length || 0, '条记录');
      console.log('- examScoreTerms:', this.examScoreTerms?.length || 0, '个分段');
      console.log('- examAges:', this.examAges?.length || 0, '个年龄组');
      console.log('- loading状态:', this.loading);
      console.log('- isInitialized:', this.isInitialized);
      console.log('- isLoadingScreenData:', this.isLoadingScreenData);
      
      // 检查DOM元素是否存在
      const allnumsEl = document.getElementById('allnums');
      const examnumsEl = document.getElementById('examnums');
      console.log('DOM元素检查:');
      console.log('- allnums元素:', allnumsEl?.textContent || '未找到');
      console.log('- examnums元素:', examnumsEl?.textContent || '未找到');
      
      // 强制更新
      this.$forceUpdate();
      console.log('🔧 已执行强制更新');
    },
    
    resizeCharts() {
      // 重新调整图表大小
      setTimeout(() => {
        // 调整得分分段分布图表
        if (this.scoreDistributionChart) {
          this.scoreDistributionChart.resize();
        }
        
        // 调整公司分布图表
        if (this.companyDistributionChart) {
          this.companyDistributionChart.resize();
        } else {
          // 如果图表实例不存在，尝试重新初始化
          console.warn('公司分布图表实例不存在，尝试重新初始化');
          this.initCompanyDistributionChart();
        }
        
        const chart4 = document.getElementById('statExamScoreTerms');
        const chart6 = document.getElementById('statExamAgeTerms');
        
        if (chart4 && chart4._echarts_instance_) {
          chart4._echarts_instance_.resize();
        }
        
        if (chart6 && chart6._echarts_instance_) {
          chart6._echarts_instance_.resize();
        }
        
        // 如果ECharts实例存在于全局变量中，也可以尝试这样调用
        if (window.statExamScoreTermsChart) {
          window.statExamScoreTermsChart.resize();
        }
        
        if (window.statExamAgeTermsChart) {
          window.statExamAgeTermsChart.resize();
        }
      }, 300);
    }
  }
};
</script>

<style scoped lang="scss">
.exam-screen {
  width: 100%;
  height: calc(100vh - 60px); /* 精确适配AppMain的高度 */
  font-family: "微软雅黑";
  max-width: unset;
  background: #e7f0fb;
  font-size: 0.5rem;
  overflow: hidden; /* 禁止滚动 */
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  .clear {
    clear: both;
  }
  .fs_top {
    margin: 0;
    width: 100%;
    height: 50px; /* 减少顶部高度 */
    margin-bottom: 8px; /* 减少底部间距 */
    background: url("../../../assets/images/exam-header.png") no-repeat center center;
    background-size: cover;
    position: relative;
    
    .header_content {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .top_name {
      font-size: 32px; /* 减少字体大小 */
      color: #01f1e4;
      font-weight: bold;
      text-align: center;
    }
    
    .top_time {
      position: absolute;
      top: 15px; /* 调整位置 */
      right: 15px;
      font-family: DIN Alternate, DIN Alternate;
      font-weight: bold;
      font-size: 16px; /* 减少字体大小 */
      color: #181E2C;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }

    .top_location {
      position: absolute;
      top: 12px; /* 调整位置 */
      left: 15px;
    }

    /* 美化下拉框（作用域样式） */
    ::v-deep .el-select .el-input__inner {
      height: 28px;
      line-height: 28px;
      background: rgba(255, 255, 255, 0.9);
      border-color: #dcdfe6;
      color: #181E2C;
    }
    ::v-deep .el-select .el-input__icon {
      line-height: 28px;
    }
  }
  .fs_main {
    margin: 0;
    width: 100%;
    height: calc(100vh - 118px); /* 减去navbar(60px) + 头部(50px) + 间距(8px) */
    display: flex;
    flex-direction: row;
    gap: 15px; /* 减少间距 */
    padding: 0 12px; /* 减少左右内边距 */
    box-sizing: border-box;
  }
  .fs_left {
    flex: 0 0 23%; /* 左侧区域占23%宽度 */
    min-width: 350px;
    max-width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: visible; /* 允许子元素滚动 */
  }
  .fs_left_bg1 {
    width: 100%;
    background: #fff;
    padding: 8px 15px; /* 减少内边距 */
    border: 1px solid #ffffff;
    box-sizing: border-box;
  }
  .fs_left_bg1:first-child {
    height: 150px; /* 减少学习统计区域高度 */
    flex-shrink: 0;
  }
  .fs_left_bg1:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止溢出 */
  }
  .in_title {
    width: 100%;
    height: 25px; /* 减少高度 */
    text-align: center;
    font-size: 14px; /* 减少字体大小 */
    color: #242a3a;
    line-height: 25px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 3px; /* 减少底部间距 */
    .in_title_icon {
      width: 12px;
      height: 18px;
      background: url("../../../assets/images/exam_icon.png") no-repeat center
        center;
      background-size: contain;
      margin-right: 12px;
    }
    .in_title_arrow {
      width: 14px;
      height: 8px;
      background: url("../../../assets/images/exam_arrow.png") no-repeat center
        center;
      background-size: contain;
      margin-left: 16px;
    }
  }
  .xuexi_box {
    background: url("../../../assets/images/xue_box.png") no-repeat center
      center;
    background-size: contain;
    width: 100%;
    max-width: 350px; /* 减少最大宽度 */
    height: 70px; /* 减少高度 */
    margin: 15px auto 0; /* 减少上边距 */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    min-width: 250px; /* 减少最小宽度 */
  }

  .fs_box1 {
    width: 440px;
    height: 157px;
    padding: 10px;
  }
  .clearfix {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 0 10px;
    width: 100%;
    box-sizing: border-box;
  }

  html,
  body,
  p,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 0px;
    padding: 0px;
  }

  .xuexi_num {
    padding-top: 6px;
    font-size: 24px; /* 减少字体大小 */
    color: #181e2c;
    font-weight: bold;
    text-align: center;
    font-family: DIN Alternate, DIN Alternate;

    span {
      color: #6b7899;
      font-size: 14px; /* 减少字体大小 */
      margin-left: 6px;
    }
  }
  .xuexi_list {
    flex: 1;
    min-width: 120px;
    max-width: 180px;
    margin: 0 5px;
    text-align: center;
  }
  .xuexi_title {
    margin-top: 3px;
    padding: 0 8px;
    font-size: 12px; /* 减少字体大小 */
    height: 25px; /* 减少高度 */
    color: #6b7899;
    text-align: center;
    line-height: 25px;
  }
  .xuexi_title p {
    float: right;
    font-size: 14px;
    color: #fff;
    padding: 0px;
    margin: 0px;
    padding-top: 5px;
    padding-right: 20px;
  }

  .totop10 {
    margin-top: 10px;
    padding-left: 0;
    padding-right: 0;
  }
  .fs_box2 {
    width: 100%;
    flex: 1;
    padding: 5px 8px; /* 减少内边距 */
    background: #fff;
    box-sizing: border-box;
    overflow-y: auto;
    /* 确保完全填充剩余空间 */
    height: 0; /* 让flex:1生效 */
    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    
    /* 可选：添加平滑滚动 */
    scroll-behavior: smooth;
  }
  
  /* 对于WebKit内核浏览器（Chrome, Safari等）隐藏滚动条 */
  .fs_box2::-webkit-scrollbar {
    display: none;
  }
  .score {
    margin-top: 10px;
    margin-left: 15px;
    padding-left: 0px;
  }

  .score li,
  .scoreli_title {
    font-size: 13px; /* 减少字体大小 */
    color: #181e2c;
    height: 32px; /* 减少高度 */
    border-bottom: 1px dashed #2f6577;
    padding-top: 6px; /* 减少上边距 */
    font-weight: lighter;
    list-style: none;
  }
  .scoreli_title span,
  .score li span {
    display: inline-block;
    width: 32px; /* 减少宽度 */
    height: 22px; /* 减少高度 */
    line-height: 22px;
    color: #000;
    font-size: 12px; /* 减少字体大小 */
    font-weight: lighter;
    text-align: center;
    margin-right: 20px; /* 减少右边距 */
    background: url("../../../assets/images/exam_rank_bg.png") no-repeat center
      center;
    background-size: 22px 22px; /* 减少背景大小 */
  }

  /* 第一名特殊样式 */
  .score li span.ranking-first {
    background: linear-gradient(152deg, #FA9632 1%, rgba(250,150,50,0.6) 100%);
    border-radius: 50%;
    // border: 2px solid;
    border-image: radial-gradient(circle, rgba(255, 247, 213, 1), rgba(255, 227, 110, 0)) 2 2;
    width: 22px; /* 保持正圆形 */
    height: 22px; /* 保持正圆形 */
    line-height: 22px; /* 文字垂直居中 */
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    background-size: 22px 22px !important; /* 覆盖默认背景图片 */
    display: inline-block; /* 确保与普通圆圈显示方式一致 */
    text-align: center; /* 确保文字居中 */
    margin-right: 20px; /* 与普通圆圈右边距一致 */
    /* 通过margin调整水平位置对齐 */
    margin-left: 5px;
  }

  /* 第二名特殊样式 */
  .score li span.ranking-second {
    background: linear-gradient(152deg, #3A89FF 1%, rgba(58, 137, 255, 0.6) 100%);
    border-radius: 50%;
    // border: 2px solid rgba(58, 137, 255, 0.3);
    width: 22px; /* 保持正圆形 */
    height: 22px; /* 保持正圆形 */
    line-height: 24px; /* 文字垂直居中 */
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    background-size: 22px 22px !important; /* 覆盖默认背景图片 */
    display: inline-block; /* 确保与普通圆圈显示方式一致 */
    text-align: center; /* 确保文字居中 */
    margin-right: 20px; /* 与普通圆圈右边距一致 */
    /* 通过margin调整水平位置对齐 */
    margin-left: 5px;
  }

  /* 第三名特殊样式 */
  .score li span.ranking-third {
    background: linear-gradient(152deg, #FA5332 1%, rgba(250, 83, 50, 0.6) 100%);
    border-radius: 50%;
    // border: 2px solid rgba(250, 83, 50, 0.3);
    width: 22px; /* 保持正圆形 */
    height: 22px; /* 保持正圆形 */
    line-height: 22px; /* 文字垂直居中 */
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    background-size: 22px 22px !important; /* 覆盖默认背景图片 */
    display: inline-block; /* 确保与普通圆圈显示方式一致 */
    text-align: center; /* 确保文字居中 */
    margin-right: 20px; /* 与普通圆圈右边距一致 */
    /* 通过margin调整水平位置对齐 */
    margin-left: 5px;
  }
  .scoreli_title p,
  .score li p {
    float: right;
    color: #181e2c;
    font-size: 13px; /* 减少字体大小 */
    font-weight: lighter;
    margin-right: 15px; /* 减少右边距 */
  }
  .scoreli_title {
    padding-top: 10px;
    margin-left: 15px;
    margin-right: 0px;
    // color: #E7F0FB !important;
    span,
    p {
      color: #6b7899 !important;
      background: none !important;
    }
  }
  .fleft {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 15px; /* 减少间距 */
  }
  
  /* 第一行容器 - flex布局让考试总览和得分分段分布并排显示 */
  .first-row {
    display: flex;
    flex-direction: row;
    gap: 15px; /* 减少间距 */
    flex: 1;
    min-height: 260px; /* 减少最小高度 */
  }
  
  .first-row .fs_center {
    flex: 2;
    min-width: 0;
  }
  
  .first-row .fs_right {
    flex: 1;
    min-width: 0;
  }
  .boxbg_in {
    position: relative;
    margin-top: 0px;
    margin-left: 0px;
    background: #fff;
    padding: 3px 10px; /* 减少内边距 */
    border: 1px solid #ffffff;
  }
  .fs_center_bg1 {
    width: 100%;
    height: 100%;
  }
  /* 考试总览网格布局 */
  .exam-overview-grid {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: 1fr 2fr 1fr;
    gap: 0.5rem; /* 减少间距 */
    height: calc(100% - 30px); /* 减去标题高度 */
    padding: 0.5rem; /* 减少内边距 */
    box-sizing: border-box;
  }
  
  .exam-overview-grid .fx_box {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    border-radius: 12px;
    padding: 0.5rem;
    box-sizing: border-box;
    transition: all 0.3s ease;
    min-height: 60px;
  }
  
  /* 网格位置分配 */
  .exam-overview-grid .exam-highest {
    grid-column: 1;
    grid-row: 1;
  }
  
  .exam-overview-grid .exam-lowest {
    grid-column: 3;
    grid-row: 1;
  }
  
  .exam-overview-grid .exam-average {
    grid-column: 1;
    grid-row: 3;
  }
  
  .exam-overview-grid .exam-absent {
    grid-column: 3;
    grid-row: 3;
  }
  
  .exam-overview-grid .exam-pass-rate {
    grid-column: 2;
    grid-row: 1 / 4;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent;
    border: none;
    border-radius: 50%;
    width: 200px;
    height: 200px;
    position: relative;
    place-self: center;
    aspect-ratio: 1;
  }
  
  .fx_icon {
    width: 2.4rem;
    height: 2.4rem;
    margin-right: 0.5rem;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    flex-shrink: 0;
  }
  
  .fx_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 0;
  }
  
  .fx_value-row {
    display: flex;
    align-items: center;
    gap: 0.2rem;
    margin-bottom: 0.1rem;
  }

  .exam-overview-grid .fx_box h3 {
    font-family: DINPro, DINPro;
    font-weight: 500;
    font-size: 24px;
    color: #181E2C;
    line-height: 24px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
  }
  .exam-overview-grid .fx_box h5 {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #6B7899;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0;
    margin-top: 0.2rem;
  }
  
  /* 图标背景样式 */
  .exam-highest .fx_icon {
    background-image: url("../../../assets/images/exam.png");
  }
  
  .exam-lowest .fx_icon {
    background-image: url("../../../assets/images/exam.png");
  }
  
  .exam-average .fx_icon {
    background-image: url("../../../assets/images/exam.png");
  }
  
  .exam-absent .fx_icon {
    background-image: url("../../../assets/images/exam.png");
  }
  
  /* 趋势指示器样式 */
  .exam-overview-grid .trend-up {
    color: #00e5ed;
    font-size: 0.4rem;
    display: flex;
    align-items: center;
    font-weight: 600;
  }
  
  .exam-overview-grid .trend-up::before {
    content: '';
    width: 0.4rem;
    height: 0.4rem;
    background-image: url("../../../assets/images/rise.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 0.1rem;
  }
  
  .exam-overview-grid .trend-down {
    color: #ff6b6b;
    font-size: 0.4rem;
    display: flex;
    align-items: center;
    font-weight: 600;
  }
  
  .exam-overview-grid .trend-down::before {
    content: '';
    width: 0.4rem;
    height: 0.4rem;
    background-image: url("../../../assets/images/decline.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    margin-right: 0.1rem;
  }

  .fs_right_bg1 {
    width: 100%;
    height: 100%;
    background: #fff;
    padding: 10px 20px;
    border: 1px solid #ffffff;
    box-sizing: border-box;
  }
  .fs_center3 {
    width: 100%;
    flex: 1;
    min-height: 300px;
    clear: both; /* 确保在第一行下方 */
  }
  .totop5 {
    margin-top: 0px;
  }
  .fs_center_bg3 {
    width: 100%;
    height: 100%;
    background: #fff;
    padding: 10px 20px;
    border: 1px solid #ffffff;
    box-sizing: border-box;
  }
  /* 及格率圆形样式 */
  .exam-overview-grid .exam-pass-rate .wc {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #515B6B;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-bottom: 0.2rem;
    order: 1;
  }
  
  .exam-overview-grid .exam-pass-rate .bili {
    font-family: DINPro, DINPro;
    font-weight: 500;
    font-size: 28px;
    color: #181E2C;
    line-height: 30px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 0;
    order: 2;
  }
  
  .exam-overview-grid .exam-pass-rate .bili .percent {
    font-family: DINPro, DINPro;
    font-weight: 500;
    font-size: 20px;
    color: #181E2C;
  }
  
  /* 及格率圆形背景样式 */
  .exam-overview-grid .exam-pass-rate {
    background-image: url("../../../assets/images/exam-center.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  /* 图表容器高度自适应 */
  .fs_box4 {
    width: 100% !important;
    height: calc(100% - 50px) !important;
  }
  .fs_box5 {
    width: 100% !important;
    height: calc(100% - 50px) !important;
  }
  .fs_box6 {
    width: 100% !important;
    height: calc(100% - 50px) !important;
  }
  
  /* 确保图表内部div也适应高度 */
  .fs_box4 > div,
  .fs_box5 > div,
  .fs_box6 > div {
    width: 100% !important;
    height: 100% !important;
  }
  
  /* 确保canvas元素适应父容器 */
  .fs_box4 canvas,
  .fs_box5 canvas,
  .fs_box6 canvas {
    width: 100% !important;
    height: 100% !important;
  }

  /* 处理Layout框架中tags-view的情况 */
  &.hasTagsView {
    height: calc(100vh - 84px); /* 适应有tags-view的Layout框架 (navbar 50px + tags-view 34px) */
    
    .fs_main {
      height: calc(100vh - 142px); /* 减去navbar(50px) + tags-view(34px) + 头部(50px) + 间距(8px) */
    }
  }
  
  /* 响应式媒体查询 - 1800px以下 */
  @media screen and (max-width: 1800px) {
    
    /* 得分排名字体设置 - 1600px以上 */
    .score li,
    .scoreli_title {
      font-size: 14px;
    }
    .scoreli_title span,
    .score li span {
      font-size: 14px;
      width: 35px;
      height: 25px;
      line-height: 25px;
      background-size: 25px 25px;
    }
    .scoreli_title p,
    .score li p {
      font-size: 14px;
    }
    
    /* 响应式排名样式调整 - 1800px以下 */
    .score li span.ranking-first,
    .score li span.ranking-second,
    .score li span.ranking-third {
      width: 25px; /* 保持正圆形 */
      height: 25px; /* 保持正圆形 */
      line-height: 25px; /* 文字垂直居中 */
      font-size: 13px;
      margin-left: 5px; /* 调整水平对齐 */
    }
  }

  /* 响应式媒体查询 - 1600px以下 */
  @media screen and (max-width: 1600px) {
    .fs_main {
      gap: 18px;
      padding: 0 12px; /* 适度内边距 */
    }
    .exam-overview-grid .fx_box h3 {
      font-size: 1.1rem;
    }
    .exam-overview-grid .fx_box h5 {
      font-size: 0.45rem;
    }
    
    /* 得分排名字体设置 - 1600px以下 */
    .score li,
    .scoreli_title {
      font-size: 12px;
    }
    .scoreli_title span,
    .score li span {
      font-size: 12px;
      width: 30px;
      height: 22px;
      line-height: 22px;
      background-size: 22px 22px;
    }
    .scoreli_title p,
    .score li p {
      font-size: 12px;
    }
    
    /* 响应式排名样式调整 - 1600px以下 */
    .score li span.ranking-first,
    .score li span.ranking-second,
    .score li span.ranking-third {
      width: 22px; /* 保持正圆形 */
      height: 22px; /* 保持正圆形 */
      line-height: 22px; /* 文字垂直居中 */
      font-size: 11px;
      margin-left: 4px; /* 调整水平对齐 */
    }
  }

  /* 响应式媒体查询 - 1400px以下 */
  @media screen and (max-width: 1400px) {
    .fs_main {
      gap: 15px;
      padding: 0 10px; /* 适度内边距 */
    }
    .fs_left {
      min-width: 320px;
      max-width: 400px;
    }
    .exam-overview-grid .fx_box h3 {
      font-size: 1rem;
    }
    .exam-overview-grid .fx_box h5 {
      font-size: 0.4rem;
    }
    
    /* 减小学习统计内容字体大小，避免换行 */
    .xuexi_num {
      font-size: 24px !important;
    }
    .xuexi_num span {
      font-size: 14px !important;
    }
    .xuexi_title {
      font-size: 12px !important;
    }
    
    /* 调整学习统计布局，避免换行 */
    .xuexi_box {
      height: auto !important;
      min-height: 88px !important;
      padding: 10px !important;
    }
    .clearfix {
      flex-wrap: nowrap !important;
      gap: 5px !important;
      padding: 0 5px !important;
    }
    .xuexi_list {
      min-width: 100px !important;
      max-width: 150px !important;
      margin: 0 2px !important;
    }
  }

  /* 响应式媒体查询 - 1200px以下 */
  @media screen and (max-width: 1200px) {
    .fs_main {
      flex-direction: column;
      gap: 12px;
      padding: 0 8px; /* 小屏幕下的内边距 */
    }
    .fs_left {
      flex: none;
      min-width: auto;
      max-width: none;
      width: 100%;
    }
    .fleft {
      width: 100%;
    }
    .first-row {
      flex-direction: column;
    }
    .first-row .fs_center,
    .first-row .fs_right {
      flex: none;
      width: 100%;
    }
    .xuexi_box {
      height: auto;
      min-height: 88px;
      padding: 10px;
      margin: 10px auto 0;
    }
    .clearfix {
      flex-direction: column;
      gap: 15px;
    }
    .xuexi_list {
      max-width: none;
      width: 100%;
      margin: 0;
    }
    .xuexi_num {
      font-size: 24px;
    }
    .exam-overview-grid {
      grid-template-columns: 1fr;
      grid-template-rows: auto;
      gap: 0.5rem;
      padding: 0.5rem;
    }
    .exam-overview-grid .exam-pass-rate {
      grid-column: 1;
      grid-row: auto;
      width: 150px;
      height: 150px;
      margin: 0 auto;
    }
    .exam-overview-grid .fx_box h3 {
      font-size: 1rem;
    }
    .exam-overview-grid .fx_box h5 {
      font-size: 0.75rem;
    }
    
    /* 进一步减小学习统计内容字体大小 - 1200px以下 */
    .xuexi_num {
      font-size: 20px !important;
    }
    .xuexi_num span {
      font-size: 12px !important;
    }
    .xuexi_title {
      font-size: 11px !important;
    }
    
    /* 进一步调整学习统计布局 - 1200px以下 */
    .xuexi_box {
      height: auto !important;
      min-height: 80px !important;
      padding: 8px !important;
    }
    .clearfix {
      flex-wrap: nowrap !important;
      gap: 3px !important;
      padding: 0 3px !important;
    }
    .xuexi_list {
      min-width: 90px !important;
      max-width: 130px !important;
      margin: 0 1px !important;
    }
    
    /* 得分排名图标尺寸调整 - 配合12px字体 */
    .scoreli_title span,
    .score li span {
      width: 30px;
      height: 22px;
      line-height: 22px;
      background-size: 22px 22px;
    }
    
    /* 响应式排名样式调整 - 1200px以下 */
    .score li span.ranking-first,
    .score li span.ranking-second,
    .score li span.ranking-third {
      width: 22px; /* 保持正圆形 */
      height: 22px; /* 保持正圆形 */
      line-height: 22px; /* 文字垂直居中 */
      font-size: 10px;
      margin-left: 4px; /* 调整水平对齐 */
    }
  }
}
</style>
