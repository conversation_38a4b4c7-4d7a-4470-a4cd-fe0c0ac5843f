import request from '@/utils/request'

// 查询承包商信息列表
export function listZjContractorInfo(query) {
  return request({
    url: '/contractor/zjContractorInfo/list',
    method: 'get',
    params: query
  })
}

// 查询承包商信息详细
export function getZjContractorInfo(id) {
  return request({
    url: '/contractor/zjContractorInfo/' + id,
    method: 'get'
  })
}

// 新增承包商信息
export function addZjContractorInfo(data) {
  return request({
    url: '/contractor/zjContractorInfo',
    method: 'post',
    data: data
  })
}

// 修改承包商信息
export function updateZjContractorInfo(data) {
  return request({
    url: '/contractor/zjContractorInfo',
    method: 'put',
    data: data
  })
}

// 删除承包商信息
export function delZjContractorInfo(id) {
  return request({
    url: '/contractor/zjContractorInfo/' + id,
    method: 'delete'
  })
}
//管理人
export function queryUserTree() {
  return request({
    url: '/app/queryUserTree',
    method: 'get'
  })
}

// 获取承包商信息（支持黑名单状态筛选）
export function getContractorInfo(blacklistStatus) {
  return request({
    url: '/contractor/zjContractorInfo/getContractorInfo',
    method: 'get',
    params: { blacklistStatus }
  })
}

