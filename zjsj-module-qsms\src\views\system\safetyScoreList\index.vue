<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="120px">
      <!-- <el-form-item label="对象ID" prop="targetId">
        <el-input
          v-model="queryParams.targetId"
          placeholder="请输入对象ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="对象名称" prop="targetName">
        <el-input v-model="queryParams.targetName" placeholder="请输入对象名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="行为描述" prop="behaviorDesc">
        <el-input
          v-model="queryParams.behaviorDesc"
          placeholder="请输入行为描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="积分变动值" prop="points">
        <el-input
          v-model="queryParams.points"
          placeholder="请输入积分变动值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联业务ID" prop="referenceId">
        <el-input
          v-model="queryParams.referenceId"
          placeholder="请输入关联业务ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="行为发生时间" prop="behaviorTime">
        <el-date-picker v-model="queryParams.behaviorTime" clearable type="date" value-format="yyyy-MM-dd"
          placeholder="请选择行为发生时间" />
      </el-form-item>
      <el-form-item label="累计总积分" prop="totalPoints">
        <el-input v-model="queryParams.totalPoints" placeholder="请输入累计总积分" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="当月积分" prop="currentMonthPoints">
        <el-input v-model="queryParams.currentMonthPoints" placeholder="请输入当月积分" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="当季积分" prop="currentQuarterPoints">
        <el-input v-model="queryParams.currentQuarterPoints" placeholder="请输入当季积分" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="当年积分" prop="currentYearPoints">
        <el-input v-model="queryParams.currentYearPoints" placeholder="请输入当年积分" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="最新排名" prop="latestRank">
        <el-input v-model="queryParams.latestRank" placeholder="请输入最新排名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="排名变动" prop="rankChange">
        <el-input v-model="queryParams.rankChange" placeholder="请输入排名变动" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="积分操作人" prop="operator">
        <el-input v-model="queryParams.operator" placeholder="请输入积分操作人" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:ranking:add']" type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:ranking:edit']" type="success" plain icon="el-icon-edit" size="mini"
          :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:ranking:remove']" type="danger" plain icon="el-icon-delete" size="mini"
          :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:ranking:export']" type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="rankingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="积分对象类型" align="center" prop="targetType" />
      <!-- <el-table-column label="对象ID" align="center" prop="targetId" /> -->
      <el-table-column label="对象名称" align="center" prop="targetName" />
      <!-- <el-table-column
        label="行为类型：隐患排查/培训完成/无违章/安全操作/奖励/处罚"
        align="center"
        prop="behaviorType"
      /> -->
      <el-table-column label="行为描述" align="center" prop="behaviorDesc" />
      <el-table-column label="积分变动值" align="center" prop="points" />
      <!-- <el-table-column label="关联业务ID" align="center" prop="referenceId" />
      <el-table-column
        label="行为发生时间"
        align="center"
        prop="behaviorTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.behaviorTime, "{y}-{m}-{d}") }}</span>
        </template>
</el-table-column>
<el-table-column label="累计总积分" align="center" prop="totalPoints" />
<el-table-column label="当月积分" align="center" prop="currentMonthPoints" />
<el-table-column label="当季积分" align="center" prop="currentQuarterPoints" />
<el-table-column label="当年积分" align="center" prop="currentYearPoints" />
<el-table-column label="最新排名" align="center" prop="latestRank" />
<el-table-column label="排名变动" align="center" prop="rankChange" />
<el-table-column label="积分操作人" align="center" prop="operator" />
<el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" fixed="right" width="150" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-hasPermi="['system:ranking:edit']" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['system:ranking:remove']" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改安全积分排行榜（记录个人和部门积分及行为）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="对象ID" prop="targetId">
          <el-input v-model="form.targetId" placeholder="请输入对象ID" />
        </el-form-item>
        <el-form-item label="对象名称" prop="targetName">
          <el-input v-model="form.targetName" placeholder="请输入对象名称" />
        </el-form-item>
        <el-form-item label="行为描述" prop="behaviorDesc">
          <el-input v-model="form.behaviorDesc" placeholder="请输入行为描述" />
        </el-form-item>
        <el-form-item label="积分变动值" prop="points">
          <el-input v-model="form.points" placeholder="请输入积分变动值" />
        </el-form-item>
        <!-- <el-form-item label="关联业务ID" prop="referenceId">
          <el-input v-model="form.referenceId" placeholder="请输入关联业务ID" />
        </el-form-item> -->
        <el-form-item label="行为发生时间" prop="behaviorTime">
          <el-date-picker v-model="form.behaviorTime" clearable type="date" value-format="yyyy-MM-dd"
            placeholder="请选择行为发生时间" />
        </el-form-item>
        <el-form-item label="累计总积分" prop="totalPoints">
          <el-input v-model="form.totalPoints" placeholder="自动计算" readonly />
        </el-form-item>
        <el-form-item label="当月积分" prop="currentMonthPoints">
          <el-input v-model="form.currentMonthPoints" placeholder="请输入当月积分" @input="calculateTotalPoints" />
        </el-form-item>
        <el-form-item label="当季积分" prop="currentQuarterPoints">
          <el-input v-model="form.currentQuarterPoints" placeholder="请输入当季积分" @input="calculateTotalPoints" />
        </el-form-item>
        <el-form-item label="当年积分" prop="currentYearPoints">
          <el-input v-model="form.currentYearPoints" placeholder="请输入当年积分" @input="calculateTotalPoints" />
        </el-form-item>
        <el-form-item label="最新排名" prop="latestRank">
          <el-input v-model="form.latestRank" placeholder="请输入最新排名" />
        </el-form-item>
        <el-form-item label="排名变动" prop="rankChange">
          <el-input v-model="form.rankChange" placeholder="请输入排名变动" />
        </el-form-item>
        <el-form-item label="积分操作人" prop="operator">
          <el-input v-model="form.operator" placeholder="请输入积分操作人" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRanking,
  getRanking,
  delRanking,
  addRanking,
  updateRanking
} from '@/api/system/safetyScoreList/index'

export default {
  name: 'Ranking',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全积分排行榜（记录个人和部门积分及行为）表格数据
      rankingList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        targetType: null,
        targetId: null,
        targetName: null,
        behaviorType: null,
        behaviorDesc: null,
        points: null,
        referenceId: null,
        behaviorTime: null,
        totalPoints: null,
        currentMonthPoints: null,
        currentQuarterPoints: null,
        currentYearPoints: null,
        latestRank: null,
        rankChange: null,
        operator: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        targetType: [
          {
            required: true,
            message: '积分对象类型：个人/部门不能为空',
            trigger: 'change'
          }
        ],
        targetId: [
          { required: true, message: '对象ID不能为空', trigger: 'blur' }
        ],
        targetName: [
          { required: true, message: '对象名称不能为空', trigger: 'blur' }
        ],
        behaviorType: [
          {
            required: true,
            message:
              '行为类型：隐患排查/培训完成/无违章/安全操作/奖励/处罚不能为空',
            trigger: 'change'
          }
        ],
        behaviorDesc: [
          { required: true, message: '行为描述不能为空', trigger: 'blur' }
        ],
        points: [
          { required: true, message: '积分变动值不能为空', trigger: 'blur' }
        ],
        behaviorTime: [
          { required: true, message: '行为发生时间不能为空', trigger: 'blur' }
        ],
        // totalPoints: [
        //   { required: true, message: '累计总积分不能为空', trigger: 'blur' }
        // ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询安全积分排行榜（记录个人和部门积分及行为）列表 */
    getList() {
      this.loading = true
      listRanking(this.queryParams).then((response) => {
        this.rankingList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        targetType: null,
        targetId: null,
        targetName: null,
        behaviorType: null,
        behaviorDesc: null,
        points: null,
        referenceId: null,
        behaviorTime: null,
        totalPoints: null,
        currentMonthPoints: null,
        currentQuarterPoints: null,
        currentYearPoints: null,
        latestRank: null,
        rankChange: null,
        operator: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加安全积分排行榜（记录个人和部门积分及行为）'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getRanking(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改安全积分排行榜（记录个人和部门积分及行为）'
        // 加载数据后重新计算总积分
        this.$nextTick(() => {
          this.calculateTotalPoints()
        })
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateRanking(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addRanking(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm(
          '是否确认删除安全积分排行榜（记录个人和部门积分及行为）编号为"' +
          ids +
          '"的数据项？'
        )
        .then(function () {
          return delRanking(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'system/ranking/export',
        {
          ...this.queryParams
        },
        `ranking_${new Date().getTime()}.xlsx`
      )
    },
    /** 计算累计总积分 */
    calculateTotalPoints() {
      const currentMonthPoints = parseFloat(this.form.currentMonthPoints) || 0
      const currentQuarterPoints = parseFloat(this.form.currentQuarterPoints) || 0
      const currentYearPoints = parseFloat(this.form.currentYearPoints) || 0
      this.form.totalPoints = currentMonthPoints + currentQuarterPoints + currentYearPoints
    }
  }
}
</script>
