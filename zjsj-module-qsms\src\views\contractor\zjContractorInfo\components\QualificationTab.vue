<template>
  <div class="qualification-tab">
    <!-- 操作按钮区域 -->
    <div class="toolbar" style="margin-bottom: 20px;">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
    </div>
    
    <el-table v-loading="loading" :data="qualificationList" border style="width: 100%">
      <el-table-column prop="qualificationName" label="资质名称" align="left" min-width="200" />
      <el-table-column prop="qualificationNumber" label="资质编号" align="left" min-width="180" />
      <el-table-column prop="expirationEndDate" label="资质到期时间" align="center" min-width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expirationEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="qualificationType" label="资质类型" align="center" min-width="120">
        <template slot-scope="scope">
          <span>{{ getQualificationTypeText(scope.row.qualificationType) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="attachmentUrl" label="资质文件" align="center" min-width="120">
        <template slot-scope="scope">
          <el-button 
            v-if="scope.row.attachmentUrl" 
            type="text" 
            @click="handleViewFile(scope.row.attachmentUrl)"
          >
            查看文件
          </el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增资质信息弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资质信息" prop="qualificationName">
              <el-input v-model="form.qualificationName" placeholder="请输入资质信息" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资质编号" prop="certificateNumber">
              <el-input v-model="form.certificateNumber" placeholder="请输入资质编号" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="资质有效期" prop="validityPeriod">
              <el-date-picker
                v-model="form.validityPeriod"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资质等级" prop="qualificationLevel">
              <el-input v-model="form.qualificationLevel" placeholder="请输入资质等级" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资质类型" prop="qualificationType">
              <el-select v-model="form.qualificationType" placeholder="请选择资质类型" style="width: 100%">
                <el-option label="施工总承包" value="1" />
                <el-option label="专业承包" value="2" />
                <el-option label="劳务分包" value="3" />
                <el-option label="设计资质" value="4" />
                <el-option label="监理资质" value="5" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="发证机关" prop="issuingAuthority">
              <el-input v-model="form.issuingAuthority" placeholder="请输入发证机关" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="附件" prop="attachments">
              <file-upload v-model="form.attachments" :limit="5" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listZjContractorQualificationInfo, addZjContractorQualificationInfo } from "@/api/contractor/zjContractorQualificationInfo";

export default {
  name: "QualificationTab",
  props: {
    contractorId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      qualificationList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorId: null
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        qualificationName: [
          { required: true, message: "资质信息不能为空", trigger: "blur" }
        ],
        certificateNumber: [
          { required: true, message: "资质编号不能为空", trigger: "blur" }
        ],
        validityPeriod: [
          { required: true, message: "资质有效期不能为空", trigger: "change" }
        ],
        qualificationLevel: [
          { required: true, message: "资质等级不能为空", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    contractorId: {
      handler(newVal) {
        if (newVal) {
          this.queryParams.contractorId = newVal;
          this.getList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /** 查询资质信息列表 */
    getList() {
      if (!this.contractorId) return;
      this.loading = true;
      listZjContractorQualificationInfo(this.queryParams).then(response => {
        this.qualificationList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
        this.qualificationList = [];
        this.total = 0;
      });
    },

    /** 新增按钮操作 */
    handleAdd() {
      console.log('新增按钮被点击');
      this.reset();
      this.open = true;
      this.title = "新增资质信息";
      console.log('弹窗状态:', this.open);
    },

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        contractorId: this.contractorId,
        qualificationName: null,
        certificateNumber: null,
        validityPeriod: null,
        issueDate: null,
        validDate: null,
        qualificationLevel: null,
        qualificationType: null,
        issuingAuthority: null,
        status: 1,
        attachments: null
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理日期范围
          if (this.form.validityPeriod && this.form.validityPeriod.length === 2) {
            this.form.issueDate = this.form.validityPeriod[0];
            this.form.validDate = this.form.validityPeriod[1];
          }
          
          addZjContractorQualificationInfo(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError(error.msg || "新增失败");
          });
        }
      });
    },

    /** 获取资质类型文本 */
    getQualificationTypeText(type) {
      const typeMap = {
        '1': '施工总承包',
        '2': '专业承包',
        '3': '劳务分包',
        '4': '设计资质',
        '5': '监理资质'
      };
      return typeMap[type] || '-';
    },

    /** 查看文件 */
    handleViewFile(fileUrl) {
      if (fileUrl) {
        window.open(fileUrl, '_blank');
      } else {
        this.$modal.msgWarning("文件链接不存在");
      }
    }
  }
};
</script>

<style scoped>
.qualification-tab {
  padding: 20px;
}
</style>
