<template>
  <div class="safety-operation-container">
    <div class="header">
      <el-button icon="el-icon-arrow-left" class="back-btn" @click="handleBack">
        返回
      </el-button>
    </div>

    <div class="operation-cards">
      <!-- 临时用电安全作业 -->
      <div
        class="operation-group"
        v-for="(item, index) in workTicketType"
        :key="index"
      >
        <h3 class="group-title">{{ item.typeTitle }}</h3>
        <el-row :gutter="20">
          <el-col
            :span="7"
            v-for="item2 in item.workTicketTypeList"
            :key="item2.id"
          >
            <el-card
              class="operation-card"
              shadow="hover"
              @click.native="handleCardClick(item, item2)"
            >
              <div class="card-content">
                <div class="card-title">{{ item2.title }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SelectWork",

  props: {
    step: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      workTicketType: [
        {
          typeTitle: "临时用电安全作业",
          type: "lsyd",
          workTicketTypeList: [
            { id: 1, title: "测试", subtitle: "", type: "test" },
            {
              id: 2,
              title: "国际-临时用电安全作业证(非防爆区)",
              subtitle: "非防爆区",
              type: "temporary-electricity-non-explosion",
            },
            {
              id: 3,
              title: "国际-临时用电安全作业证(防爆区)",
              subtitle: "防爆区",
              type: "temporary-electricity-explosion",
            },
          ],
        },
        {
          typeTitle: "动火安全作业",
          type: "dh",
          workTicketTypeList: [
            {
              id: 4,
              title: "国际-动火安全作业证（二级）",
              subtitle: "二级",
              type: "fire-level2",
            },
            {
              id: 5,
              title: "国际-动火安全作业证（一级）",
              subtitle: "一级",
              type: "fire-level1",
            },
            {
              id: 6,
              title: "国际-动火安全作业证（特级）",
              subtitle: "特级",
              type: "fire-special",
            },
          ],
        },
        {
          typeTitle: "受限空间安全作业",
          type: "sx",
          workTicketTypeList: [
            {
              id: 7,
              title: "国际-受限空间安全作业证",
              subtitle: "",
              type: "confined-space",
            },
          ],
        },
        {
          typeTitle: "盲板抽堵安全作业",
          type: "mb",

          workTicketTypeList: [
            {
              id: 8,
              title: "国际-盲板抽堵安全作业证",
              subtitle: "",
              type: "blind-plate",
            },
          ],
        },
        {
          typeTitle: "吊装安全作业",
          type: "dz",

          workTicketTypeList: [
            {
              id: 9,
              title: "国标-吊装安全作业证 (三级)",
              subtitle: "",
              type: "blind-plate",
            },
            {
              id: 10,
              title: "国标-吊装安全作业证 (二级)",
              subtitle: "",
              type: "blind-plate",
            },
            {
              id: 11,
              title: "国标-吊装安全作业证（一级)",
              subtitle: "",
              type: "blind-plate",
            },
          ],
        },
        {
          typeTitle: "动土安全作业",
          type: "dt",
          workTicketTypeList: [
            {
              id: 12,
              title: "国标-动土安全作业证",
              subtitle: "",
              type: "blind-plate",
            },
          ],
        },
        {
          typeTitle: "断路安全作业",
          type: "dl",

          workTicketTypeList: [
            {
              id: 13,
              title: "国标-断路安全作业证",
              subtitle: "",
              type: "blind-plate",
            },
          ],
        },
        {
          typeTitle: "高处安全作业",
          type: "gc",

          workTicketTypeList: [
            {
              id: 14,
              title: "国标-高处安全作业证(四级)",
              subtitle: "",
              type: "blind-plate",
            },
            {
              id: 15,
              title: "国标-高处安全作业证 (三级)",
              subtitle: "",
              type: "blind-plate",
            },
            {
              id: 16,
              title: "国标-高处安全作业证 (二级)",
              subtitle: "",
              type: "blind-plate",
            },
            {
              id: 17,
              title: "国标-高处安全作业证 (一级)",
              subtitle: "",
              type: "blind-plate",
            },
          ],
        },
        {
          typeTitle: "其他",
          type: "qt",
          workTicketTypeList: [],
        },
      ],
    };
  },
  methods: {
    handleBack() {
      this.$emit("back");

      // 或者使用路由返回
      // this.$router.go(-1);
    },
    handleCardClick(item, item2) {
      console.log("点击了:", item);
      this.$emit("add", item, item2);
    },
  },
};
</script>

<style scoped>
.safety-operation-container {
  min-height: 100vh;
}

.header {
  margin-bottom: 30px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  /* 吸顶 */
  position: absolute;
  width: 101.5%;
  left: -20px;
  top: 0;
  z-index: 100;
}

.back-btn {
  background-color: #fff;
  color: #000;
  font-weight: 600;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  padding: 10px 20px;
}

.back-btn:hover {
  background-color: rgba(46, 50, 56, 0.05);
}
.operation-cards {
  height: calc(100vh - 100px);
  overflow: auto;
  margin-top: 50px;
}

.operation-group {
  /* margin-bottom: 30px; */
  background: white;
  padding: 20px;
  border-radius: 8px;
  /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
}

.group-title {
  color: #1c1f23;
  font-size: 20px;
  /* font-weight: bold; */
  margin-bottom: 20px;
  padding-bottom: 10px;
}

.operation-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f3f7fb;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  font-size: 14px;
}

.operation-card:hover {
  /* transform: translateY(-2px); */
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); */
  /* border-color: #409eff; */
  background: #e6f7ff;
}

.card-content {
  padding: 7px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-title {
  font-size: 14px;
  /* font-weight: bold; */
  color: #303133;
  /* margin-bottom: 8px; */
  /* line-height: 1.4; */
}

.card-subtitle {
  font-size: 14px;
  color: #909399;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-col {
    margin-bottom: 15px;
  }

  .operation-group {
    padding: 15px;
  }

  .card-content {
    padding: 15px;
  }

  .card-title {
    font-size: 14px;
  }
}
</style>
