import request from '@/utils/request'

// 查询海外人员信息管理（支持从国际工程数据同步）列表
export function listPersonnel(query) {
  return request({
    url: '/system/personnel/list',
    method: 'get',
    params: query
  })
}

// 查询海外人员信息管理（支持从国际工程数据同步）详细
export function getPersonnel(id) {
  return request({
    url: '/system/personnel/' + id,
    method: 'get'
  })
}

// 新增海外人员信息管理（支持从国际工程数据同步）
export function addPersonnel(data) {
  return request({
    url: '/system/personnel',
    method: 'post',
    data: data
  })
}

// 修改海外人员信息管理（支持从国际工程数据同步）
export function updatePersonnel(data) {
  return request({
    url: '/system/personnel',
    method: 'put',
    data: data
  })
}

// 删除海外人员信息管理（支持从国际工程数据同步）
export function delPersonnel(id) {
  return request({
    url: '/system/personnel/' + id,
    method: 'delete'
  })
}
