<template>
  <el-select
    ref="peopleSelectRef"
    :value="value"
    :placeholder="placeholder"
    :disabled="disabled"
    :visible="selectVisible"
    :filter-method="filterPeople"
    clearable
    filterable
    style="width: 100%"
    class="people-tree-select"
    popper-class="people-tree-dropdown"
    @input="handleInput"
    @change="handleSelectChange"
    @visible-change="handleVisibleChange"
    @focus="handleFocus"
    @clear="handleClear"
  >
    <el-option :value="value" style="height: auto; padding: 0">
      <el-tree
        ref="peopleTree"
        :data="treeData"
        :props="treeProps"
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        :class="['people-tree', { searching: searchText && searchText.trim() }]"
        node-key="id"
        highlight-current
        style="padding: 5px 0"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <i v-if="data.children && data.children.length" />

          <span
            :class="['tree-label', { department: data.type === '1' }]"
            v-html="highlightSearchText(node.label, searchText)"
          />
        </span>
      </el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    peopleList: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      searchText: "",
      selectVisible: false,
      searchTimer: null,
      listenerBound: false, // 防止重复绑定监听器
      inputHandler: null, // 存储输入处理函数引用
      keyupHandler: null, // 存储按键处理函数引用
      compositionHandler: null, // 存储组合输入处理函数引用

      treeProps: {
        label: "label",
        children: "children",
      },
    };
  },

  computed: {
    // 转换为树形结构数据
    treeData() {
      return this.processTreeData(this.peopleList);
    },
  },

  watch: {
    searchText: {
      immediate: true,
      handler(val) {
        // 使用 $nextTick 确保组件已完全渲染
        this.$nextTick(() => {
          if (this.$refs.peopleTree) {
            this.$refs.peopleTree.filter(val);

            // 搜索时自动展开匹配的父节点（无论是否首次）
            if (val && val.trim()) {
              // 使用短延迟确保过滤完成
              setTimeout(() => {
                this.expandMatchedNodes(val.trim());
              }, 150);
            } else {
              // 清空搜索时收起所有节点
              this.collapseAllNodes();
            }
          } else {
            // 如果树组件还没准备好，短暂延迟后重试
            setTimeout(() => {
              if (this.$refs.peopleTree && val === this.searchText) {
                this.$refs.peopleTree.filter(val);
                if (val && val.trim()) {
                  setTimeout(() => {
                    this.expandMatchedNodes(val.trim());
                  }, 100);
                }
              }
            }, 200);
          }
        });
      },
    },

    peopleList: {
      handler(newVal) {
        // 树数据更新后重新过滤
        this.$nextTick(() => {
          if (this.searchText) {
            this.$refs.peopleTree.filter(this.searchText);
          }
        });
      },
      deep: true,
    },
  },

  mounted() {
    // 组件挂载后的初始化
    this.$nextTick(() => {
      this.setupInputListener();
    });
  },

  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    // 清理事件监听器
    if (this.inputHandler || this.keyupHandler || this.compositionHandler) {
      const selectEl = this.$refs.peopleSelectRef;
      if (selectEl && selectEl.$el) {
        const input = selectEl.$el.querySelector("input");
        if (input) {
          input.removeEventListener("input", this.inputHandler);
          input.removeEventListener("keyup", this.keyupHandler);
          input.removeEventListener("compositionend", this.compositionHandler);
        }
      }
    }
  },

  methods: {
    // 设置输入监听器
    setupInputListener() {
      // 如果已经绑定过，跳过
      if (this.listenerBound) {
        return;
      }

      // 减少延迟，提高响应速度
      setTimeout(() => {
        const selectEl = this.$refs.peopleSelectRef;

        if (selectEl) {
          // 更详细的输入元素检测
          let actualInput = null;

          // 方式1: 通过选择器查找
          const inputBySelector = selectEl.$el
            ? selectEl.$el.querySelector("input")
            : null;
          if (inputBySelector) {
            actualInput = inputBySelector;
          }

          // 方式2: 通过 $refs.input
          else if (selectEl.$refs && selectEl.$refs.input) {
            if (selectEl.$refs.input.$el) {
              if (selectEl.$refs.input.$el.tagName === "INPUT") {
                actualInput = selectEl.$refs.input.$el;
              } else {
                const nestedInput =
                  selectEl.$refs.input.$el.querySelector("input");
                if (nestedInput) {
                  actualInput = nestedInput;
                }
              }
            } else if (selectEl.$refs.input.tagName === "INPUT") {
              actualInput = selectEl.$refs.input;
            }
          }

          if (actualInput && actualInput.tagName === "INPUT") {
            // 移除可能存在的旧监听器
            actualInput.removeEventListener("input", this.inputHandler);
            actualInput.removeEventListener("keyup", this.keyupHandler);
            actualInput.removeEventListener(
              "compositionend",
              this.compositionHandler
            );

            // 创建绑定的处理函数
            this.inputHandler = (e) => {
              this.filterPeople(e.target.value);
            };

            this.keyupHandler = (e) => {
              this.filterPeople(e.target.value);
            };

            this.compositionHandler = (e) => {
              this.filterPeople(e.target.value);
            };

            // 添加事件监听器
            actualInput.addEventListener("input", this.inputHandler);
            actualInput.addEventListener("keyup", this.keyupHandler);
            actualInput.addEventListener(
              "compositionend",
              this.compositionHandler
            );

            this.listenerBound = true;
          } else {
            // 如果找不到输入元素，稍后重试
            setTimeout(() => {
              this.listenerBound = false;
              this.setupInputListener();
            }, 300);
          }
        } else {
          setTimeout(() => {
            this.setupInputListener();
          }, 300);
        }
      }, 200); // 增加延迟确保DOM完全渲染
    },

    handleVisibleChange(isVisible) {
      if (isVisible) {
        // 下拉框打开时，强制重新设置监听器
        this.listenerBound = false;
        this.$nextTick(() => {
          this.setupInputListener();
        });

        // 如果有现有搜索文本，立即触发过滤和展开
        if (this.searchText) {
          this.$nextTick(() => {
            this.filterPeople(this.searchText);
          });
        }
      } else {
        // 关闭下拉框时清空搜索
        this.searchText = "";
        if (this.$refs.peopleTree) {
          this.$refs.peopleTree.filter("");
          this.collapseAllNodes();
        }
      }
    },

    handleFocus() {
      // 聚焦时确保监听器已设置
      if (!this.listenerBound) {
        this.$nextTick(() => {
          this.setupInputListener();
        });
      }
    },

    handleClear() {
      this.searchText = "";
      this.filterPeople("");
    },
    closeDropdown() {
      this.$refs.peopleSelectRef.blur();
    },

    // 处理树形数据
    // processTreeData(data) {
    //   return data.map((item) => ({
    //     ...item,
    //     label: item.label ? item.label.trim() : "",
    //     children: item.children ? this.processTreeData(item.children) : [],
    //   }));
    // },
    processTreeData(data, parent = null) {
      return data.map((item) => {
        // 构建当前节点，继承原有属性
        const currentNode = {
          ...item,
          label: item.label ? item.label.trim() : "",
          parentId: parent?.id ?? null,
          parentName: parent?.label ?? null,
          // 递归处理子节点，并将当前节点作为父节点传递
          children: item.children
            ? this.processTreeData(item.children, item)
            : [],
        };
        // console.log("currentNode", currentNode);
        return currentNode;
      });
    },

    // 节点过滤方法 - 增强的模糊搜索
    filterNode(value, data) {
      if (!value) return true;

      const searchValue = value.toLowerCase().trim();
      const label = data.label ? data.label.toLowerCase() : "";
      const name = data.name ? data.name.toLowerCase() : "";

      // 1. 精确匹配
      if (label.includes(searchValue) || name.includes(searchValue)) {
        return true;
      }

      // 2. 拼音首字母匹配
      if (
        this.matchPinyinInitials(label, searchValue) ||
        this.matchPinyinInitials(name, searchValue)
      ) {
        return true;
      }

      // 3. 分词匹配 - 支持空格分隔的多个关键词
      const keywords = searchValue.split(/\s+/).filter((k) => k.length > 0);
      if (keywords.length > 1) {
        return keywords.every(
          (keyword) =>
            label.includes(keyword) ||
            name.includes(keyword) ||
            this.matchPinyinInitials(label, keyword) ||
            this.matchPinyinInitials(name, keyword)
        );
      }

      return false;
    },

    // 节点点击事件
    handleNodeClick(data, node) {
      // console.log("点击当前节点", data, node);
      // 如果是 general-project 类型，可以选择
      if (data.type === "general-project") {
        this.handleSelectChange(data.id, data);

        // 更新树的高亮选择
        this.$refs.peopleTree.setCurrentKey(data.id);
        this.$nextTick(() => {
          this.closeDropdown();
        });
        return;
      }

      // 如果是其他类型且有子节点，切换展开状态
      if (data.children && data.children.length) {
        node.expanded = !node.expanded;
        if (data.type != null) {
          return;
        }
        // return;
      }

      // 如果是人员节点（type !== '1' 且不是 general-project），触发选择
      if (data.type !== "1") {
        this.handleSelectChange(data.id, data);

        // 更新树的高亮选择
        this.$refs.peopleTree.setCurrentKey(data.id);
        this.$nextTick(() => {
          this.closeDropdown();
        });
      }

      // 其他情况（没有子节点的节点）不做任何操作
    },

    handleInput(value) {
      this.$emit("input", value);
      // 主动调用 filterPeople 确保搜索生效
      this.filterPeople(value);
    },

    handleSelectChange(selectedId, selectedItem) {
      if (!selectedId) {
        this.$emit("change", null);
        return;
      }

      // const selectedItem = this.findNodeById(this.treeData, selectedId);
      // console.log("选择的信息", selectedItem);
      if (selectedItem && selectedItem.type !== "1") {
        const cleanItem = {
          id: selectedItem.id,
          label: selectedItem.label || "",
          name: selectedItem.name || "",
          type: selectedItem.type,
          parentId: selectedItem.parentId,
          parentName: selectedItem.parentName,
        };

        this.$emit("change", cleanItem);
      }
    },

    // 根据ID查找节点
    findNodeById(nodes, id) {
      for (const node of nodes) {
        if (node.id === id) return node;
        if (node.children && node.children.length) {
          const found = this.findNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },

    // 拼音首字母匹配方法
    matchPinyinInitials(text, searchValue) {
      if (!text || !searchValue) return false;

      // 简单的中文拼音首字母映射
      const pinyinMap = {
        a: "阿啊",
        b: "不把白本部被北备比表必标",
        c: "从程成产出常场长城创传",
        d: "的大都到道地点电当得对多段",
        e: "二",
        f: "发方法分风服费发放防发",
        g: "工个过公管国广高改工构",
        h: "和好后会化回还海合黄行",
        i: "一",
        j: "就进建交机计经检基级见技结",
        k: "可看科开口控课快客",
        l: "了来理里立流料量联力领路类",
        m: "没么面民明名目模美门",
        n: "年能内南那女你农",
        o: "哦",
        p: "平配品排培破片跑",
        q: "去其全清情期前起请求区",
        r: "人如让日然认入任",
        s: "是时实施所说三设生手市上四十",
        t: "他通同体统头条特提图天",
        u: "有用于",
        v: "",
        w: "我为文物位问外王万五网维",
        x: "现系性新学小心选许信下项行西",
        y: "一要用有以业已应意音元月研运",
        z: "中在这者主专注资制知至重组",
      };

      // 尝试拼音首字母匹配
      for (let i = 0; i < searchValue.length; i++) {
        const char = searchValue[i];
        const pinyinChars = pinyinMap[char];
        if (pinyinChars && i < text.length) {
          if (!pinyinChars.includes(text[i])) {
            return false;
          }
        } else if (char !== text[i]) {
          return false;
        }
      }

      return searchValue.length > 0 && searchValue.length <= text.length;
    },

    // 即时搜索方法 - 移除防抖，确保立即响应
    filterPeople(value) {
      // 立即更新搜索文本并触发过滤
      this.searchText = value;

      // 立即触发树过滤
      if (this.$refs.peopleTree) {
        this.$refs.peopleTree.filter(value);

        // 如果有搜索值，立即展开匹配的节点
        if (value && value.trim()) {
          // 使用 setTimeout 确保过滤完成后再展开
          setTimeout(() => {
            this.expandMatchedNodes(value.trim());
          }, 50);
        }
      }

      // 对于 filter-method，我们总是返回 true，让所有选项都显示
      // 因为我们的过滤逻辑是在树组件内部处理的
      return true;
    },

    // 增强expandMatchedNodes方法，确保节点正确展开
    expandMatchedNodes(searchValue) {
      if (!this.$refs.peopleTree || !searchValue) return;

      const expandedKeys = [];
      this.collectExpandedNodes(this.treeData, searchValue, expandedKeys);

      // 去重并确保展开逻辑生效
      const uniqueKeys = [...new Set(expandedKeys)];

      // 立即展开节点，不使用 nextTick 延迟
      uniqueKeys.forEach((key) => {
        const node = this.$refs.peopleTree.store.nodesMap[key];
        if (node && !node.expanded) {
          node.expand();
        }
      });
    },

    // 收起所有节点的方法
    collapseAllNodes() {
      if (!this.$refs.peopleTree) return;

      const allNodes = this.$refs.peopleTree.store.nodesMap;
      Object.values(allNodes).forEach((node) => {
        if (node.expanded && node.childNodes && node.childNodes.length > 0) {
          node.collapse();
        }
      });
    },

    // 递归收集需要展开的节点
    collectExpandedNodes(nodes, searchValue, expandedKeys, parentKey = null) {
      let hasMatchedChild = false;

      for (const node of nodes) {
        // 检查当前节点是否匹配
        if (this.filterNode(searchValue, node)) {
          hasMatchedChild = true;

          // 如果有父节点，添加到展开列表
          if (parentKey) {
            expandedKeys.push(parentKey);
          }
        }

        // 递归检查子节点
        if (node.children && node.children.length > 0) {
          const childMatched = this.collectExpandedNodes(
            node.children,
            searchValue,
            expandedKeys,
            node.id
          );

          if (childMatched) {
            hasMatchedChild = true;
            // 如果子节点有匹配，当前节点也需要展开
            if (node.id) {
              expandedKeys.push(node.id);
            }
            // 如果有父节点，父节点也需要展开
            if (parentKey) {
              expandedKeys.push(parentKey);
            }
          }
        }
      }

      return hasMatchedChild;
    },

    // 高亮搜索文本
    highlightSearchText(text, searchValue) {
      if (!text) return "";
      if (!searchValue || !searchValue.trim()) return text;

      const searchText = searchValue.trim();
      // 防止XSS攻击，转义HTML特殊字符
      const escapedText = text.replace(/[&<>"']/g, function (match) {
        const escapeMap = {
          "&": "&amp;",
          "<": "&lt;",
          ">": "&gt;",
          '"': "&quot;",
          "'": "&#x27;",
        };
        return escapeMap[match];
      });

      // 如果搜索文本包含在显示文本中，高亮显示
      if (escapedText.toLowerCase().includes(searchText.toLowerCase())) {
        const regex = new RegExp(
          `(${searchText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
          "gi"
        );
        return escapedText.replace(
          regex,
          '<span class="search-highlight">$1</span>'
        );
      }

      return escapedText;
    },
  },
};
</script>

<style scoped>
.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-icon {
  margin-right: 6px;
  font-size: 14px;
  color: #909399;
}

.tree-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 调整树组件样式 */
:deep(.people-tree) {
  min-width: 180px;
  width: auto;
}

/* 控制下拉框宽度 - 使用特定class确保只影响这个组件 */
:deep(.people-tree-dropdown) {
  max-width: 250px !important;
  min-width: 180px !important;
  width: auto !important;
}

/* 全局样式 - 更高优先级 */
::v-deep .people-tree-dropdown.el-select-dropdown {
  max-width: 250px !important;
  min-width: 180px !important;
  width: auto !important;
}

:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(
    .el-tree--highlight-current
      .el-tree-node.is-current
      > .el-tree-node__content
  ) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item) {
  padding: 0;
  height: auto;
}

/* 搜索高亮样式 */
:deep(.search-highlight) {
  background-color: #fffacd;
  color: #d32f2f;
  font-weight: bold;
  padding: 1px 2px;
  border-radius: 2px;
}
</style>

<style>
/* 全局样式 - 不受scoped限制，确保能够覆盖Element UI的默认样式 */
.people-tree-dropdown.el-select-dropdown {
  max-width: 650px !important;
  min-width: 650px !important;
  width: auto !important;
}

.people-tree-dropdown .el-select-dropdown__item {
  padding: 0;
  height: auto;
}
</style>
