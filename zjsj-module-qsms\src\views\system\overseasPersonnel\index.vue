<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="人员编号" prop="personnelNo">
        <el-input
          v-model="queryParams.personnelNo"
          placeholder="请输入人员编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="请输入身份证号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="护照号码" prop="passportNo">
        <el-input
          v-model="queryParams.passportNo"
          placeholder="请输入护照号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="出生日期" prop="birthDate">
        <el-date-picker
          clearable
          v-model="queryParams.birthDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择出生日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="国籍" prop="nationality">
        <el-input
          v-model="queryParams.nationality"
          placeholder="请输入国籍"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属海外项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入所属海外项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入所属部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="岗位" prop="position">
        <el-input
          v-model="queryParams.position"
          placeholder="请输入岗位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="派驻海外开始日期" prop="overseasStartDate">
        <el-date-picker
          clearable
          v-model="queryParams.overseasStartDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择派驻海外开始日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划回国日期" prop="plannedReturnDate">
        <el-date-picker
          clearable
          v-model="queryParams.plannedReturnDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划回国日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="实际回国日期" prop="actualReturnDate">
        <el-date-picker
          clearable
          v-model="queryParams.actualReturnDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择实际回国日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="所在国家" prop="country">
        <el-input
          v-model="queryParams.country"
          placeholder="请输入所在国家"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所在城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入所在城市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="具体位置" prop="specificLocation">
        <el-input
          v-model="queryParams.specificLocation"
          placeholder="请输入具体位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="当地联系方式" prop="localContact">
        <el-input
          v-model="queryParams.localContact"
          placeholder="请输入当地联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="国内紧急联系人" prop="emergencyContact">
        <el-input
          v-model="queryParams.emergencyContact"
          placeholder="请输入国内紧急联系人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="紧急联系电话" prop="emergencyPhone">
        <el-input
          v-model="queryParams.emergencyPhone"
          placeholder="请输入紧急联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="护照有效期至" prop="passportValidUntil">
        <el-date-picker
          clearable
          v-model="queryParams.passportValidUntil"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择护照有效期至"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="签证有效期至" prop="visaValidUntil">
        <el-date-picker
          clearable
          v-model="queryParams.visaValidUntil"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择签证有效期至"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="工作许可编号" prop="workPermitNo">
        <el-input
          v-model="queryParams.workPermitNo"
          placeholder="请输入工作许可编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作许可有效期至" prop="workPermitValidUntil">
        <el-date-picker
          clearable
          v-model="queryParams.workPermitValidUntil"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择工作许可有效期至"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-input
          v-model="queryParams.dataSource"
          placeholder="请输入数据来源"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最后同步时间" prop="lastSyncTime">
        <el-date-picker
          clearable
          v-model="queryParams.lastSyncTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择最后同步时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:personnel:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:personnel:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:personnel:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:personnel:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="personnelList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="人员编号" align="center" prop="personnelNo" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="身份证号" align="center" prop="idCard" />
      <el-table-column label="护照号码" align="center" prop="passportNo" />
      <el-table-column label="性别" align="center" prop="gender" />
      <el-table-column
        label="出生日期"
        align="center"
        prop="birthDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.birthDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="国籍" align="center" prop="nationality" />
      <el-table-column
        label="所属海外项目名称"
        align="center"
        prop="projectName"
        width="140"
      />
      <el-table-column label="项目编码" align="center" prop="projectCode" />
      <el-table-column label="所属部门" align="center" prop="department" />
      <el-table-column label="岗位" align="center" prop="position" />
      <!-- <el-table-column
        label="用工类型：正式/合同/临时"
        align="center"
        prop="employmentType"
      />
      <el-table-column
        label="派驻海外开始日期"
        align="center"
        prop="overseasStartDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.overseasStartDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划回国日期"
        align="center"
        prop="plannedReturnDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.plannedReturnDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="实际回国日期"
        align="center"
        prop="actualReturnDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.actualReturnDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="当前状态：在海外/休假中/已回国/其他"
        align="center"
        prop="currentStatus"
      />
      <el-table-column label="所在国家" align="center" prop="country" />
      <el-table-column label="所在城市" align="center" prop="city" />
      <el-table-column
        label="具体位置"
        align="center"
        prop="specificLocation"
      />
      <el-table-column
        label="当地联系方式"
        align="center"
        prop="localContact"
      />
      <el-table-column
        label="国内紧急联系人"
        align="center"
        prop="emergencyContact"
      />
      <el-table-column
        label="紧急联系电话"
        align="center"
        prop="emergencyPhone"
      />
      <el-table-column
        label="护照有效期至"
        align="center"
        prop="passportValidUntil"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.passportValidUntil, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签证类型" align="center" prop="visaType" />
      <el-table-column
        label="签证有效期至"
        align="center"
        prop="visaValidUntil"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.visaValidUntil, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="工作许可编号"
        align="center"
        prop="workPermitNo"
      />
      <el-table-column
        label="工作许可有效期至"
        align="center"
        prop="workPermitValidUntil"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.workPermitValidUntil, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="dataSource" />
      <el-table-column
        label="最后同步时间"
        align="center"
        prop="lastSyncTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastSyncTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="同步状态" align="center" prop="syncStatus" />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:personnel:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:personnel:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改海外人员信息管理（支持从国际工程数据同步）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="人员编号" prop="personnelNo">
          <el-input v-model="form.personnelNo" placeholder="请输入人员编号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="护照号码" prop="passportNo">
          <el-input v-model="form.passportNo" placeholder="请输入护照号码" />
        </el-form-item>
        <el-form-item label="出生日期" prop="birthDate">
          <el-date-picker
            clearable
            v-model="form.birthDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出生日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="国籍" prop="nationality">
          <el-input v-model="form.nationality" placeholder="请输入国籍" />
        </el-form-item>
        <el-form-item label="所属海外项目名称" prop="projectName">
          <el-input
            v-model="form.projectName"
            placeholder="请输入所属海外项目名称"
          />
        </el-form-item>
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="所属部门" prop="department">
          <el-input v-model="form.department" placeholder="请输入所属部门" />
        </el-form-item>
        <el-form-item label="岗位" prop="position">
          <el-input v-model="form.position" placeholder="请输入岗位" />
        </el-form-item>
        <el-form-item label="派驻海外开始日期" prop="overseasStartDate">
          <el-date-picker
            clearable
            v-model="form.overseasStartDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择派驻海外开始日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划回国日期" prop="plannedReturnDate">
          <el-date-picker
            clearable
            v-model="form.plannedReturnDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划回国日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际回国日期" prop="actualReturnDate">
          <el-date-picker
            clearable
            v-model="form.actualReturnDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择实际回国日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所在国家" prop="country">
          <el-input v-model="form.country" placeholder="请输入所在国家" />
        </el-form-item>
        <el-form-item label="所在城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入所在城市" />
        </el-form-item>
        <el-form-item label="具体位置" prop="specificLocation">
          <el-input
            v-model="form.specificLocation"
            placeholder="请输入具体位置"
          />
        </el-form-item>
        <el-form-item label="当地联系方式" prop="localContact">
          <el-input
            v-model="form.localContact"
            placeholder="请输入当地联系方式"
          />
        </el-form-item>
        <el-form-item label="国内紧急联系人" prop="emergencyContact">
          <el-input
            v-model="form.emergencyContact"
            placeholder="请输入国内紧急联系人"
          />
        </el-form-item>
        <el-form-item label="紧急联系电话" prop="emergencyPhone">
          <el-input
            v-model="form.emergencyPhone"
            placeholder="请输入紧急联系电话"
          />
        </el-form-item>
        <el-form-item label="护照有效期至" prop="passportValidUntil">
          <el-date-picker
            clearable
            v-model="form.passportValidUntil"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择护照有效期至"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="签证有效期至" prop="visaValidUntil">
          <el-date-picker
            clearable
            v-model="form.visaValidUntil"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择签证有效期至"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="工作许可编号" prop="workPermitNo">
          <el-input
            v-model="form.workPermitNo"
            placeholder="请输入工作许可编号"
          />
        </el-form-item>
        <el-form-item label="工作许可有效期至" prop="workPermitValidUntil">
          <el-date-picker
            clearable
            v-model="form.workPermitValidUntil"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择工作许可有效期至"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="数据来源" prop="dataSource">
          <el-input v-model="form.dataSource" placeholder="请输入数据来源" />
        </el-form-item>
        <el-form-item label="最后同步时间" prop="lastSyncTime">
          <el-date-picker
            clearable
            v-model="form.lastSyncTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择最后同步时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPersonnel,
  getPersonnel,
  delPersonnel,
  addPersonnel,
  updatePersonnel,
} from "@/api/system/overseasPersonnel/index";

export default {
  name: "Personnel",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 海外人员信息管理（支持从国际工程数据同步）表格数据
      personnelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        personnelNo: null,
        name: null,
        idCard: null,
        passportNo: null,
        gender: null,
        birthDate: null,
        nationality: null,
        projectName: null,
        projectCode: null,
        department: null,
        position: null,
        employmentType: null,
        overseasStartDate: null,
        plannedReturnDate: null,
        actualReturnDate: null,
        currentStatus: null,
        country: null,
        city: null,
        specificLocation: null,
        localContact: null,
        emergencyContact: null,
        emergencyPhone: null,
        passportValidUntil: null,
        visaType: null,
        visaValidUntil: null,
        workPermitNo: null,
        workPermitValidUntil: null,
        dataSource: null,
        lastSyncTime: null,
        syncStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        personnelNo: [
          { required: true, message: "人员编号不能为空", trigger: "blur" },
        ],
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        idCard: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
        ],
        passportNo: [
          { required: true, message: "护照号码不能为空", trigger: "blur" },
        ],
        projectName: [
          {
            required: true,
            message: "所属海外项目名称不能为空",
            trigger: "blur",
          },
        ],
        position: [
          { required: true, message: "岗位不能为空", trigger: "blur" },
        ],
        overseasStartDate: [
          {
            required: true,
            message: "派驻海外开始日期不能为空",
            trigger: "blur",
          },
        ],
        country: [
          { required: true, message: "所在国家不能为空", trigger: "blur" },
        ],
        emergencyContact: [
          {
            required: true,
            message: "国内紧急联系人不能为空",
            trigger: "blur",
          },
        ],
        emergencyPhone: [
          { required: true, message: "紧急联系电话不能为空", trigger: "blur" },
        ],
        passportValidUntil: [
          { required: true, message: "护照有效期至不能为空", trigger: "blur" },
        ],
        visaValidUntil: [
          { required: true, message: "签证有效期至不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询海外人员信息管理（支持从国际工程数据同步）列表 */
    getList() {
      this.loading = true;
      listPersonnel(this.queryParams).then((response) => {
        this.personnelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        personnelNo: null,
        name: null,
        idCard: null,
        passportNo: null,
        gender: null,
        birthDate: null,
        nationality: null,
        projectName: null,
        projectCode: null,
        department: null,
        position: null,
        employmentType: null,
        overseasStartDate: null,
        plannedReturnDate: null,
        actualReturnDate: null,
        currentStatus: null,
        country: null,
        city: null,
        specificLocation: null,
        localContact: null,
        emergencyContact: null,
        emergencyPhone: null,
        passportValidUntil: null,
        visaType: null,
        visaValidUntil: null,
        workPermitNo: null,
        workPermitValidUntil: null,
        dataSource: null,
        lastSyncTime: null,
        syncStatus: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加海外人员信息管理（支持从国际工程数据同步）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPersonnel(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改海外人员信息管理（支持从国际工程数据同步）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatePersonnel(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPersonnel(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除海外人员信息管理（支持从国际工程数据同步）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delPersonnel(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/personnel/export",
        {
          ...this.queryParams,
        },
        `personnel_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
