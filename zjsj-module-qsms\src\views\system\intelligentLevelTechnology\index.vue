<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="所属项目" prop="projectName">
        <selectPeopleTree
          v-model="queryParams.projectName"
          :people-list="projectList"
          placeholder="请选择所属项目"
          @change="handleSearchProjectChange"
          style="width: 300px"
        />
      </el-form-item>
      <el-form-item label="所属公司" prop="companyName">
        <selectPeopleTree
          ref="chargePersonName"
          v-model="queryParams.companyName"
          :people-list="companyList"
          placeholder="请搜索或选择所属公司"
          @change="handleSearchCompanyChange"
        />
      </el-form-item>
      <el-form-item label="智能技术" prop="techId">
        <el-select
          v-model="queryParams.techId"
          placeholder="请选择智能技术"
          style="width: 100%"
        >
          <el-option
            v-for="item in smartTechDictionaryList"
            :key="item.id"
            :label="item.techName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:smartTechApplication:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:smartTechApplication:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:smartTechApplication:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="smartTechApplicationList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="所属项目" align="center" prop="projectName" />
      <el-table-column label="所属公司" align="center" prop="companyName" />
      <el-table-column
        label="应用考核时间"
        align="center"
        prop="snapshotDate"
        width="180"
      />
      <el-table-column label="技术标准名称" align="center" prop="techName" />
      <!-- <el-table-column label="技术分类" align="center" prop="techCategory" /> -->
      <el-table-column label="技术分类" align="center" prop="techCategory">
        <template slot-scope="scope">
          <span>{{
            getDictLabel(techCategoryOptions, scope.row.techCategory) ||
            scope.row.techCategory ||
            "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="应用状态"
        align="center"
        prop="applicationStatus"
        :formatter="formatStatus"
      />
      <el-table-column
        label="该项技术得分"
        align="center"
        prop="evaluationScore"
      />
      <el-table-column label="评分人" align="center" prop="nickName" />
      <el-table-column
        label="评分时间"
        align="center"
        prop="evaluationTime"
        width="180"
      />
      <!-- <el-table-column label="附件" align="center" prop="smartUpgradeProjects">
        <template slot-scope="scope">
          <div v-if="scope.row.smartUpgradeProjects" class="contract-file">
            <div
              v-for="(item, index) in scope.row.smartUpgradeProjects.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:smartTechApplication:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:smartTechApplication:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改智能化技术应用记录对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="140px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-form-item label="所属项目" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择所属项目"
            @change="handleFormSearchProjectChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="所属公司" prop="companyName">
          <selectPeopleTree
            v-model="form.companyName"
            :people-list="companyList"
            placeholder="请选择所属公司"
            @change="handleFormSearchCompanyChange"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="智能技术" prop="techName">
          <el-select
            v-model="form.techName"
            placeholder="请选择智能技术"
            style="width: 100%"
            @change="handleTechIdSelectChange"
            value-key="id"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in smartTechDictionaryList"
              :key="item.id"
              :label="item.techName"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="应用考核时间" prop="snapshotDate">
          <el-date-picker
            clearable
            v-model="form.snapshotDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择应用考核时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="应用状态" prop="applicationStatus">
          <el-select
            v-model="form.applicationStatus"
            placeholder="请选择应用状态"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in applicationStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="智能技术ID" prop="techId">
          <el-input v-model="form.techId" placeholder="请输入智能技术ID" />
        </el-form-item>
        <el-form-item label="技术标准名称" prop="techName">
          <el-input v-model="form.techName" placeholder="请输入技术标准名称" />
        </el-form-item>
        <el-form-item label="技术分类" prop="techCategory">
          <el-input v-model="form.techCategory" placeholder="请输入技术分类" />
        </el-form-item> -->
        <el-form-item label="应用情况描述" prop="applicationDesc">
          <el-input
            v-model="form.applicationDesc"
            type="textarea"
            placeholder="请输入应用情况描述"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="该项技术得分" prop="evaluationScore">
          <el-input
            v-model="form.evaluationScore"
            placeholder="请输入该项技术得分"
            @input="handleEvaluationScoreInput"
            @blur="handleEvaluationScoreBlur"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="评分人" prop="evaluatorUserId">
          <el-select
            v-model="form.evaluatorUserId"
            placeholder="请选择评分人"
            style="width: 100%"
            filterable
            :filter-method="handleFilter"
            @visible-change="handleVisibleChange"
            :disabled="isCheck"
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评分时间" prop="evaluationTime">
          <el-date-picker
            clearable
            v-model="form.evaluationTime"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择评分时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="附件" prop="smartUpgradeProjects">
          <file-upload
            v-model="form.smartUpgradeProjects"
            :disabled="isCheck"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSmartTechApplication,
  getSmartTechApplication,
  delSmartTechApplication,
  addSmartTechApplication,
  updateSmartTechApplication,
} from "@/api/system/intelligentLevelTechnology/index";
import { listSmartTechDictionary } from "@/api/system/smartTechDictionary/index";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { getDicts } from "@/api/system/dict/data";
import { getFileOrignalName } from "@/utils/common.js";
import { querytree } from "@/api/system/info";
import { getEnterpriseInfo } from "@/api/system/info";
import { listUser } from "@/api/system/user";
export default {
  name: "SmartTechApplication",
  components: {
    selectPeopleTree,
  },
  data() {
    return {
      userList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      applicationStatusOptions: [
        {
          label: "已应用",
          value: 1,
        },
        {
          label: "计划应用中",
          value: 2,
        },
        {
          label: "未应用",
          value: 3,
        },
      ],
      isCheck: false,
      projectList: [],
      companyList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 智能化技术应用记录表格数据
      smartTechApplicationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        projectName: null,
        companyId: null,
        companyName: null,
        snapshotDate: null,
        techId: null,
        techName: null,
        techCategory: null,
        applicationStatus: null,
        applicationDesc: null,
        evaluationScore: null,
        evaluatorUserId: null,
        evaluatorUserName: null,
        evaluationTime: null,
        smartUpgradeProjects: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectName: [
          {
            required: true,
            message: "所属项目不能为空",
            trigger: "blur",
          },
        ],
        companyName: [
          {
            required: true,
            message: "所属公司不能为空",
            trigger: "blur",
          },
        ],
        snapshotDate: [
          { required: true, message: "应用考核时间不能为空", trigger: "blur" },
        ],
        techId: [
          { required: true, message: "智能技术不能为空", trigger: "blur" },
        ],
        techName: [
          { required: true, message: "技术标准名称不能为空", trigger: "blur" },
        ],
        techCategory: [
          { required: true, message: "技术分类不能为空", trigger: "blur" },
        ],
        applicationStatus: [
          { required: true, message: "应用状态不能为空", trigger: "change" },
        ],
      },
      smartTechDictionaryList: [],
      allOptions: [],
      isSearching: false,
      showCount: 500,
      techCategoryOptions: [],
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    this.getCompanyList();
    this.getListSmartTechDictionary();
    this.getUserList();
    this.loadDict();
  },
  methods: {
    getDictLabel(dictOptions, value) {
      if (!dictOptions || !Array.isArray(dictOptions) || !value) {
        return "";
      }
      const dict = dictOptions.find(
        (item) => item.dictValue === value || item.dictValue === String(value)
      );
      return dict ? dict.dictLabel : "";
    },
    loadDict() {
      getDicts("technical_classification").then((response) => {
        this.techCategoryOptions = response.data;
      });
    },
    getUserList() {
      listUser({
        pageNum: 1,
        pageSize: 9999999,
      }).then((response) => {
        let tempUserList = response.rows;
        this.allOptions = tempUserList.map((item) => {
          return {
            userId: item.userId,
            userName: item.userName,
            nickName: item.nickName,
          };
        });
        this.userList = this.allOptions.slice(0, this.showCount);
      });
    },
    handleFilter(query) {
      this.isSearching = true;
      if (!query) {
        this.userList = this.allOptions.slice(0, this.showCount);
      } else {
        const lowerQuery = query.toString().toLowerCase();
        this.userList = this.allOptions.filter((item) =>
          item.nickName.toLowerCase().includes(lowerQuery)
        );
      }
    },
    handleVisibleChange(visible) {
      if (!visible) {
        this.isSearching = false;
        this.userList = this.allOptions.slice(0, this.showCount);
      }
    },
    handleEvaluationScoreInput(value) {
      let val = value.replace(/[^\d.]/g, "");
      val = val.replace(/\.{2,}/g, ".");
      val = val.replace(/^\./g, "");
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      if (!val || val === ".") {
        val = "0";
      }
      this.form.evaluationScore = val;
    },
    handleEvaluationScoreBlur(value) {
      let val = value.replace(/[^\d.]/g, "");
      if (val && val[val.length - 1] === ".") {
        val = "0";
        this.form.evaluationScore = val;
      }
    },
    handleTechIdSelectChange(selectedObj) {
      if (selectedObj) {
        this.form.techId = selectedObj.id;
        this.form.techName = selectedObj.techName;
        this.form.techCategory = selectedObj.techCategory;
      } else {
        this.form.techId = null;
        this.form.techName = null;
        this.form.techCategory = null;
      }
    },
    formatStatus(row, column, value) {
      const option = this.applicationStatusOptions.find(
        (item) => item.value == value
      );
      return option ? option.label : value;
    },
    getListSmartTechDictionary() {
      listSmartTechDictionary({
        pageNum: 1,
        pageSize: 99999,
        isActive: 1,
      }).then((response) => {
        this.smartTechDictionaryList = response.rows;
      });
    },
    handleSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectId = selectedItem.id;
        this.queryParams.projectName = selectedItem.label;
      } else {
        this.queryParams.projectId = null;
      }
    },
    handleSearchCompanyChange(selectedItem) {
      if (selectedItem) {
        this.queryParams.companyName = selectedItem.label;
        this.queryParams.companyId = selectedItem.id;
      } else {
        this.queryParams.companyName = null;
        this.queryParams.companyId = null;
      }
    },
    handleFormSearchProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectId = selectedItem.id;
        this.form.projectName = selectedItem.label;
      } else {
        this.form.projectId = null;
        this.form.projectName = null;
      }
    },
    handleFormSearchCompanyChange(selectedItem) {
      if (selectedItem) {
        this.form.companyId = selectedItem.id;
        this.form.companyName = selectedItem.label;
      } else {
        this.form.companyId = null;
        this.form.companyName = null;
      }
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    getFileOrignalName,
    /** 查询智能化技术应用记录列表 */
    getList() {
      this.loading = true;
      listSmartTechApplication(this.queryParams).then((response) => {
        this.smartTechApplicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        projectName: null,
        companyId: null,
        companyName: null,
        snapshotDate: null,
        techId: null,
        techName: null,
        techCategory: null,
        applicationStatus: null,
        applicationDesc: null,
        evaluationScore: null,
        evaluatorUserId: null,
        evaluatorUserName: null,
        evaluationTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        smartUpgradeProjects: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.companyId = null;
      this.queryParams.projectId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加智能化技术应用记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSmartTechApplication(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改智能化技术应用记录";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getSmartTechApplication(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "查看智能化技术应用记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSmartTechApplication(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSmartTechApplication(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除智能化技术应用记录编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSmartTechApplication(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/smartTechApplication/export",
        {
          ...this.queryParams,
        },
        `智能化水平技术应用.xlsx`
      );
    },
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>

