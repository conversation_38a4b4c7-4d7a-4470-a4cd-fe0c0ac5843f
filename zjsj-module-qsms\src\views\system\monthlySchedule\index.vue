<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="120px"
    >
      <el-form-item label="所属公司" prop="company">
        <selectPeopleTree
          ref="chargePersonName1"
          v-model="queryParams.company"
          :people-list="companyList"
          placeholder="请搜索或选择所属公司"
          @change="handleSearchChange"
        />
      </el-form-item>
      <el-form-item label="预算年度" prop="budgetYear">
        <el-date-picker
          v-model="queryParams.budgetYear"
          clearable
          type="year"
          value-format="yyyy"
          placeholder="请选择预算年度"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="实际投入月" prop="actualInputMonth">
        <!-- <el-input
          v-model="queryParams.actualInputMonth"
          placeholder="请输入实际投入月"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <el-select
          v-model="queryParams.actualInputMonth"
          placeholder="请选择实际投入月"
        >
          <el-option
            v-for="item in actualInputMonthOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:input:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:input:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:input:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="inputList"
      @selection-change="handleSelectionChange"
      max-height="1000"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column
        label="预算年度"
        align="center"
        prop="budgetYear"
        width="100"
      />
      <el-table-column label="所属公司" align="center" prop="company" />
      <el-table-column
        label="投入类型"
        align="center"
        prop="inputType"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        label="实际投入金额"
        align="center"
        prop="actualInputAmount"
      />
      <el-table-column
        label="投入说明"
        align="center"
        prop="inputDescription"
      />
      <!-- <el-table-column label="投入细则" align="center" prop="inputRules" /> -->
      <el-table-column
        label="实际投入月"
        align="center"
        prop="actualInputMonth"
        width="100"
      />
      <el-table-column label="附件" align="center" prop="attachment">
        <template slot-scope="scope">
          <div v-if="scope.row.attachment" class="contract-file">
            <div
              v-for="(item, index) in scope.row.attachment.split(',')"
              :key="index"
              class="attachment-item"
              @click="handleAttach(item)"
            >
              {{ getFileOrignalName(item) }}
              <!-- {{ item.split("/").pop() }} -->
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            v-hasPermi="['system:input:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['system:input:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全投入月度记录表对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="160px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属公司" prop="company">
              <selectPeopleTree
                ref="chargePersonName1"
                v-model="form.company"
                :people-list="companyList"
                placeholder="请搜索或选择所属公司"
                @change="handleChange"
                :disabled="isCheck"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预算年度" prop="budgetYear">
              <el-date-picker
                v-model="form.budgetYear"
                clearable
                type="year"
                value-format="yyyy"
                placeholder="请选择预算年度"
                style="width: 100%"
                :disabled="isCheck"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="实际投入金额(万元)" prop="actualInputAmount">
              <el-input
                v-model="form.actualInputAmount"
                placeholder="请输入实际投入金额(万元)"
                @input="handleFormInput"
                :disabled="isCheck"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际投入月" prop="actualInputMonth">
              <el-select
                v-model="form.actualInputMonth"
                placeholder="请选择实际投入月"
                style="width: 100%"
                :disabled="isCheck"
              >
                <el-option
                  v-for="item in actualInputMonthOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-col :span="24">
          <el-form-item label="投入类型" prop="inputType">
            <el-input
              v-model="form.inputType"
              placeholder="请输入投入类型"
              :disabled="isCheck"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="投入说明" prop="inputDescription">
            <el-input
              v-model="form.inputDescription"
              type="textarea"
              placeholder="请输入投入说明"
              :disabled="isCheck"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="投入细则" prop="inputRules">
            <el-input
              v-model="form.inputRules"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="附件" prop="attachment">
            <file-upload
              v-model="form.attachment"
              :file-type="['pdf', 'docx', 'png', 'jpg', 'doc', 'xls', 'xlsx']"
              :disabled="isCheck"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <!-- <el-table
            :data="tableData"
            border
            style="width: 100%"
            height="300px"
            :span-method="objectSpanMethod"
          >
            <el-table-column
              label="类别"
              prop="category"
              align="center"
              width="180"
            />
            <el-table-column
              label="项目名称"
              prop="projectName"
              align="center"
            />
            <el-table-column label="" prop="projectItem" align="center" />
            <el-table-column label="投入金额（万元）" align="center">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.amount"
                  placeholder="请输入"
                  @input="handleInput(scope.row, $event)"
                />
              </template>
            </el-table-column>
          </el-table> -->
          <el-table
            :data="tableSimpleData"
            border
            style="width: 100%"
            height="300px"
          >
            <el-table-column label="类别" prop="category" align="center" />
            <el-table-column label="投入金额（万元）" align="center">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.amount"
                  placeholder="请输入"
                  @input="handleSimpleInput(scope.row, $event)"
                  :disabled="isCheck"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="isCheck"
          >确 定</el-button
        >
        <el-button @click="cancel" :disabled="isCheck">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInput,
  getInput,
  delInput,
  addInput,
  updateInput,
} from "@/api/system/monthlySchedule/index";
import { getEnterpriseInfo } from "@/api/system/info";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { getFileOrignalName } from "@/utils/common.js";
export default {
  name: "Input",
  components: {
    AttachmentDialog,
    selectPeopleTree,
  },
  data() {
    return {
      isCheck: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全投入月度记录表表格数据
      inputList: [],
      companyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        budgetYear: null,
        company: null,
        actualInputMonth: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        budgetYear: [
          { required: true, message: "请选择预算年度", trigger: "change" },
        ],
        company: [
          { required: true, message: "请选择所属公司", trigger: "change" },
        ],
        inputType: [
          { required: true, message: "请输入投入类型", trigger: "blur" },
        ],
        actualInputAmount: [
          { required: true, message: "请输入实际投入金额", trigger: "blur" },
        ],
        actualInputMonth: [
          { required: true, message: "请选择实际投入月", trigger: "change" },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_API,
      actualInputMonthOptions: [
        {
          label: "1月",
          value: "1月",
        },
        {
          label: "2月",
          value: "2月",
        },
        {
          label: "3月",
          value: "3月",
        },
        {
          label: "4月",
          value: "4月",
        },
        {
          label: "5月",
          value: "5月",
        },
        {
          label: "6月",
          value: "6月",
        },
        {
          label: "7月",
          value: "7月",
        },
        {
          label: "8月",
          value: "8月",
        },
        {
          label: "9月",
          value: "9月",
        },
        {
          label: "10月",
          value: "10月",
        },
        {
          label: "11月",
          value: "11月",
        },
        {
          label: "12月",
          value: "12月",
        },
      ],
      tableData: [
        {
          category: "文明施工与环境保护",
          projectName: "安全警示标志牌",
          projectItem: "",
          amount: "0",
        },
        {
          category: "文明施工与环境保护",
          projectName: "现场围挡",
          projectItem: "",
          amount: "0",
        },
        {
          category: "文明施工与环境保护",
          projectName: "图、板",
          projectItem: "",
          amount: "0",
        },
        {
          category: "文明施工与环境保护",
          projectName: "企业标志",
          projectItem: "",
          amount: "0",
        },
        {
          category: "文明施工与环境保护",
          projectName: "场容场貌",
          projectItem: "",
          amount: "0",
        },
        {
          category: "文明施工与环境保护",
          projectName: "材料堆放",
          projectItem: "",
          amount: "0",
        },
        {
          category: "文明施工与环境保护",
          projectName: "现场防火",
          projectItem: "",
          amount: "0",
        },
        {
          category: "文明施工与环境保护",
          projectName: "垃圾处理",
          projectItem: "",
          amount: "0",
        },
        {
          category: "临时设施",
          projectName: "现场办公、生活区（安全）",
          projectItem: "",
          amount: "0",
        },
        {
          category: "临时设施",
          projectName: "临时用电设施",
          projectItem: "配电线路防护",
          amount: "0",
        },
        {
          category: "临时设施",
          projectName: "临时用电设施",
          projectItem: "配电箱开关箱",
          amount: "0",
        },
        {
          category: "临时设施",
          projectName: "临时用电设施",
          projectItem: "接地保护装置",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "临边洞口防护",
          projectItem: "临边防护",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "",
          projectItem: "通道口防护",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "",
          projectItem: "预留洞口防护",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "",
          projectItem: "电梯井口防护",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "交叉作业防护",
          projectItem: "",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "高处作业防护",
          projectItem: "",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "培训教育、安全评优",
          projectItem: "",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "劳动保护用品",
          projectItem: "",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "应急救援",
          projectItem: "",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "用于安全的检验费用",
          projectItem: "",
          amount: "0",
        },
        {
          category: "安全施工",
          projectName: "季节性施工安全措施",
          projectItem: "",
          amount: "0",
        },
        {
          category: "绿色施工（创优）",
          projectName: "节水",
          projectItem: "",
          amount: "0",
        },
        {
          category: "绿色施工（创优）",
          projectName: "节材",
          projectItem: "",
          amount: "0",
        },
        {
          category: "绿色施工（创优）",
          projectName: "节能",
          projectItem: "",
          amount: "0",
        },
        {
          category: "绿色施工（创优）",
          projectName: "节地",
          projectItem: "",
          amount: "0",
        },
        {
          category: "绿色施工（创优）",
          projectName: "环保",
          projectItem: "",
          amount: "0",
        },
        {
          category: "专项安全费用",
          projectName: "智慧工地",
          projectItem: "",
          amount: "0",
        },
        {
          category: "专项安全费用",
          projectName: "专家费用",
          projectItem: "",
          amount: "0",
        },
        {
          category: "专项安全费用",
          projectName: "实名制",
          projectItem: "",
          amount: "0",
        },
        {
          category: "专项安全费用",
          projectName: "标准化工地",
          projectItem: "",
          amount: "0",
        },
        {
          category: "其他",
          projectName: "",
          projectItem: "",
          amount: "0",
        },
      ],
      tableSimpleData: [
        {
          category: "文明施工与环境保护",
          amount: "0",
        },
        {
          category: "临时设施",
          amount: "0",
        },

        {
          category: "安全施工",

          amount: "0",
        },
        {
          category: "绿色施工（创优）",
          amount: "0",
        },
        {
          category: "专项安全费用",
          amount: "0",
        },
        {
          category: "其他",
          amount: "0",
        },
      ],
    };
  },
  created() {
    this.getList();
    this.getCompanyList();
  },
  methods: {
    getFileOrignalName,
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex == 0) {
          return {
            rowspan: 8,
            colspan: 1,
          };
        } else if (rowIndex == 8) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else if (rowIndex == 12) {
          return {
            rowspan: 11,
            colspan: 1,
          };
        } else if (rowIndex == 23) {
          return {
            rowspan: 5,
            colspan: 1,
          };
        } else if (rowIndex == 28) {
          return {
            rowspan: 4,
            colspan: 1,
          };
        } else if (rowIndex == 32) {
          return {
            rowspan: 1,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      if (columnIndex === 1) {
        if (rowIndex < 9) {
          return {
            rowspan: 1,
            colspan: 2,
          };
        } else if (rowIndex == 9) {
          return {
            rowspan: 3,
            colspan: 1,
          };
        } else if (rowIndex >= 12 && rowIndex <= 15) {
          return {
            rowspan: 1,
            colspan: 1,
          };
        } else if (rowIndex >= 16 && rowIndex <= 32) {
          return {
            rowspan: 1,
            colspan: 2,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
      if (columnIndex === 2) {
        if (rowIndex < 9 || (rowIndex >= 16 && rowIndex <= 32)) {
          return {
            rowspan: 0,
            colspan: 0,
          };
        } else if (rowIndex >= 9 && rowIndex < 16) {
          return {
            rowspan: 1,
            colspan: 1,
          };
        }
      }
    },
    handleInput(row, value) {
      // 1. 过滤非数字和非小数点字符
      let val = value.replace(/[^\d.]/g, "");

      // 2. 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");

      // 3. 小数点不能在开头
      val = val.replace(/^\./g, "");

      // 4. 限制小数点后最多两位
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      // 更新回数据
      row.amount = val;
      // this.handleSecondStep(row)
      if (!row.amount || row.amount === ".") {
        row.amount = "0";
        this.calculateTotal();
        return;
      } else {
        this.calculateTotal();
      }
    },
    calculateTotal() {
      let total = 0;
      this.tableData.forEach((item) => {
        total += parseFloat(item.amount) || 0;
      });
      this.form.actualInputAmount = total;
    },
    handleSimpleInput(row, value) {
      // 1. 过滤非数字和非小数点字符
      let val = value.replace(/[^\d.]/g, "");

      // 2. 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");

      // 3. 小数点不能在开头
      val = val.replace(/^\./g, "");

      // 4. 限制小数点后最多两位
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      // 更新回数据
      row.amount = val;
      // this.handleSecondStep(row)
      if (!row.amount || row.amount === ".") {
        row.amount = "0";
        this.calculateSimpleTotal();
        return;
      } else {
        this.calculateSimpleTotal();
      }
    },
    calculateSimpleTotal() {
      let total = 0;
      this.tableSimpleData.forEach((item) => {
        total += parseFloat(item.amount) || 0;
      });
      this.form.actualInputAmount = total;
    },

    /**
     * 失焦时处理（补全两位小数/清除无效值）
     * @param {Object} row 当前行数据
     */
    handleSecondStep(row) {
      //   console.log("handleBlur", row);
      const val = row.amount;

      // 空值处理
      if (!val || val === ".") {
        row.amount = "0";
        this.calculateTotal();
        return;
      }

      // 补全两位小数（如：100 → 100，12.5 → 12.50）
      if (!val.includes(".")) {
        row.amount = val + ".00";
      } else {
        // 处理小数点后只有一位的情况（如：123.4 → 123.40）
        const arr = val.split(".");
        if (arr[1].length === 1) {
          row.amount = val + "0";
        }
      }
      this.calculateTotal();
    },
    /** 查询安全投入月度记录表列表 */
    getList() {
      this.loading = true;
      listInput(this.queryParams).then((response) => {
        this.inputList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        company: null,
        budgetYear: null,
        actualInputAmount: null,
        inputType: null,
        inputDescription: null,
        attachment: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        inputRules: null,
        actualInputMonth: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.tableSimpleData = [
        {
          category: "文明施工与环境保护",
          amount: "0",
        },
        {
          category: "临时设施",
          amount: "0",
        },

        {
          category: "安全施工",

          amount: "0",
        },
        {
          category: "绿色施工（创优）",
          amount: "0",
        },
        {
          category: "专项安全费用",
          amount: "0",
        },
        {
          category: "其他",
          amount: "0",
        },
      ];
      this.isCheck = false;
      this.title = "添加安全投入月度记录表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInput(id).then((response) => {
        this.form = response.data;
        this.form.budgetYear = response.data.budgetYear?.toString() ?? "";
        if (response && response.data.inputRules) {
          //   this.tableData = JSON.parse(response.data.inputRules);
          this.tableSimpleData = JSON.parse(response.data.inputRules);
        }
        this.open = true;
        this.isCheck = false;
        this.title = "修改安全投入月度记录表";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getInput(id).then((response) => {
        this.form = response.data;
        this.form.budgetYear = response.data.budgetYear?.toString() ?? "";
        if (response && response.data.inputRules) {
          //   this.tableData = JSON.parse(response.data.inputRules);
          this.tableSimpleData = JSON.parse(response.data.inputRules);
        }
        this.open = true;
        this.isCheck = true;
        this.title = "修改安全投入月度记录表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          //   this.form.inputRules = JSON.stringify(this.tableData);
          this.form.inputRules = JSON.stringify(this.tableSimpleData);
          console.log("传输得分", this.form.inputRules);
          if (this.form.id != null) {
            updateInput(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInput(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全投入月度记录表编号为"' + ids + '"的数据项？')
        .then(function () {
          return delInput(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/input/export",
        {
          ...this.queryParams,
        },
        `input_${new Date().getTime()}.xlsx`
      );
    },
    async handleAttach(value) {
      try {
        // 获取文件完整URL
        const fileUrl = this.baseUrl + value;
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
    handleChange(selectedItem) {
      //   console.log(selectedItem);
      if (selectedItem) {
        this.form.company = selectedItem.label;
      } else {
        this.form.company = null;
      }
    },
    handleSearchChange(selectedItem) {
      if (selectedItem) {
        this.queryParams.company = selectedItem.label;
      } else {
        this.queryParams.company = null;
      }
    },
    handleFormInput(value) {
      // 清除除数字和小数点外的所有字符
      let val = value.replace(/[^\d.]/g, "");
      // 限制只能有一个小数点
      val = val.replace(/\.{2,}/g, ".");
      // 确保小数点不在开头
      val = val.replace(/^\./g, "");
      // 只保留两位小数
      val = val.replace(/(\.\d{2})\d*/g, "$1");
      // 更新输入值
      this.form.actualInputAmount = val;
      //   this.tableData.forEach((item) => {
      //     item.amount = "0";
      //   });
      this.tableSimpleData.forEach((item) => {
        item.amount = "0";
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__footer {
  margin-top: 12vh;
}
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>