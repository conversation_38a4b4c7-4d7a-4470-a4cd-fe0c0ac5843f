import request from '@/utils/request'

// 查询安全教育培训体系-自培记录列表
export function listZjSelfTrainingRecords(query) {
  return request({
    url: '/training/zjSelfTrainingRecords/list',
    method: 'get',
    params: query
  })
}

// 查询安全教育培训体系-自培记录详细
export function getZjSelfTrainingRecords(id) {
  return request({
    url: '/training/zjSelfTrainingRecords/' + id,
    method: 'get'
  })
}

// 新增安全教育培训体系-自培记录
export function addZjSelfTrainingRecords(data) {
  return request({
    url: '/training/zjSelfTrainingRecords',
    method: 'post',
    data: data
  })
}

// 修改安全教育培训体系-自培记录
export function updateZjSelfTrainingRecords(data) {
  return request({
    url: '/training/zjSelfTrainingRecords',
    method: 'put',
    data: data
  })
}

// 删除安全教育培训体系-自培记录
export function delZjSelfTrainingRecords(id) {
  return request({
    url: '/training/zjSelfTrainingRecords/' + id,
    method: 'delete'
  })
}
