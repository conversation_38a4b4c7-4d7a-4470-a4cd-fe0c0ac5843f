<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="88px"
    >
      <el-form-item label="检查名称" prop="checkName">
        <el-input
          v-model="queryParams.checkName"
          placeholder="请输入检查名称关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查类型" prop="checkType">
        <el-select
          v-model="queryParams.checkType"
          placeholder="请选择检查类型"
          clearable
        >
          <el-option
            v-for="item in checkTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检查人员" prop="inspector">
        <el-input
          v-model="queryParams.inspector"
          placeholder="请输入检查人员"
          clearable
        />
      </el-form-item>
      <el-form-item label="检查时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="待提交" value="待提交" />
          <el-option label="已提交" value="已提交" />
          <el-option label="已审核" value="已审核" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增记录</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="recordList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 250px)"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        prop="recordCode"
        label="检查名称"
        :show-overflow-tooltip="true"
        width="180"
      />
      <el-table-column
        prop="checkType"
        label="检查类别"
        align="center"
        width="100"
      />
      <el-table-column
        prop="checkDate"
        label="检查时间"
        align="center"
        width="160"
      />
      <el-table-column
        prop="inspector"
        label="检查人"
        align="center"
        width="100"
      />
      <el-table-column
        prop="checkObject"
        label="检查对象"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="checkIssueDesc"
        label="检查情况概述"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="是否发现隐患" align="center" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.hasHazard === '是' ? 'danger' : 'success'">
            {{ scope.row.hasHazard }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="附件" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.hasAttachment" type="info">
            {{ scope.row.hasAttachment }}
          </el-tag>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改检查记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="检查任务名称" prop="checkName">
              <el-input
                v-model="form.checkName"
                placeholder="请输入检查任务名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查类型" prop="checkType">
              <el-select
                v-model="form.checkType"
                placeholder="请选择检查类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in checkTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查时间" prop="checkDate">
              <el-date-picker
                v-model="form.checkDate"
                type="datetime"
                placeholder="选择检查时间"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="执行人" prop="inspector">
              <el-input v-model="form.inspector" placeholder="请输入执行人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查类型" prop="checkCategory">
              <el-select
                v-model="form.checkCategory"
                placeholder="请选择检查类型"
                style="width: 100%"
              >
                <el-option label="专项检查" value="专项检查" />
                <el-option label="日常检查" value="日常检查" />
                <el-option label="综合检查" value="综合检查" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查范围" prop="checkScope">
              <el-input
                v-model="form.checkScope"
                placeholder="请输入检查范围"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查内容" prop="checkContent">
              <el-input
                v-model="form.checkContent"
                type="textarea"
                placeholder="请输入检查内容"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检查结论" prop="checkResult">
              <el-select
                v-model="form.checkResult"
                placeholder="请选择检查结论"
                style="width: 100%"
              >
                <el-option label="合格" value="合格" />
                <el-option label="基本合格" value="基本合格" />
                <el-option label="不合格" value="不合格" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否发现隐患" prop="hasHazard">
              <el-radio-group v-model="form.hasHazard">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="问题描述" prop="problemDesc">
              <el-input
                v-model="form.problemDesc"
                type="textarea"
                placeholder="检查了设备布线、电机箱等..."
                :rows="3"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="检查结论" prop="conclusion">
              <el-select
                v-model="form.conclusion"
                placeholder="选择/文本"
                style="width: 200px"
              >
                <el-option label="先审大曝露" value="先审大曝露" />
                <el-option
                  label="是(支持其他现有详情)"
                  value="是(支持其他现有详情)"
                />
              </el-select>
            </el-form-item> </el-col
        ></el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="问题描述">
              <el-input
                v-model="form.issueDesc"
                placeholder="文字描述内容"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="附件">
              <el-upload
                ref="upload"
                :action="uploadAction"
                :headers="uploadHeaders"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                multiple
                :limit="5"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
              >
                <el-button size="small" type="primary" icon="el-icon-upload">
                  支持查看大图、预览文档
                </el-button>
                <div slot="tip" class="el-upload__tip">
                  支持上传PDF/图片/Word文档，单文件不超过10MB，最多5个文件
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作按钮">
              <el-button type="primary" @click="handleSave">关闭</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看检查记录详情对话框 -->
    <el-dialog
      title="查看检查记录详情 (点击查看)"
      :visible.sync="viewOpen"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="检查任务名称">{{
          viewForm.checkName
        }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{
          viewForm.checkType
        }}</el-descriptions-item>
        <el-descriptions-item label="示例内容">文本展示</el-descriptions-item>
        <el-descriptions-item label="检查时间">{{
          viewForm.checkDate
        }}</el-descriptions-item>
        <el-descriptions-item label="执行人">{{
          viewForm.inspector
        }}</el-descriptions-item>
        <el-descriptions-item label="检查类型">{{
          viewForm.checkCategory
        }}</el-descriptions-item>
        <el-descriptions-item label="检查范围" :span="2">{{
          viewForm.checkScope
        }}</el-descriptions-item>
        <el-descriptions-item label="检查内容" :span="2">{{
          viewForm.checkContent
        }}</el-descriptions-item>
        <el-descriptions-item label="检查结论">{{
          viewForm.checkResult
        }}</el-descriptions-item>
        <el-descriptions-item label="是否发现隐患">
          <el-tag :type="viewForm.hasHazard === '是' ? 'danger' : 'success'">{{
            viewForm.hasHazard
          }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="问题描述" :span="2">{{
          viewForm.problemDesc
        }}</el-descriptions-item>
        <el-descriptions-item label="TextArea">TextArea</el-descriptions-item>
        <el-descriptions-item label="Button">Button</el-descriptions-item>
      </el-descriptions>

      <div
        v-if="viewForm.attachments && viewForm.attachments.length > 0"
        style="margin-top: 20px"
      >
        <h4>相关附件：</h4>
        <el-tag
          v-for="file in viewForm.attachments"
          :key="file.name"
          style="margin-right: 10px; margin-bottom: 5px; cursor: pointer"
          @click="downloadFile(file)"
        >
          <i class="el-icon-paperclip" /> {{ file.name }}
        </el-tag>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "CheckRecordManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检查记录表格数据
      recordList: [
        {
          id: 1,
          recordCode: "节前专项检查",
          checkType: "专项检查",
          checkDate: "2025-05-12 12:00:00",
          inspector: "里斯",
          checkObject: "机房区域电间",
          checkIssueDesc: "发现1处问题、已上传",
          hasHazard: "是",
          hasAttachment: "xxxxxx",
          checkName: "节前专项检查",
          checkCategory: "专项检查",
          checkScope: "机房区域电间",
          checkContent: "检查了设备布线、电机箱等...",
          checkResult: "基本合格",
          problemDesc: "检查了设备布线、电机箱等...",
          conclusion: "先审大曝露",
          issueDesc: "文字描述内容",
          attachments: [
            { name: "检查照片.jpg", url: "/files/check-photo.jpg" },
          ],
        },
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹窗
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkName: undefined,
        checkType: undefined,
        inspector: undefined,
        dateRange: [],
        status: undefined,
      },
      // 表单参数
      form: {},
      // 查看表单数据
      viewForm: {},
      // 检查类型选项
      checkTypeOptions: [
        { label: "专项检查", value: "专项检查" },
        { label: "日常检查", value: "日常检查" },
        { label: "综合检查", value: "综合检查" },
        { label: "安全生产", value: "安全生产" },
        { label: "在建工地", value: "在建工地" },
        { label: "燃气检查", value: "燃气检查" },
        { label: "食品安全", value: "食品安全" },
      ],
      // 文件列表
      fileList: [],
      // 上传地址
      uploadAction: process.env.VUE_APP_BASE_API + "/system/upload",
      // 上传请求头
      uploadHeaders: {
        Authorization: "Bearer " + this.$store.getters.token,
      },
      // 表单校验
      rules: {
        checkName: [
          { required: true, message: "检查任务名称不能为空", trigger: "blur" },
        ],
        checkType: [
          { required: true, message: "检查类型不能为空", trigger: "change" },
        ],
        checkDate: [
          { required: true, message: "检查时间不能为空", trigger: "change" },
        ],
        inspector: [
          { required: true, message: "执行人不能为空", trigger: "blur" },
        ],
        checkContent: [
          { required: true, message: "检查内容不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 时间格式化
    parseTime,
    /** 查询检查记录列表 */
    getList() {
      this.loading = true;
      // 模拟数据加载
      setTimeout(() => {
        this.total = this.recordList.length;
        this.loading = false;
      }, 500);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        checkName: undefined,
        checkType: undefined,
        checkDate: undefined,
        inspector: undefined,
        checkCategory: undefined,
        checkScope: undefined,
        checkContent: undefined,
        checkResult: undefined,
        hasHazard: "否",
        problemDesc: undefined,
        conclusion: undefined,
        issueDesc: undefined,
        attachments: [],
      };
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检查记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const selectedRow =
        row || this.recordList.find((item) => this.ids.includes(item.id));
      if (selectedRow) {
        this.form = { ...selectedRow };
        this.fileList = selectedRow.attachments || [];
      }
      this.open = true;
      this.title = "修改检查记录";
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.viewForm = { ...row };
      this.viewOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.attachments = this.fileList;
          if (this.form.id != null) {
            this.$message.success("修改成功");
          } else {
            this.$message.success("新增成功");
          }
          this.open = false;
          this.getList();
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      const names = row
        ? [row.recordCode]
        : this.recordList
            .filter((item) => ids.includes(item.id))
            .map((item) => item.recordCode);
      this.$confirm('是否确认删除检查记录"' + names.join("、") + '"?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        this.getList();
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.$message.info("导入功能待实现");
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$message.info("导出功能待实现");
    },
    /** 保存按钮操作 */
    handleSave() {
      this.open = false;
    },
    /** 文件上传前检查 */
    beforeUpload(file) {
      const isValidType = [
        "application/pdf",
        "image/jpeg",
        "image/png",
        "image/jpg",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ].includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isValidType) {
        this.$message.error("只能上传PDF、图片或Word文档!");
        return false;
      }
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 10MB!");
        return false;
      }
      return true;
    },
    /** 文件上传成功 */
    handleUploadSuccess(response, file, fileList) {
      this.fileList = fileList;
      this.$message.success("文件上传成功");
    },
    /** 删除文件 */
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    /** 预览文件 */
    handlePreview(file) {
      window.open(file.url);
    },
    /** 下载文件 */
    downloadFile(file) {
      window.open(file.url);
    },
  },
};
</script>

<style scoped>
.el-upload__tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.dialog-footer {
  text-align: center;
}

.dialog-footer .el-button {
  margin: 0 10px;
  min-width: 80px;
}
</style>
