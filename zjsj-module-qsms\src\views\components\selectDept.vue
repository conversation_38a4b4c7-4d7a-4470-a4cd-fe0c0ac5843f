<template>
  <el-select
    v-model="belongingUnit"
    :placeholder="placeholder"
    clearable
    filterable
    :filter-method="filterDept"
    style="width: 100%"
    @change="handleChange"
  >
    <template>
      <div v-for="group in deptList" :key="group.id">
        <el-option
          :label="group.name"
          :value="group.id"
          style="padding-left: 20px"
        />
        <template v-if="group.children && group.children.length">
          <el-option
            v-for="child in renderDeptOptions(group.children)"
            :key="child.id"
            :label="child.name"
            :value="child.id"
            :style="{ paddingLeft: child.level * 20 + 'px' }"
          />
        </template>
      </div>
    </template>
  </el-select>
</template>

<script>
import { listInfo, getOrgTree } from "@/api/system/info";
export default {
  props: {
    belongingUnit: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择单位",
    },
  },
  data() {
    return {
      deptList: [],
      deptParams: {
        name: undefined,
        status: undefined,
      },
      localBelongingUnit: this.belongingUnit.slice(),
    };
  },
  mounted() {
    this.getDeptList();
  },
  watch: {
    belongingUnit: {
      handler(newVal) {
        this.localBelongingUnit = newVal;
      },
      deep: true,
    },
  },
  methods: {
    getDeptList() {
      getOrgTree(this.deptParams).then((res) => {
        // this.deptList = this.handleTree(res.data, "id");
        this.deptList = res.rows;
        this.loading = false;
      });
    },
    // 不扁平化
    renderDeptOptions(children, level = 1) {
      let result = [];
      children.forEach((child) => {
        result.push({
          ...child,
          level,
          name: " ".repeat(level * 2) + child.name,
        });
        if (child.children && child.children.length) {
          result = result.concat(
            this.renderDeptOptions(child.children, level + 1)
          );
        }
      });
      return result;
    },
    handleChange(value) {
      this.$emit("update:belongingUnit", value);
    },
    filterDept(query) {
      if (query) {
        this.getDeptList();
        this.deptList = this.deptList.map((item) => {
          const hasMatch = this.checkMatch(item, query);
          return { ...item, collapsed: !hasMatch };
        });
      } else {
        this.getDeptList();
      }
    },
    // 检查是否匹配搜索关键词
    checkMatch(item, query) {
      if (item.name.includes(query)) {
        return true;
      }
      if (item.children && item.children.length) {
        return item.children.some((child) => this.checkMatch(child, query));
      }
      return false;
    },
    // 切换折叠状态
    // toggleCollapse(group) {
    //   group.collapsed = !group.collapsed;
    // },
  },
};
</script>

<style></style>
