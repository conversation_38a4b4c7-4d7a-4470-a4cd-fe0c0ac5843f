import request from '@/utils/request'

// 查询工程图纸管理列表
export function listBlueprintmanage(query) {
  return request({
    url: '/pingzhan/blueprintmanage/list',
    method: 'get',
    params: query
  })
}

// 查询工程图纸管理详细
export function getBlueprintmanage(id) {
  return request({
    url: '/pingzhan/blueprintmanage/' + id,
    method: 'get'
  })
}

// 新增工程图纸管理
export function addBlueprintmanage(data) {
  return request({
    url: '/pingzhan/blueprintmanage',
    method: 'post',
    data: data
  })
}

// 修改工程图纸管理
export function updateBlueprintmanage(data) {
  return request({
    url: '/pingzhan/blueprintmanage',
    method: 'put',
    data: data
  })
}

// 删除工程图纸管理
export function delBlueprintmanage(id) {
  return request({
    url: '/pingzhan/blueprintmanage/' + id,
    method: 'delete'
  })
}
