{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue?vue&type=style&index=0&id=a83bd3b0&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\index.vue", "mtime": 1757497419375}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757382153709}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757382157092}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757382154814}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757382152798}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouaG9tZSB7DQogIHBhZGRpbmc6IDE1cHg7IC8vIOm7mOiupOWbuuWumuWGhei+uei3ne+8jOmBv+WFjeWkp+Wxj+W5leS4i+i/h+Wkpw0KICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KICBmb250LXNpemU6IDE0cHg7IC8vIOm7mOiupOWbuuWumuWfuuehgOWtl+S9k+Wkp+Wwjw0KICAvKiDlk43lupTlvI/ln7rnoYDlrZfkvZPlpKflsI8gKi8NCg0KICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgcGFkZGluZzogMSU7DQogICAgZm9udC1zaXplOiBjbGFtcCgxMnB4LCAxLjV2dywgMTZweCk7DQogIH0NCn0NCg0KLmVsLXJvdyB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7IC8vIOm7mOiupOWbuuWumumXtOi3nQ0KDQogIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxJTsNCiAgfQ0KDQogICY6bGFzdC1jaGlsZCB7DQogICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgfQ0KfQ0KDQovLyDpobbpg6jnu5/orqHljaHniYfmoLflvI/vvIjnrKzkuIDooYwgLSAz5Liq5aSn5Y2h54mH77yJDQoudG9wLXN0YXRzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQoNCiAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgIGdhcDogMS41JTsNCiAgICBtYXJnaW4tYm90dG9tOiAxJTsNCiAgfQ0KDQogIC5zdGF0LWNhcmQgew0KICAgIGZsZXg6IDE7DQogICAgYmFja2dyb3VuZDogd2hpdGU7DQogICAgYm9yZGVyLXJhZGl1czogN3B4Ow0KICAgIHBhZGRpbmc6IDIwcHg7DQogICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogICAgbWluLWhlaWdodDogMTIwcHg7DQogICAgbWluLXdpZHRoOiAzMCU7DQoNCiAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICBwYWRkaW5nOiAzJSAyJTsNCiAgICB9DQoNCiAgICAuc3RhdC1oZWFkZXIgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICAgIA0KICAgICAgaDMgew0KICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICBtYXJnaW46IDA7DQogICAgICAgIA0KICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgxcmVtLCAxLjh2dywgMS4xMjVyZW0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLnN0YXQtY29udGVudC1kdWFsIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBnYXA6IDIwcHg7DQogICAgICANCiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgZ2FwOiAxLjUlOw0KICAgICAgfQ0KICAgICAgDQogICAgICAuc3RhdC1pdGVtIHsNCiAgICAgICAgZmxleDogMTsNCiAgICAgICAgDQogICAgICAgIC5zdGF0LXRpdGxlIHsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7DQogICAgICAgICAgDQogICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjYyNXJlbSwgMXZ3LCAwLjc1cmVtKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAuc3RhdC1udW1iZXItcm93IHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBiYXNlbGluZTsNCiAgICAgICAgICBnYXA6IDRweDsNCg0KICAgICAgICAgIC5zdGF0LW51bWJlciB7DQogICAgICAgICAgICBmb250LXNpemU6IDI4cHg7DQogICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgIGNvbG9yOiAjMTQ3OWZjOw0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE7DQoNCiAgICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgxcmVtLCAyLjJ2dywgMS43NXJlbSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnN0YXQtdW5pdCB7DQogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQoNCiAgICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjc1cmVtLCAxLjF2dywgMC44NzVyZW0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgICYuc3RhdC1jYXJkLTEgew0KICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEyMGRlZywgI0Y3NEEzNCAwJSwgI0ZGN0M1RSAxMDAlKTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDdweDsNCiAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgIA0KICAgICAgLnN0YXQtaGVhZGVyIGgzIHsNCiAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgfQ0KICAgICAgDQogICAgICAuc3RhdC1jb250ZW50LWR1YWwgLnN0YXQtaXRlbSB7DQogICAgICAgIC5zdGF0LXRpdGxlIHsNCiAgICAgICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpOw0KICAgICAgICB9DQogICAgICAgIC5zdGF0LW51bWJlciB7DQogICAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICB9DQogICAgICAgIC5zdGF0LXVuaXQgew0KICAgICAgICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAmLnN0YXQtY2FyZC0yIHsNCiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzdkZWcsICMxNjg4RTYgMCUsICM0NkFCRkYgMTAwJSk7DQogICAgICBib3JkZXItcmFkaXVzOiA3cHg7DQogICAgICBjb2xvcjogd2hpdGU7DQogICAgICANCiAgICAgIC5zdGF0LWhlYWRlciBoMyB7DQogICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgIH0NCiAgICAgIA0KICAgICAgLnN0YXQtY29udGVudC1kdWFsIC5zdGF0LWl0ZW0gew0KICAgICAgICAuc3RhdC10aXRsZSB7DQogICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCiAgICAgICAgfQ0KICAgICAgICAuc3RhdC1udW1iZXIgew0KICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgICAgfQ0KICAgICAgICAuc3RhdC11bml0IHsNCiAgICAgICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgJi5zdGF0LWNhcmQtMyB7DQogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM3ZGVnLCAjRjU4NzNFIDAlLCAjRjVBNjQ1IDEwMCUpOw0KICAgICAgYm9yZGVyLXJhZGl1czogN3B4Ow0KICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgDQogICAgICAuc3RhdC1oZWFkZXIgaDMgew0KICAgICAgICBjb2xvcjogd2hpdGU7DQogICAgICB9DQogICAgICANCiAgICAgIC5zdGF0LWNvbnRlbnQtZHVhbCAuc3RhdC1pdGVtIHsNCiAgICAgICAgLnN0YXQtdGl0bGUgew0KICAgICAgICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7DQogICAgICAgIH0NCiAgICAgICAgLnN0YXQtbnVtYmVyIHsNCiAgICAgICAgICBjb2xvcjogd2hpdGU7DQogICAgICAgIH0NCiAgICAgICAgLnN0YXQtdW5pdCB7DQogICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQovLyDnrKzkuozooYznu5/orqHljaHniYfmoLflvI/vvIg05Liq5bCP5Y2h54mH77yJDQouc2Vjb25kLXN0YXRzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQoNCiAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgIGdhcDogMS41JTsNCiAgICBtYXJnaW4tYm90dG9tOiAxJTsNCiAgfQ0KDQogIC5zdGF0LWNhcmQtc21hbGwgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgICBib3JkZXItcmFkaXVzOiA3cHg7DQogICAgcGFkZGluZzogMTZweDsNCiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA4KTsNCiAgICBmbGV4OiAxOw0KICAgIG1pbi13aWR0aDogMjIlOw0KICAgIG1pbi1oZWlnaHQ6IDExMHB4Ow0KDQogICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgcGFkZGluZzogMiUgMS41JTsNCiAgICB9DQoNCiAgICAuc3RhdC1pY29uIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICB3aWR0aDogNDBweDsNCiAgICAgIGhlaWdodDogNDBweDsNCiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDsNCg0KICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICB3aWR0aDogMzVweDsNCiAgICAgICAgaGVpZ2h0OiAzNXB4Ow0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgICAgIH0NCg0KICAgICAgaSB7DQogICAgICAgIGZvbnQtc2l6ZTogMjBweDsNCiAgICAgICAgY29sb3I6ICM2NjY7DQoNCiAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5zdGF0LWNvbnRlbnQtc21hbGwgew0KICAgICAgZmxleDogMTsNCg0KICAgICAgLnN0YXQtdGl0bGUtc21hbGwgew0KICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7DQoNCiAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDF2dywgMC43NXJlbSk7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLnN0YXQtbnVtYmVyLXJvdy1zbWFsbCB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBiYXNlbGluZTsNCiAgICAgICAgZ2FwOiA0cHg7DQoNCiAgICAgICAgLnN0YXQtbnVtYmVyLXNtYWxsIHsNCiAgICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDE7DQoNCiAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDFyZW0sIDEuNnZ3LCAxLjI1cmVtKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAuc3RhdC11bml0LXNtYWxsIHsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IG5vcm1hbDsNCg0KICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDF2dywgMC43NXJlbSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgJi5zdGF0LWNhcmQtc21hbGwtMSB7DQogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM3ZGVnLCAjMTU2QkY2IDAlLCAjNDY4MUZGIDEwMCUpOw0KICAgICAgYm9yZGVyLXJhZGl1czogN3B4Ow0KICAgICAgY29sb3I6IHdoaXRlOw0KDQogICAgICAuc3RhdC1pY29uIHsNCiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICAgICAgICANCiAgICAgICAgaSB7DQogICAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5zdGF0LWNvbnRlbnQtc21hbGwgew0KICAgICAgICAuc3RhdC10aXRsZS1zbWFsbCB7DQogICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LW51bWJlci1zbWFsbCB7DQogICAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtdW5pdC1zbWFsbCB7DQogICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgICYuc3RhdC1jYXJkLXNtYWxsLTIgew0KICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzN2RlZywgI0ZDOTkyMCAwJSwgI0Y1QUM0NSAxMDAlKTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDdweDsNCiAgICAgIGNvbG9yOiB3aGl0ZTsNCg0KICAgICAgLnN0YXQtaWNvbiB7DQogICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTsNCiAgICAgICAgDQogICAgICAgIGkgew0KICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuc3RhdC1jb250ZW50LXNtYWxsIHsNCiAgICAgICAgLnN0YXQtdGl0bGUtc21hbGwgew0KICAgICAgICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7DQogICAgICAgIH0NCg0KICAgICAgICAuc3RhdC1udW1iZXItc21hbGwgew0KICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LXVuaXQtc21hbGwgew0KICAgICAgICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOCk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAmLnN0YXQtY2FyZC1zbWFsbC0zIHsNCiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzdkZWcsICM5RDU5RkYgMCUsICNDQTc5RjUgMTAwJSk7DQogICAgICBib3JkZXItcmFkaXVzOiA3cHg7DQogICAgICBjb2xvcjogd2hpdGU7DQoNCiAgICAgIC5zdGF0LWljb24gew0KICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7DQogICAgICAgIA0KICAgICAgICBpIHsNCiAgICAgICAgICBjb2xvcjogd2hpdGU7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLnN0YXQtY29udGVudC1zbWFsbCB7DQogICAgICAgIC5zdGF0LXRpdGxlLXNtYWxsIHsNCiAgICAgICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpOw0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtbnVtYmVyLXNtYWxsIHsNCiAgICAgICAgICBjb2xvcjogd2hpdGU7DQogICAgICAgIH0NCg0KICAgICAgICAuc3RhdC11bml0LXNtYWxsIHsNCiAgICAgICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgJi5zdGF0LWNhcmQtc21hbGwtNCB7DQogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTQ3ZGVnLCAjMThDNjhDIDAlLCAjMkJEMTgxIDEwMCUpOw0KICAgICAgYm9yZGVyLXJhZGl1czogN3B4Ow0KICAgICAgY29sb3I6IHdoaXRlOw0KDQogICAgICAuc3RhdC1pY29uIHsNCiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOw0KICAgICAgICANCiAgICAgICAgaSB7DQogICAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5zdGF0LWNvbnRlbnQtc21hbGwgew0KICAgICAgICAuc3RhdC10aXRsZS1zbWFsbCB7DQogICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LW51bWJlci1zbWFsbCB7DQogICAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtdW5pdC1zbWFsbCB7DQogICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQovLyDnrqHnkIbljaHniYfmoLflvI8NCi5tYW5hZ2VtZW50LXJvdyB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7IC8vIOm7mOiupOWbuuWumumXtOi3nQ0KDQogIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxJTsNCiAgfQ0KDQogIC5tYW5hZ2VtZW50LWNhcmQgew0KICAgIGJhY2tncm91bmQ6IHdoaXRlOw0KICAgIGJvcmRlci1yYWRpdXM6IDAuNzVyZW07DQogICAgcGFkZGluZzogMTVweDsgLy8g5YeP5bCR5YaF6L656Led77yM6buY6K6k5Zu65a6a5YaF6L656LedDQogICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgICBoZWlnaHQ6IDM3MHB4Ow0KICAgIG92ZXJmbG93OiBoaWRkZW47IC8vIOmYsuatouWGheWuuei2heWHug0KICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7IC8vIOehruS/neWGhei+uei3neiuoeeul+WcqOWGhQ0KDQogICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgcGFkZGluZzogMSU7DQogICAgfQ0KDQogICAgLm1hbmFnZW1lbnQtaGVhZGVyIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7IC8vIOWHj+WwkemXtOi3ne+8jOm7mOiupOWbuuWumumXtOi3nQ0KDQogICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDElOw0KICAgICAgfQ0KDQogICAgICAuaGVhZGVyLWNvbnRlbnQgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICB9DQoNCiAgICAgIGgzIHsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBmb250LXNpemU6IDE2cHg7IC8vIOm7mOiupOWbuuWumuWtl+S9k+Wkp+Wwjw0KICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICBjb2xvcjogIzMzMzsNCg0KICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjg3NXJlbSwgMS42dncsIDFyZW0pOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC50aW1lLXRhYnMgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBnYXA6IDRweDsNCg0KICAgICAgICAudGFiLWl0ZW0gew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgICAgcGFkZGluZzogMnB4IDZweDsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgICAgICAgICB1c2VyLXNlbGVjdDogbm9uZTsNCg0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgY29sb3I6ICMyNjU2ZjU7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDM4LCA4NiwgMjQ1LCAwLjEpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgICYuYWN0aXZlIHsNCiAgICAgICAgICAgIGNvbG9yOiAjMjY1NmY1Ow0KICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgzOCwgODYsIDI0NSwgMC4xKTsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjYyNXJlbSwgMXZ3LCAwLjc1cmVtKTsNCiAgICAgICAgICAgIHBhZGRpbmc6IDFweCA0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLnRhYi1kaXZpZGVyIHsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgY29sb3I6ICNjY2M7DQogICAgICAgICAgbWFyZ2luOiAwIDJweDsNCg0KICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDF2dywgMC43NXJlbSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLy8g6LSo6YeP566h55CG5qC35byPDQogICAgLnNlY3Rpb24taGVhZGVyIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDElOw0KDQogICAgICAuc2VjdGlvbi10aXRsZSB7DQogICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDF2dywgMC43NXJlbSk7DQogICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTsNCiAgICAgICAgYmFja2dyb3VuZDogI2Y1ZjdmYTsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAucXVhbGl0eS1zdGF0cyB7DQogICAgICBtYXJnaW4tYm90dG9tOiAxJTsNCg0KICAgICAgLnN0YXQtZ3JvdXAgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBnYXA6IDEuNSU7DQogICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KDQogICAgICAgIC5zdGF0LWl0ZW0gew0KICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KDQogICAgICAgICAgJi5zdGF0LXBsYWNlaG9sZGVyIHsNCiAgICAgICAgICAgIHZpc2liaWxpdHk6IGhpZGRlbjsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc3RhdC1oZWFkZXIgew0KICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICBnYXA6IDAuNXJlbTsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTsNCiAgICAgICAgICAgIG1pbi1oZWlnaHQ6IDI4cHg7DQoNCiAgICAgICAgICAgIC5zdGF0LWljb24gew0KICAgICAgICAgICAgICB3aWR0aDogMS4yNXJlbTsNCiAgICAgICAgICAgICAgaGVpZ2h0OiAxLjI1cmVtOw0KICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjI1cmVtOw0KICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAxdncsIDAuNzVyZW0pOw0KDQogICAgICAgICAgICAgICYuYmx1ZSB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzI2NTZmNTsNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICYub3JhbmdlIHsNCiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmY5MjBkOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgJi5ncmVlbiB7DQogICAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzU0YzI1NTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAuc3RhdC1sYWJlbCB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC43NXJlbSwgMS4ydncsIDAuODc1cmVtKTsNCiAgICAgICAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnN0YXQtbnVtYmVyIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC43NXJlbSwgMS4zdncsIDEuMjVyZW0pOw0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgICBjb2xvcjogIzI2NTZmNTsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtOw0KICAgICAgICAgICAgbWluLWhlaWdodDogMjRweDsNCiAgICAgICAgICAgIHRleHQtYWxpZ246IGxlZnQ7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnN0YXQtZGV0YWlsIHsNCiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICBnYXA6IDEuNSU7DQogICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCg0KICAgICAgICAgICAgc3BhbiB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC41cmVtLCAwLjd2dywgMC42MjVyZW0pOw0KICAgICAgICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDsNCg0KICAgICAgICAgICAgICAubnVtYmVyIHsNCiAgICAgICAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLy8g5a6J5YWo566h55CG5LiT55So5qC35byPDQogICAgLy8g5paw55qE5a6J5YWo566h55CG5qC35byPDQogICAgLnNhZmV0eS10b3Atc3RhdHMgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGdhcDogMTVweDsNCiAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgZ2FwOiAxLjUlOw0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxLjUlOw0KICAgICAgfQ0KDQogICAgICAuc2FmZXR5LXN0YXQtY2FyZC1uZXcgew0KICAgICAgICBmbGV4OiAxOw0KICAgICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICAgIHBhZGRpbmc6IDE1cHg7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIGdhcDogMTJweDsNCiAgICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7DQoNCiAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgIHBhZGRpbmc6IDEuMiU7DQogICAgICAgICAgZ2FwOiAxJTsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zYWZldHktc3RhdC1pY29uIHsNCiAgICAgICAgICB3aWR0aDogNDhweDsNCiAgICAgICAgICBoZWlnaHQ6IDQ4cHg7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgICAgIGZsZXgtc2hyaW5rOiAwOw0KDQogICAgICAgICAgJi5yYXRlLWljb24gew0KICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiAwJSwgI2Y1NTc2YyAxMDAlKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICB3aWR0aDogY2xhbXAoMzZweCwgNHZ3LCA0OHB4KTsNCiAgICAgICAgICAgIGhlaWdodDogY2xhbXAoMzZweCwgNHZ3LCA0OHB4KTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBpIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTsNCg0KICAgICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDE4cHgsIDJ2dywgMjRweCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLnNhZmV0eS1zdGF0LWNvbnRlbnQtbmV3IHsNCiAgICAgICAgICBmbGV4OiAxOw0KDQogICAgICAgICAgLnNhZmV0eS1zdGF0LWxhYmVsIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogNnB4Ow0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjsNCg0KICAgICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNzVyZW0sIDEuMnZ3LCAwLjg3NXJlbSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnNhZmV0eS1zdGF0LW51bWJlci1uZXcgew0KICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBiYXNlbGluZTsNCiAgICAgICAgICAgIGdhcDogNHB4Ow0KDQogICAgICAgICAgICAubnVtYmVyIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAyOHB4Ow0KICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgICAgY29sb3I6ICMxNDc5ZmM7DQogICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KDQogICAgICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDEuMjVyZW0sIDIuNXZ3LCAxLjc1cmVtKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAudW5pdCB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQoNCiAgICAgICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC44NzVyZW0sIDEuNHZ3LCAxcmVtKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5zYWZldHktY2hhcnQtYXJlYSB7DQogICAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICAgICAgcGFkZGluZzogMCAyMHB4Ow0KDQogICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEuNSU7DQogICAgICAgIHBhZGRpbmc6IDAgMiU7DQogICAgICB9DQogICAgfQ0KDQogICAgLnNhZmV0eS1ib3R0b20tc3RhdHMgew0KICAgICAgLnN0YXRzLXJvdyB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCg0KICAgICAgICAmOmxhc3QtY2hpbGQgew0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICAgIH0NCg0KICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMSU7DQogICAgICAgIH0NCg0KICAgICAgICAuc3RhdC1pdGVtIHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgZ2FwOiA4cHg7DQogICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCg0KICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgIGdhcDogMC44JTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc3RhdC1kb3Qgew0KICAgICAgICAgICAgd2lkdGg6IDEwcHg7DQogICAgICAgICAgICBoZWlnaHQ6IDEwcHg7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgICAgICBmbGV4LXNocmluazogMDsNCg0KICAgICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgICB3aWR0aDogY2xhbXAoOHB4LCAxdncsIDEwcHgpOw0KICAgICAgICAgICAgICBoZWlnaHQ6IGNsYW1wKDhweCwgMXZ3LCAxMHB4KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc3RhdC1sYWJlbCB7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgICAgIG1pbi13aWR0aDogNDBweDsNCg0KICAgICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAxdncsIDAuNzVyZW0pOw0KICAgICAgICAgICAgICBtaW4td2lkdGg6IGNsYW1wKDMwcHgsIDN2dywgNDBweCk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgICAgIG1pbi13aWR0aDogMjBweDsNCiAgICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KDQogICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC43NXJlbSwgMS4ydncsIDAuODc1cmVtKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc3RhdC11bml0IHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIGNvbG9yOiAjNjY2Ow0KDQogICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDF2dywgMC43NXJlbSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLnNhZmV0eS1zZWN0aW9ucy1jb250YWluZXIgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGdhcDogMTVweDsgLy8g5YeP5bCR6Ze06Led77yM6buY6K6k5Zu65a6a6Ze06LedDQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIG92ZXJmbG93OiBoaWRkZW47IC8vIOmYsuatouWGheWuuei2heWHug0KDQogICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgIGdhcDogMS41JTsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuc2FmZXR5LXNlY3Rpb24taW5kZXBlbmRlbnQgew0KICAgICAgZmxleDogMTsNCiAgICAgIGJhY2tncm91bmQ6ICNmNWY3Zjk7DQogICAgICBwYWRkaW5nOiAxMHB4IDE1cHggMTJweCAxNXB4OyAvLyDlh4/lsJHlhoXovrnot53vvIzpu5jorqTlm7rlrprlhoXovrnot50NCiAgICAgIGJvcmRlci1yYWRpdXM6IDAuNXJlbTsNCiAgICAgIG1pbi13aWR0aDogMDsgLy8g5YWB6K64ZmxleOmhueebrue8qeWwj+WIsOWGheWuueWkp+Wwj+S7peS4iw0KICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsgLy8g56Gu5L+d5YaF6L656Led6K6h566X5Zyo5YaFDQoNCiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgcGFkZGluZzogMC44JSAxLjUlIDElIDEuNSU7DQogICAgICB9DQoNCiAgICAgIC5zZWN0aW9uLWhlYWRlciB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7IC8vIOm7mOiupOWbuuWumumXtOi3nQ0KDQogICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxJTsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zZWN0aW9uLXRpdGxlIHsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7IC8vIOm7mOiupOWbuuWumuWtl+S9k+Wkp+Wwjw0KICAgICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtOw0KICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjQ1LCAyNDcsIDI1MCwgMC44KTsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjI1cmVtOw0KDQogICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjYyNXJlbSwgMXZ3LCAwLjc1cmVtKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICAuc2FmZXR5LXN0YXRzLXJvdyB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZ2FwOiA4cHg7IC8vIOi/m+S4gOatpeWHj+WwkemXtOi3ne+8jOm7mOiupOWbuuWumumXtOi3nQ0KICAgICAgbWFyZ2luLXRvcDogMTBweDsgLy8g5YeP5bCR5LiK6L656Led77yM6buY6K6k5Zu65a6a6Ze06LedDQoNCiAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgZ2FwOiAxLjUlOw0KICAgICAgICBtYXJnaW4tdG9wOiAxJTsNCiAgICAgIH0NCg0KICAgICAgLnNhZmV0eS1zdGF0LWNhcmQgew0KICAgICAgICBmbGV4OiAxOw0KICAgICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMC41cmVtOw0KICAgICAgICBwYWRkaW5nOiAxMHB4IDEycHg7IC8vIOWkp+W5heWHj+WwkeWGhei+uei3ne+8jOm7mOiupOWbuuWumuWGhei+uei3nQ0KICAgICAgICBib3gtc2hhZG93OiAwIDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjA0KTsNCiAgICAgICAgbWluLXdpZHRoOiAwOyAvLyDlhYHorrhmbGV46aG555uu57yp5bCPDQogICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7IC8vIOehruS/neWGhei+uei3neiuoeeul+WcqOWGhQ0KDQogICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICBwYWRkaW5nOiAxLjIlIDEuNiU7DQogICAgICAgIH0NCg0KICAgICAgICAuc2FmZXR5LXN0YXQtdGl0bGUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTBweDsgLy8g6buY6K6k5Zu65a6a5a2X5L2T5aSn5bCPDQogICAgICAgICAgY29sb3I6ICM2NjY2NjY7DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtOw0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7DQoNCiAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNXJlbSwgMC44dncsIDAuNjI1cmVtKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAuc2FmZXR5LXN0YXQtY29udGVudC1yb3cgew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtZW5kOw0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgICAgICBnYXA6IDE1cHg7IC8vIOm7mOiupOWbuuWumumXtOi3nQ0KDQogICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgICAgICAgICAgZ2FwOiAxLjIlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5zYWZldHktc3RhdC1tYWluIHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBiYXNlbGluZTsNCiAgICAgICAgICBnYXA6IDAuMjVyZW07DQogICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCg0KICAgICAgICAgIC5zYWZldHktc3RhdC1udW1iZXIgew0KICAgICAgICAgICAgZm9udC1zaXplOiAyMnB4OyAvLyDosIPmlbTpu5jorqTlm7rlrprlrZfkvZPlpKflsI/vvIzpgILlkIgxMzAwcHgr5bGP5bmVDQogICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgIGNvbG9yOiAjMTQ3OWZjOw0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE7DQoNCiAgICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjc1cmVtLCAxLjV2dywgMS41cmVtKTsNCiAgICAgICAgICAgICAgLyog5YeP5bCP5ZON5bqU5byP5a2X5L2T77yaMTJweC0yNHB4ICovDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnNhZmV0eS1zdGF0LXVuaXQgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxMHB4OyAvLyDpu5jorqTlm7rlrprlrZfkvZPlpKflsI8NCiAgICAgICAgICAgIGNvbG9yOiAjNjY2NjY2Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IG5vcm1hbDsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KDQogICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC41cmVtLCAwLjh2dywgMC42MjVyZW0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5zYWZldHktc3RhdC1zdWJ0aXRsZSB7DQogICAgICAgICAgZm9udC1zaXplOiA5cHg7IC8vIOm7mOiupOWbuuWumuWtl+S9k+Wkp+Wwjw0KICAgICAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7DQogICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCg0KICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC40Mzc1cmVtLCAwLjd2dywgMC41NjI1cmVtKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc2FmZXR5LWhpZ2hsaWdodC1udW1iZXIgew0KICAgICAgICAgICAgY29sb3I6ICNmZjkyMGQ7DQogICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC8vIOWGheW1jOWbvuihqOagt+W8jw0KICAgIC5jaGFydC1zZWN0aW9uLWluLXNlY3Rpb24gew0KICAgICAgLmNoYXJ0LXRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAxMnB4OyAvLyDpu5jorqTlm7rlrprlrZfkvZPlpKflsI8NCiAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgIHBhZGRpbmc6IDAuMjVyZW0gMC41cmVtOw0KICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI0NSwgMjQ3LCAyNTAsIDAuOCk7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07DQogICAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTsNCiAgICAgICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KDQogICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAxdncsIDAuNzVyZW0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmNoYXJ0LXNlY3Rpb24gew0KICAgICAgLmNoYXJ0LXJvdyB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGdhcDogMS41JTsNCg0KICAgICAgICAuY2hhcnQtaXRlbSB7DQogICAgICAgICAgZmxleDogMTsNCg0KICAgICAgICAgIC5jaGFydC10aXRsZSB7DQogICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAxdncsIDAuNzVyZW0pOw0KICAgICAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLnBpZS1jaGFydC1zZWN0aW9uIHsNCiAgICAgIC5jaGFydC10aXRsZSB7DQogICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC43NXJlbSwgMS4ydncsIDAuODc1cmVtKTsNCiAgICAgICAgY29sb3I6ICM2NjY7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgIH0NCg0KICAgICAgJi5mdWxsLWhlaWdodCB7DQogICAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gNXJlbSk7DQoNCiAgICAgICAgLmNoYXJ0LWNvbnRhaW5lciB7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi8vIOW6lemDqOWbvuihqOWNoeeJh+agt+W8jw0KLmNoYXJ0cy1yb3cgew0KICAuY2hhcnQtY2FyZCB7DQogICAgYmFja2dyb3VuZDogd2hpdGU7DQogICAgYm9yZGVyLXJhZGl1czogMC43NXJlbTsNCiAgICBwYWRkaW5nOiAyLjUlOw0KICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogICAgaGVpZ2h0OiAzNDBweDsNCg0KICAgIC5jaGFydC1oZWFkZXIgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMS41JTsNCg0KICAgICAgaDQgew0KICAgICAgICBtYXJnaW46IDA7DQogICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC44NzVyZW0sIDEuNHZ3LCAxcmVtKTsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICB9DQogICAgfQ0KDQogICAgLmNoYXJ0LWNvbnRlbnQgew0KICAgICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA0cmVtKTsNCiAgICB9DQogIH0NCn0NCg0KLy8g5Zu+6KGo5a655Zmo5qC35byPDQouY2hhcnQtY29udGFpbmVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCn0NCg0KLy8g6K6+5aSH5Zu+6KGo5a655Zmo5qC35byPDQouZXF1aXBtZW50LWNoYXJ0LWNvbnRhaW5lciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgY3Vyc29yOiBwb2ludGVyOw0KfQ0KDQovLyDljbHlpKflt6XnqIvlm77ooajlrrnlmajmoLflvI8NCi5kYW5nZXJvdXMtY2hhcnQtY29udGFpbmVyIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KfQ0KDQovLyDlronlhajnlJ/kuqfmipXlhaXlrrnlmajmoLflvI8NCi5zYWZldHktaW52ZXN0bWVudC1jb250YWluZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KICBnYXA6IDI0cHg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgQG1lZGlhIChtYXgtd2lkdGg6IDExOTlweCkgew0KICAgIGdhcDogMiU7DQogIH0NCg0KICAucGllLWNoYXJ0LXNlY3Rpb24gew0KICAgIHdpZHRoOiAyODBweDsNCiAgICBoZWlnaHQ6IDI4MHB4Ow0KICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICANCiAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICB3aWR0aDogMzUlOw0KICAgICAgaGVpZ2h0OiBhdXRvOw0KICAgICAgYXNwZWN0LXJhdGlvOiAxOw0KICAgIH0NCiAgICANCiAgICAuY2hhcnQtd3JhcHBlciB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICB9DQogIH0NCg0KICAucHJvamVjdC1saXN0LXNlY3Rpb24gew0KICAgIGZsZXg6IDE7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIHBhZGRpbmctbGVmdDogMjRweDsNCg0KICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgIHBhZGRpbmctbGVmdDogMiU7DQogICAgfQ0KDQogICAgLnByb2plY3QtbGlzdCB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIG1heC13aWR0aDogMzAwcHg7DQogICAgICANCiAgICAgIC5wcm9qZWN0LWl0ZW0gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxOHB4Ow0KICAgICAgICBwYWRkaW5nOiAxMHB4IDA7DQogICAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycyBlYXNlOw0KDQogICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxLjglOw0KICAgICAgICAgIHBhZGRpbmc6IDElIDA7DQogICAgICAgIH0NCg0KICAgICAgICAmOmxhc3QtY2hpbGQgew0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICAgIH0NCg0KICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDIwLCAxMjEsIDI1MiwgMC4wNSk7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgIHBhZGRpbmctbGVmdDogOHB4Ow0KICAgICAgICAgIHBhZGRpbmctcmlnaHQ6IDhweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5wcm9qZWN0LWRvdCB7DQogICAgICAgICAgd2lkdGg6IDE0cHg7DQogICAgICAgICAgaGVpZ2h0OiAxNHB4Ow0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQogICAgICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICAgICAgYm94LXNoYWRvdzogMCAxcHggM3B4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCg0KICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICAgICAgICAgIHdpZHRoOiAxLjR2dzsNCiAgICAgICAgICAgIGhlaWdodDogMS40dnc7DQogICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEuNSU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLnByb2plY3QtaW5mbyB7DQogICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIG1pbi13aWR0aDogMDsNCg0KICAgICAgICAgIC5wcm9qZWN0LW5hbWUgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNXB4Ow0KICAgICAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMS40Ow0KDQogICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC44cmVtLCAxLjV2dywgMC45Mzc1cmVtKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAucHJvamVjdC1hbW91bnQgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgY29sb3I6ICMxNDc5ZmM7DQogICAgICAgICAgICBmb250LXdlaWdodDogNzAwOw0KICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KDQogICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC44NzVyZW0sIDEuNnZ3LCAxcmVtKTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgJjo6YWZ0ZXIgew0KICAgICAgICAgICAgICBjb250ZW50OiAn5LiHJzsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxM3B4Ow0KICAgICAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDNweDsNCiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCg0KICAgICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTE5OXB4KSB7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjdyZW0sIDEuM3Z3LCAwLjgxMjVyZW0pOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi8vIOaCrOa1ruahhuagt+W8jw0KLnByb2plY3QtdG9vbHRpcCB7DQogIHBvc2l0aW9uOiBmaXhlZDsNCiAgei1pbmRleDogMTAwMDsNCiAgYmFja2dyb3VuZDogd2hpdGU7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNWU3ZWI7DQogIGJvcmRlci1yYWRpdXM6IDAuNXJlbTsNCiAgYm94LXNoYWRvdzogMCAxMHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBwYWRkaW5nOiAwOw0KICBtaW4td2lkdGg6IDEyLjVyZW07DQogIG1heC13aWR0aDogNTByZW07DQogIHdpZHRoOiBhdXRvOw0KICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAxdncsIDAuNzVyZW0pOw0KDQogIC50b29sdGlwLWhlYWRlciB7DQogICAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgICBwYWRkaW5nOiAwLjc1cmVtIDFyZW07DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU3ZWI7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBjb2xvcjogIzM3NDE1MTsNCiAgICBib3JkZXItcmFkaXVzOiAwLjVyZW0gMC41cmVtIDAgMDsNCiAgICBmb250LXNpemU6IGNsYW1wKDAuNzVyZW0sIDEuMXZ3LCAwLjgxMjVyZW0pOw0KICAgIHdvcmQtd3JhcDogYnJlYWstd29yZDsNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsNCiAgICBsaW5lLWhlaWdodDogMS40Ow0KICB9DQoNCiAgLnRvb2x0aXAtbG9hZGluZywNCiAgLnRvb2x0aXAtZXJyb3IsDQogIC50b29sdGlwLWVtcHR5IHsNCiAgICBwYWRkaW5nOiAxcmVtOw0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICBjb2xvcjogIzZiNzI4MDsNCiAgfQ0KDQogIC50b29sdGlwLWVycm9yIHsNCiAgICBjb2xvcjogI2VmNDQ0NDsNCiAgfQ0KDQogIC50b29sdGlwLWNvbnRlbnQgew0KICAgIHBhZGRpbmc6IDAuNXJlbSAwOw0KICAgIG1heC1oZWlnaHQ6IDE4Ljc1cmVtOw0KICAgIG92ZXJmbG93LXk6IGF1dG87DQoNCiAgICAudG9vbHRpcC1pdGVtIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgcGFkZGluZzogMC41cmVtIDFyZW07DQogICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YzZjRmNjsNCg0KICAgICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsNCiAgICAgIH0NCg0KICAgICAgJjpob3ZlciB7DQogICAgICAgIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogICAgICB9DQoNCiAgICAgIC50b29sdGlwLW5hbWUgew0KICAgICAgICBmbGV4OiAxOw0KICAgICAgICBjb2xvcjogIzM3NDE1MTsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAwLjc1cmVtOw0KICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAxdncsIDAuNzVyZW0pOw0KICAgICAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogICAgICB9DQoNCiAgICAgIC50b29sdGlwLXZhbHVlIHsNCiAgICAgICAgY29sb3I6ICMyNTYzZWI7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDF2dywgMC43NXJlbSk7DQogICAgICAgIG1pbi13aWR0aDogMS44NzVyZW07DQogICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0KDQovLyDlk43lupTlvI/lm77moIfmoLflvI8NCi5zdGF0LWljb24taW1nIHsNCiAgd2lkdGg6IDQwcHg7IC8vIOm7mOiupOWbuuWumuWkp+Wwjw0KICBoZWlnaHQ6IDQwcHg7DQogIG9iamVjdC1maXQ6IGNvbnRhaW47DQogIGZsZXgtc2hyaW5rOiAwOw0KDQogIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICB3aWR0aDogY2xhbXAoMjRweCwgM3Z3LCA0OHB4KTsNCiAgICBoZWlnaHQ6IGNsYW1wKDI0cHgsIDN2dywgNDhweCk7DQogIH0NCn0NCg0KLy8g5bCP5Y2h54mH5Zu+5qCH5qC35byP77yI546w5Zyo5L2/55So5a2X5L2T5Zu+5qCH77yM5q2k5qC35byP5bey5LiN6ZyA6KaB77yJDQovLyAuc3RhdC1pY29uLWltZy1zbWFsbCB7DQovLyAgIHdpZHRoOiAyNHB4Ow0KLy8gICBoZWlnaHQ6IDI0cHg7DQovLyAgIG9iamVjdC1maXQ6IGNvbnRhaW47DQovLyAgIGZsZXgtc2hyaW5rOiAwOw0KDQovLyAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCi8vICAgICB3aWR0aDogY2xhbXAoMjBweCwgMi41dncsIDI4cHgpOw0KLy8gICAgIGhlaWdodDogY2xhbXAoMjBweCwgMi41dncsIDI4cHgpOw0KLy8gICB9DQovLyB9DQoNCi5jaGVjay1pY29uLWltZyB7DQogIHdpZHRoOiAxNHB4OyAvLyDpu5jorqTlm7rlrprlpKflsI8NCiAgaGVpZ2h0OiAxNHB4Ow0KDQogIEBtZWRpYSAobWF4LXdpZHRoOiAxMTk5cHgpIHsNCiAgICB3aWR0aDogY2xhbXAoMTJweCwgMS41dncsIDE2cHgpOw0KICAgIGhlaWdodDogY2xhbXAoMTJweCwgMS41dncsIDE2cHgpOw0KICB9DQp9DQoNCi8vIOWTjeW6lOW8j+WqkuS9k+afpeivoiAtIOmSiOWvuTEyMDBweC0xOTIwcHjojIPlm7TkvJjljJbvvIzorqkxMjAwcHjkuZ/mnIkxOTIwcHjnmoTmlYjmnpwNCkBtZWRpYSAobWluLXdpZHRoOiAxMjAwcHgpIGFuZCAobWF4LXdpZHRoOiAxOTIwcHgpIHsNCiAgLmhvbWUgew0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAvKiDlm7rlrprlrZfkvZPlpKflsI8gKi8NCiAgfQ0KDQogIC50b3Atc3RhdHMgew0KICAgIC5zdGF0LWNhcmQgew0KICAgICAgLnN0YXQtaWNvbiB7DQogICAgICAgIHdpZHRoOiA1MHB4Ow0KICAgICAgICBoZWlnaHQ6IDUwcHg7DQogICAgICAgIG1pbi13aWR0aDogNTBweDsNCiAgICAgICAgbWluLWhlaWdodDogNTBweDsNCiAgICAgICAgbWF4LXdpZHRoOiA1MHB4Ow0KICAgICAgICBtYXgtaGVpZ2h0OiA1MHB4Ow0KICAgICAgfQ0KDQogICAgICAuc3RhdC1jb250ZW50IHsNCiAgICAgICAgLnN0YXQtdGl0bGUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LW51bWJlci1yb3cgew0KICAgICAgICAgIC5zdGF0LW51bWJlciB7DQogICAgICAgICAgICBmb250LXNpemU6IDI0cHg7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnN0YXQtdW5pdCB7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLnN0YXQtaWNvbi1pbWcgew0KICAgIHdpZHRoOiA0MHB4ICFpbXBvcnRhbnQ7DQogICAgaGVpZ2h0OiA0MHB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAubWFuYWdlbWVudC1jYXJkIHsNCiAgICAubWFuYWdlbWVudC1oZWFkZXIgaDMgew0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIH0NCg0KICAgIC5zZWN0aW9uLXRpdGxlIHsNCiAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICB9DQoNCiAgICAucXVhbGl0eS1zdGF0cyAuc3RhdC1ncm91cCAuc3RhdC1pdGVtIHsNCiAgICAgIC5zdGF0LWhlYWRlciB7DQogICAgICAgIC5zdGF0LWljb24gew0KICAgICAgICAgIHdpZHRoOiAxOHB4Ow0KICAgICAgICAgIGhlaWdodDogMThweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LWxhYmVsIHsNCiAgICAgICAgICBmb250LXNpemU6IDEzcHg7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLnN0YXQtbnVtYmVyIHsNCiAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgfQ0KDQogICAgICAuc3RhdC1kZXRhaWwgc3BhbiB7DQogICAgICAgIGZvbnQtc2l6ZTogMTBweDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAvLyDmt7vliqDlronlhajnrqHnkIbmlbDlrZflrZfkvZPlpKflsI/orr7nva4NCiAgICAuc2FmZXR5LXN0YXQtY2FyZCB7DQogICAgICAuc2FmZXR5LXN0YXQtbWFpbiB7DQogICAgICAgIC5zYWZldHktc3RhdC1udW1iZXIgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMjJweDsNCiAgICAgICAgICAvKiAxMzAwLTE5MjBweOS4i+mAguS4reeahOWtl+S9k+Wkp+WwjyAqLw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLmNoZWNrLWljb24taW1nIHsNCiAgICB3aWR0aDogMTRweDsNCiAgICBoZWlnaHQ6IDE0cHg7DQogIH0NCg0KICAuY2hhcnQtY2FyZCB7DQogICAgLmNoYXJ0LWhlYWRlciBoNCB7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgfQ0KICB9DQoNCiAgLnByb2plY3QtdG9vbHRpcCB7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KDQogICAgLnRvb2x0aXAtaGVhZGVyIHsNCiAgICAgIGZvbnQtc2l6ZTogMTNweDsNCiAgICB9DQoNCiAgICAudG9vbHRpcC1jb250ZW50IC50b29sdGlwLWl0ZW0gew0KICAgICAgLnRvb2x0aXAtbmFtZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgIH0NCg0KICAgICAgLnRvb2x0aXAtdmFsdWUgew0KICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5Zu65a6a6Ze06Led5ZKM5biD5bGADQogIC50b3Atc3RhdHMgew0KICAgIGdhcDogMjBweDsNCiAgICAvKiDlm7rlrprpl7Tot50gKi8NCg0KICAgIC5zdGF0LWNhcmQgew0KICAgICAgcGFkZGluZzogMjBweCAxNXB4Ow0KICAgICAgLyog5Zu65a6a5YaF6L656LedICovDQogICAgICBtaW4taGVpZ2h0OiAxMjBweDsNCiAgICB9DQogIH0NCg0KICAubWFuYWdlbWVudC1yb3cgew0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAubWFuYWdlbWVudC1jYXJkIHsNCiAgICAgIHBhZGRpbmc6IDE1cHg7DQogICAgICAvKiDkuI7pu5jorqTmoLflvI/kv53mjIHkuIDoh7QgKi8NCiAgICAgIGhlaWdodDogMzcwcHg7DQogICAgICAvKiDnoa7kv53pq5jluqbkuIDoh7QgKi8NCg0KICAgICAgLm1hbmFnZW1lbnQtaGVhZGVyIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICAgICAgLyog5LiO6buY6K6k5qC35byP5L+d5oyB5LiA6Ie0ICovDQogICAgICB9DQoNCiAgICAgIC5zZWN0aW9uLWhlYWRlciB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogICAgICB9DQoNCiAgICAgIC5xdWFsaXR5LXN0YXRzIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsNCg0KICAgICAgICAuc3RhdC1ncm91cCB7DQogICAgICAgICAgZ2FwOiAyMHB4Ow0KICAgICAgICAgIC8qIOWbuuWumumXtOi3nSAqLw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5zYWZldHktc2VjdGlvbnMtY29udGFpbmVyIHsNCiAgICAgICAgZ2FwOiAxNXB4Ow0KICAgICAgICAvKiDkuI7pu5jorqTmoLflvI/kv53mjIHkuIDoh7QgKi8NCiAgICAgICAgLyog5Zu65a6a6Ze06LedICovDQogICAgICB9DQoNCiAgICAgIC5zYWZldHktc2VjdGlvbi1pbmRlcGVuZGVudCB7DQogICAgICAgIHBhZGRpbmc6IDEwcHggMTVweCAxMnB4IDE1cHg7DQogICAgICAgIC8qIOS4jum7mOiupOagt+W8j+S/neaMgeS4gOiHtCAqLw0KICAgICAgICAvKiDlm7rlrprlhoXovrnot50gKi8NCg0KICAgICAgICAuc2FmZXR5LXN0YXRzLXJvdyB7DQogICAgICAgICAgZ2FwOiA4cHg7DQogICAgICAgICAgLyog5LiO6buY6K6k5qC35byP5L+d5oyB5LiA6Ie0ICovDQogICAgICAgICAgLyog5Zu65a6a6Ze06LedICovDQogICAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgICAvKiDkuI7pu5jorqTmoLflvI/kv53mjIHkuIDoh7QgKi8NCg0KICAgICAgICAgIC5zYWZldHktc3RhdC1jYXJkIHsNCiAgICAgICAgICAgIHBhZGRpbmc6IDEwcHggMTJweDsNCiAgICAgICAgICAgIC8qIOS4jum7mOiupOagt+W8j+S/neaMgeS4gOiHtCAqLw0KICAgICAgICAgICAgLyog5Zu65a6a5YaF6L656LedICovDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLmNoYXJ0cy1yb3cgew0KICAgIC5jaGFydC1jYXJkIHsNCiAgICAgIHBhZGRpbmc6IDIwcHg7DQogICAgICBoZWlnaHQ6IDM0MHB4Ow0KICAgICAgLyog56Gu5L+d6auY5bqm5LiA6Ie0ICovDQoNCiAgICAgIC5jaGFydC1oZWFkZXIgew0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICAgICAgfQ0KDQogICAgICAuY2hhcnQtY29udGVudCB7DQogICAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gNTBweCk7DQogICAgICAgIC8qIOWbuuWumuiuoeeul+mrmOW6piAqLw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOehruS/neagheagvOezu+e7n+mXtOi3neS4gOiHtA0KICAuZWwtcm93IHsNCiAgICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgfQ0KICB9DQoNCiAgLy8g57uf5LiA6L+b5bqm5p2h5qC35byPDQogIC5wcm9qZWN0LXByb2dyZXNzIHsNCiAgICAucHJvZ3Jlc3MtYmFyIHsNCiAgICAgIGhlaWdodDogMTJweDsNCiAgICAgIC8qIOWbuuWumumrmOW6piAqLw0KICAgIH0NCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHsNCiAgLmhvbWUgew0KICAgIHBhZGRpbmc6IDElOw0KICAgIGZvbnQtc2l6ZTogY2xhbXAoMTBweCwgMnZ3LCAxNHB4KTsNCiAgfQ0KDQogIC50b3Atc3RhdHMgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxJTsNCg0KICAgIC5zdGF0LWNhcmQgew0KICAgICAgbWluLWhlaWdodDogMTQwcHg7DQogICAgICBtaW4td2lkdGg6IDEwMCU7DQoNCiAgICAgIC5zdGF0LWhlYWRlciBoMyB7DQogICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMXJlbSwgM3Z3LCAxLjI1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICAgICAgfQ0KDQogICAgICAuc3RhdC1jb250ZW50LWR1YWwgew0KICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICBnYXA6IDE2cHg7DQoNCiAgICAgICAgLnN0YXQtaXRlbSB7DQogICAgICAgICAgLnN0YXQtdGl0bGUgew0KICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjc1cmVtLCAyLjV2dywgMXJlbSkgIWltcG9ydGFudDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc3RhdC1udW1iZXItcm93IC5zdGF0LW51bWJlciB7DQogICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDEuMjVyZW0sIDR2dywgMnJlbSkgIWltcG9ydGFudDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuc3RhdC1udW1iZXItcm93IC5zdGF0LXVuaXQgew0KICAgICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjg3NXJlbSwgMi41dncsIDEuMTI1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5zZWNvbmQtc3RhdHMgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxJTsNCg0KICAgIC5zdGF0LWNhcmQtc21hbGwgew0KICAgICAgbWluLXdpZHRoOiAxMDAlOw0KICAgICAgbWluLWhlaWdodDogNzBweDsNCg0KICAgICAgLnN0YXQtY29udGVudC1zbWFsbCB7DQogICAgICAgIC5zdGF0LXRpdGxlLXNtYWxsIHsNCiAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNzVyZW0sIDIuNXZ3LCAxcmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtbnVtYmVyLXJvdy1zbWFsbCAuc3RhdC1udW1iZXItc21hbGwgew0KICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMXJlbSwgMy41dncsIDEuNXJlbSkgIWltcG9ydGFudDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5zdGF0LW51bWJlci1yb3ctc21hbGwgLnN0YXQtdW5pdC1zbWFsbCB7DQogICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjc1cmVtLCAyLjV2dywgMXJlbSkgIWltcG9ydGFudDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5zdGF0LWljb24taW1nIHsNCiAgICB3aWR0aDogNDBweCAhaW1wb3J0YW50Ow0KICAgIGhlaWdodDogNDBweCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnF1YWxpdHktc3RhdHMgLnN0YXQtZ3JvdXAgLnN0YXQtaXRlbSAuc3RhdC1udW1iZXIgew0KICAgIGZvbnQtc2l6ZTogY2xhbXAoMC43NXJlbSwgMnZ3LCAxcmVtKSAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnNhZmV0eS1zdGF0LW1haW4gLnNhZmV0eS1zdGF0LW51bWJlciB7DQogICAgZm9udC1zaXplOiBjbGFtcCgwLjc1cmVtLCAydncsIDEuMTI1cmVtKSAhaW1wb3J0YW50Ow0KICAgIC8qIOWHj+Wwj+Wtl+S9k++8mjEycHgtMThweCAqLw0KICB9DQoNCiAgLm1hbmFnZW1lbnQtcm93IHsNCiAgICAuZWwtY29sIHsNCiAgICAgIG1hcmdpbi1ib3R0b206IDElOw0KICAgIH0NCiAgfQ0KDQogIC5zYWZldHktc2VjdGlvbnMtY29udGFpbmVyIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMSU7DQogIH0NCg0KICAubWFuYWdlbWVudC1jYXJkIHsNCiAgICBoZWlnaHQ6IDM1MHB4Ow0KICB9DQoNCiAgLmNoYXJ0LWNhcmQgew0KICAgIGhlaWdodDogMzAwcHg7DQogIH0NCg0KICAucHJvamVjdC10b29sdGlwIHsNCiAgICBtYXgtd2lkdGg6IDk1dnc7DQogICAgbWluLXdpZHRoOiA4NXZ3Ow0KICAgIHdpZHRoOiBhdXRvOw0KICB9DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkgew0KICAuaG9tZSB7DQogICAgZm9udC1zaXplOiBjbGFtcCg4cHgsIDN2dywgMTJweCk7DQogIH0NCg0KICAudG9wLXN0YXRzIHsNCiAgICAuc3RhdC1jYXJkIHsNCiAgICAgIG1pbi1oZWlnaHQ6IDEyMHB4Ow0KICAgICAgcGFkZGluZzogMyU7DQoNCiAgICAgIC5zdGF0LWhlYWRlciBoMyB7DQogICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC44NzVyZW0sIDR2dywgMS4xMjVyZW0pICFpbXBvcnRhbnQ7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgICB9DQoNCiAgICAgIC5zdGF0LWNvbnRlbnQtZHVhbCB7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgIGdhcDogMTJweDsNCg0KICAgICAgICAuc3RhdC1pdGVtIHsNCiAgICAgICAgICAuc3RhdC10aXRsZSB7DQogICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAzdncsIDAuODc1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5zdGF0LW51bWJlci1yb3cgLnN0YXQtbnVtYmVyIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMXJlbSwgNXZ3LCAxLjc1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5zdGF0LW51bWJlci1yb3cgLnN0YXQtdW5pdCB7DQogICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNzVyZW0sIDN2dywgMXJlbSkgIWltcG9ydGFudDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAuc2Vjb25kLXN0YXRzIHsNCiAgICAuc3RhdC1jYXJkLXNtYWxsIHsNCiAgICAgIG1pbi1oZWlnaHQ6IDYwcHg7DQogICAgICBwYWRkaW5nOiAyLjUlOw0KDQogICAgICAuc3RhdC1jb250ZW50LXNtYWxsIHsNCiAgICAgICAgLnN0YXQtdGl0bGUtc21hbGwgew0KICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDN2dywgMC44NzVyZW0pICFpbXBvcnRhbnQ7DQogICAgICAgIH0NCg0KICAgICAgICAuc3RhdC1udW1iZXItcm93LXNtYWxsIC5zdGF0LW51bWJlci1zbWFsbCB7DQogICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjg3NXJlbSwgNHZ3LCAxLjI1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtbnVtYmVyLXJvdy1zbWFsbCAuc3RhdC11bml0LXNtYWxsIHsNCiAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAzdncsIDAuODc1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLnN0YXQtaWNvbi1pbWcgew0KICAgIHdpZHRoOiAzMnB4ICFpbXBvcnRhbnQ7DQogICAgaGVpZ2h0OiAzMnB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAucXVhbGl0eS1zdGF0cyAuc3RhdC1ncm91cCAuc3RhdC1pdGVtIC5zdGF0LW51bWJlciB7DQogICAgZm9udC1zaXplOiBjbGFtcCgwLjc1cmVtLCAzLjV2dywgMXJlbSkgIWltcG9ydGFudDsNCiAgfQ0KDQogIC5zYWZldHktc3RhdC1tYWluIC5zYWZldHktc3RhdC1udW1iZXIgew0KICAgIGZvbnQtc2l6ZTogY2xhbXAoMC42MjVyZW0sIDN2dywgMXJlbSkgIWltcG9ydGFudDsNCiAgICAvKiDov5vkuIDmraXlh4/lsI/vvJoxMHB4LTE2cHggKi8NCiAgfQ0KDQogIC8vIOaWsOeahOWuieWFqOeuoeeQhuenu+WKqOerr+agt+W8jw0KICAuc2FmZXR5LXRvcC1zdGF0cyB7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDElOw0KDQogICAgLnNhZmV0eS1zdGF0LWNhcmQtbmV3IHsNCiAgICAgIHBhZGRpbmc6IDMlOw0KDQogICAgICAuc2FmZXR5LXN0YXQtaWNvbiB7DQogICAgICAgIHdpZHRoOiA0MHB4ICFpbXBvcnRhbnQ7DQogICAgICAgIGhlaWdodDogNDBweCAhaW1wb3J0YW50Ow0KDQogICAgICAgIGkgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMjBweCAhaW1wb3J0YW50Ow0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5zYWZldHktc3RhdC1jb250ZW50LW5ldyB7DQogICAgICAgIC5zYWZldHktc3RhdC1sYWJlbCB7DQogICAgICAgICAgZm9udC1zaXplOiBjbGFtcCgwLjc1cmVtLCAzdncsIDFyZW0pICFpbXBvcnRhbnQ7DQogICAgICAgIH0NCg0KICAgICAgICAuc2FmZXR5LXN0YXQtbnVtYmVyLW5ldyB7DQogICAgICAgICAgLm51bWJlciB7DQogICAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDFyZW0sIDR2dywgMS41cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC51bml0IHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC43NXJlbSwgM3Z3LCAxcmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5zYWZldHktYm90dG9tLXN0YXRzIHsNCiAgICAuc3RhdHMtcm93IHsNCiAgICAgIC5zdGF0LWl0ZW0gew0KICAgICAgICBnYXA6IDElOw0KDQogICAgICAgIC5zdGF0LWxhYmVsIHsNCiAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAzdncsIDAuODc1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICAgIG1pbi13aWR0aDogMjVweCAhaW1wb3J0YW50Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnN0YXQtdmFsdWUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogY2xhbXAoMC43NXJlbSwgMy41dncsIDFyZW0pICFpbXBvcnRhbnQ7DQogICAgICAgIH0NCg0KICAgICAgICAuc3RhdC11bml0IHsNCiAgICAgICAgICBmb250LXNpemU6IGNsYW1wKDAuNjI1cmVtLCAzdncsIDAuODc1cmVtKSAhaW1wb3J0YW50Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLm1hbmFnZW1lbnQtY2FyZCB7DQogICAgaGVpZ2h0OiAzMjBweDsNCiAgfQ0KDQogIC5jaGFydC1jYXJkIHsNCiAgICBoZWlnaHQ6IDI4MHB4Ow0KICB9DQoNCiAgLnNhZmV0eS1zdGF0cy1yb3cgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxJTsNCiAgfQ0KDQogIC5xdWFsaXR5LXN0YXRzIC5zdGF0LWdyb3VwIHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMSU7DQogIH0NCn0NCg0KLy8g5YWo5bGA5qC35byP6KaG55uWDQo6OnYtZGVlcCAuZWwtY2FyZCB7DQogIGJvcmRlcjogbm9uZTsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KOjp2LWRlZXAgLmVsLWNhcmRfX2hlYWRlciB7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOw0KICBwYWRkaW5nOiAxcmVtIDEuMjVyZW07DQp9DQoNCjo6di1kZWVwIC5lbC1jYXJkX19ib2R5IHsNCiAgcGFkZGluZzogMS4yNXJlbTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA42DA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"app-container home bg\">\r\n    <!-- 顶部统计卡片 -->\r\n    <div class=\"top-stats\">\r\n      <!-- 超危大工程 -->\r\n      <div class=\"stat-card stat-card-1\">\r\n        <div class=\"stat-header\">\r\n          <h3>超危大工程</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">超危工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.superDangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">危大工程数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.dangerousProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 安全生产 -->\r\n      <div class=\"stat-card stat-card-2\">\r\n        <div class=\"stat-header\">\r\n          <h3>安全生产</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际安全生产投入</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalSafetyInvestment }}</span>\r\n              <span class=\"stat-unit\">万</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 在建项目 -->\r\n      <div class=\"stat-card stat-card-3\">\r\n        <div class=\"stat-header\">\r\n          <h3>在建项目</h3>\r\n        </div>\r\n        <div class=\"stat-content-dual\">\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国内在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.domesticProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"stat-item\">\r\n            <div class=\"stat-title\">国际在建项目数</div>\r\n            <div class=\"stat-number-row\">\r\n              <span class=\"stat-number\">{{ statsData.internationalProjects }}</span>\r\n              <span class=\"stat-unit\">个</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 第二行统计卡片 -->\r\n    <div class=\"second-stats\">\r\n      <div class=\"stat-card-small stat-card-small-1\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-office-building\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">企业分支</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.companyBranches }}</span>\r\n            <span class=\"stat-unit-small\">个</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-2\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">伤亡事故人数</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.casualties }}</span>\r\n            <span class=\"stat-unit-small\">人</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-3\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-warning-outline\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">重大危险源项目</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.majorHazards }}</span>\r\n            <span class=\"stat-unit-small\">项</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card-small stat-card-small-4\">\r\n        <div class=\"stat-icon\">\r\n          <i class=\"el-icon-cpu\"></i>\r\n        </div>\r\n        <div class=\"stat-content-small\">\r\n          <div class=\"stat-title-small\">大型设备数量</div>\r\n          <div class=\"stat-number-row-small\">\r\n            <span class=\"stat-number-small\">{{ statsData.largeEquipment }}</span>\r\n            <span class=\"stat-unit-small\">台</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 第三行：超危大工程和安全生产投入 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>超危大工程</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"dangerous-chart-container\">\r\n              <div\r\n                ref=\"dangerousProjectChart\"\r\n                class=\"chart-container\"\r\n                style=\"height: 280px\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"12\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>安全生产投入</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div class=\"safety-investment-container\">\r\n              <!-- 左侧环形图 -->\r\n              <div class=\"pie-chart-section\">\r\n                <div class=\"chart-wrapper\">\r\n                  <pieChart \r\n                    height=\"280px\" \r\n                    :data=\"safetyInvestmentPieChart\" \r\n                    :showCenterText=\"true\"\r\n                    :centerText=\"safetyInvestmentTotal\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              <!-- 右侧项目列表 -->\r\n              <div class=\"project-list-section\">\r\n                <div class=\"project-list\">\r\n                  <div \r\n                    v-for=\"(item, index) in safetyInvestmentProjects\" \r\n                    :key=\"index\"\r\n                    class=\"project-item\"\r\n                  >\r\n                    <div class=\"project-dot\" :style=\"{backgroundColor: item.color}\"></div>\r\n                    <div class=\"project-info\">\r\n                      <div class=\"project-name\">{{ item.name }}</div>\r\n                      <div class=\"project-amount\">{{ item.value }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 底部：质量管理和安全管理 -->\r\n    <el-row :gutter=\"20\" class=\"management-row\">\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>质量管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <!-- timeType=1  月  2：年 -->\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 2 }\"\r\n                  @click=\"changeQualityTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: qualityTimeType === 1 }\"\r\n                  @click=\"changeQualityTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div style=\"background: #f5f7f9; padding: 10px 0px 1px 20px\">\r\n            <!-- 检查下发部分 -->\r\n            <div class=\"section-header\">\r\n              <span class=\"section-title\">检查下发</span>\r\n            </div>\r\n            <div class=\"quality-stats\">\r\n              <div class=\"stat-group\">\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"检查计划数\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">检查计划数</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.inspectionPlans }}\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >日常巡检\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.dailyInspections\r\n                      }}</span>\r\n                      次</span\r\n                    >\r\n                    <span\r\n                      >专项巡检\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.specialInspections\r\n                      }}</span>\r\n                      次</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"发现问题数\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">发现问题数</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.foundProblems }}\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >已整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.fixedProblems\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >待整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.pendingProblems\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"按时整改率\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">按时整改率</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.onTimeRate }}%\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.onTimeFixed\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >未按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.overdueFixed\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div\r\n            style=\"\r\n              background: #f5f7f9;\r\n              padding: 10px 0px 1px 20px;\r\n              margin-top: 20px;\r\n            \"\r\n          >\r\n            <!-- 自主上报部分 -->\r\n            <div class=\"section-header\">\r\n              <span class=\"section-title\">自主上报</span>\r\n            </div>\r\n            <div class=\"quality-stats\">\r\n              <div class=\"stat-group\">\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"自主上报问题数\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">自主上报问题数</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.selfReportProblems }}\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >已整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportFixed\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >待整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportPending\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n\r\n                <div class=\"stat-item\">\r\n                  <div class=\"stat-header\">\r\n                    <div class=\"stat-icon\">\r\n                      <img\r\n                        src=\"@/assets/images/home/<USER>\"\r\n                        alt=\"按时整改率\"\r\n                        class=\"check-icon-img\"\r\n                      />\r\n                    </div>\r\n                    <div class=\"stat-label\">按时整改率</div>\r\n                  </div>\r\n                  <div class=\"stat-number\">\r\n                    {{ qualityManagement.selfReportRate }}%\r\n                  </div>\r\n                  <div class=\"stat-detail\">\r\n                    <span\r\n                      >按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportOnTime\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                    <span\r\n                      >未按时整改\r\n                      <span class=\"number\">{{\r\n                        qualityManagement.selfReportOverdue\r\n                      }}</span>\r\n                      个</span\r\n                    >\r\n                  </div>\r\n                </div>\r\n                <!-- 空占位符，用于对齐 -->\r\n                <div class=\"stat-item stat-placeholder\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\">\r\n        <div class=\"management-card\">\r\n          <div class=\"management-header\">\r\n            <div class=\"header-content\">\r\n              <h3>安全管理</h3>\r\n              <div class=\"time-tabs\">\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 2 }\"\r\n                  @click=\"changeSafetyTimeType(2)\"\r\n                >\r\n                  本年\r\n                </span>\r\n                <span class=\"tab-divider\">/</span>\r\n                <span\r\n                  class=\"tab-item\"\r\n                  :class=\"{ active: safetyTimeType === 1 }\"\r\n                  @click=\"changeSafetyTimeType(1)\"\r\n                >\r\n                  本月\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 顶部统计卡片 -->\r\n          <div class=\"safety-top-stats\">\r\n            <div class=\"safety-stat-card-new\">\r\n              <div class=\"safety-stat-icon\">\r\n                <i class=\"el-icon-warning-outline\"></i>\r\n              </div>\r\n              <div class=\"safety-stat-content-new\">\r\n                <div class=\"safety-stat-label\">安全隐患数</div>\r\n                <div class=\"safety-stat-number-new\">\r\n                  <span class=\"number\">{{ safetyManagement.hazardCount }}</span>\r\n                  <span class=\"unit\">个</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"safety-stat-card-new\">\r\n              <div class=\"safety-stat-icon rate-icon\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n              </div>\r\n              <div class=\"safety-stat-content-new\">\r\n                <div class=\"safety-stat-label\">按时整改率</div>\r\n                <div class=\"safety-stat-number-new\">\r\n                  <span class=\"number\">{{ safetyManagement.reportOnTimeRate }}</span>\r\n                  <span class=\"unit\">%</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 饼状图区域 -->\r\n          <div class=\"safety-chart-area\">\r\n            <pieChart\r\n              :data=\"safetyManagement.hazardTypeChart2\"\r\n              height=\"180px\"\r\n              :showCenterText=\"true\"\r\n              :centerText=\"{\r\n                value: safetyManagement.hazardCount.toString(),\r\n                unit: '安全隐患总数',\r\n                label: ''\r\n              }\"\r\n            />\r\n          </div>\r\n\r\n          <!-- 底部统计数据 -->\r\n          <div class=\"safety-bottom-stats\">\r\n            <div class=\"stats-row\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #2656F5;\"></div>\r\n                <span class=\"stat-label\">待整改</span>\r\n                <span class=\"stat-value\">4</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #FF920D;\"></div>\r\n                <span class=\"stat-label\">已整改</span>\r\n                <span class=\"stat-value\">4</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"stats-row\">\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #54C255;\"></div>\r\n                <span class=\"stat-label\">已合格</span>\r\n                <span class=\"stat-value\">50</span>\r\n                <span class=\"stat-unit\">个</span>\r\n              </div>\r\n              <div class=\"stat-item\">\r\n                <div class=\"stat-dot\" style=\"background: #8EE98F;\"></div>\r\n                <span class=\"stat-label\">整改率</span>\r\n                <span class=\"stat-value\">50</span>\r\n                <span class=\"stat-unit\">%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 底部大型设备图表 -->\r\n    <el-row :gutter=\"20\" class=\"charts-row\">\r\n      <el-col :span=\"24\">\r\n        <div class=\"chart-card\">\r\n          <div class=\"chart-header\">\r\n            <h4>大型设备</h4>\r\n          </div>\r\n          <div class=\"chart-content\">\r\n            <div\r\n              class=\"equipment-chart-container\"\r\n              @mousemove=\"handleEquipmentMouseMove\"\r\n              @mouseleave=\"handleEquipmentMouseLeave\"\r\n            >\r\n              <barChart\r\n                height=\"300px\"\r\n                :data=\"largeEquipmentChart\"\r\n                :show-tooltip=\"false\"\r\n              />\r\n\r\n              <!-- 大型设备悬浮框 -->\r\n              <div\r\n                v-show=\"equipmentTooltip.visible\"\r\n                class=\"project-tooltip\"\r\n                :style=\"{\r\n                  left: equipmentTooltip.x + 'px',\r\n                  top: equipmentTooltip.y + 'px',\r\n                }\"\r\n              >\r\n                <div class=\"tooltip-header\">{{ equipmentTooltip.title }}</div>\r\n                <div v-if=\"equipmentTooltip.loading\" class=\"tooltip-loading\">\r\n                  加载中...\r\n                </div>\r\n                <div v-else-if=\"equipmentTooltip.error\" class=\"tooltip-error\">\r\n                  {{ equipmentTooltip.error }}\r\n                </div>\r\n                <div\r\n                  v-else-if=\"equipmentTooltip.data.length > 0\"\r\n                  class=\"tooltip-content\"\r\n                >\r\n                  <div\r\n                    v-for=\"item in equipmentTooltip.data\"\r\n                    :key=\"item.name\"\r\n                    class=\"tooltip-item\"\r\n                  >\r\n                    <span class=\"tooltip-name\">{{ item.name }}</span>\r\n                    <span class=\"tooltip-value\">{{ item.value }}</span>\r\n                  </div>\r\n                </div>\r\n                <div v-else class=\"tooltip-empty\">暂无详细数据</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport barChart from \"./components/barChart.vue\";\r\nimport pieChart from \"./components/pieChart.vue\";\r\nimport {\r\n  getManagementOverview,\r\n  getQualityStatistics,\r\n  getSafetyStatistics,\r\n  getDangerTypeStatistics,\r\n  getSafetyProductionStatistics,\r\n  getDangerousProStatistics,\r\n  getLargeEquipmentStatistics,\r\n  getLargeEquipmentByNameStatistics,\r\n} from \"@/api/statistics\";\r\nexport default {\r\n  name: \"Index\",\r\n  components: {\r\n    barChart,\r\n    pieChart,\r\n  },\r\n  data() {\r\n    return {\r\n      // 时间类型选择 (1-月, 2-年)\r\n      qualityTimeType: 2, // 默认为年\r\n      safetyTimeType: 2, // 默认为年\r\n      // 顶部统计数据\r\n      statsData: {\r\n        // 原有数据\r\n        domesticProjects: 1126,\r\n        internationalProjects: 1126,\r\n        safetyInvestment: 1500,\r\n        largeEquipment: 1126,\r\n        dangerousProjects: 1126,\r\n        // 新增数据\r\n        superDangerousProjects: 1126,\r\n        domesticSafetyInvestment: 1126,\r\n        internationalSafetyInvestment: 1126,\r\n        companyBranches: 1126,\r\n        casualties: 0,\r\n        majorHazards: 1126,\r\n      },\r\n      // 质量管理数据\r\n      qualityManagement: {\r\n        // 检查下发\r\n        inspectionPlans: 122,\r\n        dailyInspections: 89,\r\n        specialInspections: 33,\r\n        foundProblems: 31,\r\n        fixedProblems: 29,\r\n        pendingProblems: 2,\r\n        onTimeRate: 96,\r\n        onTimeFixed: 28,\r\n        overdueFixed: 1,\r\n        // 自主上报\r\n        selfReportProblems: 103,\r\n        selfReportFixed: 100,\r\n        selfReportPending: 3,\r\n        selfReportRate: 86.45,\r\n        selfReportOnTime: 31,\r\n        selfReportOverdue: 7,\r\n      },\r\n      // 安全管理数据\r\n      safetyManagement: {\r\n        // 检查下发\r\n        inspectionCount: 58,\r\n        hazardFound: 20,\r\n        onTimeRate: 89,\r\n        // 自主上报\r\n        hazardCount: 103,\r\n        reportOnTimeRate: 86.45,\r\n        // 隐患类别统计图表\r\n        hazardTypeChart1: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#E54545\",\r\n            \"#FECF77\",\r\n            \"#FF7730\",\r\n            \"#B38DFF\",\r\n            \"#A1FFEB\",\r\n          ],\r\n          data: [\r\n            { value: 35, name: \"基础设施\" },\r\n            { value: 28, name: \"设备维护\" },\r\n            { value: 25, name: \"消防安全\" },\r\n            { value: 22, name: \"电气安全\" },\r\n            { value: 18, name: \"高空作业\" },\r\n            { value: 15, name: \"机械操作\" },\r\n            { value: 12, name: \"化学品管理\" },\r\n            { value: 10, name: \"个人防护\" },\r\n            { value: 8, name: \"环境卫生\" },\r\n            { value: 5, name: \"其他\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n        hazardTypeChart2: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#FF920D\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n          ],\r\n          data: [\r\n            { value: 15, name: \"基础设施\" },\r\n            { value: 12, name: \"设备维护\" },\r\n            { value: 11, name: \"消防安全\" },\r\n            { value: 10, name: \"电气安全\" },\r\n            { value: 10, name: \"高空作业\" },\r\n          ],\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        },\r\n      },\r\n      // 自主上报数据\r\n      selfReport: {\r\n        reportCount: 103,\r\n        completed: 100,\r\n        pending: 3,\r\n        onTimeRate: 86.45,\r\n        onTimeCompleted: 31,\r\n        overdueCompleted: 7,\r\n      },\r\n      // 统计分析数据\r\n      statisticsAnalysis: {\r\n        overallChart: {\r\n          colorList: [\r\n            \"#2656F5\",\r\n            \"#8EE98F\",\r\n            \"#A1FFEB\",\r\n            \"#54C255\",\r\n            \"#A1CDFF\",\r\n            \"#FF920D\",\r\n          ],\r\n          data: [\r\n            { value: 25, name: \"安全基础管理\" },\r\n            { value: 20, name: \"消防安全\" },\r\n            { value: 18, name: \"电气安全\" },\r\n            { value: 15, name: \"特种设备\" },\r\n            { value: 12, name: \"危化品\" },\r\n            { value: 10, name: \"其他\" },\r\n          ],\r\n        },\r\n      },\r\n      // 危大工程项目列表数据\r\n      dangerousProjectsList: [\r\n        { name: \"苏电产业科创园(NO.2010G32)07-13地块项目\", progress: 100 },\r\n        { name: \"未来出行产业园项目（直流分公司）\", progress: 80 },\r\n        { name: \"华为网络石代表处项目\", progress: 70 },\r\n        { name: \"年产3001万件汽车底盘等部件生产线项目\", progress: 30 },\r\n        { name: \"泪源城土壤生产及新客体验中心二期建设项目\", progress: 30 },\r\n      ],\r\n      // 危大工程图表配置\r\n      dangerousProjectChart: {\r\n        colorList: [\"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\", \"#5990FD\"],\r\n        grid: {\r\n          top: 30,\r\n          left: \"35%\",\r\n          right: \"10%\",\r\n          bottom: \"5%\",\r\n        },\r\n        xAxis: {\r\n          type: \"value\",\r\n          max: 10,\r\n          axisLabel: {\r\n            show: false,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"苏电产业科创园(NO.2010G32) 07-13地块项目\",\r\n            \"未来出行产业园项目（直流分公司）\",\r\n            \"华为网络石代表处项目\",\r\n            \"年产3001万件汽车底盘等部件生产线项目\",\r\n            \"泪源城土壤生产及新客体验中心二期建设项目\",\r\n          ],\r\n          axisLabel: {\r\n            fontSize: 12,\r\n            color: \"#333\",\r\n            interval: 0,\r\n          },\r\n          axisTick: {\r\n            show: false,\r\n          },\r\n          axisLine: {\r\n            show: false,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"危大工程数量\",\r\n            type: \"bar\",\r\n            data: [10, 8, 7, 3, 3],\r\n            itemStyle: {\r\n              color: \"#5990FD\",\r\n              borderRadius: [0, 4, 4, 0],\r\n            },\r\n            barWidth: \"60%\",\r\n            label: {\r\n              show: true,\r\n              position: \"right\",\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n              formatter: \"{c}\",\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      largeEquipmentChart: {\r\n        colorList: [\r\n          \"#FF920D\",\r\n          \"#FECF77\",\r\n          \"#FF7730\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#2C2C2C\",\r\n        ],\r\n        grid: {\r\n          top: 30,\r\n          left: \"8%\",\r\n          right: \"8%\",\r\n          bottom: \"25%\",\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          data: [\r\n            \"设备分类1\",\r\n            \"设备分类2\",\r\n            \"设备分类3\",\r\n            \"设备分类4\",\r\n            \"设备分类5\",\r\n            \"设备分类6\",\r\n          ],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0,\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"value\",\r\n          max: 210,\r\n          axisLabel: {\r\n            fontSize: 12,\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            name: \"未安装\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF920D\" },\r\n            data: [35, 0, 20, 0, 0, 35],\r\n          },\r\n          {\r\n            name: \"安装中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FECF77\" },\r\n            data: [0, 0, 0, 0, 0, 5],\r\n          },\r\n          {\r\n            name: \"验收中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#FF7730\" },\r\n            data: [0, 0, 10, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"运行中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#54C255\" },\r\n            data: [175, 120, 150, 30, 180, 150],\r\n          },\r\n          {\r\n            name: \"维修中\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2656F5\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n          {\r\n            name: \"已报废\",\r\n            type: \"bar\",\r\n            stack: \"total\",\r\n            itemStyle: { color: \"#2C2C2C\" },\r\n            data: [0, 0, 0, 0, 0, 0],\r\n          },\r\n        ],\r\n      },\r\n      // 安全生产投入环形图数据\r\n      safetyInvestmentPieChart: {\r\n        colorList: [\r\n          \"#2656F5\",\r\n          \"#FF920D\", \r\n          \"#54C255\",\r\n          \"#E54545\",\r\n          \"#8EE98F\",\r\n          \"#A1CDFF\"\r\n        ],\r\n        data: [\r\n          { value: 2000, name: \"海外工程一公司\" },\r\n          { value: 2000, name: \"海外工程二公司\" },\r\n          { value: 2000, name: \"海外工程三公司\" },\r\n          { value: 2000, name: \"中江国际集团\" },\r\n          { value: 2000, name: \"第五建设分公司\" }\r\n        ],\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"50%\"],\r\n              radius: [\"55%\", \"75%\"],\r\n              label: {\r\n                show: false\r\n              },\r\n              labelLine: {\r\n                show: false\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      },\r\n      // 安全生产投入总金额\r\n      safetyInvestmentTotal: {\r\n        value: \"6000\",\r\n        unit: \"总投入(万元)\",\r\n        label: \"安全生产投入\"\r\n      },\r\n      // 安全生产投入项目列表\r\n      safetyInvestmentProjects: [\r\n        { name: \"海外工程一公司\", value: \"2000\", color: \"#2656F5\" },\r\n        { name: \"海外工程二公司\", value: \"2000\", color: \"#FF920D\" },\r\n        { name: \"海外工程三公司\", value: \"2000\", color: \"#54C255\" },\r\n        { name: \"中江国际集团\", value: \"2000\", color: \"#E54545\" },\r\n        { name: \"第五建设分公司\", value: \"2000\", color: \"#8EE98F\" }\r\n      ],\r\n      // 保留这些旧数据结构以防止报错，后续可以逐步清理\r\n      lineData: {\r\n        grid: {\r\n          top: 10,\r\n          left: \"6%\",\r\n          right: \"6%\",\r\n          bottom: \"12%\",\r\n        },\r\n        xAxisData: [],\r\n        seriesData: [],\r\n      },\r\n      mainData: {},\r\n      yearCount: {},\r\n      dangerList: [],\r\n      echartData: { colorList: [], data: [] },\r\n      cateBarData: { colorList: [], series: [] },\r\n      yearBarData: { series: [] },\r\n      chart2Lengend: [],\r\n      echartType1: 1,\r\n      echartType2: 1,\r\n      echartTypeList1: [],\r\n      echartTypeList2: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 悬浮框相关数据\r\n      tooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      // 危大工程图表实例\r\n      dangerousProjectChartInstance: null,\r\n      // 设备详细信息缓存\r\n      equipmentDetailsCache: {},\r\n      // 设备悬浮框相关数据\r\n      equipmentTooltip: {\r\n        visible: false,\r\n        x: 0,\r\n        y: 0,\r\n        title: \"\",\r\n        loading: false,\r\n        error: \"\",\r\n        data: [],\r\n      },\r\n      requestQueue: new Set(), // 正在请求的项目名称队列\r\n    };\r\n  },\r\n  created() {\r\n    this.loadManagementOverview();\r\n    this.loadQualityStatistics();\r\n    this.loadSafetyStatistics();\r\n    this.loadDangerTypeStatistics();\r\n    this.loadSafetyProductionStatistics();\r\n    this.loadDangerousProStatistics();\r\n    this.loadLargeEquipmentStatistics();\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁图表实例\r\n    if (this.dangerousProjectChartInstance) {\r\n      this.dangerousProjectChartInstance.dispose();\r\n      this.dangerousProjectChartInstance = null;\r\n    }\r\n\r\n    // 清理请求队列\r\n    this.requestQueue.clear();\r\n  },\r\n  methods: {\r\n    // 切换质量管理时间类型\r\n    changeQualityTimeType(timeType) {\r\n      if (this.qualityTimeType !== timeType) {\r\n        this.qualityTimeType = timeType;\r\n        this.loadQualityStatistics();\r\n      }\r\n    },\r\n\r\n    // 切换安全管理时间类型\r\n    changeSafetyTimeType(timeType) {\r\n      if (this.safetyTimeType !== timeType) {\r\n        this.safetyTimeType = timeType;\r\n        this.loadSafetyStatistics();\r\n        this.loadDangerTypeStatistics();\r\n      }\r\n    },\r\n\r\n    // 获取安全管理总览数据\r\n    async loadManagementOverview() {\r\n      try {\r\n        console.log(\"开始加载安全管理总览数据...\");\r\n        const response = await getManagementOverview();\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的数据结构\r\n          this.statsData.dangerousProjects = data.wdgcs || 0; // 危大工程数\r\n          this.statsData.safetyInvestment = data.aqtzzje || 0; // 安全投资总金额\r\n          this.statsData.domesticProjects = data.gnzjxms || 0; // 国内在建项目\r\n          this.statsData.internationalProjects = data.gjzjxms || 0; // 国际在建项目\r\n          this.statsData.largeEquipment = data.dxsbs || 0; // 大型设备数\r\n\r\n          console.log(\"安全管理总览数据加载成功:\", this.statsData);\r\n        } else {\r\n          console.warn(\"接口返回数据格式异常:\", response);\r\n          this.handleDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理总览数据失败:\", error);\r\n        this.handleDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理数据加载错误\r\n    handleDataLoadError() {\r\n      console.log(\"使用默认数据\");\r\n      // 保持原有的默认数据，确保页面正常显示\r\n      this.$modal.msgWarning(\"数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取质量管理统计数据\r\n    async loadQualityStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载质量管理统计数据...\",\r\n          \"timeType:\",\r\n          this.qualityTimeType\r\n        );\r\n        const response = await getQualityStatistics({\r\n          timeType: this.qualityTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的质量管理数据结构（自主上报部分）\r\n          this.qualityManagement.selfReportProblems = data.jczs || 0; // 自主上报问题数\r\n          this.qualityManagement.selfReportFixed = data.ywcsl || 0; // 已整改数量\r\n          this.qualityManagement.selfReportPending = data.kwcsl || 0; // 待整改数量\r\n          this.qualityManagement.selfReportRate = data.aszgl\r\n            ? data.aszgl * 100\r\n            : 0; // 按时整改率（转换为百分比）\r\n          this.qualityManagement.selfReportOnTime = data.aszg || 0; // 按时整改\r\n          this.qualityManagement.selfReportOverdue = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"质量管理统计数据加载成功:\", this.qualityManagement);\r\n        } else {\r\n          console.warn(\"质量管理统计接口返回数据格式异常:\", response);\r\n          this.handleQualityDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取质量管理统计数据失败:\", error);\r\n        this.handleQualityDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理质量管理数据加载错误\r\n    handleQualityDataLoadError() {\r\n      console.log(\"使用质量管理默认数据\");\r\n      this.$modal.msgWarning(\"质量管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全管理统计数据\r\n    async loadSafetyStatistics() {\r\n      try {\r\n        console.log(\r\n          \"开始加载安全管理统计数据...\",\r\n          \"timeType:\",\r\n          this.safetyTimeType\r\n        );\r\n        const response = await getSafetyStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        if (response && response.code === 200 && response.data) {\r\n          const data = response.data;\r\n\r\n          // 映射接口数据到现有的安全管理数据结构（自主上报部分）\r\n          this.safetyManagement.hazardCount = data.jczs || 0; // 隐患数（问题数）\r\n          this.safetyManagement.reportOnTimeRate = data.aszgl\r\n            ? (data.aszgl * 100).toFixed(1)\r\n            : 0; // 按时整改率（转换为百分比）\r\n          // 为了保持数据一致性，也可以存储更多详细数据备用\r\n          this.safetyManagement.safetyFixedProblems = data.ywcsl || 0; // 已整改数量\r\n          this.safetyManagement.safetyPendingProblems = data.kwcsl || 0; // 待整改数量\r\n          this.safetyManagement.safetyOnTimeFixed = data.aszg || 0; // 按时整改\r\n          this.safetyManagement.safetyOverdueFixed = data.waszg || 0; // 未按时整改\r\n\r\n          console.log(\"安全管理统计数据加载成功:\", this.safetyManagement);\r\n        } else {\r\n          console.warn(\"安全管理统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全管理统计数据失败:\", error);\r\n        this.handleSafetyDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全管理数据加载错误\r\n    handleSafetyDataLoadError() {\r\n      console.log(\"使用安全管理默认数据\");\r\n      this.$modal.msgWarning(\"安全管理数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取隐患类别统计数据\r\n    async loadDangerTypeStatistics() {\r\n      try {\r\n        console.log(\"开始加载隐患类别统计数据...\");\r\n        const response = await getDangerTypeStatistics({\r\n          timeType: this.safetyTimeType,\r\n        });\r\n\r\n        // 检查下发的图表使用默认数据，总和等于检查次数58\r\n        const defaultDataForInspection = [\r\n          { value: 15, name: \"基础设施\" },\r\n          { value: 12, name: \"设备维护\" },\r\n          { value: 11, name: \"消防安全\" },\r\n          { value: 10, name: \"电气安全\" },\r\n          { value: 10, name: \"高空作业\" },\r\n        ];\r\n\r\n        // 定义颜色数组\r\n        const colorList1 = [\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n          \"#FF920D\",\r\n          \"#54C255\",\r\n          \"#A1CDFF\",\r\n        ];\r\n        const colorList2 = [\r\n          \"#FF920D\",\r\n          \"#E54545\",\r\n          \"#54C255\",\r\n          \"#2656F5\",\r\n          \"#8EE98F\",\r\n        ];\r\n\r\n        // 更新检查下发的隐患类别统计图表（使用默认数据）\r\n        this.safetyManagement.hazardTypeChart1 = {\r\n          colorList: colorList1,\r\n          data: defaultDataForInspection,\r\n          option: {\r\n            series: [\r\n              {\r\n                center: [\"50%\", \"52%\"],\r\n                radius: [\"45%\", \"75%\"],\r\n                label: {\r\n                  show: true,\r\n                  position: \"outside\",\r\n                  formatter: \"{b}\\n{c}\",\r\n                  fontSize: 10,\r\n                  color: \"#666\",\r\n                  lineHeight: 14,\r\n                },\r\n                labelLine: {\r\n                  show: true,\r\n                  length: 8,\r\n                  length2: 15,\r\n                  lineStyle: {\r\n                    color: \"#666\",\r\n                    width: 1,\r\n                  },\r\n                },\r\n              },\r\n            ],\r\n          },\r\n        };\r\n\r\n        // 自主上报的图表使用接口数据\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n          // 对数据按value值降序排序，只取前5个\r\n          const sortedData = data\r\n            .sort((a, b) => (b.value || 0) - (a.value || 0))\r\n            .slice(0, 5);\r\n\r\n          // 更新自主上报的隐患类别统计图表（使用接口数据）\r\n          this.safetyManagement.hazardTypeChart2 = {\r\n            colorList: colorList2,\r\n            data: sortedData,\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"52%\"],\r\n                  radius: [\"45%\", \"75%\"],\r\n                  label: {\r\n                    show: true,\r\n                    position: \"outside\",\r\n                    formatter: \"{b}\\n{c}\",\r\n                    fontSize: 10,\r\n                    color: \"#666\",\r\n                    lineHeight: 14,\r\n                  },\r\n                  labelLine: {\r\n                    show: true,\r\n                    length: 8,\r\n                    length2: 15,\r\n                    lineStyle: {\r\n                      color: \"#666\",\r\n                      width: 1,\r\n                    },\r\n                  },\r\n                },\r\n              ],\r\n            },\r\n          };\r\n\r\n          console.log(\"隐患类别统计数据加载成功:\", {\r\n            chart1: \"使用默认数据\",\r\n            chart2: this.safetyManagement.hazardTypeChart2,\r\n          });\r\n        } else {\r\n          console.warn(\"隐患类别统计接口返回数据格式异常:\", response);\r\n          this.handleDangerTypeDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取隐患类别统计数据失败:\", error);\r\n        this.handleDangerTypeDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理隐患类别统计数据加载错误\r\n    handleDangerTypeDataLoadError() {\r\n      console.log(\"自主上报图表使用默认数据\");\r\n      // 如果接口失败，自主上报图表也使用默认数据\r\n      const defaultDataForReport = [\r\n        { value: 8, name: \"基础设施\" },\r\n        { value: 7, name: \"设备维护\" },\r\n        { value: 6, name: \"消防安全\" },\r\n        { value: 5, name: \"电气安全\" },\r\n        { value: 4, name: \"高空作业\" },\r\n      ];\r\n\r\n      const colorList2 = [\r\n        \"#FF920D\",\r\n        \"#E54545\",\r\n        \"#54C255\",\r\n        \"#2656F5\",\r\n        \"#8EE98F\",\r\n      ];\r\n\r\n      this.safetyManagement.hazardTypeChart2 = {\r\n        colorList: colorList2,\r\n        data: defaultDataForReport,\r\n        option: {\r\n          series: [\r\n            {\r\n              center: [\"50%\", \"52%\"],\r\n              radius: [\"45%\", \"75%\"],\r\n              label: {\r\n                show: true,\r\n                position: \"outside\",\r\n                formatter: \"{b}\\n{c}\",\r\n                fontSize: 10,\r\n                color: \"#666\",\r\n                lineHeight: 14,\r\n              },\r\n              labelLine: {\r\n                show: true,\r\n                length: 8,\r\n                length2: 15,\r\n                lineStyle: {\r\n                  color: \"#666\",\r\n                  width: 1,\r\n                },\r\n              },\r\n            },\r\n          ],\r\n        },\r\n      };\r\n      this.$modal.msgWarning(\"隐患类别统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取安全生产投入统计数据\r\n    async loadSafetyProductionStatistics() {\r\n      try {\r\n        console.log(\"开始加载安全生产投入统计数据...\");\r\n        const response = await getSafetyProductionStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取公司名称作为X轴标签（截取显示）\r\n          const xAxisData = data.map((item) => {\r\n            // 截取公司名称用于X轴显示，保持图表美观\r\n            const companyName = item.company || \"未知公司\";\r\n            return companyName.length > 10\r\n              ? companyName.substring(0, 10) + \"...\"\r\n              : companyName;\r\n          });\r\n\r\n          // 保存完整的公司名称用于tooltip显示\r\n          const fullCompanyNames = data.map(\r\n            (item) => item.company || \"未知公司\"\r\n          );\r\n\r\n          // 提取年度预算金额数据，并保存完整公司名称\r\n          const budgetData = data.map((item, index) => ({\r\n            value: parseFloat(item.annualBudgetAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n          // 提取实际投入金额数据，并保存完整公司名称\r\n          const actualData = data.map((item, index) => ({\r\n            value: parseFloat(item.actualInputAmount || 0),\r\n            fullName: fullCompanyNames[index],\r\n          }));\r\n\r\n          // 动态计算Y轴最大值\r\n          const maxValue = Math.max(\r\n            ...budgetData.map((item) => item.value),\r\n            ...actualData.map((item) => item.value)\r\n          );\r\n          const yAxisMax = Math.ceil((maxValue * 1.2) / 1000) * 1000; // 向上取整到千位\r\n\r\n          // 计算总投入\r\n          const totalInvestment = data.reduce((sum, item) => sum + parseFloat(item.actualInputAmount || 0), 0);\r\n\r\n          // 更新环形图数据\r\n          const colorList = [\"#2656F5\", \"#FF920D\", \"#54C255\", \"#E54545\", \"#8EE98F\", \"#A1CDFF\"];\r\n          \r\n          this.safetyInvestmentPieChart = {\r\n            colorList: colorList,\r\n            data: data.map((item, index) => ({\r\n              value: parseFloat(item.actualInputAmount || 0),\r\n              name: item.company || \"未知公司\"\r\n            })),\r\n            option: {\r\n              series: [\r\n                {\r\n                  center: [\"50%\", \"50%\"],\r\n                  radius: [\"55%\", \"75%\"],\r\n                  label: {\r\n                    show: false\r\n                  },\r\n                  labelLine: {\r\n                    show: false\r\n                  }\r\n                }\r\n              ]\r\n            }\r\n          };\r\n\r\n          // 更新中心文字\r\n          this.safetyInvestmentTotal = {\r\n            value: Math.round(totalInvestment).toString(),\r\n            unit: \"总投入(万元)\",\r\n            label: \"安全生产投入\"\r\n          };\r\n\r\n          // 更新项目列表\r\n          this.safetyInvestmentProjects = data.map((item, index) => ({\r\n            name: item.company || \"未知公司\",\r\n            value: Math.round(parseFloat(item.actualInputAmount || 0)).toString(),\r\n            color: colorList[index % colorList.length]\r\n          }));\r\n\r\n          console.log(\r\n            \"安全生产投入统计数据加载成功:\",\r\n            this.safetyInvestmentChart\r\n          );\r\n        } else {\r\n          console.warn(\"安全生产投入统计接口返回数据格式异常:\", response);\r\n          this.handleSafetyProductionDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取安全生产投入统计数据失败:\", error);\r\n        this.handleSafetyProductionDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理安全生产投入统计数据加载错误\r\n    handleSafetyProductionDataLoadError() {\r\n      console.log(\"使用安全生产投入统计默认数据\");\r\n      this.$modal.msgWarning(\"安全生产投入统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 获取危大工程统计数据\r\n    async loadDangerousProStatistics() {\r\n      try {\r\n        console.log(\"开始加载危大工程统计数据...\");\r\n        const response = await getDangerousProStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 获取前5个项目数据\r\n          const topProjects = data.slice(0, 5);\r\n\r\n          // 找出最大的value值，用于计算X轴最大值\r\n          const maxValue = Math.max(...data.map((item) => item.value));\r\n\r\n          // 提取项目名称和数值，保留完整数据用于tooltip\r\n          const projectNames = topProjects.map((item) => {\r\n            // 截取项目名称，避免过长\r\n            return item.name && item.name.length > 10\r\n              ? item.name.substring(0, 10) + \"...\"\r\n              : item.name || \"未知项目\";\r\n          });\r\n\r\n          // 构建包含详细信息的数据\r\n          const projectData = topProjects.map((item, index) => ({\r\n            name: projectNames[index],\r\n            value: item.value || 0,\r\n            fullName: item.name || \"未知项目\",\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 更新图表配置\r\n          this.dangerousProjectChart.yAxis.data = projectNames.reverse();\r\n          this.dangerousProjectChart.series[0].data = projectData.reverse();\r\n          this.dangerousProjectChart.xAxis.max = maxValue;\r\n\r\n          // 保存原始数据供其他用途使用\r\n          this.dangerousProjectsList = topProjects.map((item) => ({\r\n            name: item.name || \"未知项目\",\r\n            value: item.value || 0,\r\n            originalName: item.name,\r\n            detalList: item.detalList || [],\r\n          }));\r\n\r\n          // 初始化图表\r\n          this.$nextTick(() => {\r\n            this.initDangerousProjectChart();\r\n          });\r\n\r\n          console.log(\"危大工程统计数据加载成功:\", this.dangerousProjectChart);\r\n        } else {\r\n          console.warn(\"危大工程统计接口返回数据格式异常:\", response);\r\n          this.handleDangerousProDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取危大工程统计数据失败:\", error);\r\n        this.handleDangerousProDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理危大工程统计数据加载错误\r\n    handleDangerousProDataLoadError() {\r\n      console.log(\"使用危大工程统计默认数据\");\r\n      this.$modal.msgWarning(\"危大工程统计数据加载失败，显示默认数据\");\r\n      // 使用默认数据初始化图表\r\n      this.$nextTick(() => {\r\n        this.initDangerousProjectChart();\r\n      });\r\n    },\r\n\r\n    // 初始化危大工程图表\r\n    initDangerousProjectChart() {\r\n      if (this.$refs.dangerousProjectChart) {\r\n        // 销毁现有实例\r\n        if (this.dangerousProjectChartInstance) {\r\n          this.dangerousProjectChartInstance.dispose();\r\n        }\r\n\r\n        // 创建新的图表实例\r\n        this.dangerousProjectChartInstance = echarts.init(\r\n          this.$refs.dangerousProjectChart\r\n        );\r\n\r\n        // 设置图表选项\r\n        const option = {\r\n          grid: this.dangerousProjectChart.grid,\r\n          xAxis: this.dangerousProjectChart.xAxis,\r\n          yAxis: this.dangerousProjectChart.yAxis,\r\n          series: this.dangerousProjectChart.series,\r\n          tooltip: {\r\n            show: true,\r\n            trigger: \"item\",\r\n            backgroundColor: \"#fff\",\r\n            borderColor: \"#ccc\",\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: \"#333\",\r\n              fontSize: 12,\r\n            },\r\n            extraCssText:\r\n              \"box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 400px; white-space: normal;\",\r\n            position: function (point, params, dom, rect, size) {\r\n              // 获取 tooltip 的实际尺寸\r\n              const tooltipWidth = size.contentSize[0];\r\n              const tooltipHeight = size.contentSize[1];\r\n\r\n              // 图表容器尺寸\r\n              const chartWidth = rect.width;\r\n              const chartHeight = rect.height;\r\n\r\n              // 计算右边和左边的可用空间\r\n              const rightSpace = chartWidth - point[0] - 10;\r\n              const leftSpace = point[0] - 10;\r\n\r\n              let x, y;\r\n\r\n              // 优先选择空间更大的一边，但确保不会被遮挡\r\n              if (rightSpace >= tooltipWidth || rightSpace > leftSpace) {\r\n                // 显示在右边\r\n                x = point[0] + 10;\r\n                // 如果右边真的放不下，尝试调整到图表右边界内\r\n                if (x + tooltipWidth > chartWidth) {\r\n                  x = chartWidth - tooltipWidth - 5;\r\n                }\r\n              } else if (leftSpace >= tooltipWidth) {\r\n                // 显示在左边，但确保有足够空间\r\n                x = point[0] - tooltipWidth - 10;\r\n                // 确保不会超出左边界\r\n                if (x < 0) {\r\n                  x = 5;\r\n                }\r\n              } else {\r\n                // 如果两边都放不下，选择右边并强制显示在图表内\r\n                x = Math.max(\r\n                  5,\r\n                  Math.min(point[0] + 10, chartWidth - tooltipWidth - 5)\r\n                );\r\n              }\r\n\r\n              // 垂直居中，但确保不超出边界\r\n              y = point[1] - tooltipHeight / 2;\r\n              if (y < 10) {\r\n                y = 10;\r\n              } else if (y + tooltipHeight > chartHeight - 10) {\r\n                y = chartHeight - tooltipHeight - 10;\r\n              }\r\n\r\n              return [x, y];\r\n            },\r\n            formatter: (params) => {\r\n              if (params.data && typeof params.data === \"object\") {\r\n                const { fullName, value, detalList } = params.data;\r\n\r\n                let html = `<div style=\"font-weight: bold; margin-bottom: 8px; color: #333;\">${fullName}</div>`;\r\n                html += `<div style=\"margin-bottom: 8px;\">总数量: <span style=\"color: #1890ff; font-weight: bold;\">${value}</span></div>`;\r\n\r\n                if (detalList && detalList.length > 0) {\r\n                  html +=\r\n                    '<div style=\"border-top: 1px solid #eee; padding-top: 8px;\">';\r\n                  html +=\r\n                    '<div style=\"font-weight: bold; margin-bottom: 6px; color: #666;\">详细信息:</div>';\r\n                  detalList.forEach((item) => {\r\n                    html += `<div style=\"margin-bottom: 4px; padding-left: 8px; border-left: 2px solid #1890ff;\">\r\n                      <div style=\"font-weight: 500;\">${\r\n                        item.name || \"未知类型\"\r\n                      }</div>\r\n                      <div style=\"color: #666; font-size: 11px;\">数量: ${\r\n                        item.value || 0\r\n                      }</div>\r\n                    </div>`;\r\n                  });\r\n                  html += \"</div>\";\r\n                } else {\r\n                  html +=\r\n                    '<div style=\"color: #999; font-style: italic;\">暂无详细信息</div>';\r\n                }\r\n\r\n                return html;\r\n              }\r\n\r\n              // 兜底显示\r\n              return `项目: ${params.name}<br/>数量: ${params.value}`;\r\n            },\r\n          },\r\n        };\r\n\r\n        this.dangerousProjectChartInstance.setOption(option);\r\n\r\n        // 自适应大小\r\n        window.addEventListener(\"resize\", () => {\r\n          if (this.dangerousProjectChartInstance) {\r\n            this.dangerousProjectChartInstance.resize();\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    // 获取大型设备统计数据\r\n    async loadLargeEquipmentStatistics() {\r\n      try {\r\n        console.log(\"开始加载大型设备统计数据...\");\r\n        const response = await getLargeEquipmentStatistics();\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          const data = response.data;\r\n\r\n          // 提取设备名称作为X轴类别\r\n          const equipmentNames = data.map((item) => item.name || \"未知设备\");\r\n\r\n          // 提取设备数量，全部设为\"运行中\"状态\r\n          const runningData = data.map((item) => item.value || 0);\r\n          // 动态计算Y轴最大值，直接使用接口数据的最大值\r\n          const maxValue = Math.max(...runningData);\r\n          const yAxisMax = maxValue > 0 ? maxValue : 10; // Y轴上限就是接口里最大的数据\r\n\r\n          // 更新大型设备图表配置\r\n          this.largeEquipmentChart = {\r\n            colorList: [\r\n              \"#FF920D\",\r\n              \"#FECF77\",\r\n              \"#FF7730\",\r\n              \"#54C255\",\r\n              \"#2656F5\",\r\n              \"#2C2C2C\",\r\n            ],\r\n            grid: {\r\n              top: 30,\r\n              left: \"8%\",\r\n              right: \"8%\",\r\n              bottom: \"25%\",\r\n            },\r\n            legend: undefined, // 显式移除图例\r\n            xAxis: {\r\n              type: \"category\",\r\n              data: equipmentNames,\r\n              axisLabel: {\r\n                interval: 0,\r\n                rotate: 0,\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            yAxis: {\r\n              type: \"value\",\r\n              max: yAxisMax > 0 ? yAxisMax : 10,\r\n              min: 0,\r\n              interval: yAxisMax > 0 ? Math.ceil(yAxisMax / 5) : 2, // 将Y轴等分为5个区间，确保间隔为整数\r\n              axisLabel: {\r\n                fontSize: 12,\r\n              },\r\n            },\r\n            series: [\r\n              {\r\n                name: \"未安装\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF920D\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"安装中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FECF77\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"验收中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#FF7730\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"运行中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#54C255\" },\r\n                data: runningData, // 使用真实数据\r\n              },\r\n              {\r\n                name: \"维修中\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2656F5\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n              {\r\n                name: \"已报废\",\r\n                type: \"bar\",\r\n                stack: \"total\",\r\n                itemStyle: { color: \"#2C2C2C\" },\r\n                data: new Array(data.length).fill(0), // 全部设为0\r\n              },\r\n            ],\r\n          };\r\n\r\n          console.log(\"大型设备统计数据加载成功:\", this.largeEquipmentChart);\r\n        } else {\r\n          console.warn(\"大型设备统计接口返回数据格式异常:\", response);\r\n          this.handleLargeEquipmentDataLoadError();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取大型设备统计数据失败:\", error);\r\n        this.handleLargeEquipmentDataLoadError();\r\n      }\r\n    },\r\n\r\n    // 处理大型设备统计数据加载错误\r\n    handleLargeEquipmentDataLoadError() {\r\n      console.log(\"使用大型设备统计默认数据\");\r\n      this.$modal.msgWarning(\"大型设备统计数据加载失败，显示默认数据\");\r\n    },\r\n\r\n    // 异步加载设备详细信息\r\n    async loadEquipmentDetails(equipmentName) {\r\n      // 如果已经有缓存，直接返回\r\n      if (this.equipmentDetailsCache[equipmentName]) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        console.log(\"开始加载设备详细信息:\", equipmentName);\r\n        const response = await getLargeEquipmentByNameStatistics(equipmentName);\r\n\r\n        if (\r\n          response &&\r\n          response.code === 200 &&\r\n          response.data &&\r\n          Array.isArray(response.data)\r\n        ) {\r\n          // 缓存详细数据\r\n          this.equipmentDetailsCache[equipmentName] = response.data.map(\r\n            (item) => ({\r\n              name: item.name,\r\n              value: item.value,\r\n              equipment_name: item.equipment_name,\r\n            })\r\n          );\r\n\r\n          console.log(\r\n            \"设备详细信息加载成功:\",\r\n            equipmentName,\r\n            this.equipmentDetailsCache[equipmentName]\r\n          );\r\n\r\n          // 更新当前悬浮框的数据\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.data =\r\n              this.equipmentDetailsCache[equipmentName];\r\n          }\r\n        } else {\r\n          // 设置空数据避免重复请求\r\n          this.equipmentDetailsCache[equipmentName] = [];\r\n          console.warn(\"设备详细信息接口返回数据格式异常:\", response);\r\n          if (\r\n            this.equipmentTooltip.visible &&\r\n            this.equipmentTooltip.title === equipmentName\r\n          ) {\r\n            this.equipmentTooltip.loading = false;\r\n            this.equipmentTooltip.error = \"数据格式异常\";\r\n          }\r\n        }\r\n      } catch (error) {\r\n        // 设置空数据避免重复请求\r\n        this.equipmentDetailsCache[equipmentName] = [];\r\n        console.error(\"获取设备详细信息失败:\", error);\r\n        if (\r\n          this.equipmentTooltip.visible &&\r\n          this.equipmentTooltip.title === equipmentName\r\n        ) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"加载失败\";\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标移动\r\n    handleEquipmentMouseMove(event) {\r\n      // 获取鼠标位置\r\n      const rect = event.currentTarget.getBoundingClientRect();\r\n      const x = event.clientX - rect.left;\r\n      const chartWidth = rect.width;\r\n\r\n      // 根据鼠标位置推断设备（简化实现）\r\n      const equipmentNames = this.largeEquipmentChart.xAxis?.data || [];\r\n      if (equipmentNames.length === 0) return;\r\n\r\n      const equipmentIndex = Math.floor(\r\n        (x / chartWidth) * equipmentNames.length\r\n      );\r\n      if (equipmentIndex >= 0 && equipmentIndex < equipmentNames.length) {\r\n        const equipmentName = equipmentNames[equipmentIndex];\r\n\r\n        // 显示悬浮框\r\n        this.equipmentTooltip.visible = true;\r\n        this.equipmentTooltip.x = event.clientX + 10;\r\n        this.equipmentTooltip.y = event.clientY - 10;\r\n        this.equipmentTooltip.title = equipmentName;\r\n\r\n        // 检查缓存数据\r\n        const cachedData = this.equipmentDetailsCache[equipmentName];\r\n        if (cachedData) {\r\n          this.equipmentTooltip.loading = false;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = cachedData;\r\n        } else {\r\n          this.equipmentTooltip.loading = true;\r\n          this.equipmentTooltip.error = \"\";\r\n          this.equipmentTooltip.data = [];\r\n\r\n          // 加载详细数据\r\n          this.loadEquipmentDetails(equipmentName);\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理设备图表鼠标离开\r\n    handleEquipmentMouseLeave() {\r\n      this.equipmentTooltip.visible = false;\r\n      this.equipmentTooltip.data = [];\r\n      this.equipmentTooltip.error = \"\";\r\n      this.equipmentTooltip.loading = false;\r\n    },\r\n\r\n    // 截取公司名称显示\r\n    truncateCompanyName(companyName, maxLength = 20) {\r\n      if (!companyName) return \"\";\r\n      if (companyName.length <= maxLength) return companyName;\r\n      return companyName.substring(0, maxLength) + \"...\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.home {\r\n  padding: 15px; // 默认固定内边距，避免大屏幕下过大\r\n  background: #f5f7fa;\r\n  font-size: 14px; // 默认固定基础字体大小\r\n  /* 响应式基础字体大小 */\r\n\r\n  @media (max-width: 1199px) {\r\n    padding: 1%;\r\n    font-size: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n.el-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// 顶部统计卡片样式（第一行 - 3个大卡片）\r\n.top-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card {\r\n    flex: 1;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 20px;\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n    min-height: 120px;\r\n    min-width: 30%;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 3% 2%;\r\n    }\r\n\r\n    .stat-header {\r\n      margin-bottom: 16px;\r\n      \r\n      h3 {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        margin: 0;\r\n        \r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(1rem, 1.8vw, 1.125rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-dual {\r\n      display: flex;\r\n      gap: 20px;\r\n      \r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n      \r\n      .stat-item {\r\n        flex: 1;\r\n        \r\n        .stat-title {\r\n          font-size: 12px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n          line-height: 1.2;\r\n          \r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n\r\n        .stat-number-row {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 4px;\r\n\r\n          .stat-number {\r\n            font-size: 28px;\r\n            font-weight: bold;\r\n            color: #1479fc;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(1rem, 2.2vw, 1.75rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 14px;\r\n            color: #666;\r\n            font-weight: normal;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.1vw, 0.875rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-1 {\r\n      background: linear-gradient(120deg, #F74A34 0%, #FF7C5E 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-2 {\r\n      background: linear-gradient(137deg, #1688E6 0%, #46ABFF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-3 {\r\n      background: linear-gradient(137deg, #F5873E 0%, #F5A645 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n      \r\n      .stat-header h3 {\r\n        color: white;\r\n      }\r\n      \r\n      .stat-content-dual .stat-item {\r\n        .stat-title {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .stat-number {\r\n          color: white;\r\n        }\r\n        .stat-unit {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 第二行统计卡片样式（4个小卡片）\r\n.second-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 1.5%;\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .stat-card-small {\r\n    display: flex;\r\n    align-items: center;\r\n    background: white;\r\n    border-radius: 7px;\r\n    padding: 16px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n    flex: 1;\r\n    min-width: 22%;\r\n    min-height: 110px;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 2% 1.5%;\r\n    }\r\n\r\n    .stat-icon {\r\n      margin-right: 12px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 40px;\r\n      height: 40px;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 8px;\r\n\r\n      @media (max-width: 1199px) {\r\n        width: 35px;\r\n        height: 35px;\r\n        margin-right: 8px;\r\n      }\r\n\r\n      i {\r\n        font-size: 20px;\r\n        color: #666;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: 18px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .stat-content-small {\r\n      flex: 1;\r\n\r\n      .stat-title-small {\r\n        font-size: 12px;\r\n        color: #666;\r\n        margin-bottom: 4px;\r\n        line-height: 1.2;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n\r\n      .stat-number-row-small {\r\n        display: flex;\r\n        align-items: baseline;\r\n        gap: 4px;\r\n\r\n        .stat-number-small {\r\n          font-size: 20px;\r\n          font-weight: bold;\r\n          color: #333;\r\n          line-height: 1;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(1rem, 1.6vw, 1.25rem);\r\n          }\r\n        }\r\n\r\n        .stat-unit-small {\r\n          font-size: 12px;\r\n          color: #666;\r\n          font-weight: normal;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-1 {\r\n      background: linear-gradient(137deg, #156BF6 0%, #4681FF 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-2 {\r\n      background: linear-gradient(137deg, #FC9920 0%, #F5AC45 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-3 {\r\n      background: linear-gradient(137deg, #9D59FF 0%, #CA79F5 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n\r\n    &.stat-card-small-4 {\r\n      background: linear-gradient(147deg, #18C68C 0%, #2BD181 100%);\r\n      border-radius: 7px;\r\n      color: white;\r\n\r\n      .stat-icon {\r\n        background: rgba(255, 255, 255, 0.2);\r\n        \r\n        i {\r\n          color: white;\r\n        }\r\n      }\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .stat-number-small {\r\n          color: white;\r\n        }\r\n\r\n        .stat-unit-small {\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 管理卡片样式\r\n.management-row {\r\n  margin-bottom: 20px; // 默认固定间距\r\n\r\n  @media (max-width: 1199px) {\r\n    margin-bottom: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 15px; // 减少内边距，默认固定内边距\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 370px;\r\n    overflow: hidden; // 防止内容超出\r\n    box-sizing: border-box; // 确保内边距计算在内\r\n\r\n    @media (max-width: 1199px) {\r\n      padding: 1%;\r\n    }\r\n\r\n    .management-header {\r\n      margin-bottom: 10px; // 减少间距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1%;\r\n      }\r\n\r\n      .header-content {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n      }\r\n\r\n      h3 {\r\n        margin: 0;\r\n        font-size: 16px; // 默认固定字体大小\r\n        font-weight: 400;\r\n        color: #333;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n        }\r\n      }\r\n\r\n      .time-tabs {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 4px;\r\n\r\n        .tab-item {\r\n          font-size: 12px;\r\n          color: #666;\r\n          cursor: pointer;\r\n          padding: 2px 6px;\r\n          border-radius: 4px;\r\n          transition: all 0.3s ease;\r\n          user-select: none;\r\n\r\n          &:hover {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n          }\r\n\r\n          &.active {\r\n            color: #2656f5;\r\n            background: rgba(38, 86, 245, 0.1);\r\n            font-weight: 500;\r\n          }\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            padding: 1px 4px;\r\n          }\r\n        }\r\n\r\n        .tab-divider {\r\n          font-size: 12px;\r\n          color: #ccc;\r\n          margin: 0 2px;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 质量管理样式\r\n    .section-header {\r\n      margin-bottom: 1%;\r\n\r\n      .section-title {\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: #f5f7fa;\r\n        border-radius: 0.25rem;\r\n      }\r\n    }\r\n\r\n    .quality-stats {\r\n      margin-bottom: 1%;\r\n\r\n      .stat-group {\r\n        display: flex;\r\n        gap: 1.5%;\r\n        align-items: flex-start;\r\n\r\n        .stat-item {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          &.stat-placeholder {\r\n            visibility: hidden;\r\n          }\r\n\r\n          .stat-header {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 0.5rem;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 28px;\r\n\r\n            .stat-icon {\r\n              width: 1.25rem;\r\n              height: 1.25rem;\r\n              border-radius: 0.25rem;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              color: white;\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n              &.blue {\r\n                background: #2656f5;\r\n              }\r\n\r\n              &.orange {\r\n                background: #ff920d;\r\n              }\r\n\r\n              &.green {\r\n                background: #54c255;\r\n              }\r\n            }\r\n\r\n            .stat-label {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n              color: #666;\r\n              font-weight: 500;\r\n            }\r\n          }\r\n\r\n          .stat-number {\r\n            font-size: clamp(0.75rem, 1.3vw, 1.25rem);\r\n            font-weight: bold;\r\n            color: #2656f5;\r\n            line-height: 1;\r\n            margin-bottom: 0.5rem;\r\n            min-height: 24px;\r\n            text-align: left;\r\n          }\r\n\r\n          .stat-detail {\r\n            display: flex;\r\n            gap: 1.5%;\r\n            align-items: flex-start;\r\n\r\n            span {\r\n              font-size: clamp(0.5rem, 0.7vw, 0.625rem);\r\n              color: #999;\r\n              line-height: 1.4;\r\n\r\n              .number {\r\n                color: #333;\r\n                font-weight: 500;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 安全管理专用样式\r\n    // 新的安全管理样式\r\n    .safety-top-stats {\r\n      display: flex;\r\n      gap: 15px;\r\n      margin-bottom: 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n        margin-bottom: 1.5%;\r\n      }\r\n\r\n      .safety-stat-card-new {\r\n        flex: 1;\r\n        background: #f8f9fa;\r\n        border-radius: 8px;\r\n        padding: 15px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12px;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.2%;\r\n          gap: 1%;\r\n        }\r\n\r\n        .safety-stat-icon {\r\n          width: 48px;\r\n          height: 48px;\r\n          border-radius: 50%;\r\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          flex-shrink: 0;\r\n\r\n          &.rate-icon {\r\n            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n          }\r\n\r\n          @media (max-width: 1199px) {\r\n            width: clamp(36px, 4vw, 48px);\r\n            height: clamp(36px, 4vw, 48px);\r\n          }\r\n\r\n          i {\r\n            font-size: 24px;\r\n            color: white;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(18px, 2vw, 24px);\r\n            }\r\n          }\r\n        }\r\n\r\n        .safety-stat-content-new {\r\n          flex: 1;\r\n\r\n          .safety-stat-label {\r\n            font-size: 14px;\r\n            color: #666;\r\n            margin-bottom: 6px;\r\n            line-height: 1.2;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .safety-stat-number-new {\r\n            display: flex;\r\n            align-items: baseline;\r\n            gap: 4px;\r\n\r\n            .number {\r\n              font-size: 28px;\r\n              font-weight: bold;\r\n              color: #1479fc;\r\n              line-height: 1;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(1.25rem, 2.5vw, 1.75rem);\r\n              }\r\n            }\r\n\r\n            .unit {\r\n              font-size: 16px;\r\n              color: #666;\r\n              font-weight: normal;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(0.875rem, 1.4vw, 1rem);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-chart-area {\r\n      margin-bottom: 20px;\r\n      padding: 0 20px;\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-bottom: 1.5%;\r\n        padding: 0 2%;\r\n      }\r\n    }\r\n\r\n    .safety-bottom-stats {\r\n      .stats-row {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-bottom: 12px;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .stat-item {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 8px;\r\n          flex: 1;\r\n          justify-content: center;\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 0.8%;\r\n          }\r\n\r\n          .stat-dot {\r\n            width: 10px;\r\n            height: 10px;\r\n            border-radius: 50%;\r\n            flex-shrink: 0;\r\n\r\n            @media (max-width: 1199px) {\r\n              width: clamp(8px, 1vw, 10px);\r\n              height: clamp(8px, 1vw, 10px);\r\n            }\r\n          }\r\n\r\n          .stat-label {\r\n            font-size: 12px;\r\n            color: #666;\r\n            min-width: 40px;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n              min-width: clamp(30px, 3vw, 40px);\r\n            }\r\n          }\r\n\r\n          .stat-value {\r\n            font-size: 14px;\r\n            font-weight: bold;\r\n            color: #333;\r\n            min-width: 20px;\r\n            text-align: right;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n            }\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n            color: #666;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-sections-container {\r\n      display: flex;\r\n      gap: 15px; // 减少间距，默认固定间距\r\n      width: 100%;\r\n      overflow: hidden; // 防止内容超出\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n      }\r\n    }\r\n\r\n    .safety-section-independent {\r\n      flex: 1;\r\n      background: #f5f7f9;\r\n      padding: 10px 15px 12px 15px; // 减少内边距，默认固定内边距\r\n      border-radius: 0.5rem;\r\n      min-width: 0; // 允许flex项目缩小到内容大小以下\r\n      box-sizing: border-box; // 确保内边距计算在内\r\n\r\n      @media (max-width: 1199px) {\r\n        padding: 0.8% 1.5% 1% 1.5%;\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px; // 默认固定间距\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1%;\r\n        }\r\n\r\n        .section-title {\r\n          font-size: 12px; // 默认固定字体大小\r\n          color: #666;\r\n          padding: 0.25rem 0.5rem;\r\n          background: rgba(245, 247, 250, 0.8);\r\n          border-radius: 0.25rem;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .safety-stats-row {\r\n      display: flex;\r\n      gap: 8px; // 进一步减少间距，默认固定间距\r\n      margin-top: 10px; // 减少上边距，默认固定间距\r\n\r\n      @media (max-width: 1199px) {\r\n        gap: 1.5%;\r\n        margin-top: 1%;\r\n      }\r\n\r\n      .safety-stat-card {\r\n        flex: 1;\r\n        background: white;\r\n        border-radius: 0.5rem;\r\n        padding: 10px 12px; // 大幅减少内边距，默认固定内边距\r\n        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);\r\n        min-width: 0; // 允许flex项目缩小\r\n        box-sizing: border-box; // 确保内边距计算在内\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.2% 1.6%;\r\n        }\r\n\r\n        .safety-stat-title {\r\n          font-size: 10px; // 默认固定字体大小\r\n          color: #666666;\r\n          margin-bottom: 0.5rem;\r\n          line-height: 1.2;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n          }\r\n        }\r\n\r\n        .safety-stat-content-row {\r\n          display: flex;\r\n          align-items: flex-end;\r\n          justify-content: space-between;\r\n          gap: 15px; // 默认固定间距\r\n\r\n          @media (max-width: 1199px) {\r\n            gap: 1.2%;\r\n          }\r\n        }\r\n\r\n        .safety-stat-main {\r\n          display: flex;\r\n          align-items: baseline;\r\n          gap: 0.25rem;\r\n          margin-bottom: 0;\r\n\r\n          .safety-stat-number {\r\n            font-size: 22px; // 调整默认固定字体大小，适合1300px+屏幕\r\n            font-weight: bold;\r\n            color: #1479fc;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.75rem, 1.5vw, 1.5rem);\r\n              /* 减小响应式字体：12px-24px */\r\n            }\r\n          }\r\n\r\n          .safety-stat-unit {\r\n            font-size: 10px; // 默认固定字体大小\r\n            color: #666666;\r\n            font-weight: normal;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.5rem, 0.8vw, 0.625rem);\r\n            }\r\n          }\r\n        }\r\n\r\n        .safety-stat-subtitle {\r\n          font-size: 9px; // 默认固定字体大小\r\n          color: #999999;\r\n          line-height: 1.2;\r\n          white-space: nowrap;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: clamp(0.4375rem, 0.7vw, 0.5625rem);\r\n          }\r\n\r\n          .safety-highlight-number {\r\n            color: #ff920d;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 内嵌图表样式\r\n    .chart-section-in-section {\r\n      .chart-title {\r\n        font-size: 12px; // 默认固定字体大小\r\n        color: #666;\r\n        padding: 0.25rem 0.5rem;\r\n        background: rgba(245, 247, 250, 0.8);\r\n        border-radius: 0.25rem;\r\n        margin-bottom: 0.5rem;\r\n        text-align: left;\r\n        display: inline-block;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        }\r\n      }\r\n    }\r\n\r\n    .chart-section {\r\n      .chart-row {\r\n        display: flex;\r\n        gap: 1.5%;\r\n\r\n        .chart-item {\r\n          flex: 1;\r\n\r\n          .chart-title {\r\n            font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n            color: #666;\r\n            margin-bottom: 0.5rem;\r\n            text-align: center;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .pie-chart-section {\r\n      .chart-title {\r\n        font-size: clamp(0.75rem, 1.2vw, 0.875rem);\r\n        color: #666;\r\n        margin-bottom: 1rem;\r\n        text-align: center;\r\n      }\r\n\r\n      &.full-height {\r\n        height: calc(100% - 5rem);\r\n\r\n        .chart-container {\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 底部图表卡片样式\r\n.charts-row {\r\n  .chart-card {\r\n    background: white;\r\n    border-radius: 0.75rem;\r\n    padding: 2.5%;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    height: 340px;\r\n\r\n    .chart-header {\r\n      margin-bottom: 1.5%;\r\n\r\n      h4 {\r\n        margin: 0;\r\n        font-size: clamp(0.875rem, 1.4vw, 1rem);\r\n        font-weight: 600;\r\n        color: #333;\r\n      }\r\n    }\r\n\r\n    .chart-content {\r\n      height: calc(100% - 4rem);\r\n    }\r\n  }\r\n}\r\n\r\n// 图表容器样式\r\n.chart-container {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n// 设备图表容器样式\r\n.equipment-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  cursor: pointer;\r\n}\r\n\r\n// 危大工程图表容器样式\r\n.dangerous-chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n// 安全生产投入容器样式\r\n.safety-investment-container {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 100%;\r\n  gap: 24px;\r\n  align-items: center;\r\n\r\n  @media (max-width: 1199px) {\r\n    gap: 2%;\r\n  }\r\n\r\n  .pie-chart-section {\r\n    width: 280px;\r\n    height: 280px;\r\n    flex-shrink: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    @media (max-width: 1199px) {\r\n      width: 35%;\r\n      height: auto;\r\n      aspect-ratio: 1;\r\n    }\r\n    \r\n    .chart-wrapper {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: relative;\r\n    }\r\n  }\r\n\r\n  .project-list-section {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding-left: 24px;\r\n\r\n    @media (max-width: 1199px) {\r\n      padding-left: 2%;\r\n    }\r\n\r\n    .project-list {\r\n      width: 100%;\r\n      max-width: 300px;\r\n      \r\n      .project-item {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 18px;\r\n        padding: 10px 0;\r\n        transition: background-color 0.2s ease;\r\n\r\n        @media (max-width: 1199px) {\r\n          margin-bottom: 1.8%;\r\n          padding: 1% 0;\r\n        }\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: rgba(20, 121, 252, 0.05);\r\n          border-radius: 4px;\r\n          padding-left: 8px;\r\n          padding-right: 8px;\r\n        }\r\n\r\n        .project-dot {\r\n          width: 14px;\r\n          height: 14px;\r\n          border-radius: 50%;\r\n          margin-right: 15px;\r\n          flex-shrink: 0;\r\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n\r\n          @media (max-width: 1199px) {\r\n            width: 1.4vw;\r\n            height: 1.4vw;\r\n            margin-right: 1.5%;\r\n          }\r\n        }\r\n\r\n        .project-info {\r\n          flex: 1;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          min-width: 0;\r\n\r\n          .project-name {\r\n            font-size: 15px;\r\n            color: #333;\r\n            font-weight: 500;\r\n            flex: 1;\r\n            margin-right: 12px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            line-height: 1.4;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.8rem, 1.5vw, 0.9375rem);\r\n            }\r\n          }\r\n\r\n          .project-amount {\r\n            font-size: 16px;\r\n            color: #1479fc;\r\n            font-weight: 700;\r\n            white-space: nowrap;\r\n            line-height: 1;\r\n\r\n            @media (max-width: 1199px) {\r\n              font-size: clamp(0.875rem, 1.6vw, 1rem);\r\n            }\r\n\r\n            &::after {\r\n              content: '万';\r\n              font-size: 13px;\r\n              color: #666;\r\n              margin-left: 3px;\r\n              font-weight: 400;\r\n\r\n              @media (max-width: 1199px) {\r\n                font-size: clamp(0.7rem, 1.3vw, 0.8125rem);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 悬浮框样式\r\n.project-tooltip {\r\n  position: fixed;\r\n  z-index: 1000;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\r\n  padding: 0;\r\n  min-width: 12.5rem;\r\n  max-width: 50rem;\r\n  width: auto;\r\n  font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n\r\n  .tooltip-header {\r\n    background: #f8f9fa;\r\n    padding: 0.75rem 1rem;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    font-weight: 600;\r\n    color: #374151;\r\n    border-radius: 0.5rem 0.5rem 0 0;\r\n    font-size: clamp(0.75rem, 1.1vw, 0.8125rem);\r\n    word-wrap: break-word;\r\n    word-break: break-all;\r\n    white-space: normal;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .tooltip-loading,\r\n  .tooltip-error,\r\n  .tooltip-empty {\r\n    padding: 1rem;\r\n    text-align: center;\r\n    color: #6b7280;\r\n  }\r\n\r\n  .tooltip-error {\r\n    color: #ef4444;\r\n  }\r\n\r\n  .tooltip-content {\r\n    padding: 0.5rem 0;\r\n    max-height: 18.75rem;\r\n    overflow-y: auto;\r\n\r\n    .tooltip-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0.5rem 1rem;\r\n      border-bottom: 1px solid #f3f4f6;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      &:hover {\r\n        background: #f8f9fa;\r\n      }\r\n\r\n      .tooltip-name {\r\n        flex: 1;\r\n        color: #374151;\r\n        line-height: 1.4;\r\n        margin-right: 0.75rem;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        text-align: left;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .tooltip-value {\r\n        color: #2563eb;\r\n        font-weight: 600;\r\n        font-size: clamp(0.625rem, 1vw, 0.75rem);\r\n        min-width: 1.875rem;\r\n        text-align: right;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式图标样式\r\n.stat-icon-img {\r\n  width: 40px; // 默认固定大小\r\n  height: 40px;\r\n  object-fit: contain;\r\n  flex-shrink: 0;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(24px, 3vw, 48px);\r\n    height: clamp(24px, 3vw, 48px);\r\n  }\r\n}\r\n\r\n// 小卡片图标样式（现在使用字体图标，此样式已不需要）\r\n// .stat-icon-img-small {\r\n//   width: 24px;\r\n//   height: 24px;\r\n//   object-fit: contain;\r\n//   flex-shrink: 0;\r\n\r\n//   @media (max-width: 1199px) {\r\n//     width: clamp(20px, 2.5vw, 28px);\r\n//     height: clamp(20px, 2.5vw, 28px);\r\n//   }\r\n// }\r\n\r\n.check-icon-img {\r\n  width: 14px; // 默认固定大小\r\n  height: 14px;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: clamp(12px, 1.5vw, 16px);\r\n    height: clamp(12px, 1.5vw, 16px);\r\n  }\r\n}\r\n\r\n// 响应式媒体查询 - 针对1200px-1920px范围优化，让1200px也有1920px的效果\r\n@media (min-width: 1200px) and (max-width: 1920px) {\r\n  .home {\r\n    font-size: 14px;\r\n    /* 固定字体大小 */\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      .stat-icon {\r\n        width: 50px;\r\n        height: 50px;\r\n        min-width: 50px;\r\n        min-height: 50px;\r\n        max-width: 50px;\r\n        max-height: 50px;\r\n      }\r\n\r\n      .stat-content {\r\n        .stat-title {\r\n          font-size: 12px;\r\n        }\r\n\r\n        .stat-number-row {\r\n          .stat-number {\r\n            font-size: 24px;\r\n          }\r\n\r\n          .stat-unit {\r\n            font-size: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .management-card {\r\n    .management-header h3 {\r\n      font-size: 16px;\r\n    }\r\n\r\n    .section-title {\r\n      font-size: 12px;\r\n    }\r\n\r\n    .quality-stats .stat-group .stat-item {\r\n      .stat-header {\r\n        .stat-icon {\r\n          width: 18px;\r\n          height: 18px;\r\n        }\r\n\r\n        .stat-label {\r\n          font-size: 13px;\r\n        }\r\n      }\r\n\r\n      .stat-number {\r\n        font-size: 20px;\r\n      }\r\n\r\n      .stat-detail span {\r\n        font-size: 10px;\r\n      }\r\n    }\r\n\r\n    // 添加安全管理数字字体大小设置\r\n    .safety-stat-card {\r\n      .safety-stat-main {\r\n        .safety-stat-number {\r\n          font-size: 22px;\r\n          /* 1300-1920px下适中的字体大小 */\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .check-icon-img {\r\n    width: 14px;\r\n    height: 14px;\r\n  }\r\n\r\n  .chart-card {\r\n    .chart-header h4 {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n\r\n  .project-tooltip {\r\n    font-size: 12px;\r\n\r\n    .tooltip-header {\r\n      font-size: 13px;\r\n    }\r\n\r\n    .tooltip-content .tooltip-item {\r\n      .tooltip-name {\r\n        font-size: 12px;\r\n      }\r\n\r\n      .tooltip-value {\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 固定间距和布局\r\n  .top-stats {\r\n    gap: 20px;\r\n    /* 固定间距 */\r\n\r\n    .stat-card {\r\n      padding: 20px 15px;\r\n      /* 固定内边距 */\r\n      min-height: 120px;\r\n    }\r\n  }\r\n\r\n  .management-row {\r\n    margin-bottom: 20px;\r\n\r\n    .management-card {\r\n      padding: 15px;\r\n      /* 与默认样式保持一致 */\r\n      height: 370px;\r\n      /* 确保高度一致 */\r\n\r\n      .management-header {\r\n        margin-bottom: 10px;\r\n        /* 与默认样式保持一致 */\r\n      }\r\n\r\n      .section-header {\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .quality-stats {\r\n        margin-bottom: 15px;\r\n\r\n        .stat-group {\r\n          gap: 20px;\r\n          /* 固定间距 */\r\n        }\r\n      }\r\n\r\n      .safety-sections-container {\r\n        gap: 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定间距 */\r\n      }\r\n\r\n      .safety-section-independent {\r\n        padding: 10px 15px 12px 15px;\r\n        /* 与默认样式保持一致 */\r\n        /* 固定内边距 */\r\n\r\n        .safety-stats-row {\r\n          gap: 8px;\r\n          /* 与默认样式保持一致 */\r\n          /* 固定间距 */\r\n          margin-top: 10px;\r\n          /* 与默认样式保持一致 */\r\n\r\n          .safety-stat-card {\r\n            padding: 10px 12px;\r\n            /* 与默认样式保持一致 */\r\n            /* 固定内边距 */\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .charts-row {\r\n    .chart-card {\r\n      padding: 20px;\r\n      height: 340px;\r\n      /* 确保高度一致 */\r\n\r\n      .chart-header {\r\n        margin-bottom: 15px;\r\n      }\r\n\r\n      .chart-content {\r\n        height: calc(100% - 50px);\r\n        /* 固定计算高度 */\r\n      }\r\n    }\r\n  }\r\n\r\n  // 确保栅格系统间距一致\r\n  .el-row {\r\n    margin-bottom: 20px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  // 统一进度条样式\r\n  .project-progress {\r\n    .progress-bar {\r\n      height: 12px;\r\n      /* 固定高度 */\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .home {\r\n    padding: 1%;\r\n    font-size: clamp(10px, 2vw, 14px);\r\n  }\r\n\r\n  .top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card {\r\n      min-height: 140px;\r\n      min-width: 100%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(1rem, 3vw, 1.25rem) !important;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 16px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1.25rem, 4vw, 2rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.875rem, 2.5vw, 1.125rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .stat-card-small {\r\n      min-width: 100%;\r\n      min-height: 70px;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(1rem, 3.5vw, 1.5rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.75rem, 2.5vw, 1rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.75rem, 2vw, 1.125rem) !important;\r\n    /* 减小字体：12px-18px */\r\n  }\r\n\r\n  .management-row {\r\n    .el-col {\r\n      margin-bottom: 1%;\r\n    }\r\n  }\r\n\r\n  .safety-sections-container {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .management-card {\r\n    height: 350px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 300px;\r\n  }\r\n\r\n  .project-tooltip {\r\n    max-width: 95vw;\r\n    min-width: 85vw;\r\n    width: auto;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .home {\r\n    font-size: clamp(8px, 3vw, 12px);\r\n  }\r\n\r\n  .top-stats {\r\n    .stat-card {\r\n      min-height: 120px;\r\n      padding: 3%;\r\n\r\n      .stat-header h3 {\r\n        font-size: clamp(0.875rem, 4vw, 1.125rem) !important;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .stat-content-dual {\r\n        flex-direction: column;\r\n        gap: 12px;\r\n\r\n        .stat-item {\r\n          .stat-title {\r\n            font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-number {\r\n            font-size: clamp(1rem, 5vw, 1.75rem) !important;\r\n          }\r\n\r\n          .stat-number-row .stat-unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .second-stats {\r\n    .stat-card-small {\r\n      min-height: 60px;\r\n      padding: 2.5%;\r\n\r\n      .stat-content-small {\r\n        .stat-title-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-number-small {\r\n          font-size: clamp(0.875rem, 4vw, 1.25rem) !important;\r\n        }\r\n\r\n        .stat-number-row-small .stat-unit-small {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .stat-icon-img {\r\n    width: 32px !important;\r\n    height: 32px !important;\r\n  }\r\n\r\n  .quality-stats .stat-group .stat-item .stat-number {\r\n    font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n  }\r\n\r\n  .safety-stat-main .safety-stat-number {\r\n    font-size: clamp(0.625rem, 3vw, 1rem) !important;\r\n    /* 进一步减小：10px-16px */\r\n  }\r\n\r\n  // 新的安全管理移动端样式\r\n  .safety-top-stats {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n\r\n    .safety-stat-card-new {\r\n      padding: 3%;\r\n\r\n      .safety-stat-icon {\r\n        width: 40px !important;\r\n        height: 40px !important;\r\n\r\n        i {\r\n          font-size: 20px !important;\r\n        }\r\n      }\r\n\r\n      .safety-stat-content-new {\r\n        .safety-stat-label {\r\n          font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n        }\r\n\r\n        .safety-stat-number-new {\r\n          .number {\r\n            font-size: clamp(1rem, 4vw, 1.5rem) !important;\r\n          }\r\n\r\n          .unit {\r\n            font-size: clamp(0.75rem, 3vw, 1rem) !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .safety-bottom-stats {\r\n    .stats-row {\r\n      .stat-item {\r\n        gap: 1%;\r\n\r\n        .stat-label {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n          min-width: 25px !important;\r\n        }\r\n\r\n        .stat-value {\r\n          font-size: clamp(0.75rem, 3.5vw, 1rem) !important;\r\n        }\r\n\r\n        .stat-unit {\r\n          font-size: clamp(0.625rem, 3vw, 0.875rem) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .management-card {\r\n    height: 320px;\r\n  }\r\n\r\n  .chart-card {\r\n    height: 280px;\r\n  }\r\n\r\n  .safety-stats-row {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n\r\n  .quality-stats .stat-group {\r\n    flex-direction: column;\r\n    gap: 1%;\r\n  }\r\n}\r\n\r\n// 全局样式覆盖\r\n::v-deep .el-card {\r\n  border: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n::v-deep .el-card__header {\r\n  border-bottom: 1px solid #f0f0f0;\r\n  padding: 1rem 1.25rem;\r\n}\r\n\r\n::v-deep .el-card__body {\r\n  padding: 1.25rem;\r\n}\r\n</style>\r\n"]}]}