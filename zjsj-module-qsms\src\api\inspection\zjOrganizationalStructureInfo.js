import request from '@/utils/request'

// 查询中江组织架构列表
export function listZjOrganizationalStructureInfo(query) {
  return request({
    url: '/inspection/zjOrganizationalStructureInfo/list',
    method: 'get',
    params: query
  })
}

// 查询中江组织架构详细
export function getZjOrganizationalStructureInfo(id) {
  return request({
    url: '/inspection/zjOrganizationalStructureInfo/' + id,
    method: 'get'
  })
}

// 新增中江组织架构
export function addZjOrganizationalStructureInfo(data) {
  return request({
    url: '/inspection/zjOrganizationalStructureInfo',
    method: 'post',
    data: data
  })
}

// 修改中江组织架构
export function updateZjOrganizationalStructureInfo(data) {
  return request({
    url: '/inspection/zjOrganizationalStructureInfo',
    method: 'put',
    data: data
  })
}

// 删除中江组织架构
export function delZjOrganizationalStructureInfo(id) {
  return request({
    url: '/inspection/zjOrganizationalStructureInfo/' + id,
    method: 'delete'
  })
}
