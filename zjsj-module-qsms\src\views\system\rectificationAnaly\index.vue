<template>
    <div class="app-container">
        <el-row>
            <el-col :span="24" :xs="24">
                <el-card body-style="padding: 12px">
                    <div class="top">
                        <div class="top-left">整改分析</div>
                        <div class="top-right">
                            <el-date-picker style="margin-right: 20px;" v-model="timeRange" type="daterange"
                                range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                            </el-date-picker>
                            <el-button type="primary" icon="el-icon-download" size="small">搜索</el-button>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="24">
                <el-card class="box-card">
                    <el-tabs v-model="activeName" @tab-click="handleClick">
                        <el-tab-pane :label="tabItem.label" :name="tabItem.name" v-for="tabItem in tabsList">
                            <div>
                                <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
                                    <el-form-item label="问题等级" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="规范大类" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="用户名称" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="项目名称" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="检查类型" prop="questionlevel">
                                        <el-select v-model="queryParams.questionlevel" multiple collapse-tags
                                            style="margin-left: 20px;" placeholder="请选择">
                                            <el-option v-for="selectItem in questionLevelList" :key="selectItem.value"
                                                :label="selectItem.label" :value="selectItem.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" icon="el-icon-search" size="mini"
                                            @click="handleQuery">搜索</el-button>
                                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                                    </el-form-item>
                                </el-form>
                                <div style="width: 100%; height: calc(100vh - 380px); overflow-x: auto;">
                                    <table>
                                        <tr>
                                            <th v-for="item in thList">{{ item }}</th>
                                        </tr>
                                        <tr v-if="!row.isHide" v-for="(row, rowIndex) in tableData">
                                            <td v-for="(tdItem, tdIndex) in row.col">
                                                <span style="cursor: pointer;">{{ tdItem.data }}</span>
                                                <template v-if="tdItem.children">
                                                    <template v-if="tdItem.isHide">
                                                        <el-button type="text" icon="el-icon-circle-plus-outline" circle
                                                            style="padding: 0; margin-left: 12px;"
                                                            @click="showRow(rowIndex, tdIndex)"></el-button>
                                                    </template>
                                                    <template v-if="!tdItem.isHide">
                                                        <el-button type="text" icon="el-icon-remove-outline" circle
                                                            style="padding: 0; margin-left: 12px;"
                                                            @click="hideRow(rowIndex, tdIndex)"></el-button>
                                                    </template>
                                                </template>
                                                <template v-if="tdItem.label == '小计'">
                                                    <el-button type="text" icon="el-icon-data-line" circle
                                                        style="padding: 0; margin-left: 12px;"
                                                        @click="showLinePop = true"></el-button>
                                                    <el-button type="text" icon="el-icon-pie-chart" circle
                                                        style="padding: 0;" @click="showPiePop = true"></el-button>
                                                </template>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </el-card>
            </el-col>
        </el-row>
        <!-- 选择要添加的列 -->
        <el-dialog title="请选择" :visible.sync="showPopCheckRow" width="30%" style="margin-top: 30vh;">
            <div>
                <el-radio v-model="curDynamicColumn" :label="item.value" :disabled="item.disabled"
                    v-for="item in dynamicColumns" @input="handleCheck">{{ item.label }}</el-radio>
            </div>
        </el-dialog>

        <!-- 饼图 -->
        <el-dialog title="饼图" :visible.sync="showPiePop" width="45%" style="margin-top: 16vh;">
            <div style="width: 100%; height: 44vh; display: flex; align-items: center;">
                <div style="width: 50%; height: 100%; display: flex; align-items: center;">
                    <pieChart height="86%" :data="echartData" />
                </div>
                <div
                    style="width: 50%; max-height: 100%; overflow: auto; display: flex; flex-direction: column; align-items: center;">
                    <div class="lengend-item" v-for="(item, index) in echartData.data">
                        <div class="lengend-color" :style="{ backgroundColor: echartData.colorList[index] }"></div>
                        <div class="lengend-name">{{ item.name }}</div>
                    </div>
                </div>
            </div>
        </el-dialog>
        <!-- 折线图 -->
        <el-dialog title="折线图" :visible.sync="showLinePop" width="80%" style="margin-top: 5vh;">
            <div style="width: 100%; height: 66vh;">
                <div style="width: 100%; height: 12%; overflow: auto; display: flex; flex-wrap: wrap;">
                    <div class="lengend-item" v-for="(item, index) in chart2Lengend">
                        <div class="lengend-color" :style="{ backgroundColor: item.color }"></div>
                        <div class="lengend-name">{{ item.label }}</div>
                    </div>
                </div>
                <div style="width: 100%; height: 100%; display: flex; align-items: center;">
                    <lineChart height="88%" :data="lineData" />
                </div>
            </div>
        </el-dialog>
    </div>

</template>

<script>
import pieChart from "@/views/components/pieChart.vue";
import lineChart from "@/views/components/lineChart.vue";
export default {
    components: {
        pieChart,
        lineChart
    },
    data() {
        return {
            // 显示折线图弹窗
            showLinePop: false,
            // 显示饼图弹窗
            showPiePop: false,
            // 饼图数据
            echartData: {
                colorList: ['#3C80E8', '#8EE98F', '#A1FFEB', '#54C255', '#A1CDFF', '#FF920D', '#FECF77', '#F3B2B1', '#B38DFF'],
                data: [
                    { value: 310, name: '消防安全' },
                    { value: 335, name: '安全基础管理' },
                    { value: 234, name: '电气安全' },
                    { value: 135, name: '特种设备' },
                    { value: 148, name: '危化品' },
                    { value: 234, name: '固废' },
                    { value: 135, name: '仓储安全管理' },
                    { value: 148, name: '特殊作业' },
                    { value: 148, name: '其他40项' }
                ]
            },
            // 折线图数据
            lineData: {
                grid: {
                    top: 10,
                    left: '5%',
                    right: '5%',
                    bottom: '8%',
                },
                xAxisData: ['6月', '7月', '8月', '9月', '10月', '11月', '12月', '1月', '2月', '3月', '4月', '5月'],
                seriesData: [
                    {
                        name: '一般',
                        data: [
                            {
                                value: 310,
                                name: '6月'
                            },
                            {
                                value: 335,
                                name: '7月'
                            },
                            {
                                value: 234,
                                name: '8月'
                            },
                            {
                                value: 135,
                                name: '9月'
                            },
                            {
                                value: 148,
                                name: '10月'
                            },
                            {
                                value: 135,
                                name: '11月'
                            },
                            {
                                value: 148,
                                name: '12月'
                            },
                            {
                                value: 148,
                                name: '1月'
                            },
                            {
                                value: 135,
                                name: '2月'
                            },
                            {
                                value: 148,
                                name: '3月'
                            },
                            {
                                value: 135,
                                name: '4月'
                            },
                            {
                                value: 148,
                                name: '5月'
                            }
                        ],
                        backgroundColor: '#54C255'
                    },
                    {
                        name: '较大',
                        data: [
                            {
                                value: 210,
                                name: '6月'
                            },
                            {
                                value: 235,
                                name: '7月'
                            },
                            {
                                value: 334,
                                name: '8月'
                            },
                            {
                                value: 235,
                                name: '9月'
                            },
                            {
                                value: 108,
                                name: '10月'
                            },
                            {
                                value: 204,
                                name: '11月'
                            },
                            {
                                value: 105,
                                name: '12月'
                            },
                            {
                                value: 108,
                                name: '1月'
                            },
                            {
                                value: 204,
                                name: '2月'
                            },
                            {
                                value: 105,
                                name: '3月'
                            },
                            {
                                value: 108,
                                name: '4月'
                            },
                            {
                                value: 204,
                                name: '5月'
                            }
                        ],
                        backgroundColor: '#FF920D'
                    },
                    {
                        name: '重大',
                        data: [
                            {
                                value: 206,
                                name: '6月'
                            },
                            {
                                value: 205,
                                name: '7月'
                            },
                            {
                                value: 304,
                                name: '8月'
                            },
                            {
                                value: 205,
                                name: '9月'
                            },
                            {
                                value: 104,
                                name: '10月'
                            },
                            {
                                value: 206,
                                name: '11月'
                            },
                            {
                                value: 113,
                                name: '12月'
                            },
                            {
                                value: 118,
                                name: '1月'
                            },
                            {
                                value: 118,
                                name: '2月'
                            },
                            {
                                value: 115,
                                name: '3月'
                            },
                            {
                                value: 118,
                                name: '4月'
                            },
                            {
                                value: 318,
                                name: '5月'
                            }
                        ],
                        backgroundColor: '#E54545'
                    }
                ]
            },
            // 折线图图例
            chart2Lengend: [
                {
                    color: '#54C255',
                    label: '一般'
                },
                {
                    color: '#FF920D',
                    label: '较大'
                },
                {
                    color: '#E54545',
                    label: '重大'
                },

            ],
            timeRange: [],
            activeName: '1',
            tabsList: [
                { name: '1', label: '问题等级', prop: 'questionlevel' },
                { name: '2', label: '大类', prop: 'bigclass' },
                { name: '3', label: '小类', prop: 'smallclass' },
                { name: '4', label: '用户', prop: 'user' },
                { name: '5', label: '检查角色', prop: 'checkrole' },
                { name: '6', label: '所属园区', prop: 'park' },
                { name: '7', label: '检查类型', prop: 'checktype' },
                { name: '8', label: '项目名称', prop: 'projectname' },
                { name: '9', label: '工程类型', prop: 'projecttype' },
                { name: '10', label: '按月分类', prop: 'month' },
                { name: '11', label: '按年分类', prop: 'year' }
            ],
            queryParams: {
                questionlevel: []
            },
            questionLevelList: [
                {
                    value: 1,
                    label: '一般'
                },
                {
                    value: 2,
                    label: '较大'
                },
                {
                    value: 3,
                    label: '重大'
                }
            ],
            // 当前选中的列
            curDynamicColumn: '1',
            level: 0,
            // 动态添加列
            dynamicColumns: [
                {
                    label: '问题等级',
                    value: '1',
                    disabled: true,
                },
                {
                    label: '大类',
                    value: '2',
                    disabled: false,
                },
                {
                    label: '用户',
                    value: '4',
                    disabled: false,
                },
                {
                    label: '检查类型',
                    value: '7',
                    disabled: false,
                },
                {
                    label: '项目名称',
                    value: '8',
                    disabled: false,
                }
            ],
            showPopCheckRow: false,
            thList: [
                '',
                '小计',
                '一般',
                '较大',
                '重大'
            ],
            tableData: [
                {
                    id: 1,
                    col: [
                        {
                            label: '全部',
                            data: '全部',
                            children: [],
                            isHide: true,
                        },
                        {
                            label: '小计',
                            data: '50%',
                        },
                        {
                            label: '一般',
                            data: '50%',
                        },
                        {
                            label: '较大',
                            data: '12%',
                        },
                        {
                            label: '重大',
                            data: '38%',
                        },
                    ]
                },
            ],
            rowIndex: '',
            colIndex: '',

        };
    },
    created() {

    },
    methods: {
        handleCheck(value) {
            this.showPopCheckRow = false
            let index = this.dynamicColumns.findIndex(item => item.value == value)
            this.dynamicColumns[index].disabled = true
            this.thList.splice(this.colIndex + 1, 0, this.dynamicColumns[index].label)
            this.level += 1
            this.tableData.forEach(trItem => {
                trItem.col.splice(this.colIndex + 1, 0, {
                    label: '',
                    data: '',
                })
            })
            let arr1 = []
            let arr2 = []
            let children = []
            arr1 = this.thList.map((thItem, thIndex) => {
                if (thIndex == this.colIndex + 1) {
                    return {
                        label: '',
                        data: this.dynamicColumns[index].label + '1',
                        children: [],
                        isHide: true,
                    }
                } else {
                    return {
                        label: '',
                        data: '',
                    }
                }
            })
            arr2 = this.thList.map((thItem, thIndex) => {
                if (thIndex == this.colIndex + 1) {
                    return {
                        label: '',
                        data: this.dynamicColumns[index].label + '2',
                        children: [],
                        isHide: true,
                    }
                } else {
                    return {
                        label: '',
                        data: '',
                    }
                }
            })
            let arr = [
                {
                    id: this.tableData.length + 1,
                    isHide: false,
                    col: arr1
                },
                {
                    id: this.tableData.length + 2,
                    isHide: false,
                    col: arr2
                }
            ]

            this.tableData.splice(this.rowIndex + 1, 0, ...arr)
            children = [this.tableData.length - 1, this.tableData.length]
            this.tableData[this.rowIndex].col[this.colIndex] = {
                ...this.tableData[this.rowIndex].col[this.colIndex],
                children,
                isHide: false,
            }
        },
        addNewRow() {
            let arr1 = []
            let arr2 = []
            let children = []
            arr1 = this.thList.map((thItem, thIndex) => {
                if (thIndex == this.colIndex + 1) {
                    return {
                        label: '',
                        data: this.thList[this.colIndex + 1] + '1',
                        children: [],
                        isHide: true,
                    }
                } else {
                    return {
                        label: '',
                        data: '',
                    }
                }
            })
            arr2 = this.thList.map((thItem, thIndex) => {
                if (thIndex == this.colIndex + 1) {
                    return {
                        label: '',
                        data: this.thList[this.colIndex + 1] + '2',
                        children: [],
                        isHide: true,
                    }
                } else {
                    return {
                        label: '',
                        data: '',
                    }
                }
            })
            let arr = [
                {
                    id: this.tableData.length + 1,
                    isHide: false,
                    col: arr1
                },
                {
                    id: this.tableData.length + 2,
                    isHide: false,
                    col: arr2
                }
            ]

            this.tableData.splice(this.rowIndex + 1, 0, ...arr)
            children = [this.tableData.length - 1, this.tableData.length]
            this.tableData[this.rowIndex].col[this.colIndex] = {
                ...this.tableData[this.rowIndex].col[this.colIndex],
                children,
                isHide: false,
            }
        },
        showRow(rowIndex, colIndex) {
            if (this.tableData[rowIndex].col[colIndex].children && this.tableData[rowIndex].col[colIndex].children.length > 0) {
                // 有子集 children列表里的元素展开
                this.tableData[rowIndex].col[colIndex].isHide = false
                this.tableData[rowIndex].col[colIndex].children.forEach(item => {
                    let index = this.tableData.findIndex(row => row.id == item)
                    this.tableData[index].isHide = false;
                });
            } else if (colIndex == this.level) {
                // 添加新列
                this.showPopCheckRow = true
                this.rowIndex = rowIndex
                this.colIndex = colIndex
            } else {
                // 添加新行
                this.rowIndex = rowIndex
                this.colIndex = colIndex
                this.addNewRow()
            }
        },

        hideRow(rowIndex, colIndex) {
            this.hideChildrenRecursively(this.tableData[rowIndex].col[colIndex])
        },

        hideChildrenRecursively(node) {
            if (!node || !node.children) return;
            node.isHide = true;

            node.children.forEach(item => {
                // item为要隐藏的id
                let index = this.tableData.findIndex(row => row.id === item);
                if (index !== -1) {
                    this.tableData[index].isHide = true;
                    this.tableData[index].col.forEach(colItem => {
                        this.hideChildrenRecursively(colItem)
                    })
                }
            })
        },

        handleClick(tab, event) {
            console.log(tab.name);
            this.curDynamicColumn = tab.name;
            this.dynamicColumns[this.dynamicColumns.findIndex(item => item.value == tab.name)].disabled = true;
            // 初始化表格

        },
        handleQuery() {

        },
        resetQuery() {

        }
    }
};
</script>
<style lang="scss" scoped>
.app-container {
    width: 100%;
    height: calc(100vh - 100px);


}

::v-deep .el-dialog__header {
    border-bottom: 1px solid #EBEBEB;
}

::v-deep .el-dialog__body {
    padding: 10px 20px 30px 20px !important;
}

.el-select {
    margin-left: 0px !important;
    margin-right: 10px;
}

.top {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .top-left {
        font-weight: bold;
    }

    .top-right {
        display: flex;
    }
}

.el-row {
    margin-bottom: 10px;

    &:last-child {
        margin-bottom: 0;
    }
}

.box-card {
    position: relative;
    height: calc(100vh - 162px);
    font-size: 14px;


}

table {
    min-width: 100%;
    border-collapse: collapse;
    border: none;
}

th {
    background-color: rgba(#3C80E8, 0.1);
    font-size: 14px;
    line-height: 24px;
    padding: 12px;
    color: #666666;
    font-weight: normal;
}

td {
    text-align: center;
    padding: 0 12px;
    line-height: 44px;
    color: #333333;
    min-width: 80px;
    font-size: 12px;
}

tr:nth-child(2n-1) {
    background-color: rgba(#000, 0.02);
}

.td-title {
    width: 80px;
}

.lengend-item {
    display: flex;
    align-items: center;

    .lengend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .lengend-name {
        font-size: 14px;
        line-height: 24px;
        color: #333333;
        margin-left: 8px;
        margin-right: 18px;
    }
}
</style>