<template>
  <el-select
    ref="peopleSelectRef"
    :value="value"
    :placeholder="placeholder"
    :disabled="disabled"
    :visible="selectVisible"
    :filter-method="filterPeople"
    clearable
    filterable
    style="width: 100%"
    class="people-tree-select"
    popper-class="people-tree-dropdown"
    @input="handleInput"
    @change="handleSelectChange"
    @visible-change="handleVisibleChange"
  >
    <el-option :value="value" style="height: auto; padding: 0">
      <el-tree
        ref="peopleTree"
        :data="treeData"
        :props="treeProps"
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        :class="['people-tree', { searching: searchText && searchText.trim() }]"
        node-key="hazardId"
        highlight-current
        style="padding: 5px 0"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <i v-if="data.children && data.children.length" />

          <span
            :class="['tree-label', { department: data.type === '1' }]"
            v-html="highlightSearchText(node.label, searchText)"
          />
        </span>
      </el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    peopleList: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      searchText: "",
      selectVisible: false,
      searchTimer: null,

      treeProps: {
        children: "hazardDTOList",
        label: "hazardName",
      },
    };
  },

  computed: {
    // 转换为树形结构数据
    treeData() {
      return this.processTreeData(this.peopleList);
    },
  },

  watch: {
    searchText(val) {
      // console.log("searchText", val);
      if (this.$refs.peopleTree) {
        this.$refs.peopleTree.filter(val);
      }
      // 搜索时自动展开匹配的父节点（无论是否首次）
      if (val && val.trim()) {
        // 增加强制延迟，确保树节点已渲染
        setTimeout(() => {
          this.$nextTick(() => {
            this.expandMatchedNodes(val.trim());
          });
        }, 100);
      }
    },

    peopleList: {
      handler(newVal) {
        // 树数据更新后重新过滤
        this.$nextTick(() => {
          if (this.searchText) {
            this.$refs.peopleTree.filter(this.searchText);
          }
        });
      },
      deep: true,
    },
  },

  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },

  methods: {
    handleVisibleChange(isVisible) {
      if (isVisible) {
        this.searchText = "";
        if (this.$refs.peopleTree) {
          this.$refs.peopleTree.filter("");
        }
        // 修复：先判断 input 存在再赋值
        if (
          this.$refs.peopleSelectRef &&
          this.$refs.peopleSelectRef.$refs.input
        ) {
          this.$refs.peopleSelectRef.$refs.input.value = "";
        }
        // this.resetTreeExpand();
      }
    },
    closeDropdown() {
      this.$refs.peopleSelectRef.blur();
    },

    // 处理树形数据
    processTreeData(data) {
      return data.map((item) => ({
        ...item,
        label: item.label ? item.label.trim() : "",
        children: item.children ? this.processTreeData(item.children) : [],
      }));
    },

    // 节点过滤方法 - 增强的模糊搜索
    filterNode(value, data) {
      if (!value) return true;

      const searchValue = value.toLowerCase().trim();
      const label = data.label ? data.label.toLowerCase() : "";
      const name = data.name ? data.name.toLowerCase() : "";

      // 1. 精确匹配
      if (label.includes(searchValue) || name.includes(searchValue)) {
        return true;
      }

      // 2. 拼音首字母匹配
      if (
        this.matchPinyinInitials(label, searchValue) ||
        this.matchPinyinInitials(name, searchValue)
      ) {
        return true;
      }

      // 3. 分词匹配 - 支持空格分隔的多个关键词
      const keywords = searchValue.split(/\s+/).filter((k) => k.length > 0);
      if (keywords.length > 1) {
        return keywords.every(
          (keyword) =>
            label.includes(keyword) ||
            name.includes(keyword) ||
            this.matchPinyinInitials(label, keyword) ||
            this.matchPinyinInitials(name, keyword)
        );
      }

      return false;
    },

    // 节点点击事件
    handleNodeClick(data, node) {
      // 如果是其他类型且有子节点，切换展开状态
      if (data.hazardDTOList && data.hazardDTOList.length) {
        node.expanded = !node.expanded;
        return;
      }

      // 如果是人员节点（type !== '1' 且不是 general-project），触发选择

      this.handleSelectChange(data.hazardId);
      // console.log("handleNodeClick", data);
      // 更新树的高亮选择
      this.$refs.peopleTree.setCurrentKey(data.hazardId);
      this.$nextTick(() => {
        this.closeDropdown();
      });

      // 其他情况（没有子节点的节点）不做任何操作
    },

    handleInput(value) {
      this.$emit("input", value);
    },

    handleSelectChange(selectedId) {
      if (!selectedId) {
        this.$emit("change", null);
        return;
      }
      // console.log("selectedId", selectedId);

      const selectedItem = this.findNodeById(this.treeData, selectedId);
      // console.log("selectedItem", selectedItem);
      if (selectedItem && selectedItem.type !== "1") {
        const cleanItem = {
          id: selectedItem.hazardId,
          label: selectedItem.hazardName || "",
          name: selectedItem.hazardName || "",
          type: selectedItem.type,
        };

        this.$emit("change", cleanItem);
      }
    },

    // 根据ID查找节点
    findNodeById(nodes, hazardId) {
      // console.log("nodes", nodes, hazardId);
      for (const node of nodes) {
        if (node.hazardId === hazardId) return node;
        if (node.hazardDTOList && node.hazardDTOList.length) {
          const found = this.findNodeById(node.hazardDTOList, hazardId);
          if (found) return found;
        }
      }
      return null;
    },

    // 拼音首字母匹配方法
    matchPinyinInitials(text, searchValue) {
      if (!text || !searchValue) return false;

      // 简单的中文拼音首字母映射
      const pinyinMap = {
        a: "阿啊",
        b: "不把白本部被北备比表必标",
        c: "从程成产出常场长城创传",
        d: "的大都到道地点电当得对多段",
        e: "二",
        f: "发方法分风服费发放防发",
        g: "工个过公管国广高改工构",
        h: "和好后会化回还海合黄行",
        i: "一",
        j: "就进建交机计经检基级见技结",
        k: "可看科开口控课快客",
        l: "了来理里立流料量联力领路类",
        m: "没么面民明名目模美门",
        n: "年能内南那女你农",
        o: "哦",
        p: "平配品排培破片跑",
        q: "去其全清情期前起请求区",
        r: "人如让日然认入任",
        s: "是时实施所说三设生手市上四十",
        t: "他通同体统头条特提图天",
        u: "有用于",
        v: "",
        w: "我为文物位问外王万五网维",
        x: "现系性新学小心选许信下项行西",
        y: "一要用有以业已应意音元月研运",
        z: "中在这者主专注资制知至重组",
      };

      // 尝试拼音首字母匹配
      for (let i = 0; i < searchValue.length; i++) {
        const char = searchValue[i];
        const pinyinChars = pinyinMap[char];
        if (pinyinChars && i < text.length) {
          if (!pinyinChars.includes(text[i])) {
            return false;
          }
        } else if (char !== text[i]) {
          return false;
        }
      }

      return searchValue.length > 0 && searchValue.length <= text.length;
    },

    // 带防抖的搜索方法
    filterPeople(value) {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      // 首次输入或搜索内容变化时，缩短防抖时间（100ms）并立即执行
      const delay = this.searchText === "" && value ? 150 : 300;

      this.searchTimer = setTimeout(() => {
        this.searchText = value;
        // 强制触发展开逻辑（针对首次搜索）
        if (value) {
          this.$nextTick(() => {
            this.expandMatchedNodes(value.trim());
          });
        }
      }, delay);
    },

    // 增强expandMatchedNodes方法，确保节点正确展开
    expandMatchedNodes(searchValue) {
      if (!this.$refs.peopleTree || !searchValue) return;

      const expandedKeys = [];
      this.collectExpandedNodes(this.treeData, searchValue, expandedKeys);

      // 去重并确保展开逻辑生效
      const uniqueKeys = [...new Set(expandedKeys)];

      this.$nextTick(() => {
        uniqueKeys.forEach((key) => {
          const node = this.$refs.peopleTree.store.nodesMap[key];
          if (node && !node.expanded) {
            node.expand();
          }
        });
      });
    },

    // 递归收集需要展开的节点
    collectExpandedNodes(nodes, searchValue, expandedKeys, parentKey = null) {
      let hasMatchedChild = false;

      for (const node of nodes) {
        // 检查当前节点是否匹配
        if (this.filterNode(searchValue, node)) {
          hasMatchedChild = true;

          // 如果有父节点，添加到展开列表
          if (parentKey) {
            expandedKeys.push(parentKey);
          }
        }

        // 递归检查子节点
        if (node.children && node.children.length > 0) {
          const childMatched = this.collectExpandedNodes(
            node.children,
            searchValue,
            expandedKeys,
            node.hazardId
          );

          if (childMatched) {
            hasMatchedChild = true;
            // 如果子节点有匹配，当前节点也需要展开
            if (node.hazardId) {
              expandedKeys.push(node.hazardId);
            }
            // 如果有父节点，父节点也需要展开
            if (parentKey) {
              expandedKeys.push(parentKey);
            }
          }
        }
      }

      return hasMatchedChild;
    },

    // 高亮搜索文本
    highlightSearchText(text, searchValue) {
      if (!text) return "";
      if (!searchValue || !searchValue.trim()) return text;

      const searchText = searchValue.trim();
      // 防止XSS攻击，转义HTML特殊字符
      const escapedText = text.replace(/[&<>"']/g, function (match) {
        const escapeMap = {
          "&": "&amp;",
          "<": "&lt;",
          ">": "&gt;",
          '"': "&quot;",
          "'": "&#x27;",
        };
        return escapeMap[match];
      });

      // 如果搜索文本包含在显示文本中，高亮显示
      if (escapedText.toLowerCase().includes(searchText.toLowerCase())) {
        const regex = new RegExp(
          `(${searchText.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
          "gi"
        );
        return escapedText.replace(
          regex,
          '<span class="search-highlight">$1</span>'
        );
      }

      return escapedText;
    },
  },
};
</script>

<style scoped>
.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.tree-icon {
  margin-right: 6px;
  font-size: 14px;
  color: #909399;
}

.tree-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 调整树组件样式 */
:deep(.people-tree) {
  min-width: 180px;
  width: auto;
}

/* 控制下拉框宽度 - 使用特定class确保只影响这个组件 */
:deep(.people-tree-dropdown) {
  max-width: 250px !important;
  min-width: 180px !important;
  width: auto !important;
}

/* 全局样式 - 更高优先级 */
::v-deep .people-tree-dropdown.el-select-dropdown {
  max-width: 250px !important;
  min-width: 180px !important;
  width: auto !important;
}

:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(
    .el-tree--highlight-current
      .el-tree-node.is-current
      > .el-tree-node__content
  ) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item) {
  padding: 0;
  height: auto;
}

/* 搜索高亮样式 */
:deep(.search-highlight) {
  background-color: #fffacd;
  color: #d32f2f;
  font-weight: bold;
  padding: 1px 2px;
  border-radius: 2px;
}
</style>

<style>
/* 全局样式 - 不受scoped限制，确保能够覆盖Element UI的默认样式 */
.people-tree-dropdown.el-select-dropdown {
  max-width: 650px !important;
  min-width: 650px !important;
  width: auto !important;
}

.people-tree-dropdown .el-select-dropdown__item {
  padding: 0;
  height: auto;
}
</style>
