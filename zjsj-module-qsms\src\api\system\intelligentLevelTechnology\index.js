import request from '@/utils/request'

// 查询智能化技术应用记录列表
export function listSmartTechApplication(query) {
  return request({
    url: '/system/smartTechApplication/list',
    method: 'get',
    params: query
  })
}

// 查询智能化技术应用记录详细
export function getSmartTechApplication(id) {
  return request({
    url: '/system/smartTechApplication/' + id,
    method: 'get'
  })
}

// 新增智能化技术应用记录
export function addSmartTechApplication(data) {
  return request({
    url: '/system/smartTechApplication',
    method: 'post',
    data: data
  })
}

// 修改智能化技术应用记录
export function updateSmartTechApplication(data) {
  return request({
    url: '/system/smartTechApplication',
    method: 'put',
    data: data
  })
}

// 删除智能化技术应用记录
export function delSmartTechApplication(id) {
  return request({
    url: '/system/smartTechApplication/' + id,
    method: 'delete'
  })
}
