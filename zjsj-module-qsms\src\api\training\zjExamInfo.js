import request from "@/utils/request";

// 查询考试信息列表
export function listZjExamInfo(query) {
  return request({
    url: "/training/zjExamInfo/list",
    method: "get",
    params: query,
  });
}

// 查询考试信息详细
export function getZjExamInfo(id) {
  return request({
    url: "/training/zjExamInfo/" + id,
    method: "get",
  });
}

// 新增考试信息
export function addZjExamInfo(data) {
  return request({
    url: "/training/zjExamInfo",
    method: "post",
    data: data,
  });
}

// 修改考试信息
export function updateZjExamInfo(data) {
  return request({
    url: "/training/zjExamInfo",
    method: "put",
    data: data,
  });
}

// 删除考试信息
export function delZjExamInfo(id) {
  return request({
    url: "/training/zjExamInfo/" + id,
    method: "delete",
  });
}
// 试卷名称
export function getPaperNameList() {
  return request({
    url: "/training/zjExamInfo/getPapertitle",
    method: "get",
  });
}

// 获取考试日期选项
export function getExamDateOptions() {
  return request({
    url: "/examscreen/queryItem",
    method: "get",
  });
}

// 查询考试大屏数据
export function queryExamScreenData(examDate) {
  return request({
    url: "/examscreen/queryData",
    method: "post",
    data: {
      examDate: examDate || ""  // 左上角什么都不选传空字符串，选了传选择的内容
    }
  });
}
