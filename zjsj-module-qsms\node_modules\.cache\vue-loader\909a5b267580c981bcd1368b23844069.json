{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\pieChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\pieChart.vue", "mtime": 1757495783032}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pieChart.vue"], "names": [], "mappings": ";;;;;AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "pieChart.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n    <div :class=\"className\" :style=\"{ height: height, width: width }\" />\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n    props: {\r\n        className: {\r\n            type: String,\r\n            default: 'chart'\r\n        },\r\n        width: {\r\n            type: String,\r\n            default: '100%'\r\n        },\r\n        height: {\r\n            type: String,\r\n            default: '300px'\r\n        },\r\n        data: {\r\n            type: Object,\r\n            default: () => ({})\r\n        },\r\n        showCenterText: {\r\n            type: Boolean,\r\n            default: false\r\n        },\r\n        centerText: {\r\n            type: Object,\r\n            default: () => ({})\r\n        }\r\n    },\r\n    data() {\r\n        return {\r\n            chart: null,\r\n            colorList: [\r\n                '#3C80E8',\r\n                '#8EE98F',\r\n                '#A1FFEB',\r\n                '#54C255',\r\n                '#A1CDFF',\r\n                '#FF920D',\r\n                '#FECF77',\r\n                '#F3B2B1',\r\n                '#B38DFF'\r\n            ]\r\n        }\r\n    },\r\n    watch: {\r\n        data: {\r\n            handler(newVal, oldVal) {\r\n                if (this.chart) {\r\n                    // 更新图表\r\n                    console.log(newVal)\r\n                    this.data = newVal\r\n                    this.setOption()\r\n                } else {\r\n                    // 初始化图表\r\n                    this.$nextTick(() => {\r\n                        this.initChart()\r\n                    })\r\n                }\r\n            },\r\n            immediate: true\r\n        }\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    beforeDestroy() {\r\n        if (!this.chart) {\r\n            return\r\n        }\r\n        this.chart.dispose()\r\n        this.chart = null\r\n        window.removeEventListener('resize', this.chartResize)\r\n    },\r\n    methods: {\r\n        chartResize() {\r\n            this.chart.resize()\r\n        },\r\n        initChart() {\r\n            const that = this\r\n            that.chart = echarts.init(that.$el)\r\n            window.addEventListener('resize', that.chartResize)\r\n            that.chart.on('click', (params) => {\r\n                that.$emit('pieClick', params.data)\r\n            })\r\n            that.setOption()\r\n        },\r\n        setOption() {\r\n            // 构建默认配置\r\n            const defaultOption = {\r\n                tooltip: {},\r\n                series: [\r\n                    {\r\n                        type: 'pie',\r\n                        radius: ['60%', '94%'],\r\n                        color: this.data.colorList,\r\n                        data: this.data.data,\r\n                        labelLine: {\r\n                            normal: {\r\n                                show: false\r\n                            }\r\n                        },\r\n                        label: {\r\n                            normal: {\r\n                                position: 'inner',\r\n                                formatter: '{d}%',\r\n                                textStyle: {\r\n                                    color: '#fff',\r\n                                    fontSize: 12,\r\n                                    textShadowColor: 'rgba(0, 0, 0, 0.5)',\r\n                                    textShadowBlur: 10\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                ]\r\n            }\r\n\r\n            // 如果需要显示中心文字\r\n            if (this.showCenterText && this.centerText) {\r\n                defaultOption.graphic = {\r\n                    type: 'group',\r\n                    left: 'center',\r\n                    top: 'center',\r\n                    children: [\r\n                        {\r\n                            type: 'text',\r\n                            z: 100,\r\n                            left: 'center',\r\n                            top: 'middle',\r\n                            style: {\r\n                                fill: '#333',\r\n                                text: this.centerText.value || '',\r\n                                font: 'bold 28px Microsoft YaHei'\r\n                            }\r\n                        },\r\n                        {\r\n                            type: 'text',\r\n                            z: 100,\r\n                            left: 'center',\r\n                            top: 15,\r\n                            style: {\r\n                                fill: '#666',\r\n                                text: this.centerText.unit || '',\r\n                                font: '12px Microsoft YaHei'\r\n                            }\r\n                        }\r\n                    ]\r\n                }\r\n            }\r\n\r\n            // 合并用户传入的配置\r\n            const option = { ...defaultOption, ...this.data.option }\r\n\r\n            // 如果传入了series配置，需要合并series\r\n            if (this.data.option && this.data.option.series) {\r\n                option.series = [{\r\n                    ...defaultOption.series[0],\r\n                    ...this.data.option.series[0]\r\n                }]\r\n            }\r\n\r\n            this.chart.setOption(option)\r\n        }\r\n    }\r\n}\r\n</script>\r\n"]}]}