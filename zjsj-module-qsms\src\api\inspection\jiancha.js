import request from '@/utils/request'

// 新增任务
export function addJianChaTask(data) {
    return request({
        url: '/inspect/taskinfo',
        method: 'post',
        data
    })
}

// 获取任务列表
export function getJianChaTaskList(params) {
    return request({
        url: '/inspect/taskinfo/list',
        method: 'get',
        params
    })
}

// 修改任务
export function editJianChaTask(data) {
    return request({
        url: '/inspect/taskinfo',
        method: 'put',
        data
    })
}


// 修改评价
export function editTaskDetail(data) {
    return request({
        url: '/inspect/taskdetail/editpf',
        method: 'put',
        data
    })
}


// 删除任务
export function deleteJianChaTask(id) {
    return request({
        url: '/inspect/taskinfo/' + id,
        method: 'delete',
    })
}

// 获取任务详情
export function getJianChaTaskInfo(id) {
    return request({
        url: '/inspect/taskinfo/' + id,
        method: 'get',
    })
}

// 发送
export function sendJianChaTask(data) {
    return request({
        url: '/inspect/taskinfo/editStatus',
        method: 'post',
        data
    })
}

// 线索核实
export function lian(data) {
    return request({
        url: '/inspect/taskdetail/updateinfo',
        method: 'post',
        data
    })
}

// 获取问题核查详情
export function getHeChaList(params) {
    return request({
        url: '/inspect/taskdetail/hclist',
        method: 'get',
        params
    })
}

// 设置周期
export function setCycle(data) {
    return request({
        url: '/inspect/taskinfo/editZhouqi',
        method: 'post',
        data
    })
}

//下发检查员列表
export function selectUserByRoleKey(params) {
    return request({
        url: '/system/user/selectUserByRoleKey',
        method: 'get',
        params
    })
}
//确认下发
export function xiafa(data) {
    return request({
        url: '/inspect/taskdetail/xiafa',
        method: 'post',
        data
    })
}

//任务
export function getDetailInfo(params) {
    return request({
        url: '/inspect/taskinfo/getDetailInfo',
        method: 'get',
        params
    })
}