import request from '@/utils/request'

// 安全管理总览
export function getManagementOverview(query) {
  return request({
    url: '/pcStatistics/managementOverview',
    method: 'get',
    params: query
  })
}

// 质量管理统计（预留接口）
export function getQualityStatistics(query) {
  return request({
    url: '/pcStatistics/qualityStatistics',
    method: 'get',
    params: query
  })
}

// 安全管理统计（预留接口）
export function getSafetyStatistics(query) {
  return request({
    url: '/pcStatistics/safetyStatistics',
    method: 'get',
    params: query
  })
}

// 危大工程统计（预留接口）
export function getDangerousProjectsStatistics(query) {
  return request({
    url: '/pcStatistics/dangerousProjectsStatistics',
    method: 'get',
    params: query
  })
}

// 大型设备统计（预留接口）
export function getLargeEquipmentStatistics(query) {
  return request({
    url: '/pcStatistics/largeEquipmentStatistics',
    method: 'get',
    params: query
  })
}

// 安全投资统计（预留接口）
export function getSafetyInvestmentStatistics(query) {
  return request({
    url: '/pcStatistics/safetyInvestmentStatistics',
    method: 'get',
    params: query
  })
}

// 隐患类别统计
export function getDangerTypeStatistics(query) {
  return request({
    url: '/pcStatistics/dangerTypeStatistics',
    method: 'get',
    params: query
  })
}

// 安全生产投入统计
export function getSafetyProductionStatistics(query) {
  return request({
    url: '/pcStatistics/safetyProductionStatistics',
    method: 'get',
    params: query
  })
}

// 危大工程统计
export function getDangerousProStatistics(query) {
  return request({
    url: '/pcStatistics/dangerousProStatistics',
    method: 'get',
    params: query
  })
}

// 根据项目名称获取危大工程详细统计
export function getDangerousProByProjectStatistics(projectName) {
  return request({
    url: '/pcStatistics/dangerousProByProjectStatistics',
    method: 'get',
    params: { projectName }
  })
}

// 根据设备名称获取大型设备详细统计
export function getLargeEquipmentByNameStatistics(equipmentName) {
  return request({
    url: '/pcStatistics/largeEquipmentByNameStatistics',
    method: 'get',
    params: { equipmentName }
  })
}
