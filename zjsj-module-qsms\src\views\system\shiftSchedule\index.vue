<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="带班计划编号" prop="planNo">
        <el-input
          v-model="queryParams.planNo"
          placeholder="请输入带班计划编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划周期" prop="planPeriod">
        <el-input
          v-model="queryParams.planPeriod"
          placeholder="请输入计划周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作地点" prop="workplace">
        <el-input
          v-model="queryParams.workplace"
          placeholder="请输入工作地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属部门/班组" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入所属部门/班组"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="关联项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入关联项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关联项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入关联项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第一天日期" prop="day1Date">
        <el-date-picker
          clearable
          v-model="queryParams.day1Date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择第一天日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第一天带班负责人" prop="day1Leader">
        <el-input
          v-model="queryParams.day1Leader"
          placeholder="请输入第一天带班负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第一天负责人联系方式" prop="day1LeaderContact">
        <el-input
          v-model="queryParams.day1LeaderContact"
          placeholder="请输入第一天负责人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第二天日期" prop="day2Date">
        <el-date-picker
          clearable
          v-model="queryParams.day2Date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择第二天日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第二天带班负责人" prop="day2Leader">
        <el-input
          v-model="queryParams.day2Leader"
          placeholder="请输入第二天带班负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第二天负责人联系方式" prop="day2LeaderContact">
        <el-input
          v-model="queryParams.day2LeaderContact"
          placeholder="请输入第二天负责人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第三天日期" prop="day3Date">
        <el-date-picker
          clearable
          v-model="queryParams.day3Date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择第三天日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第三天带班负责人" prop="day3Leader">
        <el-input
          v-model="queryParams.day3Leader"
          placeholder="请输入第三天带班负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第三天负责人联系方式" prop="day3LeaderContact">
        <el-input
          v-model="queryParams.day3LeaderContact"
          placeholder="请输入第三天负责人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第四天日期" prop="day4Date">
        <el-date-picker
          clearable
          v-model="queryParams.day4Date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择第四天日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第四天带班负责人" prop="day4Leader">
        <el-input
          v-model="queryParams.day4Leader"
          placeholder="请输入第四天带班负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第四天负责人联系方式" prop="day4LeaderContact">
        <el-input
          v-model="queryParams.day4LeaderContact"
          placeholder="请输入第四天负责人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第五天日期" prop="day5Date">
        <el-date-picker
          clearable
          v-model="queryParams.day5Date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择第五天日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第五天带班负责人" prop="day5Leader">
        <el-input
          v-model="queryParams.day5Leader"
          placeholder="请输入第五天带班负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第五天负责人联系方式" prop="day5LeaderContact">
        <el-input
          v-model="queryParams.day5LeaderContact"
          placeholder="请输入第五天负责人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第六天日期" prop="day6Date">
        <el-date-picker
          clearable
          v-model="queryParams.day6Date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择第六天日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第六天带班负责人" prop="day6Leader">
        <el-input
          v-model="queryParams.day6Leader"
          placeholder="请输入第六天带班负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第六天负责人联系方式" prop="day6LeaderContact">
        <el-input
          v-model="queryParams.day6LeaderContact"
          placeholder="请输入第六天负责人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第七天日期" prop="day7Date">
        <el-date-picker
          clearable
          v-model="queryParams.day7Date"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择第七天日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="第七天带班负责人" prop="day7Leader">
        <el-input
          v-model="queryParams.day7Leader"
          placeholder="请输入第七天带班负责人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="第七天负责人联系方式" prop="day7LeaderContact">
        <el-input
          v-model="queryParams.day7LeaderContact"
          placeholder="请输入第七天负责人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划总天数" prop="totalDays">
        <el-input
          v-model="queryParams.totalDays"
          placeholder="请输入计划总天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参与带班总人数" prop="participantCount">
        <el-input
          v-model="queryParams.participantCount"
          placeholder="请输入参与带班总人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划编制人" prop="compiler">
        <el-input
          v-model="queryParams.compiler"
          placeholder="请输入计划编制人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="reviewer">
        <el-input
          v-model="queryParams.reviewer"
          placeholder="请输入审核人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审批人" prop="approver">
        <el-input
          v-model="queryParams.approver"
          placeholder="请输入审批人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="编制时间" prop="compileTime">
        <el-date-picker
          clearable
          v-model="queryParams.compileTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择编制时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="审批时间" prop="approveTime">
        <el-date-picker
          clearable
          v-model="queryParams.approveTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择审批时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:plan:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:plan:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:plan:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:plan:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="planList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="带班计划编号" align="center" prop="planNo" />
      <el-table-column label="计划周期" align="center" prop="planPeriod" />
      <!-- 日常/周计划/月计划/专项带班 -->
      <el-table-column label="计划类型" align="center" prop="planType" />
      <el-table-column label="工作地点" align="center" prop="workplace" />
      <el-table-column label="所属部门/班组" align="center" prop="department" />
      <el-table-column label="关联项目编码" align="center" prop="projectCode" />
      <el-table-column label="关联项目名称" align="center" prop="projectName" />
      <!-- <el-table-column
        label="第一天日期"
        align="center"
        prop="day1Date"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.day1Date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="第一天带班负责人"
        align="center"
        prop="day1Leader"
      />
      <el-table-column
        label="第一天负责人联系方式"
        align="center"
        prop="day1LeaderContact"
      />
      <el-table-column
        label="第一天主要工作内容"
        align="center"
        prop="day1WorkContent"
      />
      <el-table-column
        label="第一天安全注意事项"
        align="center"
        prop="day1SafetyFocus"
      />
      <el-table-column
        label="第二天日期"
        align="center"
        prop="day2Date"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.day2Date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="第二天带班负责人"
        align="center"
        prop="day2Leader"
      />
      <el-table-column
        label="第二天负责人联系方式"
        align="center"
        prop="day2LeaderContact"
      />
      <el-table-column
        label="第二天主要工作内容"
        align="center"
        prop="day2WorkContent"
      />
      <el-table-column
        label="第二天安全注意事项"
        align="center"
        prop="day2SafetyFocus"
      />
      <el-table-column
        label="第三天日期"
        align="center"
        prop="day3Date"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.day3Date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="第三天带班负责人"
        align="center"
        prop="day3Leader"
      />
      <el-table-column
        label="第三天负责人联系方式"
        align="center"
        prop="day3LeaderContact"
      />
      <el-table-column
        label="第三天主要工作内容"
        align="center"
        prop="day3WorkContent"
      />
      <el-table-column
        label="第三天安全注意事项"
        align="center"
        prop="day3SafetyFocus"
      />
      <el-table-column
        label="第四天日期"
        align="center"
        prop="day4Date"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.day4Date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="第四天带班负责人"
        align="center"
        prop="day4Leader"
      />
      <el-table-column
        label="第四天负责人联系方式"
        align="center"
        prop="day4LeaderContact"
      />
      <el-table-column
        label="第四天主要工作内容"
        align="center"
        prop="day4WorkContent"
      />
      <el-table-column
        label="第四天安全注意事项"
        align="center"
        prop="day4SafetyFocus"
      />
      <el-table-column
        label="第五天日期"
        align="center"
        prop="day5Date"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.day5Date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="第五天带班负责人"
        align="center"
        prop="day5Leader"
      />
      <el-table-column
        label="第五天负责人联系方式"
        align="center"
        prop="day5LeaderContact"
      />
      <el-table-column
        label="第五天主要工作内容"
        align="center"
        prop="day5WorkContent"
      />
      <el-table-column
        label="第五天安全注意事项"
        align="center"
        prop="day5SafetyFocus"
      />
      <el-table-column
        label="第六天日期"
        align="center"
        prop="day6Date"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.day6Date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="第六天带班负责人"
        align="center"
        prop="day6Leader"
      />
      <el-table-column
        label="第六天负责人联系方式"
        align="center"
        prop="day6LeaderContact"
      />
      <el-table-column
        label="第六天主要工作内容"
        align="center"
        prop="day6WorkContent"
      />
      <el-table-column
        label="第六天安全注意事项"
        align="center"
        prop="day6SafetyFocus"
      />
      <el-table-column
        label="第七天日期"
        align="center"
        prop="day7Date"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.day7Date, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="第七天带班负责人"
        align="center"
        prop="day7Leader"
      />
      <el-table-column
        label="第七天负责人联系方式"
        align="center"
        prop="day7LeaderContact"
      />
      <el-table-column
        label="第七天主要工作内容"
        align="center"
        prop="day7WorkContent"
      />
      <el-table-column
        label="第七天安全注意事项"
        align="center"
        prop="day7SafetyFocus"
      />
      <el-table-column label="计划总天数" align="center" prop="totalDays" />
      <el-table-column
        label="参与带班总人数"
        align="center"
        prop="participantCount"
      />
      <el-table-column
        label="通用安全要求"
        align="center"
        prop="generalSafetyRequirements"
      />
      <el-table-column
        label="工作小结要求"
        align="center"
        prop="workSummaryStandard"
      />
      <el-table-column
        label="计划状态：草稿/待审批/已批准/执行中/已完成/已取消"
        align="center"
        prop="planStatus"
      />
      <el-table-column label="计划编制人" align="center" prop="compiler" />
      <el-table-column label="审核人" align="center" prop="reviewer" />
      <el-table-column label="审批人" align="center" prop="approver" />
      <el-table-column
        label="编制时间"
        align="center"
        prop="compileTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.compileTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审批时间"
        align="center"
        prop="approveTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approveTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="带班计划文件地址"
        align="center"
        prop="planFileUrl"
      />
      <el-table-column
        label="执行记录汇总地址"
        align="center"
        prop="executionRecordUrl"
      />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:plan:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:plan:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改带班计划（记录各时段带班安排、人员及工作内容）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="带班计划编号" prop="planNo">
          <el-input v-model="form.planNo" placeholder="请输入带班计划编号" />
        </el-form-item>
        <el-form-item label="计划周期" prop="planPeriod">
          <el-input v-model="form.planPeriod" placeholder="请输入计划周期" />
        </el-form-item>
        <el-form-item label="工作地点" prop="workplace">
          <el-input v-model="form.workplace" placeholder="请输入工作地点" />
        </el-form-item>
        <el-form-item label="所属部门/班组" prop="department">
          <el-input
            v-model="form.department"
            placeholder="请输入所属部门/班组"
          />
        </el-form-item>
        <el-form-item label="关联项目编码" prop="projectCode">
          <el-input
            v-model="form.projectCode"
            placeholder="请输入关联项目编码"
          />
        </el-form-item>
        <el-form-item label="关联项目名称" prop="projectName">
          <el-input
            v-model="form.projectName"
            placeholder="请输入关联项目名称"
          />
        </el-form-item>
        <el-form-item label="第一天日期" prop="day1Date">
          <el-date-picker
            clearable
            v-model="form.day1Date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择第一天日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第一天带班负责人" prop="day1Leader">
          <el-input
            v-model="form.day1Leader"
            placeholder="请输入第一天带班负责人"
          />
        </el-form-item>
        <el-form-item label="第一天负责人联系方式" prop="day1LeaderContact">
          <el-input
            v-model="form.day1LeaderContact"
            placeholder="请输入第一天负责人联系方式"
          />
        </el-form-item>
        <el-form-item label="第一天主要工作内容">
          <editor v-model="form.day1WorkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="第一天安全注意事项" prop="day1SafetyFocus">
          <el-input
            v-model="form.day1SafetyFocus"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="第二天日期" prop="day2Date">
          <el-date-picker
            clearable
            v-model="form.day2Date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择第二天日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第二天带班负责人" prop="day2Leader">
          <el-input
            v-model="form.day2Leader"
            placeholder="请输入第二天带班负责人"
          />
        </el-form-item>
        <el-form-item label="第二天负责人联系方式" prop="day2LeaderContact">
          <el-input
            v-model="form.day2LeaderContact"
            placeholder="请输入第二天负责人联系方式"
          />
        </el-form-item>
        <el-form-item label="第二天主要工作内容">
          <editor v-model="form.day2WorkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="第二天安全注意事项" prop="day2SafetyFocus">
          <el-input
            v-model="form.day2SafetyFocus"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="第三天日期" prop="day3Date">
          <el-date-picker
            clearable
            v-model="form.day3Date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择第三天日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第三天带班负责人" prop="day3Leader">
          <el-input
            v-model="form.day3Leader"
            placeholder="请输入第三天带班负责人"
          />
        </el-form-item>
        <el-form-item label="第三天负责人联系方式" prop="day3LeaderContact">
          <el-input
            v-model="form.day3LeaderContact"
            placeholder="请输入第三天负责人联系方式"
          />
        </el-form-item>
        <el-form-item label="第三天主要工作内容">
          <editor v-model="form.day3WorkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="第三天安全注意事项" prop="day3SafetyFocus">
          <el-input
            v-model="form.day3SafetyFocus"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="第四天日期" prop="day4Date">
          <el-date-picker
            clearable
            v-model="form.day4Date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择第四天日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第四天带班负责人" prop="day4Leader">
          <el-input
            v-model="form.day4Leader"
            placeholder="请输入第四天带班负责人"
          />
        </el-form-item>
        <el-form-item label="第四天负责人联系方式" prop="day4LeaderContact">
          <el-input
            v-model="form.day4LeaderContact"
            placeholder="请输入第四天负责人联系方式"
          />
        </el-form-item>
        <el-form-item label="第四天主要工作内容">
          <editor v-model="form.day4WorkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="第四天安全注意事项" prop="day4SafetyFocus">
          <el-input
            v-model="form.day4SafetyFocus"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="第五天日期" prop="day5Date">
          <el-date-picker
            clearable
            v-model="form.day5Date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择第五天日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第五天带班负责人" prop="day5Leader">
          <el-input
            v-model="form.day5Leader"
            placeholder="请输入第五天带班负责人"
          />
        </el-form-item>
        <el-form-item label="第五天负责人联系方式" prop="day5LeaderContact">
          <el-input
            v-model="form.day5LeaderContact"
            placeholder="请输入第五天负责人联系方式"
          />
        </el-form-item>
        <el-form-item label="第五天主要工作内容">
          <editor v-model="form.day5WorkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="第五天安全注意事项" prop="day5SafetyFocus">
          <el-input
            v-model="form.day5SafetyFocus"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="第六天日期" prop="day6Date">
          <el-date-picker
            clearable
            v-model="form.day6Date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择第六天日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第六天带班负责人" prop="day6Leader">
          <el-input
            v-model="form.day6Leader"
            placeholder="请输入第六天带班负责人"
          />
        </el-form-item>
        <el-form-item label="第六天负责人联系方式" prop="day6LeaderContact">
          <el-input
            v-model="form.day6LeaderContact"
            placeholder="请输入第六天负责人联系方式"
          />
        </el-form-item>
        <el-form-item label="第六天主要工作内容">
          <editor v-model="form.day6WorkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="第六天安全注意事项" prop="day6SafetyFocus">
          <el-input
            v-model="form.day6SafetyFocus"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="第七天日期" prop="day7Date">
          <el-date-picker
            clearable
            v-model="form.day7Date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择第七天日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第七天带班负责人" prop="day7Leader">
          <el-input
            v-model="form.day7Leader"
            placeholder="请输入第七天带班负责人"
          />
        </el-form-item>
        <el-form-item label="第七天负责人联系方式" prop="day7LeaderContact">
          <el-input
            v-model="form.day7LeaderContact"
            placeholder="请输入第七天负责人联系方式"
          />
        </el-form-item>
        <el-form-item label="第七天主要工作内容">
          <editor v-model="form.day7WorkContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="第七天安全注意事项" prop="day7SafetyFocus">
          <el-input
            v-model="form.day7SafetyFocus"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="计划总天数" prop="totalDays">
          <el-input v-model="form.totalDays" placeholder="请输入计划总天数" />
        </el-form-item>
        <el-form-item label="参与带班总人数" prop="participantCount">
          <el-input
            v-model="form.participantCount"
            placeholder="请输入参与带班总人数"
          />
        </el-form-item>
        <el-form-item label="通用安全要求" prop="generalSafetyRequirements">
          <el-input
            v-model="form.generalSafetyRequirements"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="工作小结要求" prop="workSummaryStandard">
          <el-input
            v-model="form.workSummaryStandard"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="计划编制人" prop="compiler">
          <el-input v-model="form.compiler" placeholder="请输入计划编制人" />
        </el-form-item>
        <el-form-item label="审核人" prop="reviewer">
          <el-input v-model="form.reviewer" placeholder="请输入审核人" />
        </el-form-item>
        <el-form-item label="审批人" prop="approver">
          <el-input v-model="form.approver" placeholder="请输入审批人" />
        </el-form-item>
        <el-form-item label="编制时间" prop="compileTime">
          <el-date-picker
            clearable
            v-model="form.compileTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择编制时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审批时间" prop="approveTime">
          <el-date-picker
            clearable
            v-model="form.approveTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择审批时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="带班计划文件地址" prop="planFileUrl">
          <el-input
            v-model="form.planFileUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="执行记录汇总地址" prop="executionRecordUrl">
          <el-input
            v-model="form.executionRecordUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPlan,
  getPlan,
  delPlan,
  addPlan,
  updatePlan,
} from "@/api/system/shiftSchedule/index";

export default {
  name: "Plan",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 带班计划（记录各时段带班安排、人员及工作内容）表格数据
      planList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planNo: null,
        planPeriod: null,
        planType: null,
        workplace: null,
        department: null,
        projectCode: null,
        projectName: null,
        day1Date: null,
        day1Leader: null,
        day1LeaderContact: null,
        day1WorkContent: null,
        day1SafetyFocus: null,
        day2Date: null,
        day2Leader: null,
        day2LeaderContact: null,
        day2WorkContent: null,
        day2SafetyFocus: null,
        day3Date: null,
        day3Leader: null,
        day3LeaderContact: null,
        day3WorkContent: null,
        day3SafetyFocus: null,
        day4Date: null,
        day4Leader: null,
        day4LeaderContact: null,
        day4WorkContent: null,
        day4SafetyFocus: null,
        day5Date: null,
        day5Leader: null,
        day5LeaderContact: null,
        day5WorkContent: null,
        day5SafetyFocus: null,
        day6Date: null,
        day6Leader: null,
        day6LeaderContact: null,
        day6WorkContent: null,
        day6SafetyFocus: null,
        day7Date: null,
        day7Leader: null,
        day7LeaderContact: null,
        day7WorkContent: null,
        day7SafetyFocus: null,
        totalDays: null,
        participantCount: null,
        generalSafetyRequirements: null,
        workSummaryStandard: null,
        planStatus: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
        planFileUrl: null,
        executionRecordUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        planNo: [
          { required: true, message: "带班计划编号不能为空", trigger: "blur" },
        ],
        planPeriod: [
          { required: true, message: "计划周期不能为空", trigger: "blur" },
        ],
        planType: [
          {
            required: true,
            message: "计划类型：日常/周计划/月计划/专项带班不能为空",
            trigger: "change",
          },
        ],
        workplace: [
          { required: true, message: "工作地点不能为空", trigger: "blur" },
        ],
        department: [
          { required: true, message: "所属部门/班组不能为空", trigger: "blur" },
        ],
        compiler: [
          { required: true, message: "计划编制人不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询带班计划（记录各时段带班安排、人员及工作内容）列表 */
    getList() {
      this.loading = true;
      listPlan(this.queryParams).then((response) => {
        this.planList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        planNo: null,
        planPeriod: null,
        planType: null,
        workplace: null,
        department: null,
        projectCode: null,
        projectName: null,
        day1Date: null,
        day1Leader: null,
        day1LeaderContact: null,
        day1WorkContent: null,
        day1SafetyFocus: null,
        day2Date: null,
        day2Leader: null,
        day2LeaderContact: null,
        day2WorkContent: null,
        day2SafetyFocus: null,
        day3Date: null,
        day3Leader: null,
        day3LeaderContact: null,
        day3WorkContent: null,
        day3SafetyFocus: null,
        day4Date: null,
        day4Leader: null,
        day4LeaderContact: null,
        day4WorkContent: null,
        day4SafetyFocus: null,
        day5Date: null,
        day5Leader: null,
        day5LeaderContact: null,
        day5WorkContent: null,
        day5SafetyFocus: null,
        day6Date: null,
        day6Leader: null,
        day6LeaderContact: null,
        day6WorkContent: null,
        day6SafetyFocus: null,
        day7Date: null,
        day7Leader: null,
        day7LeaderContact: null,
        day7WorkContent: null,
        day7SafetyFocus: null,
        totalDays: null,
        participantCount: null,
        generalSafetyRequirements: null,
        workSummaryStandard: null,
        planStatus: null,
        compiler: null,
        reviewer: null,
        approver: null,
        compileTime: null,
        approveTime: null,
        planFileUrl: null,
        executionRecordUrl: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加带班计划（记录各时段带班安排、人员及工作内容）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPlan(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改带班计划（记录各时段带班安排、人员及工作内容）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updatePlan(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPlan(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除带班计划（记录各时段带班安排、人员及工作内容）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delPlan(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/plan/export",
        {
          ...this.queryParams,
        },
        `plan_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
