<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="88px">
      <el-form-item label="检查人" prop="creatorName">
        <el-input v-model="queryParams.creatorName" placeholder="请输入检查人姓名" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="整改人" prop="changeName">
        <el-input v-model="queryParams.changeName" placeholder="请输入整改人名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="复查人" prop="reviewName">
        <el-input v-model="queryParams.reviewName" placeholder="请输入复查人姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="核验人" prop="verifyName">
        <el-input v-model="queryParams.verifyName" placeholder="请输入核验人姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="检查结果" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择检查结果" clearable>
          <el-option label="待整改" value="1" />
          <el-option label="待复查" value="2" />
          <el-option label="已合格" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-edit" size="mini" :disabled="multiple"
          @click="handleBatchEdit">批量管理</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="violationList" style="width: 100%" height="calc(100vh - 290px)"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- 检查结果/截止时限 -->
      <el-table-column label="检查结果/截止时限" align="left" width="200">
        <template slot-scope="{ row }">
          <!-- 3 已合格 1 待整改 2待复查 -->
          <div class="font-12">
            <span class="circle" :class="getStatusClass(row.status)" />
            {{
              row.status == "1"
                ? "待整改"
                : row.status == "2"
                  ? "待复查"
                  : "已合格"
            }}
          </div>
          <div class="font-12">
            复查时限:{{
              row.changeLimitTime ? row.changeLimitTime.slice(0, 10) : ""
            }}
          </div>
        </template>
      </el-table-column>
      <!-- 所属单位/项目名称 -->
      <el-table-column label="所属单位/项目名称" align="left" width="300" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="font-12">
            {{ scope.row.ownerOrgStr || '-' }}
          </div>
          <div class="font-12">
            {{ scope.row.projectName || '-' }}
          </div>
        </template>
      </el-table-column>
      <!-- 违章人员 -->
      <el-table-column label="违章人员" align="center" prop="violatorsName" width="120" />
      <!-- 违章行为 -->
      <el-table-column label="违章行为" align="center" prop="dangerItemContent" :show-overflow-tooltip="true"
        min-width="250" />
      <!-- 违章时间 -->
      <el-table-column label="违章时间" align="center" prop="createTime" width="120">
        <template slot-scope="scope">
          {{ scope.row.createTime ? scope.row.createTime.slice(0, 10) : '' }}
        </template>
      </el-table-column>
      <!-- 检查人 -->
      <el-table-column label="检查人" align="center" prop="creatorName" min-width="140" />
      <!-- 整改人 -->
      <el-table-column label="整改人" align="center" prop="changeName" min-width="130" />
      <!-- 复查人 -->
      <el-table-column label="复查人" align="center" prop="reviewName" width="100" />
      <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或处理违章对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="违章标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入违章标题" maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="违章人员" prop="violator">
              <el-select v-model="form.violator" placeholder="请选择违章人员" style="width: 100%" filterable>
                <el-option v-for="item in employeeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="违章时间" prop="foundDate">
              <el-date-picker v-model="form.foundDate" type="date" placeholder="选择违章时间" style="width: 100%"
                value-format="yyyy-MM-dd" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="违章行为" prop="behavior">
              <el-input v-model="form.behavior" type="textarea" :rows="3" placeholder="请输入或从行为标准库选择" maxlength="500"
                show-word-limit />
              <el-button size="mini" type="primary" plain style="margin-top: 5px"
                @click="openBehaviorLibrary">从行为标准库选择</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="行为类别" prop="behaviorType">
              <el-select v-model="form.behaviorType" placeholder="请选择行为类别" style="width: 100%">
                <el-option v-for="item in behaviorTypeOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理情况" prop="status">
              <el-select v-model="form.status" placeholder="请选择处理情况" style="width: 100%">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="附件上传" prop="attachments">
              <file-upload v-model="form.attachments" :limit="10" :file-size="50"
                :file-type="['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']" />
              <div class="upload-tip">支持图片/文档格式，单文件最大50MB</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="记录来源" prop="recordSource">
              <el-input v-model="form.recordSource" placeholder="请输入记录来源，如：手动录入、来自隐患YH20240610023等" />
              <div class="form-tip">
                默认"手动录入"，或显示"来自隐患YH20240610023"
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="recordStatus">
              <el-radio-group v-model="form.recordStatus">
                <el-radio label="有效">有效</el-radio>
                <el-radio label="已纠正">已纠正</el-radio>
              </el-radio-group>
              <div class="form-tip">默认"有效"，可手动修改为"已纠正"</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog title="查看详情" :visible.sync="detailDialog" width="1000px" append-to-body custom-class="detail-dialog">
      <div v-if="detailData" class="detail-content">
        <!-- 问题记录部分 -->
        <div class="independent-section">
          <h3 class="independent-title">违章记录</h3>
          <!-- 左右两列布局 -->
          <div class="record-columns">
            <!-- 左列 -->
            <div class="left-column">
              <div class="field-row highlighted-field">
                <span class="field-label">检查部位:</span>
                <span class="field-value">{{ detailData.regionName || '-' }}</span>
              </div>

              <div class="field-row highlighted-field">
                <span class="field-label">违章人员:</span>
                <span class="field-value">{{ detailData.violatorsName || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">问题描述:</span>
                <span class="field-value">{{ detailData.dangerItemContent || detailData.dangerDesc || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">问题分类:</span>
                <span class="field-value">{{ detailData.dangerTypeFullName || detailData.dangerTypeName || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">补充说明:</span>
                <span class="field-value">{{ detailData.dangerDesc || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">问题等级:</span>
                <span class="field-value">{{ detailData.levelName || detailData.level || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">整改要求:</span>
                <span class="field-value">{{ detailData.rectificationRequirements || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">轴线位置:</span>
                <span class="field-value">{{ detailData.axisPosition || '8号18-17/D' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">分包单位:</span>
                <span class="field-value">{{ detailData.contractorName || '-' }}</span>
              </div>
            </div>

            <!-- 右列 -->
            <div class="right-column">
              <div class="field-row highlighted-field">
                <span class="field-label">检查人:</span>
                <span class="field-value">{{ detailData.creatorName || '-' }}</span>
              </div>

              <div class="field-row highlighted-field">
                <span class="field-label">检查时间:</span>
                <span class="field-value">{{ detailData.createTime || '2025-08-25' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">紧急程度:</span>
                <span class="field-value">{{ detailData.urgencyLevel || '一般' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">检查人:</span>
                <span class="field-value">{{ detailData.creatorName || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">检查时间:</span>
                <span class="field-value">{{ detailData.createTime ? detailData.createTime.slice(0, 10) : '2025-08-25'
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">整改时限:</span>
                <span class="field-value">{{ detailData.changeLimitTime ? detailData.changeLimitTime.slice(0, 10) :
                  '2025-08-27' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">复查人:</span>
                <span class="field-value">{{ detailData.reviewName || '-' }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">复查时间:</span>
                <span class="field-value">{{ detailData.reviewTime ? detailData.reviewTime.slice(0, 10) : '2025-08-30'
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">通知人:</span>
                <span class="field-value">{{ detailData.notifierName || '-' }}</span>
              </div>
            </div>
          </div>

          <!-- 现状整改部分 -->
          <div class="field-row">
            <span class="field-label">现状整改:</span>
            <div class="status-row">
              <label><input type="checkbox" :checked="detailData.status != 1" disabled> 未完成</label>
            </div>
          </div>

          <!-- 相关照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关附件:</span>
            </div>
            <div class="photo-container" v-if="detailData.hiddenDangerPictures">
              <div 
                v-for="(imageUrl, index) in getAllImageUrls(detailData.hiddenDangerPictures)" 
                :key="index" 
                class="photo-item"
              >
                <img :src="imageUrl" alt="问题照片" @click="previewImage(imageUrl)">
              </div>
            </div>
          </div>
        </div>

        <!-- 整改记录部分 -->
        <div v-if="detailData.status != 1" class="independent-section">
          <h3 class="independent-title">整改记录</h3>
          <!-- 整改信息行 -->
          <div class="rectification-info">
            <div class="field-row highlighted-field">
              <span class="field-label">整改状态:</span>
              <span class="field-value">{{ getRectificationStatusText(detailData.status) }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">整改人:</span>
              <span class="field-value">{{ detailData.changeName || '-' }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">整改时间:</span>
              <span class="field-value">{{ detailData.changeTime ? detailData.changeTime.slice(0, 16) : '2025-08-27'
              }}</span>
            </div>
          </div>

          <!-- 整改说明 -->
          <div class="field-row full-width">
            <span class="field-label">整改说明:</span>
            <span class="field-value">{{ detailData.rectificationDesc || '已整改' }}</span>
          </div>

          <!-- 相关照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关附件:</span>
            </div>
            <div class="photo-container" v-if="detailData.rectificationPictures">
              <div 
                v-for="(imageUrl, index) in getAllImageUrls(detailData.rectificationPictures)" 
                :key="index" 
                class="photo-item"
              >
                <img :src="imageUrl" alt="整改照片" @click="previewImage(imageUrl)">
              </div>
            </div>
          </div>
        </div>

        <!-- 复查记录部分 -->
        <div v-if="detailData.status != 1 && detailData.status != 2" class="independent-section">
          <h3 class="independent-title">复查记录</h3>
          <!-- 复查信息行 -->
          <div class="rectification-info">
            <div class="field-row highlighted-field">
              <span class="field-label">复查状态:</span>
              <span class="field-value">{{ getReviewStatusText(detailData.status) }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">复查人:</span>
              <span class="field-value">{{ detailData.reviewName || '-' }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">复查时间:</span>
              <span class="field-value">{{ detailData.reviewTime ? detailData.reviewTime.slice(0, 16) : '2025-08-27'
              }}</span>
            </div>
          </div>

          <!-- 复查说明 -->
          <div class="field-row full-width">
            <span class="field-label">复查说明:</span>
            <span class="field-value">{{ detailData.reviewComments || '-' }}</span>
          </div>

          <!-- 复查照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关附件:</span>
            </div>
            <div class="photo-container" v-if="detailData.reviewPicUrl">
              <div 
                v-for="(imageUrl, index) in getAllImageUrls(detailData.reviewPicUrl)" 
                :key="index" 
                class="photo-item"
              >
                <img :src="imageUrl" alt="复查照片" @click="previewImage(imageUrl)">
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RightToolbar from '@/components/RightToolbar'
import FileUpload from '@/components/FileUpload'
import {
  listZjHazardInvestigation,
  getZjHazardInvestigation,
  delZjHazardInvestigation
} from '@/api/inspection/zjHazardInvestigation'
export default {
  name: 'ViolationManagement',
  components: {
    RightToolbar,
    FileUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 是否为新增
      isAdd: true,
      // 违章管理表格数据
      violationList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 详情弹窗
      detailDialog: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        creatorId: null,
        creatorName: null,
        routineId: null,
        projectName: null,
        projectStatus: null,
        recordId: null,
        regionId: null,
        regionFullId: null,
        regionName: null,
        regionFullName: null,
        dangerTypeId: null,
        relation: null,
        dangerTypeName: null,
        dangerTypeFullName: null,
        autoRecg: null,
        dangerItemContent: null,
        dangerDesc: null,
        changeTime: null,
        changeLimitTime: null,
        level: null,
        levelName: null,
        status: null,
        changeId: null,
        changeName: null,
        participationIds: null,
        participationNames: null,
        reviewName: null,
        verifyName: null,
        isViolators: 1 // 标识为违章记录
      },
      // 表单参数
      form: {},
      // 详情数据
      detailData: {},
      // 员工选项
      employeeOptions: [
        { label: '黄三', value: '黄三' },
        { label: '李四', value: '李四' },
        { label: '王五', value: '王五' }
      ],
      // 行为类别选项
      behaviorTypeOptions: [
        { label: '高处作业类', value: '高处作业类' },
        { label: '电气类', value: '电气类' },
        { label: '消防类', value: '消防类' },
        { label: '机械设备类', value: '机械设备类' }
      ],
      // 处理情况选项
      statusOptions: [
        { label: '警告+安全再教育', value: '警告+安全再教育' },
        { label: '罚款+安全再教育', value: '罚款+安全再教育' },
        { label: '停工整改', value: '停工整改' },
        { label: '待处理', value: '待处理' }
      ],
      // 表单校验
      rules: {
        title: [
          { required: true, message: '违章标题不能为空', trigger: 'blur' }
        ],
        violator: [
          { required: true, message: '违章人员不能为空', trigger: 'change' }
        ],
        foundDate: [
          { required: true, message: '违章时间不能为空', trigger: 'change' }
        ],
        behavior: [
          { required: true, message: '违章行为不能为空', trigger: 'blur' }
        ],
        behaviorType: [
          { required: true, message: '行为类别不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询违章管理列表 */
    getList() {
      this.loading = true
      listZjHazardInvestigation(this.queryParams).then((response) => {
        this.violationList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 获取状态样式类
    getStatusClass(type) {
      const arr = ['bg-blue', 'bg-green', 'bg-orange']
      return arr[type - 1]
    },
    // 获取状态标签类型
    getStatusType(status) {
      const statusMap = {
        '警告+安全再教育': 'warning',
        '罚款+安全再教育': 'danger',
        停工整改: 'danger',
        待处理: 'info'
      }
      return statusMap[status] || 'info'
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        title: undefined,
        violator: undefined,
        foundDate: undefined,
        behavior: undefined,
        behaviorType: undefined,
        status: undefined,
        recordSource: '手动录入',
        recordStatus: '有效',
        attachments: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.isAdd = true
      this.open = true
      this.title = '新增违章记录'
    },
    /** 查看按钮操作 */
    handleView(row) {
      const id = row.id
      getZjHazardInvestigation(id).then((response) => {
        this.detailData = response.data
        this.detailDialog = true
      })
    },
    /** 编辑按钮操作 */
    handleEdit(row) {
      this.reset()
      this.isAdd = false
      this.form = { ...row }
      this.open = true
      this.title = '编辑违章记录'
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除该违章记录?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        delZjHazardInvestigation(row.id).then(() => {
          this.getList()
        })
      })
    },
    /** 批量管理按钮操作 */
    handleBatchEdit() {
      this.$message.info('批量管理功能待实现')
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.$message.success('新增成功')
          } else {
            this.$message.success('修改成功')
          }
          this.open = false
          this.getList()
        }
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$message.info('导出功能待实现')
    },
    /** 打开行为标准库 */
    openBehaviorLibrary() {
      this.$message.info('行为标准库选择功能待实现')
    },
    /** 下载文件 */
    downloadFile(file) {
      this.$message.info(`下载文件: ${file.name}`)
    },
    // 获取整改状态文字
    getRectificationStatusText(status) {
      const statusMap = {
        0: '无需整改',
        1: '待整改',
        2: '已整改',
        3: '已合格',
        4: '不合格',
        7: '待核验'
      }
      return statusMap[status] || '未知状态'
    },
    // 获取复查状态文字
    getReviewStatusText(status) {
      const statusMap = {
        0: '无需复查',
        1: '待复查',
        2: '待复查',
        3: '复查合格',
        4: '复查不合格',
        7: '待核验'
      }
      return statusMap[status] || '未复查'
    },
    // 解析图片URL列表
    parseImageUrls(imageUrl, dataSource) {
      if (!imageUrl) return []
      
      // 当dataSource为2时，处理后端拼接好的URL
      if (dataSource === 2 || dataSource === '2') {
        // 移除开头的@符号，然后按逗号分割
        const urlStr = imageUrl.startsWith('@') ? imageUrl.substring(1) : imageUrl
        return urlStr.split(',').filter(url => url.trim()).map(url => url.trim())
      }
      
      // 默认情况：前端拼接
      if (imageUrl.startsWith('/')) {
        return [`${process.env.VUE_APP_BASE_API}${imageUrl}`]
      }
      return [imageUrl]
    },
    // 获取图片URL（兼容原有逻辑）
    getImageUrl(imageUrl) {
      const urls = this.parseImageUrls(imageUrl, this.detailData?.dataSource)
      return urls.length > 0 ? urls[0] : ''
    },
    // 获取所有图片URL
    getAllImageUrls(imageUrl) {
      return this.parseImageUrls(imageUrl, this.detailData?.dataSource)
    },
    // 预览图片
    previewImage(imageUrl) {
      if (!imageUrl) return
      // 使用element-ui的图片预览功能
      this.$alert(`<img src="${imageUrl}" style="width: 100%; max-width: 500px;" alt="预览图片">`, '图片预览', {
        dangerouslyUseHTMLString: true,
        customClass: 'image-preview-dialog',
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: '关闭'
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.el-table {
  margin-top: 10px;
}

.font-12 {
  font-size: 12px;
  line-height: 20px;
}

.circle {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.bg-blue {
  background-color: #409EFF;
}

.bg-green {
  background-color: #67C23A;
}

.bg-orange {
  background-color: #E6A23C;
}

/* 详情弹窗样式 */
:deep(.detail-dialog) {
  .el-dialog__header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
    padding: 15px 20px;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .el-dialog__body {
    padding: 0;
    max-height: 60vh;
    overflow-y: auto;
    padding: 0px 20px !important;
  }

  ::v-deep(.el-dialog__body) {
    padding: 0px 20px !important;
  }
}

.detail-content {
  padding: 0 20px;

  .independent-section {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .independent-title {
    margin: 0 0 20px 0;
    padding: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 20px;

    &:after {
      content: '';
      flex: 1;
      height: 2px;
      border-top: 2px dashed #409eff;
    }
  }

  /* 左右两列布局 */
  .record-columns {
    display: flex;
    gap: 40px;
  }

  .left-column,
  .right-column {
    flex: 1;
  }

  .field-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    &.full-width {
      width: 100%;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &.highlighted-field {
      background-color: #ebecf0;
      padding: 8px 0;
      margin-bottom: 0;
      margin-left: -20px;
      margin-right: -20px;
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  /* 非高亮字段与高亮字段之间的间距 */
  .field-row:not(.highlighted-field)+.field-row.highlighted-field,
  .field-row.highlighted-field+.field-row:not(.highlighted-field) {
    margin-top: 16px;
  }

  .field-label {
    min-width: 70px;
    font-weight: 400;
    color: #666;
    margin-right: 10px;
    white-space: nowrap;
    font-size: 14px;
    line-height: 1.5;
  }

  .field-value {
    color: #333;
    word-break: break-all;
    line-height: 1.5;
    font-size: 14px;
    flex: 1;

    &.status-tag {
      padding: 2px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  /* 整改记录部分样式 */
  .rectification-info {
    display: flex;
    gap: 0;
    margin-bottom: 16px;
    background-color: #ebecf0;
    margin-left: -20px;
    margin-right: -20px;
    padding: 8px 20px;

    .field-row {
      margin-bottom: 0;
      white-space: nowrap;
      flex: 1;
      margin-left: 0;
      margin-right: 0;
      padding: 0 20px;
      background-color: transparent;

      &:first-child {
        padding-left: 0;
      }

      &:last-child {
        padding-right: 0;
      }
    }
  }

  /* 状态标签颜色 */
  .status-no-need {
    background-color: #f0f9ff;
    color: #0369a1;
    border: 1px solid #7dd3fc;
  }

  .status-pending {
    background-color: transparent;
    color: #d97706;
    border: none;
  }

  .status-rectified {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #4ade80;
  }

  .status-qualified {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #4ade80;
  }

  .status-unqualified {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #f87171;
  }

  .status-verify {
    background-color: #ede9fe;
    color: #7c3aed;
    border: 1px solid #a78bfa;
  }

  .status-unknown {
    background-color: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
  }

  /* 状态行样式 */
  .status-row {
    margin-top: 8px;

    label {
      margin-right: 15px;
      color: #666;
      font-size: 14px;

      input[type="checkbox"] {
        margin-right: 5px;
      }
    }
  }

  /* 照片相关样式 */
  .photo-section {
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
  }

  .photo-container {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .photo-item,
  .photo-placeholder {
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;

    &:hover {
      transform: scale(1.02);
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }

    img {
      width: 120px;
      height: 90px;
      object-fit: cover;
      display: block;
    }
  }

  .photo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border: 1px dashed #d1d5db;

    &:hover {
      transform: none;
      border-color: #d1d5db;
      box-shadow: none;
    }

    img {
      opacity: 0.3;
      background: transparent;
    }
  }

  .no-photo {
    color: #999;
    font-style: italic;
    font-size: 12px;
    margin-top: 8px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .record-columns {
      flex-direction: column;
      gap: 20px;
    }

    .rectification-info {
      flex-direction: column;
      gap: 10px;
    }
  }
}

/* 图片预览弹窗样式 */
:deep(.image-preview-dialog) {
  .el-message-box__content {
    text-align: center;
  }

  .el-message-box__message {
    margin: 0;
  }
}
</style>
