import request from '@/utils/request'

// 查询安全投入年度计划列表
export function listInvestment(query) {
  return request({
    url: '/system/investment/list',
    method: 'get',
    params: query
  })
}

// 查询安全投入年度计划详细
export function getInvestment(id) {
  return request({
    url: '/system/investment/' + id,
    method: 'get'
  })
}

// 新增安全投入年度计划
export function addInvestment(data) {
  return request({
    url: '/system/investment',
    method: 'post',
    data: data
  })
}

// 修改安全投入年度计划
export function updateInvestment(data) {
  return request({
    url: '/system/investment',
    method: 'put',
    data: data
  })
}

// 删除安全投入年度计划
export function delInvestment(id) {
  return request({
    url: '/system/investment/' + id,
    method: 'delete'
  })
}
