<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="活动名称" prop="activityId">
        <el-input
          v-model="queryParams.activityId"
          placeholder="请输入活动名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="时间" prop="uploadTime">
        <el-date-picker
          type="datetimerange"
          clearable
          v-model="queryParams.uploadTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择上传时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjSafetyActivityRecords:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjSafetyActivityRecords:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjSafetyActivityRecords:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjSafetyActivityRecordsList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 250px)"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column label="活动名称" align="center" prop="activityId" />
      <!-- <el-table-column
        label="开展情况描述"
        align="center"
        prop="developmentSituationDes"
      />
      <el-table-column
        label="上传文件url"
        align="center"
        prop="developmentSituationUrl"
      />
      <el-table-column
        label="上传时间"
        align="center"
        prop="uploadTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.uploadTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        label="活动开始时间"
        align="center"
        prop="activityStartTime"
        width="180"
      />
      <el-table-column
        label="活动结束时间"
        align="center"
        prop="activityEndTime"
        width="180"
      />
      <el-table-column
        label="活动内容"
        prop="activityContent"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="负责人"
        align="center"
        prop="activityResponsible"
      />
      <el-table-column
        label="提醒时间"
        align="center"
        prop="reminderTime"
        width="180"
      />

      <!-- <el-table-column label="状态" align="center" prop="status" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">{{
            statusList[scope.row.status]
          }}</el-tag>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="附件" prop="attachment"></el-table-column> -->
      <el-table-column label="状态" prop="status">
        <template slot-scope="scope">
          {{ scope.row.status == 0 ? "正常" : "停用" }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="180"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjSafetyActivityRecords:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            style="color: red"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjSafetyActivityRecords:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改安全活动记录对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="140px"
        :class="isCheck ? 'view-mode' : ''"
      >
        <el-form-item label="活动名称" prop="activityId">
          <el-input
            v-model="form.activityId"
            placeholder="请输入活动名称"
            :disabled="isCheck"
          />
        </el-form-item>
        <!-- <el-form-item label="开展情况描述" prop="developmentSituationDes">
          <el-input
            v-model="form.developmentSituationDes"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="上传文件url" prop="developmentSituationUrl">
          <el-input
            v-model="form.developmentSituationUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item> -->
        <!-- <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker
            clearable
            v-model="form.uploadTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择上传时间"
          >
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="活动开始时间" prop="activityStartTime">
          <el-date-picker
            clearable
            v-model="form.activityStartTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择活动开始时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动结束时间" prop="activityEndTime">
          <el-date-picker
            clearable
            v-model="form.activityEndTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择活动结束时间"
            style="width: 100%"
            :disabled="isCheck"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动内容" prop="activityContent">
          <el-input
            v-model="form.activityContent"
            type="textarea"
            placeholder="请输入活动内容"
            rows="4"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="activityResponsible">
          <el-input
            v-model="form.activityResponsible"
            placeholder="请输入负责人"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="提醒时间" prop="reminderTime">
          <el-date-picker
            clearable
            v-model="form.reminderTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择提醒时间"
            style="width: 100%"
            :disabled="isCheck"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="form.status"
            placeholder="请选择状态"
            style="width: 100%"
            :disabled="isCheck"
          >
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <file-upload
            v-model="form.attachment"
            :disabled="isCheck"
          ></file-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="isCheck"
          >确 定</el-button
        >
        <el-button @click="cancel" :disabled="isCheck">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjSafetyActivityRecords,
  getZjSafetyActivityRecords,
  delZjSafetyActivityRecords,
  addZjSafetyActivityRecords,
  updateZjSafetyActivityRecords,
} from "@/api/inspection/zjSafetyActivityRecords";

export default {
  name: "ZjSafetyActivityRecords",
  data() {
    return {
      isCheck: false,
      statusList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全活动记录表格数据
      zjSafetyActivityRecordsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: null,
        developmentSituationDes: null,
        developmentSituationUrl: null,
        uploadTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        activityId: [
          { required: true, message: "活动名称不能为空", trigger: "blur" },
        ],
        activityStartTime: [
          { required: true, message: "活动开始时间不能为空", trigger: "blur" },
        ],
        activityEndTime: [
          { required: true, message: "活动结束时间不能为空", trigger: "blur" },
        ],
        activityContent: [
          { required: true, message: "活动结束时间不能为空", trigger: "blur" },
        ],
        activityResponsible: [
          { required: true, message: "负责人不能为空", trigger: "blur" },
        ],
        reminderTime: [
          { required: true, message: "提醒时间不能为空", trigger: "change" },
        ],
        status: [
          { required: true, message: "负责人不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询安全活动记录列表 */
    getList() {
      this.loading = true;
      listZjSafetyActivityRecords(this.queryParams).then((response) => {
        this.zjSafetyActivityRecordsList = response.rows;
        console.log("获取对象", this.zjSafetyActivityRecordsList);
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        activityId: null,
        developmentSituationDes: null,
        developmentSituationUrl: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        uploadTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");

      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.isCheck = false;
      this.title = "添加安全活动记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjSafetyActivityRecords(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = false;
        this.title = "修改安全活动记录";
      });
    },
    handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjSafetyActivityRecords(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.isCheck = true;
        this.title = "查看安全活动记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjSafetyActivityRecords(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjSafetyActivityRecords(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除安全活动记录编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjSafetyActivityRecords(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/safeActivityInfo/export",
        {
          ...this.queryParams,
        },
        `安全活动管理.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;

    &:hover {
      text-decoration-line: underline;
    }
  }
}
</style>