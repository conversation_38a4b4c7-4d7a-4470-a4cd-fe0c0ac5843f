<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="标题名称" prop="titleName">
        <el-input
          v-model="queryParams.titleName"
          placeholder="请输入标题名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="法规编号" prop="regulationNumber">
        <el-input
          v-model="queryParams.regulationNumber"
          placeholder="请输入法规编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布部门" prop="publicationDepartment">
        <el-input
          v-model="queryParams.publicationDepartment"
          placeholder="请输入发布部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjLegalRegulationsStandards:add']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjLegalRegulationsStandards:edit']"
          >修改</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjLegalRegulationsStandards:remove']"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjLegalRegulationsStandards:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjLegalRegulationsStandardsList"
      @selection-change="handleSelectionChange"
      height="calc(100vh - 280px)"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="标题名称" align="center" prop="titleName" />
      <el-table-column
        label="法规编号"
        align="center"
        prop="regulationNumber"
      />
      <!-- <el-table-column label="类型" align="center" prop="regulationType" /> -->
      <el-table-column
        label="发布部门"
        align="center"
        prop="publicationDepartment"
      />
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishedTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishedTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="状态" align="center" prop="regulationStatus" /> -->
      <el-table-column label="附件" align="center" prop="attachmentUrl">
        <template slot-scope="scope">
          <div class="contract-file" v-if="scope.row.attachmentUrl">
            <div
              v-for="(item, index) in scope.row.attachmentUrl.split(',')"
              :key="index"
              @click="handleAttach(item)"
              class="attachment-item"
            >
              {{ item.split("/").pop() }}
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjLegalRegulationsStandards:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjLegalRegulationsStandards:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题名称" prop="titleName">
          <el-input v-model="form.titleName" placeholder="请输入标题名称" />
        </el-form-item>
        <el-form-item label="法规编号" prop="regulationNumber">
          <el-input
            v-model="form.regulationNumber"
            placeholder="请输入法规编号"
          />
        </el-form-item>
        <el-form-item label="发布部门" prop="publicationDepartment">
          <el-input
            v-model="form.publicationDepartment"
            placeholder="请输入发布部门"
          />
        </el-form-item>
        <el-form-item label="发布时间" prop="publishedTime">
          <el-date-picker
            clearable
            v-model="form.publishedTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择发布时间"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="附件" prop="attachmentUrl">
          <file-upload
            v-model="form.attachmentUrl"
            :fileType="['pdf', 'docx', 'png', 'jpg', 'doc']"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjLegalRegulationsStandards,
  getZjLegalRegulationsStandards,
  delZjLegalRegulationsStandards,
  addZjLegalRegulationsStandards,
  updateZjLegalRegulationsStandards,
} from "@/api/inspection/zjLegalRegulationsStandards";

export default {
  name: "ZjLegalRegulationsStandards",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 法律法规与标准表格数据
      zjLegalRegulationsStandardsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        titleName: null,
        regulationNumber: null,
        regulationType: null,
        publicationDepartment: null,
        publishedTime: null,
        regulationStatus: null,
        attachmentUrl: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        titleName: [
          { required: true, message: "请输入标题名称", trigger: "blur" },
        ],
        regulationNumber: [
          { required: true, message: "请输入法规编号", trigger: "blur" },
        ],
        publicationDepartment: [
          { required: true, message: "请输入发布部门", trigger: "blur" },
        ],
        publishedTime: [
          { required: true, message: "请输入发布时间", trigger: "blur" },
        ],

        attachmentUrl: [
          {
            required: true,
            message: "请上传附件",
            trigger: "change",
          },
        ],
      },
      baseUrl: process.env.VUE_APP_BASE_API,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询法律法规与标准列表 */
    getList() {
      this.loading = true;
      listZjLegalRegulationsStandards(this.queryParams).then((response) => {
        this.zjLegalRegulationsStandardsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        titleName: null,
        regulationNumber: null,
        regulationType: null,
        publicationDepartment: null,
        publishedTime: null,
        regulationStatus: null,
        attachmentUrl: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业法律法规与标准";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjLegalRegulationsStandards(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业法律法规与标准";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjLegalRegulationsStandards(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjLegalRegulationsStandards(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除法律法规与标准编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjLegalRegulationsStandards(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjLegalRegulationsStandards/export",
        {
          ...this.queryParams,
        },
        `zjLegalRegulationsStandards_${new Date().getTime()}.xlsx`
      );
    },
    async handleAttach(value) {
      try {
        //获取文件完整URL
        const fileUrl = this.baseUrl + value;
        console.log("uuuuuuu", fileUrl);
        // 如果是TXT文件，使用fetch获取并处理编码
        if (fileUrl.toLowerCase().endsWith(".txt")) {
          const res = await fetch(fileUrl);
          const buffer = await res.arrayBuffer();

          // 尝试用常见中文编码解码
          const decoder = new TextDecoder("utf-8");
          const text = decoder.decode(buffer);

          // 在弹窗中显示文本内容
          this.$alert(text, "文件内容", {
            customClass: "txt-preview-dialog",
            showConfirmButton: false,
            closeOnClickModal: true,
          });
        } else {
          // 其他文件类型直接打开
          window.open(fileUrl);
        }
      } catch (error) {
        this.$message.error("文件打开失败: " + error.message);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.contract-file {
  .attachment-item {
    cursor: pointer;
    color: #409eff;
    &:hover {
      text-decoration-line: underline;
    }
  }
}

</style>