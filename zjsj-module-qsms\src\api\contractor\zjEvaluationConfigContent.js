import request from '@/utils/request'

// 查询评价配置内容列表
export function listZjEvaluationConfigContent(query) {
  return request({
    url: '/contractor/zjEvaluationConfigContent/list',
    method: 'get',
    params: query
  })
}

// 查询评价配置内容详细
export function getZjEvaluationConfigContent(id) {
  return request({
    url: '/contractor/zjEvaluationConfigContent/' + id,
    method: 'get'
  })
}

// 新增评价配置内容
export function addZjEvaluationConfigContent(data) {
  return request({
    url: '/contractor/zjEvaluationConfigContent',
    method: 'post',
    data: data
  })
}

// 修改评价配置内容
export function updateZjEvaluationConfigContent(data) {
  return request({
    url: '/contractor/zjEvaluationConfigContent',
    method: 'put',
    data: data
  })
}

// 删除评价配置内容
export function delZjEvaluationConfigContent(id) {
  return request({
    url: '/contractor/zjEvaluationConfigContent/' + id,
    method: 'delete'
  })
}
