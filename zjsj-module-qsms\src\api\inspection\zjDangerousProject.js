import request from '@/utils/request'

// 查询危大工程列表
export function listZjDangerousProject(query) {
  return request({
    url: '/inspection/zjDangerousProject/list',
    method: 'get',
    params: query
  })
}

// 查询危大工程详细
export function getZjDangerousProject(id) {
  return request({
    url: '/inspection/zjDangerousProject/' + id,
    method: 'get'
  })
}

// 新增危大工程
export function addZjDangerousProject(data) {
  return request({
    url: '/inspection/zjDangerousProject',
    method: 'post',
    data: data
  })
}

// 修改危大工程
export function updateZjDangerousProject(data) {
  return request({
    url: '/inspection/zjDangerousProject',
    method: 'put',
    data: data
  })
}

// 删除危大工程
export function delZjDangerousProject(id) {
  return request({
    url: '/inspection/zjDangerousProject/' + id,
    method: 'delete'
  })
}
