<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="施工单位" prop="constructionUnit">
        <el-input
          v-model="queryParams.constructionUnit"
          placeholder="请输入施工单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="开工日期" prop="startDate">
        <el-date-picker
          clearable
          v-model="queryParams.startDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择开工日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划竣工日期" prop="plannedCompletionDate">
        <el-date-picker
          clearable
          v-model="queryParams.plannedCompletionDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择计划竣工日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="实际竣工日期" prop="actualCompletionDate">
        <el-date-picker
          clearable
          v-model="queryParams.actualCompletionDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择实际竣工日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="项目规模" prop="projectScale">
        <el-input
          v-model="queryParams.projectScale"
          placeholder="请输入项目规模"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同金额" prop="contractAmount">
        <el-input
          v-model="queryParams.contractAmount"
          placeholder="请输入合同金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目经理" prop="projectManager">
        <el-input
          v-model="queryParams.projectManager"
          placeholder="请输入项目经理"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目经理联系方式" prop="pmContact">
        <el-input
          v-model="queryParams.pmContact"
          placeholder="请输入项目经理联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全员" prop="safetySupervisor">
        <el-input
          v-model="queryParams.safetySupervisor"
          placeholder="请输入安全员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监理单位" prop="supervisionUnit">
        <el-input
          v-model="queryParams.supervisionUnit"
          placeholder="请输入监理单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建设单位" prop="ownerUnit">
        <el-input
          v-model="queryParams.ownerUnit"
          placeholder="请输入建设单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工程进度" prop="progressPercentage">
        <el-input
          v-model="queryParams.progressPercentage"
          placeholder="请输入工程进度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="延期天数" prop="delayDays">
        <el-input
          v-model="queryParams.delayDays"
          placeholder="请输入延期天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="施工班组数量" prop="constructionTeamCount">
        <el-input
          v-model="queryParams.constructionTeamCount"
          placeholder="请输入施工班组数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="当前在场人数" prop="currentWorkers">
        <el-input
          v-model="queryParams.currentWorkers"
          placeholder="请输入当前在场人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="累计隐患数量" prop="hiddenDangerCount">
        <el-input
          v-model="queryParams.hiddenDangerCount"
          placeholder="请输入累计隐患数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隐患整改率" prop="rectifiedRate">
        <el-input
          v-model="queryParams.rectifiedRate"
          placeholder="请输入隐患整改率"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="累计事故次数" prop="accidentCount">
        <el-input
          v-model="queryParams.accidentCount"
          placeholder="请输入累计事故次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="安全培训次数" prop="safetyTrainingTimes">
        <el-input
          v-model="queryParams.safetyTrainingTimes"
          placeholder="请输入安全培训次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统计周期" prop="statPeriod">
        <el-input
          v-model="queryParams.statPeriod"
          placeholder="请输入统计周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否重点项目：1-是 0-否" prop="isKeyProject">
        <el-input
          v-model="queryParams.isKeyProject"
          placeholder="请输入是否重点项目：1-是 0-否"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:projects:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:projects:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:projects:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:projects:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="projectsList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="记录ID" align="center" prop="id" /> -->
      <el-table-column label="项目编码" align="center" prop="projectCode" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="项目类型" align="center" prop="projectType" />
      <!-- 国家级/省级/市级/企业级 -->
      <el-table-column label="项目级别" align="center" prop="projectLevel" />
      <el-table-column
        label="施工单位"
        align="center"
        prop="constructionUnit"
      />
      <el-table-column
        label="施工地址"
        align="center"
        prop="constructionAddress"
      />
      <el-table-column
        label="开工日期"
        align="center"
        prop="startDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="计划竣工日期"
        align="center"
        prop="plannedCompletionDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.plannedCompletionDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="实际竣工日期"
        align="center"
        prop="actualCompletionDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.actualCompletionDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目规模" align="center" prop="projectScale" />
      <el-table-column label="合同金额" align="center" prop="contractAmount" />
      <!-- <el-table-column label="项目经理" align="center" prop="projectManager" />
      <el-table-column
        label="项目经理联系方式"
        align="center"
        prop="pmContact"
      />
      <el-table-column label="安全员" align="center" prop="safetySupervisor" />
      <el-table-column label="监理单位" align="center" prop="supervisionUnit" />
      <el-table-column label="建设单位" align="center" prop="ownerUnit" />
      <el-table-column
        label="项目状态：筹备中/在建/停工/已竣工/已交付"
        align="center"
        prop="projectStatus"
      />
      <el-table-column
        label="工程进度"
        align="center"
        prop="progressPercentage"
      />
      <el-table-column label="延期天数" align="center" prop="delayDays" />
      <el-table-column
        label="施工班组数量"
        align="center"
        prop="constructionTeamCount"
      />
      <el-table-column
        label="当前在场人数"
        align="center"
        prop="currentWorkers"
      />
      <el-table-column label="安全评级" align="center" prop="safetyRating" />
      <el-table-column
        label="累计隐患数量"
        align="center"
        prop="hiddenDangerCount"
      />
      <el-table-column label="隐患整改率" align="center" prop="rectifiedRate" />
      <el-table-column
        label="累计事故次数"
        align="center"
        prop="accidentCount"
      />
      <el-table-column
        label="安全培训次数"
        align="center"
        prop="safetyTrainingTimes"
      />
      <el-table-column
        label="项目概况文档地址"
        align="center"
        prop="projectOverviewUrl"
      />
      <el-table-column
        label="进度报表地址"
        align="center"
        prop="progressReportUrl"
      />
      <el-table-column
        label="安全检查记录地址"
        align="center"
        prop="safetyCheckRecordUrl"
      />
      <el-table-column label="统计周期" align="center" prop="statPeriod" />
      <el-table-column
        label="是否重点项目：1-是 0-否"
        align="center"
        prop="isKeyProject"
      />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column
        label="操作"
        fixed="right"
        width="150"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:projects:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:projects:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改在建项目一览（汇总展示在建项目基本信息与状态）对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="施工单位" prop="constructionUnit">
          <el-input
            v-model="form.constructionUnit"
            placeholder="请输入施工单位"
          />
        </el-form-item>
        <el-form-item label="施工地址" prop="constructionAddress">
          <el-input
            v-model="form.constructionAddress"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="开工日期" prop="startDate">
          <el-date-picker
            clearable
            v-model="form.startDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开工日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划竣工日期" prop="plannedCompletionDate">
          <el-date-picker
            clearable
            v-model="form.plannedCompletionDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择计划竣工日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际竣工日期" prop="actualCompletionDate">
          <el-date-picker
            clearable
            v-model="form.actualCompletionDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择实际竣工日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目规模" prop="projectScale">
          <el-input v-model="form.projectScale" placeholder="请输入项目规模" />
        </el-form-item>
        <el-form-item label="合同金额" prop="contractAmount">
          <el-input
            v-model="form.contractAmount"
            placeholder="请输入合同金额"
          />
        </el-form-item>
        <el-form-item label="项目经理" prop="projectManager">
          <el-input
            v-model="form.projectManager"
            placeholder="请输入项目经理"
          />
        </el-form-item>
        <el-form-item label="项目经理联系方式" prop="pmContact">
          <el-input
            v-model="form.pmContact"
            placeholder="请输入项目经理联系方式"
          />
        </el-form-item>
        <el-form-item label="安全员" prop="safetySupervisor">
          <el-input
            v-model="form.safetySupervisor"
            placeholder="请输入安全员"
          />
        </el-form-item>
        <el-form-item label="监理单位" prop="supervisionUnit">
          <el-input
            v-model="form.supervisionUnit"
            placeholder="请输入监理单位"
          />
        </el-form-item>
        <el-form-item label="建设单位" prop="ownerUnit">
          <el-input v-model="form.ownerUnit" placeholder="请输入建设单位" />
        </el-form-item>
        <el-form-item label="工程进度" prop="progressPercentage">
          <el-input
            v-model="form.progressPercentage"
            placeholder="请输入工程进度"
          />
        </el-form-item>
        <el-form-item label="延期天数" prop="delayDays">
          <el-input v-model="form.delayDays" placeholder="请输入延期天数" />
        </el-form-item>
        <el-form-item label="施工班组数量" prop="constructionTeamCount">
          <el-input
            v-model="form.constructionTeamCount"
            placeholder="请输入施工班组数量"
          />
        </el-form-item>
        <el-form-item label="当前在场人数" prop="currentWorkers">
          <el-input
            v-model="form.currentWorkers"
            placeholder="请输入当前在场人数"
          />
        </el-form-item>
        <el-form-item label="累计隐患数量" prop="hiddenDangerCount">
          <el-input
            v-model="form.hiddenDangerCount"
            placeholder="请输入累计隐患数量"
          />
        </el-form-item>
        <el-form-item label="隐患整改率" prop="rectifiedRate">
          <el-input
            v-model="form.rectifiedRate"
            placeholder="请输入隐患整改率"
          />
        </el-form-item>
        <el-form-item label="累计事故次数" prop="accidentCount">
          <el-input
            v-model="form.accidentCount"
            placeholder="请输入累计事故次数"
          />
        </el-form-item>
        <el-form-item label="安全培训次数" prop="safetyTrainingTimes">
          <el-input
            v-model="form.safetyTrainingTimes"
            placeholder="请输入安全培训次数"
          />
        </el-form-item>
        <el-form-item label="项目概况文档地址" prop="projectOverviewUrl">
          <el-input
            v-model="form.projectOverviewUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="进度报表地址" prop="progressReportUrl">
          <el-input
            v-model="form.progressReportUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="安全检查记录地址" prop="safetyCheckRecordUrl">
          <el-input
            v-model="form.safetyCheckRecordUrl"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="统计周期" prop="statPeriod">
          <el-input v-model="form.statPeriod" placeholder="请输入统计周期" />
        </el-form-item>
        <el-form-item label="是否重点项目：1-是 0-否" prop="isKeyProject">
          <el-input
            v-model="form.isKeyProject"
            placeholder="请输入是否重点项目：1-是 0-否"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProjects,
  getProjects,
  delProjects,
  addProjects,
  updateProjects,
} from "@/api/system/ongoingProjects/index";

export default {
  name: "Projects",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 在建项目一览（汇总展示在建项目基本信息与状态）表格数据
      projectsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: null,
        projectName: null,
        projectType: null,
        projectLevel: null,
        constructionUnit: null,
        constructionAddress: null,
        startDate: null,
        plannedCompletionDate: null,
        actualCompletionDate: null,
        projectScale: null,
        contractAmount: null,
        projectManager: null,
        pmContact: null,
        safetySupervisor: null,
        supervisionUnit: null,
        ownerUnit: null,
        projectStatus: null,
        progressPercentage: null,
        delayDays: null,
        constructionTeamCount: null,
        currentWorkers: null,
        safetyRating: null,
        hiddenDangerCount: null,
        rectifiedRate: null,
        accidentCount: null,
        safetyTrainingTimes: null,
        projectOverviewUrl: null,
        progressReportUrl: null,
        safetyCheckRecordUrl: null,
        statPeriod: null,
        isKeyProject: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectCode: [
          { required: true, message: "项目编码不能为空", trigger: "blur" },
        ],
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        projectType: [
          { required: true, message: "项目类型不能为空", trigger: "change" },
        ],
        constructionUnit: [
          { required: true, message: "施工单位不能为空", trigger: "blur" },
        ],
        constructionAddress: [
          { required: true, message: "施工地址不能为空", trigger: "blur" },
        ],
        startDate: [
          { required: true, message: "开工日期不能为空", trigger: "blur" },
        ],
        plannedCompletionDate: [
          { required: true, message: "计划竣工日期不能为空", trigger: "blur" },
        ],
        projectManager: [
          { required: true, message: "项目经理不能为空", trigger: "blur" },
        ],
        pmContact: [
          {
            required: true,
            message: "项目经理联系方式不能为空",
            trigger: "blur",
          },
        ],
        safetySupervisor: [
          { required: true, message: "安全员不能为空", trigger: "blur" },
        ],
        ownerUnit: [
          { required: true, message: "建设单位不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询在建项目一览（汇总展示在建项目基本信息与状态）列表 */
    getList() {
      this.loading = true;
      listProjects(this.queryParams).then((response) => {
        this.projectsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectCode: null,
        projectName: null,
        projectType: null,
        projectLevel: null,
        constructionUnit: null,
        constructionAddress: null,
        startDate: null,
        plannedCompletionDate: null,
        actualCompletionDate: null,
        projectScale: null,
        contractAmount: null,
        projectManager: null,
        pmContact: null,
        safetySupervisor: null,
        supervisionUnit: null,
        ownerUnit: null,
        projectStatus: null,
        progressPercentage: null,
        delayDays: null,
        constructionTeamCount: null,
        currentWorkers: null,
        safetyRating: null,
        hiddenDangerCount: null,
        rectifiedRate: null,
        accidentCount: null,
        safetyTrainingTimes: null,
        projectOverviewUrl: null,
        progressReportUrl: null,
        safetyCheckRecordUrl: null,
        statPeriod: null,
        isKeyProject: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加在建项目一览（汇总展示在建项目基本信息与状态）";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getProjects(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改在建项目一览（汇总展示在建项目基本信息与状态）";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateProjects(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProjects(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除在建项目一览（汇总展示在建项目基本信息与状态）编号为"' +
            ids +
            '"的数据项？'
        )
        .then(function () {
          return delProjects(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/projects/export",
        {
          ...this.queryParams,
        },
        `projects_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
