<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" :xs="24">
        <el-card body-style="padding: 12px">
          <div class="top">
            <div class="top-left">整改分析</div>
            <div class="top-right">
              <el-date-picker
                style="margin-right: 20px"
                v-model="timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
              <el-select
                v-model="echartType1"
                placeholder="请选择"
                style="width: 100px"
              >
                <el-option
                  v-for="item in echartTypeList1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-button type="primary" icon="el-icon-download" size="small"
                >搜索</el-button
              >
              <el-button
                type="primary"
                icon="el-icon-download"
                size="small"
                @click="downloadSVG"
                >导出为SVG</el-button
              >
              <el-button
                type="primary"
                icon="el-icon-download"
                size="small"
                @click="downloadPNG"
                >导出为PNG</el-button
              >
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-card class="box-card">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane
              :label="tabItem.label"
              :name="tabItem.name"
              v-for="(tabItem, index) in tabsList"
              :key="index"
            >
              <div>
                <el-form
                  :model="queryParams"
                  ref="queryForm"
                  size="small"
                  :inline="true"
                >
                  <el-form-item label="问题等级" prop="questionlevel">
                    <el-select
                      v-model="queryParams.questionlevel"
                      multiple
                      collapse-tags
                      style="margin-left: 20px"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="selectItem in questionLevelList"
                        :key="selectItem.value"
                        :label="selectItem.label"
                        :value="selectItem.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="规范大类" prop="questionlevel">
                    <el-select
                      v-model="queryParams.questionlevel"
                      multiple
                      collapse-tags
                      style="margin-left: 20px"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="selectItem in questionLevelList"
                        :key="selectItem.value"
                        :label="selectItem.label"
                        :value="selectItem.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="用户名称" prop="questionlevel">
                    <el-select
                      v-model="queryParams.questionlevel"
                      multiple
                      collapse-tags
                      style="margin-left: 20px"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="selectItem in questionLevelList"
                        :key="selectItem.value"
                        :label="selectItem.label"
                        :value="selectItem.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="项目名称" prop="questionlevel">
                    <el-select
                      v-model="queryParams.questionlevel"
                      multiple
                      collapse-tags
                      style="margin-left: 20px"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="selectItem in questionLevelList"
                        :key="selectItem.value"
                        :label="selectItem.label"
                        :value="selectItem.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="检查类型" prop="questionlevel">
                    <el-select
                      v-model="queryParams.questionlevel"
                      multiple
                      collapse-tags
                      style="margin-left: 20px"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="selectItem in questionLevelList"
                        :key="selectItem.value"
                        :label="selectItem.label"
                        :value="selectItem.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handleQuery"
                      >搜索</el-button
                    >
                    <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetQuery"
                      >重置</el-button
                    >
                  </el-form-item>
                </el-form>
                <!-- 柱状图 -->
                <div
                  v-if="echartType1 == 3"
                  style="
                    width: 100%;
                    height: calc(100vh - 300px);
                    overflow-x: auto;
                  "
                >
                  <div
                    style="
                      width: 100%;
                      height: 20%;
                      display: flex;
                      flex-wrap: wrap;
                      overflow-y: auto;
                    "
                  >
                    <div
                      v-for="(item, index) in monthList"
                      :key="index"
                      style="
                        height: 48px;
                        padding: 6px 0px 6px 12px;
                        margin-right: 20px;
                        margin-bottom: 12px;
                        border-radius: 4px;
                        background-color: rgba(0, 0, 0, 0.02);
                        display: flex;
                        align-items: center;
                      "
                    >
                      <span style="margin-right: 10px">{{ item.label }}:</span>
                      <el-date-picker
                        type="daterange"
                        v-model="item.time"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                      >
                      </el-date-picker>
                      <el-button
                        type="text"
                        icon="el-icon-close"
                        circle
                        style="font-size: 18px"
                      ></el-button>
                    </div>
                    <div style="height: 36px; margin-top: 6px">
                      <!-- <span style="margin-right: 10px;">月</span>
                                            <el-date-picker type="month" placeholder="选择月">
                                            </el-date-picker> -->
                      <el-button
                        class="hide_input"
                        icon="el-icon-plus"
                        type="primary"
                      >
                        添加时段
                        <el-date-picker
                          type="month"
                          v-model="month"
                          placeholder="选择月"
                          @change="dateChange"
                        >
                        </el-date-picker>
                      </el-button>
                    </div>
                  </div>
                  <div
                    style="
                      width: 100%;
                      height: 80%;
                      display: flex;
                      align-items: center;
                    "
                  >
                    <barChart
                      ref="chart"
                      height="94%"
                      :data="barData"
                      @barClick="handleBarClick"
                    />
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 柱状图 -->
    <el-dialog
      title="柱状图"
      :visible.sync="showBarPop"
      width="80%"
      style="margin-top: 5vh"
    >
      <div style="width: 100%; height: 66vh"></div>
    </el-dialog>
  </div>
</template>

<script>
import pieChart from "@/views/components/pieChart.vue";
import lineChart from "@/views/components/lineChart.vue";
import barChart from "@/views/components/barChart.vue";
export default {
  components: {
    pieChart,
    lineChart,
    barChart,
  },
  data() {
    return {
      monthList: [
        {
          label: "5月",
          time: ["2021-05-01", "2021-05-31"],
        },
        {
          label: "6月",
          time: ["2021-06-01", "2021-06-30"],
        },
        {
          label: "7月",
          time: ["2021-07-01", "2021-07-31"],
        },
        {
          label: "8月",
          time: ["2021-08-01", "2021-08-31"],
        },
      ],
      // 增加时段月份
      month: "",
      // 分类数据 图表类型
      echartType1: 3,
      echartTypeList1: [
        {
          value: 3,
          label: "柱状图",
        },
      ],
      // 显示柱状图弹窗
      showBarPop: false,
      // 折线图图例
      chart2Lengend: [
        {
          color: "#54C255",
          label: "一般",
        },
        {
          color: "#FF920D",
          label: "较大",
        },
        {
          color: "#E54545",
          label: "重大",
        },
      ],
      // 柱状图数据
      barData: {
        colorList: [
          "#3C80E8",
          "#8EE98F",
          "#A1FFEB",
          "#54C255",
          "#A1CDFF",
          "#FF920D",
          "#FECF77",
          "#F3B2B1",
          "#B38DFF",
        ],
        chart2Lengend: [
          {
            color: "#54C255",
            label: "一般",
          },
          {
            color: "#FF920D",
            label: "较大",
          },
          {
            color: "#E54545",
            label: "重大",
          },
        ],
        grid: {
          top: 10,
          left: "3%",
          right: "3%",
          bottom: "10%",
        },
        xAxis: {
          type: "value",
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: "category",
          data: ["6月", "7月", "8月", "9月", "10月"],
          axisLabel: {
            interval: 0,
          },
        },
        series: [
          {
            name: "一般",
            type: "bar",
            stack: "total",
            data: [
              {
                value: 310,
                name: "6月",
              },
              {
                value: 335,
                name: "7月",
              },
              {
                value: 234,
                name: "8月",
              },
              {
                value: 135,
                name: "9月",
              },
              {
                value: 148,
                name: "10月",
              },
            ],
            label: {
              show: true,
              formatter: (params) =>
                Math.round(params.value * 100) / 1000 + "%",
            },
            itemStyle: {
              color: "#54C255",
            },
          },
          {
            name: "较大",
            type: "bar",
            stack: "total",
            data: [
              {
                value: 210,
                name: "6月",
              },
              {
                value: 235,
                name: "7月",
              },
              {
                value: 334,
                name: "8月",
              },
              {
                value: 235,
                name: "9月",
              },
              {
                value: 108,
                name: "10月",
              },
            ],
            label: {
              show: true,
              formatter: (params) =>
                Math.round(params.value * 100) / 1000 + "%",
            },
            itemStyle: {
              color: "#FF920D",
            },
          },
          {
            name: "重大",
            type: "bar",
            stack: "total",
            data: [
              {
                value: 206,
                name: "6月",
              },
              {
                value: 205,
                name: "7月",
              },
              {
                value: 304,
                name: "8月",
              },
              {
                value: 205,
                name: "9月",
              },
              {
                value: 104,
                name: "10月",
              },
            ],
            label: {
              show: true,
              formatter: (params) =>
                Math.round(params.value * 100) / 1000 + "%",
            },
            itemStyle: {
              color: "#E54545",
            },
          },
        ],
      },
      timeRange: [],
      activeName: "1",
      activeName2: "1",
      tabsList: [
        { name: "1", label: "问题等级", prop: "questionlevel" },
        { name: "2", label: "大类", prop: "bigclass" },
        { name: "3", label: "小类", prop: "smallclass" },
        { name: "4", label: "用户", prop: "user" },
        { name: "5", label: "检查角色", prop: "checkrole" },
        { name: "6", label: "所属园区", prop: "park" },
        { name: "7", label: "检查类型", prop: "checktype" },
        { name: "8", label: "项目名称", prop: "projectname" },
        { name: "9", label: "工程类型", prop: "projecttype" },
        { name: "10", label: "按月分类", prop: "month" },
        { name: "11", label: "按年分类", prop: "year" },
      ],
      tabsList2: [
        { name: "1", label: "问题等级", prop: "questionlevel" },
        { name: "2", label: "大类", prop: "bigclass" },
        { name: "3", label: "小类", prop: "smallclass" },
        { name: "4", label: "用户", prop: "user" },
        { name: "5", label: "检查角色", prop: "checkrole" },
        { name: "6", label: "所属园区", prop: "park" },
        { name: "7", label: "检查类型", prop: "checktype" },
        { name: "8", label: "项目名称", prop: "projectname" },
        { name: "9", label: "工程类型", prop: "projecttype" },
        { name: "10", label: "按月分类", prop: "month" },
        { name: "11", label: "按年分类", prop: "year" },
      ],
      queryParams: {
        questionlevel: [],
      },
      questionLevelList: [
        {
          value: 1,
          label: "一般",
        },
        {
          value: 2,
          label: "较大",
        },
        {
          value: 3,
          label: "重大",
        },
      ],
    };
  },
  created() {},
  methods: {
    downloadSVG() {
      this.$refs.chart[0].downloadImgSVG();
    },
    downloadPNG() {
      console.log(this.$refs.chart[0]);
      this.$refs.chart[0].downloadImgPNG();
    },
    dateChange(date) {
      console.log(date); //选择的时间
    },
    // 柱状图点击事件
    handleBarClick(params) {
      console.log(params);
      this.showBarPop = true;
    },
    handleClick(tab, event) {
      console.log(tab.name);
      // 初始化表格
    },
    handleQuery() {},
    resetQuery() {},
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  width: 100%;
  height: calc(100vh - 100px);
}

.hide_input {
  position: relative !important;
}
//修改控件自带的css
.hide_input .el-date-editor {
  position: absolute; //绝对定位
  top: 0;
  left: 0;
  opacity: 0; //设置完全透明
}

::v-deep .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}

::v-deep .el-dialog__body {
  padding: 10px 20px 30px 20px !important;
}

.el-select {
  margin-left: 0px !important;
  margin-right: 10px;
}

.top {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .top-left {
    font-weight: bold;
  }

  .top-right {
    display: flex;
  }
}

.el-row {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.box-card {
  position: relative;
  height: calc(100vh - 162px);
  font-size: 14px;
}

.lengend-item {
  display: flex;
  align-items: center;

  .lengend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .lengend-name {
    font-size: 14px;
    line-height: 24px;
    color: #333333;
    margin-left: 8px;
    margin-right: 18px;
  }
}
</style>
