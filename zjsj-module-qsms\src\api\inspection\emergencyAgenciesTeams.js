import request from '@/utils/request'

// 查询应急机构与队伍列表
export function listEmergencyAgenciesTeams(query) {
  return request({
    url: '/inspection/emergencyAgenciesTeams/list',
    method: 'get',
    params: query
  })
}

// 查询应急机构与队伍详细
export function getEmergencyAgenciesTeams(id) {
  return request({
    url: '/inspection/emergencyAgenciesTeams/' + id,
    method: 'get'
  })
}

// 新增应急机构与队伍
export function addEmergencyAgenciesTeams(data) {
  return request({
    url: '/inspection/emergencyAgenciesTeams',
    method: 'post',
    data: data
  })
}

// 修改应急机构与队伍
export function updateEmergencyAgenciesTeams(data) {
  return request({
    url: '/inspection/emergencyAgenciesTeams',
    method: 'put',
    data: data
  })
}

// 删除应急机构与队伍
export function delEmergencyAgenciesTeams(id) {
  return request({
    url: '/inspection/emergencyAgenciesTeams/' + id,
    method: 'delete'
  })
}
