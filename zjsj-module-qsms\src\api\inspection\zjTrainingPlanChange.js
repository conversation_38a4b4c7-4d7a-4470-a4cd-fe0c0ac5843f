import request from '@/utils/request'

// 查询培训计划变更列表
export function listZjTrainingPlanChange(query) {
  return request({
    url: '/inspection/zjTrainingPlanChange/list',
    method: 'get',
    params: query
  })
}

// 查询培训计划变更详细
export function getZjTrainingPlanChange(id) {
  return request({
    url: '/inspection/zjTrainingPlanChange/' + id,
    method: 'get'
  })
}

// 新增培训计划变更
export function addZjTrainingPlanChange(data) {
  return request({
    url: '/inspection/zjTrainingPlanChange',
    method: 'post',
    data: data
  })
}

// 修改培训计划变更
export function updateZjTrainingPlanChange(data) {
  return request({
    url: '/inspection/zjTrainingPlanChange',
    method: 'put',
    data: data
  })
}

// 删除培训计划变更
export function delZjTrainingPlanChange(id) {
  return request({
    url: '/inspection/zjTrainingPlanChange/' + id,
    method: 'delete'
  })
}
