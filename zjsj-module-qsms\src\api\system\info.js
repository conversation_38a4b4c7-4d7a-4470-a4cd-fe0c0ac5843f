import request from "@/utils/request";

// 查询企业信息列表
export function listInfo(query) {
  return request({
    url: "/system/info/list",
    method: "get",
    params: query,
  });
}

// 查询企业信息详细
export function getInfo(id) {
  return request({
    url: "/system/info/" + id,
    method: "get",
  });
}

// 新增企业信息
export function addInfo(data) {
  return request({
    url: "/system/info",
    method: "post",
    data: data,
  });
}

// 修改企业信息
export function updateInfo(data) {
  return request({
    url: "/system/info",
    method: "put",
    data: data,
  });
}

// 删除企业信息
export function delInfo(id) {
  return request({
    url: "/system/info/" + id,
    method: "delete",
  });
}
// 组织架构
// export function getFirstOrgTree(query) {
//   return request({
//     url: "/inspection/zjOrganizationalStructureInfo/querytree",
//     method: "get",
//     params: query,
//   });
// }

export function getOrgTree(query) {
  return request({
    url: "/inspection/zjOrganizationalStructureInfo/tree",
    method: "get",
    params: query,
  });
}
// 组织架构
export function querytree(query) {
  return request({
    url: "/app/querytree",
    method: "get",
    params: query,
  });
}
// 人员列表
export function listPeople(query) {
  return request({
    url: "/app/queryUserTree",
    method: "get",
    params: query,
  });
}
// 企业信息 组织架构 一级
export function getEnterpriseInfo(query) {
  return request({
    url: "system/info/enterpriseTree",
    method: "get",
    params: query,
  });
}
// 企业信息 组织架构 子节点
export function getEnterpriseInfoTree(query) {
  return request({
    url: "system/info/tree",
    method: "get",
    params: query,
  });
}
// 项目信息 所属公司
export function getCompanyList(query) {
  return request({
    url: "/inspection/zjProjectInfo/getBelongCompanyList",
    method: "get",
    params: query,
  });
}
