<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="120px"
    >
      <!-- <el-form-item label="年份" prop="planYear">
        <el-input
          v-model="queryParams.planYear"
          placeholder="请输入年份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计划名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="所属单位" prop="belongingUnit">
        <el-input
          v-model="queryParams.belongingUnit"
          placeholder="请输入所属单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="责任人" prop="responsibleName">
        <el-input
          v-model="queryParams.responsibleName"
          placeholder="请输入责任人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="状态">
        <el-select
          v-model="queryParams.planStatus"
          placeholder="请选择状态"
          clearable
          @keyup.enter.native="handleQuery"
        >
          <el-option label="已发布" value="1" />
          <el-option label="未发布" value="0" />
          <el-option label="审批中" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="培训时间" prop="trainingTime">
        <el-date-picker
          clearable
          v-model="queryParams.trainingTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择培训时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="关键字">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <!-- <el-form-item label="参训人员" prop="trainees">
        <el-input
          v-model="queryParams.trainees"
          placeholder="请输入参训人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参训人数" prop="participantsNumber">
        <el-input
          v-model="queryParams.participantsNumber"
          placeholder="请输入参训人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="附件url" prop="attachmentUrl">
        <el-input
          v-model="queryParams.attachmentUrl"
          placeholder="请输入附件url"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjAnnualPlanManagement:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjAnnualPlanManagement:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjAnnualPlanManagement:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjAnnualPlanManagement:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjAnnualPlanManagementList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
      <el-table-column label="年份" align="center" prop="planYear" />
      <el-table-column
        label="计划名称"
        align="center"
        prop="planName"
        width="120"
      >
        <template slot-scope="{ row }">
          <div class="two-lines-ellipsis">
            {{ row.planName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="所属单位"
        align="center"
        prop="belongingUnit"
        width="110"
      >
        <template slot-scope="{ row }">
          <UnitEcho :unit="row.belongingUnit" />
        </template>
      </el-table-column>
      <!-- 1-岗前 2-在岗 3-应急 4-专项 -->
      <el-table-column label="培训类型" align="center" prop="trainingType">
        <template slot-scope="{ row }">
          {{ getTrainingType(row.trainingType) }}
        </template>
      </el-table-column>
      <el-table-column label="责任人" align="center" prop="responsibleName" />
      <el-table-column
        label="培训时间"
        align="center"
        prop="trainingTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.trainingTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参训人员" align="center" prop="trainees" />
      <el-table-column
        label="参训人数"
        align="center"
        prop="participantsNumber"
      >
        <template #default="{ row }"> {{ row.participantsNumber }}人 </template>
      </el-table-column>
      <el-table-column label="当前状态" align="center" prop="planStatus">
        <template slot-scope="{ row }">
          {{ getPlanStatus(row.planStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="培训形式" align="center" prop="trainingForm" />
      <el-table-column label="附件" align="center" prop="attachmentUrl">
        <template slot-scope="{ row }">
          <el-button
            v-if="row.attachmentUrl"
            size="mini"
            type="text"
            @click="handleAttach(row.attachmentUrl)"
            >查看</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="250"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)"
            >查看</el-button
          >
          <el-button size="mini" type="text">提醒</el-button>
          <el-button size="mini" type="text">审批</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjAnnualPlanManagement:edit']"
            >编辑</el-button
          >
          <el-button size="mini" type="text">变更</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjAnnualPlanManagement:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改年度计划管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="年份" prop="planYear">
          <el-input v-model="form.planYear" placeholder="请输入年份" />
        </el-form-item>
        <el-form-item label="计划名称" prop="planName">
          <el-input v-model="form.planName" placeholder="请输入计划名称" />
        </el-form-item>
        <el-form-item label="所属单位" prop="belongingUnit">
          <SelectDept
            :belongingUnit="form.belongingUnit"
            @update:belongingUnit="form.belongingUnit = $event"
          ></SelectDept>
        </el-form-item>
        <el-form-item label="培训类型" prop="trainingType">
          <el-select v-model="form.trainingType" style="width: 100%">
            <el-option label="岗前" value="1"></el-option>
            <el-option label="在岗" value="2"></el-option>
            <el-option label="应急" value="3"></el-option>
            <el-option label="专项" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="责任人" prop="responsibleName">
          <el-input v-model="form.responsibleName" placeholder="请输入责任人" />
        </el-form-item>
        <el-form-item label="培训时间" prop="trainingTime" style="width: 100%">
          <el-date-picker
            clearable
            v-model="form.trainingTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择培训时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="参训人员" prop="trainees">
          <el-input v-model="form.trainees" placeholder="请输入参训人员" />
        </el-form-item>
        <el-form-item label="参训人数" prop="participantsNumber">
          <el-input
            v-model="form.participantsNumber"
            placeholder="请输入参训人数"
          />
        </el-form-item>
        <el-form-item label="附件url" prop="attachmentUrl">
          <file-upload v-model="form.attachmentUrl" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog
      v-model="attachmentDialogVisible"
      :attachmentList="attachmentList"
    ></AttachmentDialog>
  </div>
</template>

<script>
import {
  listZjAnnualPlanManagement,
  getZjAnnualPlanManagement,
  delZjAnnualPlanManagement,
  addZjAnnualPlanManagement,
  updateZjAnnualPlanManagement,
} from "@/api/inspection/zjAnnualPlanManagement";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";
import SelectDept from "@/views/components/selectDept.vue";
import UnitEcho from "@/views/components/UnitEcho.vue";

export default {
  name: "ZjAnnualPlanManagement",
  components: {
    AttachmentDialog,
    SelectDept,
    UnitEcho,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 年度计划管理表格数据
      zjAnnualPlanManagementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planYear: null,
        planName: null,
        belongingUnit: null,
        trainingType: null,
        responsibleName: null,
        trainingTime: null,
        trainees: null,
        participantsNumber: null,
        planStatus: null,
        attachmentUrl: null,
      },
      // 表单参数
      form: {
        planStatus: "",
      },
      // 表单校验
      rules: {},
      attachmentDialogVisible: false,
      attachmentList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // <!-- ：0-未开始 1-进行中 2-已完成 -->
    getPlanStatus(planStatus) {
      const arr = ["未开始", "进行中", "已完成"];
      return arr[Number(planStatus)];
    },
    handleAttach(attachmentUrl) {
      this.attachmentDialogVisible = true;
      this.attachmentList = attachmentUrl.split(",");
    },
    getTrainingType(type) {
      // <!-- 1-岗前 2-在岗 3-应急 4-专项 -->
      const arr = ["岗前", "在岗", "应急", "专项"];
      return arr[type - 1];
    },
    /** 查询年度计划管理列表 */
    getList() {
      this.loading = true;
      listZjAnnualPlanManagement(this.queryParams).then((response) => {
        this.zjAnnualPlanManagementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        planYear: null,
        planName: null,
        belongingUnit: null,
        trainingType: null,
        responsibleName: null,
        trainingTime: null,
        trainees: null,
        participantsNumber: null,
        planStatus: null,
        attachmentUrl: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加年度计划管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjAnnualPlanManagement(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改年度计划管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjAnnualPlanManagement(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.planStatus = "0";
            addZjAnnualPlanManagement(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除年度计划管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjAnnualPlanManagement(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjAnnualPlanManagement/export",
        {
          ...this.queryParams,
        },
        `zjAnnualPlanManagement_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
