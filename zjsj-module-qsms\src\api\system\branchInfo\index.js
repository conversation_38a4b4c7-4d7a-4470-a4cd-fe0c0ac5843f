import request from '@/utils/request'

// 查询分公司考核列表
export function listBranch(query) {
  return request({
    url: '/system/branch/list',
    method: 'get',
    params: query
  })
}

// 查询分公司考核详细
export function getBranch(id) {
  return request({
    url: '/system/branch/' + id,
    method: 'get'
  })
}

// 新增分公司考核
export function addBranch(data) {
  return request({
    url: '/system/branch',
    method: 'post',
    data: data
  })
}

// 修改分公司考核
export function updateBranch(data) {
  return request({
    url: '/system/branch',
    method: 'put',
    data: data
  })
}

// 删除分公司考核
export function delBranch(id) {
  return request({
    url: '/system/branch/' + id,
    method: 'delete'
  })
}
