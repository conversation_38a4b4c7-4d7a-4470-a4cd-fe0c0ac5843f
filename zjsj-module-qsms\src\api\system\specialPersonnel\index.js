import request from '@/utils/request'

// 查询特种作业人员列表
export function listWorkers(query) {
  return request({
    url: '/system/specialWorkers/list',
    method: 'get',
    params: query
  })
}

// 查询特种作业人员详细
export function getWorkers(id) {
  return request({
    url: '/system/specialWorkers/' + id,
    method: 'get'
  })
}

// 新增特种作业人员
export function addWorkers(data) {
  return request({
    url: '/system/specialWorkers',
    method: 'post',
    data: data
  })
}

// 修改特种作业人员
export function updateWorkers(data) {
  return request({
    url: '/system/specialWorkers',
    method: 'put',
    data: data
  })
}

// 删除特种作业人员
export function delWorkers(id) {
  return request({
    url: '/system/specialWorkers/' + id,
    method: 'delete'
  })
}
