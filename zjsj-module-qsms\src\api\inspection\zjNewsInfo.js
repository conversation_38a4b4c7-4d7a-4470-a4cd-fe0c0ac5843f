import request from "@/utils/request";

// 查询新闻管理列表
export function listZjNewsInfo(query) {
  return request({
    url: "/inspection/zjNewsInfo/list",
    method: "get",
    params: query,
  });
}

// 查询新闻管理详细
export function getZjNewsInfo(id) {
  return request({
    url: "/inspection/zjNewsInfo/" + id,
    method: "get",
  });
}

// 新增新闻管理
export function addZjNewsInfo(data) {
  return request({
    url: "/inspection/zjNewsInfo",
    method: "post",
    data: data,
  });
}

// 修改新闻管理
export function updateZjNewsInfo(data) {
  return request({
    url: "/inspection/zjNewsInfo",
    method: "put",
    data: data,
  });
}

// 删除新闻管理
export function delZjNewsInfo(id) {
  return request({
    url: "/inspection/zjNewsInfo/" + id,
    method: "delete",
  });
}
// 门户页面查询已发布的新闻
export function listZjNewsInfoPortal(query) {
  return request({
    url: "/inspection/zjNewsInfo/querylist",
    method: "get",
    params: query,
  });
}
